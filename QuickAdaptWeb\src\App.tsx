import React, { Suspense, lazy,useEffect,useState,useCallback } from "react";
import { BrowserRouter as Router } from "react-router-dom";
import "./App.scss";
import CircularIndeterminate from "./components/common/CircularIndeterminate";
import Layout from "./components/layout/Layout";
import { RtlProvider } from "./RtlContext";
import { SnackbarProvider } from "./SnackbarContext";
import Routing from "./routing/Routings";
import { AuthProvider, useAuth } from "./components/auth/AuthProvider";
import { useLocation,useNavigate } from "react-router-dom";
import { useParams } from "react-router-dom";
import { ResetPasswordLinkConsumed } from "./services/ExpirelinkService";
import { setupInterceptors } from "./services/APIService";
import { AccountProvider } from "./components/account/AccountContext";
import { ExtensionProvider } from "./ExtensionContext";
import Cookies from "js-cookie";
import { User } from "./models/User";
import jwtDecode from "jwt-decode";

function App() {
	const location = useLocation();
		//const passwordLogId = useParams();
  const navigate = useNavigate();
  setupInterceptors(navigate, (message: string, severity = 'info') => {
    
  });
	const { signOut, loggedOut } = useAuth();
		//const history = useHistory();
  const [isResetLinkValid, setIsResetLinkValid] = useState(false);
  const [isCheckingLink, setIsCheckingLink] = useState(false);
  //const noLayoutRoutes = ["/login", "/forgotpassword", "/resetpassword/passwordLogId", "/admin/adminlogin"];
  const [loginUserDetails, setLoginUserDetail] = useState<User | null>();
  const noLayoutRoutes = ["/login", "/forgotpassword", "/resetpassword/:passwordLogId", "/uninstall","/admin/adminlogin", "/linkexpired"];
  const uuidRegex = "[0-9a-fA-F-]{36}";
  const isNoLayoutRoute = noLayoutRoutes.some(route => {
    if (route === "/resetpassword/:passwordLogId") {
      const resetPasswordRegex = new RegExp(`^/resetpassword/${uuidRegex}$`);
      return resetPasswordRegex.test(location.pathname);
    }

		return location.pathname === route;
  });
  // Check if this is an automatic login flow from free trial
  const urlParams = new URLSearchParams(location.search);
  const isAutoLogin = urlParams.get('auto_login') === 'true';
  const urlToken = urlParams.get('token');

  // Check if this is an auto-login flow first
  const hasAutoLoginParams = urlParams.has('token') && urlParams.has('user_id') && urlParams.has('org_id');
  const isAutoLoginCompleted = sessionStorage.getItem('autoLoginCompleted') === 'true';
  const isOnLoginPage = location.pathname.includes('/login');

  // Check if we have user data from auto-login (indicates completed auto-login)
  const userInfo = JSON.parse(localStorage.getItem("userInfo") || '{}');
  const hasAutoLoginUserData = userInfo['user'] && userInfo['oidc-info'];

  // Get token from URL if auto login, otherwise from localStorage
  // Also check localStorage if auto-login was completed
  const token = (isAutoLogin && urlToken) ? urlToken : localStorage.getItem("access_token");



  // Check if we need to redirect to login for auto-login processing
  const shouldRedirectToLogin = hasAutoLoginParams && !isOnLoginPage && !isAutoLoginCompleted;

  // Skip token validation entirely if we're in auto-login flow, it's completed, or we have auto-login user data
  if (hasAutoLoginParams || isAutoLoginCompleted || hasAutoLoginUserData) {
    // Skip token validation for auto-login flow
  } else if (token) {
    // Only validate JWT tokens for regular login flow
    try {
      const decodedToken: any = jwtDecode(token);
      const currentTime = Math.floor(Date.now() / 1000);
      if (decodedToken.exp < currentTime) {
        // Double-check we're not in auto-login before clearing
        if (!hasAutoLoginParams && !isAutoLoginCompleted && !hasAutoLoginUserData) {
          localStorage.clear();
          navigate("/login")
        }
      }
    } catch (error) {
      // Check if this might be an auto-login token that's not a JWT or if we have valid user info
      const userInfo = localStorage.getItem("userInfo");
      const hasValidUserInfo = userInfo && userInfo !== '{}' && userInfo !== 'null';

      if (hasValidUserInfo) {
        // Try to parse userInfo to see if it has valid oidc-info
        try {
          const parsedUserInfo = JSON.parse(userInfo);
          if (parsedUserInfo['oidc-info'] && parsedUserInfo['user']) {
            // Don't clear storage if we have valid user info - just skip the clearing logic
          } else {
            // Invalid userInfo, proceed with clearing
            if (!hasAutoLoginParams && !isAutoLoginCompleted && !hasAutoLoginUserData) {
              localStorage.clear();
              navigate("/login");
            }
          }
        } catch (parseError) {
          // If we can't parse userInfo, proceed with clearing
          if (!hasAutoLoginParams && !isAutoLoginCompleted && !hasAutoLoginUserData) {
            localStorage.clear();
            navigate("/login");
          }
        }
      } else {
        // No valid userInfo found, proceed with clearing
        if (!hasAutoLoginParams && !isAutoLoginCompleted && !hasAutoLoginUserData) {
          localStorage.clear();
          navigate("/login");
        }
      }
    }
  } else if (!hasAutoLoginParams && !isAutoLoginCompleted && !hasAutoLoginUserData) {
    // No token and not in auto-login flow - check if we should redirect to login
    const userInfo = localStorage.getItem("userInfo");
    const hasValidUserInfo = userInfo && userInfo !== '{}' && userInfo !== 'null';

    if (!hasValidUserInfo && !isOnLoginPage) {
      navigate("/login");
    }
  }
  const extractPasswordLogId = () => {
    const match = location.pathname.match(new RegExp(`/resetpassword/(${uuidRegex})`));
    return match ? match[1] : null;
  };

  const passwordLogId = extractPasswordLogId();
  const checkResetLinkConsumed = useCallback(async (passwordLogId: any) => {
    try {
      setIsCheckingLink(true);
		const response = await ResetPasswordLinkConsumed(passwordLogId);

      if (response === true) {
        const isConsumed = await response
        setIsResetLinkValid(!isConsumed);
        if (isConsumed === true) {
          navigate("/linkexpired");
        }
      } else {
        navigate(`/resetpassword/${passwordLogId}`)
      }
	} catch (error) {

      	navigate("/login")
    } finally {
      setIsCheckingLink(false);
    }
	}, [navigate]);



  // Handle auto-login redirect
  useEffect(() => {
    if (shouldRedirectToLogin) {
      navigate('/login' + location.search, { replace: true });
    }
  }, [shouldRedirectToLogin, navigate, location.search]);

  useEffect(() => {
	if (location.pathname.includes("/resetpassword") && passwordLogId) {
		checkResetLinkConsumed(passwordLogId);
	}
  }, [passwordLogId, checkResetLinkConsumed, location.pathname]);

  // Load QuickAdopt embedded script dynamically with user details
  useEffect(() => {
    const scriptUrl = process.env.REACT_APP_EMBEDDED_SCRIPT_URL;
    const enableScript = process.env.REACT_APP_ENABLE_EMBEDDED_SCRIPT === 'true';

    if (!enableScript) {
      console.log('QuickAdopt embedded script is disabled via REACT_APP_ENABLE_EMBEDDED_SCRIPT environment variable');
      return;
    }

    if (!scriptUrl) {
      console.warn('REACT_APP_EMBEDDED_SCRIPT_URL environment variable is not set');
      return;
    }

    // QuickAdopt script with user details capture
    (function (g: any, u: any, i: any, d: any, e: any, s: any) {
      g[e] = g[e] || []
      g.AccountId = s;
      var f = u.getElementsByTagName(i)[0]
      var k = u.createElement(i) as HTMLScriptElement;
      k.async = true;
      k.src = scriptUrl;
      f.parentNode.insertBefore(k, f);
      k.onload = function() {
        if (g.captureUserDetails) {
          // Get user details from localStorage or context
          const userInfo = JSON.parse(localStorage.getItem("userInfo") || '{}');
          const userDetails = userInfo['user'] ? JSON.parse(userInfo['user']) : {};
          const orgDetails = userInfo['orgDetails'] ? JSON.parse(userInfo['orgDetails']) : {};

          g.captureUserDetails({
            OrganizationId: orgDetails.OrganizationId || '',
            UserId: userDetails.UserId || '',
            UserName: `${userDetails.FirstName || ''} ${userDetails.LastName || ''}`.trim(),
            EmailId: userDetails.EmailId || '',
            UserType: userDetails.UserType || '',
            ScreenResolution: `${window.screen.width}x${window.screen.height}`,
            SessionId: localStorage.getItem("access_token") || '',
            TimeZone: Intl.DateTimeFormat().resolvedOptions().timeZone || ''
          });
        }
      }
    })(window, document, 'script', 'guiding', 'layer', '25072025-080558004-6eaec78c-56d9-4013-ab79-aba4c21dbc44');

    return () => {
      // Cleanup: remove script when component unmounts
      const existingScript = document.querySelector('script[src="' + scriptUrl + '"]');
      if (existingScript && document.body.contains(existingScript)) {
        document.body.removeChild(existingScript);
      }
    };
  }, []); // Empty dependency array means this runs once on mount

  // Extension detection is now handled by ExtensionContext
  if (isCheckingLink) {
    return <div>Loading...</div>;
  }

  // Show loading state when redirecting for auto-login
  if (shouldRedirectToLogin) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <div>Redirecting to login...</div>
      </div>
    );
  }

  return (
	  <>
      <SnackbarProvider>
        <ExtensionProvider>
          {isNoLayoutRoute ? (
            <Routing />
          ) : (
            <AuthProvider>
              <AuthWrapper />
            </AuthProvider>
          )}
        </ExtensionProvider>
      </SnackbarProvider>
    </>
  );

}

// AuthWrapper to handle loading state
const AuthWrapper: React.FC = () => {
  const { isLoading } = useAuth();

  if (isLoading) {
    return <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
      <CircularIndeterminate />
    </div>;
  }

  return (
    <AccountProvider>
      <Suspense fallback={<div>Loading...</div>}>
        <RtlProvider>
          <Layout>
            <Routing />
          </Layout>
        </RtlProvider>
      </Suspense>
    </AccountProvider>
  );
};

export default App;
