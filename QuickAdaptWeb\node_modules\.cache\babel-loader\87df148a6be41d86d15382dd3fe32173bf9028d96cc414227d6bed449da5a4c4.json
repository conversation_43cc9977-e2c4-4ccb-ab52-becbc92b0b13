{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptWeb\\\\src\\\\components\\\\adminMenu\\\\AdminMenu.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, startTransition, useContext } from \"react\";\nimport AccountCircleOutlinedIcon from '@mui/icons-material/AccountCircleOutlined';\nimport { useNavigate } from \"react-router-dom\";\nimport i18n from '../multilingual/i18n';\nimport { Popover, Typography, Button, Box } from '@mui/material';\nimport { initialsData } from \"../auth/AuthProvider\";\nimport { initialssData } from \"../settings/ProfileSettings\";\nimport SideMenu from \"./sideMenu\";\nimport { erroricon } from \"../../assets/icons/icons\";\nimport { FormControl, InputLabel, InputAdornment, MenuItem } from \"@mui/material\";\nimport Select from \"@mui/material/Select\";\nimport Translater from \"../multilingual/Multilingual\";\nimport { useLocation } from \"react-router-dom\";\nimport { LanguageProvider } from \"../multilingual/LanguageContext\";\nimport ProfileSettings from \"../settings/ProfileSettings\";\nimport Settings from \"../settings/Settings\";\nimport { getLabels, getLanguages, updateLanguage } from \"../../services/MultilingualService\";\nimport { subscribe } from \"../adminMenu/sidemenustate\";\nimport PageWrapper from \"../pagewrapper\";\nimport { Feedback, Logout, logo, global, profile, ArrowDown } from \"../../assets/icons/icons\";\nimport { isSidebarOpen } from \"../adminMenu/sidemenustate\";\nimport TeamSettings from \"../settings/TeamSettings\";\nimport RightSettings from \"../settings/RightSettings\";\nimport UserList from \"../user/UserList\";\nimport AccountList from \"../account/AccountList\";\nimport AgentsList from \"../agents/Agentslist\";\nimport Scripts from \"../agents/Scripts\";\nimport ScriptHistory from \"../agents/ScriptHistory\";\nimport ScriptHistoryViewer from \"../agents/ScriptHistoryViewer\";\nimport Training from \"../training/Training\";\nimport DomainSettings from \"../settings/DomainSettings\";\nimport AlertSettings from \"../settings/AlertSettings\";\nimport BillingSettings from \"../settings/BillingSettings\";\nimport CodeInstall from \"../settings/InstallSettings\";\nimport { useAuth } from \"../auth/AuthProvider\";\nimport AuditLogList from \"../auditLog/AuditLogList\";\nimport ShareFeedbackPopup from \"../feedback/ShareFeedbackPopup\";\nimport { useTranslation } from \"react-i18next\";\nimport { GetAccountsByUser, GetAccountsList } from \"../../services/GuideService\";\nimport { AccountContext } from \"../account/AccountContext\";\nimport { adminApiService, userApiService } from \"../../services/APIService\";\nimport { getOrganizationById } from \"../../services/OrganizationService\";\nimport { useRtl } from \"../../RtlContext\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet accountId;\n\n//let organizationLanguages : OrganizationLanguage[] = [];\n\n// Complete list of 60 world languages (excluding English as primary language)\n// This will be replaced by dynamic data from API\nexport const availableLanguages = [];\n\n// Helper function to get native language display name using Intl.DisplayNames API\nconst getLanguageDisplayName = (languageName, languageCode) => {\n  try {\n    if (languageCode) {\n      const displayNames = new Intl.DisplayNames([languageCode], {\n        type: 'language'\n      });\n      const nativeName = displayNames.of(languageCode);\n      if (nativeName && nativeName !== languageCode) {\n        return nativeName;\n      }\n    }\n    // Fallback: return the original language name\n    return languageName;\n  } catch (error) {\n    console.warn(`Failed to get native name for language: ${languageName}`, error);\n    return languageName;\n  }\n};\n\n// // Helper to set RTL attribute on body\nfunction setBodyRtlAttribute(rtlValue) {\n  if (rtlValue) {\n    document.body.classList.add('rtl');\n    document.body.setAttribute('dir', 'rtl');\n  } else {\n    document.body.classList.contains('rtl') && document.body.classList.remove('rtl');\n    document.body.removeAttribute('dir');\n  }\n}\nconst OrgAdminMenu = () => {\n  _s();\n  var _userDetails$UserType, _userDetails$Organiza;\n  const {\n    t: translate\n  } = useTranslation();\n  const [isShareFeedbackPopup, setIsShareFeedbackPopup] = useState(false);\n  const [openDropdown, setOpenDropdown] = useState(null);\n  const [sidebarOpen, setSidebarOpen] = useState(isSidebarOpen());\n  const [openMenu, setOpenMenu] = useState(false);\n  const {\n    signOut,\n    userDetails,\n    userRoles\n  } = useAuth();\n  const [userType, setUserType] = useState((_userDetails$UserType = userDetails === null || userDetails === void 0 ? void 0 : userDetails.UserType) !== null && _userDetails$UserType !== void 0 ? _userDetails$UserType : \"\");\n  const [OrganizationId, setOrganizationId] = useState((_userDetails$Organiza = userDetails === null || userDetails === void 0 ? void 0 : userDetails.OrganizationId) !== null && _userDetails$Organiza !== void 0 ? _userDetails$Organiza : \"\");\n  const [user, setUser] = useState(null);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [openPopup, setOpenPopup] = useState(false);\n  const [sidebarLocalOpen, setLocalSidebarOpen] = useState(isSidebarOpen());\n  const navigate = useNavigate();\n  const [toLanguage, setToLanguage] = useState(() => {\n    return localStorage.getItem('selectedLanguage') || (userDetails === null || userDetails === void 0 ? void 0 : userDetails.Language) || 'English';\n  });\n  const [translatedLabels, setTranslatedLabels] = useState([]);\n  const location = useLocation();\n  const [availableLanguages, setAvailableLanguages] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedLanguages, setSelectedLanguages] = useState([]);\n  const [languageSearchTerm, setLanguageSearchTerm] = useState('');\n  const ORGANIZATION_ID = userDetails === null || userDetails === void 0 ? void 0 : userDetails.OrganizationId;\n  const [accounts, setAccounts] = useState([]);\n  const [selectedAccountId, setSelectedAccountId] = useState('');\n  const [accountsFetched, setAccountsFetched] = useState(false);\n  const {\n    setAccountId,\n    setRoles\n  } = useContext(AccountContext);\n  const firstDropdownRef = useRef(null);\n  const secondDropdownRef = useRef(null);\n  const {\n    isRtl,\n    setRtl\n  } = useRtl();\n  const handleDropdownOpen = dropdownName => {\n    const newState = dropdownName === openDropdown ? null : dropdownName;\n    setOpenDropdown(newState);\n    if (dropdownName === \"second\" && newState === null) {\n      setLanguageSearchTerm('');\n    }\n  };\n  const handleClickOutside = event => {\n    if (firstDropdownRef.current && !firstDropdownRef.current.contains(event.target) && secondDropdownRef.current && !secondDropdownRef.current.contains(event.target)) {\n      setOpenDropdown(null);\n      setLanguageSearchTerm(''); // Reset search term when clicking outside\n    }\n  };\n  useEffect(() => {\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n  useEffect(() => {\n    const organizationId = userDetails === null || userDetails === void 0 ? void 0 : userDetails.OrganizationId;\n    const userType = userDetails === null || userDetails === void 0 ? void 0 : userDetails.UserType;\n    if (organizationId && userType && !accountsFetched) {\n      const fetchAccounts = async () => {\n        setLoading(true);\n        try {\n          if (userType.toLocaleLowerCase() === 'admin') {\n            await GetAccountsList(setAccounts, setLoading, organizationId, -1, -1, \"\", \"\", \"\");\n          } else if (userType.toLocaleLowerCase() === 'user') {\n            await GetAccountsByUser(setAccounts, setLoading, organizationId, -1, -1, \"\", \"\", \"\");\n          }\n          setAccountsFetched(true);\n        } catch (err) {\n          console.error(err);\n        } finally {\n          setLoading(false);\n        }\n      };\n      fetchAccounts();\n    }\n  }, [userDetails === null || userDetails === void 0 ? void 0 : userDetails.OrganizationId, userDetails === null || userDetails === void 0 ? void 0 : userDetails.UserType, accountsFetched]);\n  const handleAccountChange = event => {\n    const AccountId = event.target.value;\n    setSelectedAccountId(AccountId);\n    setAccountId(AccountId);\n    const rolesOfAccount = userType.toLocaleLowerCase() == \"admin\" ? [\"Account Admin\"] : userRoles[AccountId];\n    setRoles(rolesOfAccount);\n    accountId = AccountId;\n    localStorage.setItem(\"CurrentAccountId\", accountId);\n  };\n  useEffect(() => {\n    const userInfoString = localStorage.getItem(\"userInfo\");\n    if (userInfoString && userInfoString != '{}') {\n      try {\n        const userInfo = JSON.parse(userInfoString);\n        if (userInfo['user']) {\n          const parsedUser = JSON.parse(userInfo['user']);\n          setUser(parsedUser);\n          if (parsedUser) {\n            var _parsedUser$UserType$, _parsedUser$UserType, _parsedUser$Organizat;\n            setUserType((_parsedUser$UserType$ = (_parsedUser$UserType = parsedUser.UserType) === null || _parsedUser$UserType === void 0 ? void 0 : _parsedUser$UserType.toLocaleLowerCase()) !== null && _parsedUser$UserType$ !== void 0 ? _parsedUser$UserType$ : '');\n            const OrgId = (_parsedUser$Organizat = parsedUser.OrganizationId) !== null && _parsedUser$Organizat !== void 0 ? _parsedUser$Organizat : '';\n            setOrganizationId(OrgId);\n          }\n        }\n      } catch (error) {\n        console.error(\"Error parsing userInfo: \", error);\n      }\n    } else if (userDetails) {\n      setUser(userDetails);\n      if (userDetails) {\n        var _userDetails$UserType2, _userDetails$UserType3, _userDetails$Organiza2;\n        setUserType((_userDetails$UserType2 = (_userDetails$UserType3 = userDetails.UserType) === null || _userDetails$UserType3 === void 0 ? void 0 : _userDetails$UserType3.toLocaleLowerCase()) !== null && _userDetails$UserType2 !== void 0 ? _userDetails$UserType2 : '');\n        const OrgId = (_userDetails$Organiza2 = userDetails.OrganizationId) !== null && _userDetails$Organiza2 !== void 0 ? _userDetails$Organiza2 : '';\n        setOrganizationId(OrgId);\n      }\n    }\n  }, [userDetails]);\n  // Fetch saved languages from API (excluding English as it's the primary language)\n  useEffect(() => {\n    const fetchSavedLanguages = async () => {\n      try {\n        if (accountId) {\n          const response = await getLanguages();\n\n          // Set all available languages\n          setAvailableLanguages(response);\n\n          // Filter out English since it's the primary language for selected languages\n          const filteredLanguages = response.filter(lang => lang.Language.toLowerCase() !== 'english');\n          setSelectedLanguages(filteredLanguages);\n\n          // Only set language from userDetails on initial load, not on every change\n          const currentLanguageExists = filteredLanguages.some(lang => lang.Language === toLanguage) || toLanguage === 'English';\n\n          // If current language doesn't exist in available languages, fall back to English\n          if (!currentLanguageExists) {\n            const fallbackLanguage = 'English';\n            setToLanguage(fallbackLanguage);\n            localStorage.setItem('selectedLanguage', fallbackLanguage);\n          }\n        }\n      } catch (err) {\n        console.error(\"Error fetching saved languages:\", err);\n        setSelectedLanguages([]);\n        setAvailableLanguages([]);\n        // Reset to userDetails.Language or English if there's an error\n        const fallbackLanguage = (userDetails === null || userDetails === void 0 ? void 0 : userDetails.Language) || 'English';\n        setToLanguage(fallbackLanguage);\n        localStorage.setItem('selectedLanguage', fallbackLanguage);\n      }\n    };\n    fetchSavedLanguages();\n  }, [accountId]); // Remove toLanguage and userDetails?.Language dependencies to prevent override\n  const ShareFeedbackClick = () => {\n    setIsShareFeedbackPopup(true);\n  };\n  const closeShareFeedback = () => {\n    setIsShareFeedbackPopup(false);\n  };\n  useEffect(() => {\n    const storedSelectedLanguages = localStorage.getItem(\"selectedLanguages\");\n    if (storedSelectedLanguages) {\n      // This can be removed as we now fetch from API\n      // setSelectedLanguages(JSON.parse(storedSelectedLanguages));\n    }\n  }, []);\n  const getLanguageName = languageCode => {\n    const language = availableLanguages.find(lang => lang.LanguageCode === languageCode);\n    return language ? language.Language : \"Unknown\";\n  };\n  const handleToLanguageChange = async event => {\n    const selectedLanguage = event.target.value;\n    console.log('Language change attempted:', selectedLanguage);\n\n    // Immediately update the state\n    setToLanguage(selectedLanguage);\n\n    // Find the language code from availableLanguages\n    const languageObj = availableLanguages.find(lang => lang.Language === selectedLanguage);\n    const languageCode = languageObj ? languageObj.LanguageCode : 'en';\n    localStorage.setItem('selectedLanguage', selectedLanguage);\n    localStorage.setItem('selectedLanguageCode', languageCode);\n\n    // Call updateLanguage API with the language name\n    try {\n      await updateLanguage(selectedLanguage);\n      console.log('Language updated successfully:', selectedLanguage);\n    } catch (error) {\n      console.error('Error updating language:', error);\n    }\n  };\n  const [labelsNew, setLabelsNew] = useState({});\n  useEffect(() => {\n    const fetchLabelsNew = async toLanguage => {\n      if (toLanguage === \"English\") {\n        i18n.changeLanguage('en');\n        return;\n      }\n      try {\n        setLoading(true);\n        const data = await getLabels(toLanguage, false);\n        if (data) {\n          // Find the language code from selectedLanguages or availableLanguages\n          const languageObj = selectedLanguages.find(lang => lang.Language === toLanguage) || availableLanguages.find(lang => lang.Language === toLanguage);\n          const languageKey = languageObj ? languageObj.LanguageCode : '';\n          if (languageKey) {\n            let parsedLabelsNew = {};\n\n            // Handle new array format\n            if (Array.isArray(data)) {\n              data.forEach(item => {\n                if (item.LabelName && item[toLanguage] !== undefined) {\n                  parsedLabelsNew[item.LabelName] = item[toLanguage];\n                }\n              });\n            } else if (data[languageKey]) {\n              // Fallback for old object format\n              parsedLabelsNew = data[languageKey];\n            }\n            if (Object.keys(parsedLabelsNew).length > 0) {\n              setLabelsNew(parsedLabelsNew);\n              i18n.addResourceBundle(languageKey, 'translation', parsedLabelsNew, true, true);\n              i18n.changeLanguage(languageKey);\n            }\n          }\n        }\n      } catch (err) {\n        console.error('Error fetching labels:', err);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchLabelsNew(toLanguage);\n  }, [toLanguage, selectedLanguages, availableLanguages]);\n  const handleMenuClick = () => {\n    setOpenMenu(prev => !prev);\n  };\n  useEffect(() => {\n    const unsubscribe = subscribe(setLocalSidebarOpen);\n    return () => unsubscribe();\n  }, []);\n  const handleHomeClick = () => {\n    startTransition(() => {\n      navigate(\"/\");\n    });\n  };\n  const handleTogglePopover = event => {\n    if (anchorEl) {\n      // Close the popover if it's open\n      setAnchorEl(null);\n    } else {\n      // Open the popover and set the anchor element\n      setAnchorEl(event.currentTarget);\n    }\n  };\n  const handleOrgClick = () => {\n    startTransition(() => {\n      navigate(\"/organizations\");\n    });\n  };\n  const handleAccountClick = () => {\n    startTransition(() => {\n      navigate(\"/accounts\");\n    });\n  };\n  const handleGuideClick = () => {\n    startTransition(() => {\n      navigate(\"/guide\");\n    });\n  };\n  const handleAccSeetingsClick = () => {\n    startTransition(() => {\n      navigate(\"/settings/account\");\n    });\n  };\n  const handleTeamClick = () => {\n    startTransition(() => {\n      navigate(\"/settings/team\");\n    });\n  };\n  const handleThemesClick = () => {\n    startTransition(() => {\n      navigate(\"/theme\");\n    });\n  };\n  const handleBillingClick = () => {\n    startTransition(() => {\n      navigate(\"/settings/billing\");\n    });\n  };\n  const handleInstallClick = () => {\n    startTransition(() => {\n      navigate(\"/settings/install\");\n    });\n  };\n  const handlenotifyClick = () => {\n    startTransition(() => {\n      navigate(\"/notifications\");\n    });\n  };\n  const handleProfileClick = () => {\n    navigate(\"/viewprofile\");\n    setAnchorEl(null);\n  };\n  const handleClick = path => {\n    switch (path) {\n      case \"user\":\n        return navigate(\"/user\");\n      case \"account\":\n        return navigate(\"/settings/account\");\n      case \"settings\":\n        return navigate(\"/settings\");\n      case \"auditlogs\":\n        return navigate(\"/superadmin/auditlogs\");\n      case \"organization\":\n        return navigate(\"/superadmin/organizations\");\n      case \"multilingual\":\n        return navigate(\"/superadmin/Multilingual\");\n      default:\n        return navigate(\"/\");\n    }\n  };\n  const handleQuickAdoptClick = () => {\n    const logedUsertype = localStorage.getItem('userType');\n    if (logedUsertype !== \"SuperAdmin\") {\n      startTransition(() => {\n        navigate(\"/\");\n      });\n    }\n  };\n  useEffect(() => {\n    if (accounts.length > 0) {\n      setSelectedAccountId(accounts[0].AccountId);\n      setAccountId(accounts[0].AccountId);\n      accountId = accounts[0].AccountId;\n      localStorage.setItem(\"CurrentAccountId\", accountId);\n      const rolesOfAccount = userType.toLocaleLowerCase() == \"admin\" ? [\"Account Admin\"] : userRoles[accounts[0].AccountId];\n      setRoles(rolesOfAccount);\n    }\n  }, [accounts]);\n  const handleLogoutClick = () => {\n    setOpenPopup(true);\n    setAnchorEl(null);\n  };\n  const toggleSidebar = () => {\n    setSidebarOpen(!sidebarLocalOpen);\n  };\n  // Fetch organization and account RTL and set body attribute\n  useEffect(() => {\n    async function fetchAndSetRtl() {\n      if (OrganizationId) {\n        // Check if we're in auto-login flow - skip API calls to avoid 401 errors\n        const urlParams = new URLSearchParams(window.location.search);\n        const hasAutoLoginParams = urlParams.has('token') && urlParams.has('user_id') && urlParams.has('org_id');\n        const isAutoLoginCompleted = sessionStorage.getItem('autoLoginCompleted') === 'true';\n        const isFreeTrialToken = sessionStorage.getItem('isFreeTrialToken') === 'true';\n\n        // Also check if we have valid userInfo indicating a recent auto-login\n        const userInfo = localStorage.getItem('userInfo');\n        const hasValidUserInfo = userInfo && userInfo !== '{}' && userInfo !== 'null';\n        let hasAutoLoginUserData = false;\n        if (hasValidUserInfo) {\n          try {\n            const parsedUserInfo = JSON.parse(userInfo);\n            hasAutoLoginUserData = parsedUserInfo['user'] && parsedUserInfo['oidc-info'];\n          } catch (e) {\n            // Ignore parse errors\n          }\n        }\n\n        // Skip API calls during auto-login flow or for free trial tokens to prevent 401 errors\n        if (hasAutoLoginParams || isFreeTrialToken) {\n          // Use default RTL setting for now\n          setRtl(false);\n          return;\n        }\n        try {\n          // Fetch organization RTL\n          const org = await getOrganizationById(OrganizationId);\n          let orgRtl = org && typeof org.RTL !== 'undefined' ? org.RTL : false;\n\n          // Fetch accounts and get first account's Rtl if available\n          let accountRtl = false;\n          const requestBody = {\n            skip: -1,\n            top: -1,\n            filters: '',\n            orderByFields: ''\n          };\n          const response = await adminApiService.post(`/Account/GetAccountsByOrgId`, requestBody);\n          const accounts = response.data.results;\n          if (Array.isArray(accounts) && accounts.length > 0) {\n            const selectedAccount = accounts.find(acc => acc.AccountId === selectedAccountId);\n            if (selectedAccount && typeof selectedAccount.Rtl !== 'undefined') {\n              accountRtl = selectedAccount.Rtl;\n            }\n          }\n          setRtl(accountRtl); // Use context setter\n        } catch (e) {\n          console.error('Failed to fetch account for RTL:', e);\n          // Set default RTL on error\n          setRtl(false);\n        }\n      }\n    }\n    fetchAndSetRtl();\n  }, [OrganizationId, selectedAccountId, setRtl]);\n  useEffect(() => {\n    setBodyRtlAttribute(isRtl);\n  }, [isRtl]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-banner\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"adapat-banner-left\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: logo,\n              alt: \"QuickAdopt Logo\",\n              className: \"qadpt-logo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 568,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 567,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"adapat-banner-right\",\n        children: [openMenu && userType.toLocaleLowerCase() === \"superadmin\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-settings-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            onClick: () => handleClick(\"organization\"),\n            className: \"qadapt-link\",\n            children: translate('Organization')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            onClick: () => handleClick(\"auditlogs\"),\n            className: \"qadapt-link\",\n            children: translate('Auditlogs')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 588,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            onClick: () => handleClick(\"multilingual\"),\n            className: \"qadapt-link\",\n            children: translate('Multilingual')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 595,\n            columnNumber: 8\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 581,\n          columnNumber: 7\n        }, this) : null, userType.toLocaleLowerCase() === \"superadmin\" && /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fal fa-admenu menu-icon\",\n          \"data-testid\": \"cog-tci\",\n          onClick: handleMenuClick\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 604,\n          columnNumber: 7\n        }, this), userType !== \"superadmin\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qxy-lang\",\n          style: {\n            width: \"auto\"\n          },\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            variant: \"standard\",\n            className: \"lang-input\",\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              value: selectedAccountId,\n              onChange: handleAccountChange,\n              displayEmpty: true,\n              fullWidth: true,\n              startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"start\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: profile,\n                  alt: \"profile Logo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 622,\n                  columnNumber: 12\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 11\n              }, this),\n              endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"end\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: ArrowDown,\n                  alt: \"Arrowdown Logo\",\n                  style: {\n                    cursor: 'pointer',\n                    zoom: \"0.9\",\n                    transform: openDropdown === \"first\" ? 'rotate(180deg)' : 'rotate(0deg)',\n                    transition: 'transform 0.3s ease'\n                  },\n                  onClick: e => {\n                    e.stopPropagation(); // Prevent default select behavior if needed\n                    handleDropdownOpen(\"first\"); // Trigger dropdown open\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 631,\n                  columnNumber: 12\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 11\n              }, this),\n              open: openDropdown === \"first\",\n              onClose: () => setOpenDropdown(null),\n              onOpen: () => handleDropdownOpen(\"first\"),\n              IconComponent: () => null,\n              sx: {\n                \"&:before, &:after\": {\n                  display: \"none\"\n                }\n              },\n              MenuProps: {\n                PaperProps: {\n                  sx: {\n                    width: '200px',\n                    // Dropdown width\n                    maxHeight: '305px',\n                    // Maximum height for dropdown\n                    overflowY: 'auto',\n                    // Enable vertical scrolling\n                    marginTop: '5px',\n                    // Small gap from select input\n                    '& .MuiMenuItem-root': {\n                      fontSize: '14px',\n                      // Font size for dropdown items\n                      whiteSpace: 'normal',\n                      // Text wrapping\n                      wordBreak: 'break-word' // Word breaking\n                    }\n                  }\n                }\n              },\n              children: accounts.length > 0 ? accounts.map(account => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: account.AccountId,\n                children: account.AccountName\n              }, account.AccountId, false, {\n                fileName: _jsxFileName,\n                lineNumber: 672,\n                columnNumber: 12\n              }, this)) : /*#__PURE__*/_jsxDEV(MenuItem, {\n                disabled: true,\n                children: \"No accounts available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 677,\n                columnNumber: 11\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 612,\n          columnNumber: 7\n        }, this) : null, location.pathname !== \"settings/multilingual\" && userType !== \"superadmin\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qxy-lang\",\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              variant: \"standard\",\n              className: \"lang-input\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                id: \"demo-simple-select\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 689,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                className: \"lang-input-box\",\n                labelId: \"demo-simple-select\",\n                id: \"demo-simple\",\n                value: toLanguage // Controlled component\n                ,\n                onChange: handleToLanguageChange,\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: global,\n                    alt: \"global Logo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 698,\n                    columnNumber: 13\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 697,\n                  columnNumber: 12\n                }, this),\n                endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"end\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: ArrowDown,\n                    alt: \"Arrowdown Logo\",\n                    style: {\n                      cursor: 'pointer',\n                      zoom: '0.9',\n                      transform: openDropdown === \"second\" ? 'rotate(180deg)' : 'rotate(0deg)',\n                      transition: 'transform 0.3s ease'\n                    },\n                    onClick: e => {\n                      e.stopPropagation(); // Prevent default select behavior\n                      setOpenDropdown(openDropdown === \"second\" ? null : \"second\"); // Toggle logic\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 703,\n                    columnNumber: 13\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 702,\n                  columnNumber: 12\n                }, this),\n                IconComponent: () => null,\n                open: openDropdown === \"second\",\n                onClose: () => {\n                  setOpenDropdown(null);\n                  setLanguageSearchTerm('');\n                },\n                onOpen: () => handleDropdownOpen(\"second\"),\n                sx: {\n                  \"&:before, &:after\": {\n                    display: \"none\"\n                  }\n                },\n                MenuProps: {\n                  PaperProps: {\n                    sx: {\n                      width: '250px',\n                      maxHeight: '350px',\n                      overflowY: 'auto',\n                      marginTop: '5px',\n                      '& .MuiMenuItem-root': {\n                        fontSize: '14px',\n                        whiteSpace: 'normal',\n                        wordBreak: 'break-word'\n                      },\n                      '& .search-container': {\n                        position: 'sticky',\n                        top: 0,\n                        backgroundColor: 'white',\n                        borderBottom: '1px solid #e0e0e0',\n                        padding: '8px 16px',\n                        zIndex: 1\n                      },\n                      '& .search-input': {\n                        width: '100%',\n                        fontSize: '14px',\n                        padding: '8px 12px',\n                        border: '1px solid #ccc',\n                        borderRadius: '4px',\n                        outline: 'none',\n                        '&:focus': {\n                          borderColor: '#1976d2'\n                        }\n                      }\n                    }\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"search-container\",\n                  children: /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    className: \"search-input\",\n                    placeholder: \"Search languages...\",\n                    value: languageSearchTerm,\n                    onChange: e => {\n                      e.stopPropagation();\n                      setLanguageSearchTerm(e.target.value);\n                    },\n                    onKeyDown: e => e.stopPropagation(),\n                    onClick: e => e.stopPropagation()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 767,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 766,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"English\",\n                  children: \"English\"\n                }, \"en\", false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 780,\n                  columnNumber: 11\n                }, this), selectedLanguages.filter(lang => lang.Language.toLowerCase().includes(languageSearchTerm.toLowerCase())).sort((a, b) => a.Language.localeCompare(b.Language)).map(lang => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: lang.Language,\n                  sx: {\n                    fontSize: '14px'\n                  },\n                  children: getLanguageDisplayName(lang.Language, lang.LanguageCode)\n                }, lang.LanguageId, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 789,\n                  columnNumber: 13\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 690,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 688,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 687,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 686,\n          columnNumber: 7\n        }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-prof\",\n          onClick: handleTogglePopover,\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: \"flex\",\n              marginRight: \"0.5rem !important\"\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"prof-div\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"prof-span\",\n                  children: initialssData ? initialssData : initialsData\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 808,\n                  columnNumber: 10\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 807,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 806,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 805,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 801,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profilepopup\",\n          children: /*#__PURE__*/_jsxDEV(Popover, {\n            open: Boolean(anchorEl),\n            anchorEl: anchorEl\n            //onClose={() => setAnchorEl(null)}\n            ,\n            onClose: handleTogglePopover,\n            className: \"qadpt-prfpopup\",\n            anchorOrigin: {\n              vertical: \"top\",\n              horizontal: \"center\"\n            },\n            transformOrigin: {\n              vertical: \"bottom\",\n              horizontal: \"center\"\n            },\n            PaperProps: {\n              style: {\n                marginTop: '32px'\n              }\n            },\n            sx: {\n              \"& .MuiPaper-root\": {\n                width: \"195px\",\n                height: \"auto\",\n                borderRadius: \"7px\",\n                padding: \"10px 12px\",\n                backgroundColor: \"#ffffff\",\n                boxShadow: \"0px 4px 8px rgba(0, 0, 0, 0.1)\",\n                zIndex: 9999\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              flexDirection: \"column\",\n              alignItems: \"center\",\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                width: \"100%\",\n                children: [userType.toLocaleLowerCase() !== \"superadmin\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    fullWidth: true,\n                    onClick: ShareFeedbackClick,\n                    sx: {\n                      padding: '10px 0',\n                      gap: '10px',\n                      justifyContent: 'flex-start',\n                      textTransform: 'none'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: Feedback,\n                      alt: \"Feedback Icon\",\n                      style: {\n                        width: '18px',\n                        height: '18px',\n                        marginRight: '8px'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 865,\n                      columnNumber: 11\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      sx: {\n                        fontFamily: 'Poppins',\n                        fontSize: '14px',\n                        fontWeight: 400,\n                        lineHeight: '21px',\n                        letterSpacing: '0.3px',\n                        textAlign: 'left',\n                        color: '#202224'\n                      },\n                      children: translate('Share Feedback')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 870,\n                      columnNumber: 11\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 854,\n                    columnNumber: 10\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    fullWidth: true,\n                    onClick: handleProfileClick,\n                    sx: {\n                      padding: '10px 0',\n                      gap: '10px',\n                      justifyContent: 'flex-start',\n                      textTransform: 'none'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(AccountCircleOutlinedIcon, {\n                      style: {\n                        color: 'black'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 895,\n                      columnNumber: 11\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      sx: {\n                        fontFamily: 'Poppins',\n                        fontSize: '14px',\n                        fontWeight: 400,\n                        lineHeight: '21px',\n                        letterSpacing: '0.3px',\n                        textAlign: 'left',\n                        color: '#202224'\n                      },\n                      children: translate('View Profile')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 896,\n                      columnNumber: 11\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 884,\n                    columnNumber: 10\n                  }, this)]\n                }, void 0, true), /*#__PURE__*/_jsxDEV(Button, {\n                  fullWidth: true,\n                  onClick: handleLogoutClick,\n                  sx: {\n                    padding: '10px 0',\n                    gap: '10px',\n                    justifyContent: 'flex-start',\n                    textTransform: 'none'\n                  },\n                  className: \"qadpt-logout\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: Logout,\n                    alt: \"Logout Icon\",\n                    style: {\n                      width: '18px',\n                      height: '18px',\n                      marginRight: '8px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 922,\n                    columnNumber: 11\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    sx: {\n                      fontFamily: 'Poppins',\n                      fontSize: '14px',\n                      fontWeight: 400,\n                      lineHeight: '21px',\n                      letterSpacing: '0.3px',\n                      textAlign: 'left',\n                      color: '#202224'\n                    },\n                    children: translate('Logout')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 927,\n                    columnNumber: 11\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 910,\n                  columnNumber: 10\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 850,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 845,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 814,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 813,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 579,\n        columnNumber: 5\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 566,\n      columnNumber: 4\n    }, this), /*#__PURE__*/_jsxDEV(ShareFeedbackPopup, {\n      open: isShareFeedbackPopup,\n      onClose: closeShareFeedback,\n      setIsShareFeedbackPopup: setIsShareFeedbackPopup\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 947,\n      columnNumber: 4\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-page-content\",\n      children: [userType.toLocaleLowerCase() !== \"superadmin\" ? /*#__PURE__*/_jsxDEV(SideMenu, {\n        selectedLanguageProp: toLanguage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 953,\n        columnNumber: 54\n      }, this) : null, openPopup ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-modal-overlay\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-usrconfirm-popup qadpt-success\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: erroricon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 958,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 957,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-popup-title\",\n            children: translate('Confirmation')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 960,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-warning\",\n            children: translate('Are you sure you want to Logout?')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 961,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-buttons\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setOpenPopup(false),\n              className: \"qadpt-cancel-button\",\n              children: translate('Cancel')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 965,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: async () => {\n                setOpenPopup(false);\n                if ((userDetails === null || userDetails === void 0 ? void 0 : userDetails.UserType) !== \"SuperAdmin\") {\n                  try {\n                    const response = await userApiService.get(`/User/Logout`);\n                    if (response.status === 401) {\n                      localStorage.clear();\n                      sessionStorage.clear();\n                      navigate(\"/login\");\n                      window.location.reload();\n                    }\n                    signOut();\n                    window.location.reload();\n                  } catch (error) {\n                    // Optional: handle error here\n                  }\n                } else {\n                  localStorage.clear();\n                  sessionStorage.clear();\n                  document.cookie.split(\";\").forEach(cookie => {\n                    const [name] = cookie.split(\"=\");\n                    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;\n                  });\n                  navigate(\"/admin/adminlogin\");\n                  window.location.reload();\n                }\n              },\n              className: \"qadpt-conform-button\",\n              type: \"button\",\n              children: translate('Logout')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 971,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 964,\n            columnNumber: 8\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 956,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 955,\n        columnNumber: 6\n      }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `qadpt-settings-content ${[`/${userDetails === null || userDetails === void 0 ? void 0 : userDetails.OrganizationId}/accounts`, `/${userDetails === null || userDetails === void 0 ? void 0 : userDetails.OrganizationId}/accounts/free-trial`, `/${userDetails === null || userDetails === void 0 ? void 0 : userDetails.OrganizationId}/roles`, `/${userDetails === null || userDetails === void 0 ? void 0 : userDetails.OrganizationId}/team`, \"/settings/domains\", \"/settings/rights\", \"/settings/alerts\", \"/settings/billing\", \"/settings/install\", \"/settings/activitylog\", \"/settings/multilingual\", \"/settings/agents\", \"/settings/scripts\", \"/settings/scripthistory\", \"/settings/scripthistoryviewer\", \"/settings/training\", \"/viewprofile\"].includes(location.pathname) ? '' : 'qadpt-hide-smenu'}\n   ${location.pathname === \"/viewprofile\" ? 'qadpt-viewprofile-page' : ''}`,\n        children: [location.pathname === `/${userDetails === null || userDetails === void 0 ? void 0 : userDetails.OrganizationId}/accounts` && userType === \"admin\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-settings-page\",\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: /*#__PURE__*/_jsxDEV(PageWrapper, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-settings-smenu\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  id: \"settingMenuBox\",\n                  children: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1041,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1040,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1039,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"usr-list-page\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: /*#__PURE__*/_jsxDEV(AccountList, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1046,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1045,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1044,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1038,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1037,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1036,\n          columnNumber: 7\n        }, this) : null, location.pathname === `/${userDetails === null || userDetails === void 0 ? void 0 : userDetails.OrganizationId}/accounts/free-trial` && userType === \"admin\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-settings-page\",\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: /*#__PURE__*/_jsxDEV(PageWrapper, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-settings-smenu\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  id: \"settingMenuBox\",\n                  children: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1062,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1061,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1060,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"usr-list-page\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: /*#__PURE__*/_jsxDEV(AccountList, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1067,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1066,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1065,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1059,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1058,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1057,\n          columnNumber: 7\n        }, this) : null, location.pathname === \"/viewprofile\" ? /*#__PURE__*/_jsxDEV(PageWrapper, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              flexDirection: 'row',\n              width: '100%'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(ProfileSettings, {\n                selectedLanguageProp: getLanguageName(toLanguage)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1082,\n                columnNumber: 10\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1081,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1080,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1079,\n          columnNumber: 7\n        }, this) : null, location.pathname === `/settings/multilingual` ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-settings-page\",\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: /*#__PURE__*/_jsxDEV(PageWrapper, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-settings-smenu\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  id: \"settingMenuBox\",\n                  children: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1094,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1093,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1092,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"usr-list-page\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: /*#__PURE__*/_jsxDEV(LanguageProvider, {\n                    children: /*#__PURE__*/_jsxDEV(Translater, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1100,\n                      columnNumber: 13\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1099,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1098,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1097,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1091,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1090,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1089,\n          columnNumber: 7\n        }, this) : null, location.pathname === `/${userDetails === null || userDetails === void 0 ? void 0 : userDetails.OrganizationId}/team` && userType === \"admin\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-settings-page\",\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: /*#__PURE__*/_jsxDEV(PageWrapper, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-settings-smenu\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  id: \"settingMenuBox\",\n                  children: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1117,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1116,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1115,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"usr-list-page\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: /*#__PURE__*/_jsxDEV(UserList, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1122,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1121,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1120,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1114,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1113,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1112,\n          columnNumber: 7\n        }, this) : null, location.pathname === `/${userDetails === null || userDetails === void 0 ? void 0 : userDetails.OrganizationId}/roles` && userType === \"admin\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-settings-page\",\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: /*#__PURE__*/_jsxDEV(PageWrapper, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-settings-smenu\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  id: \"settingMenuBox\",\n                  children: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1138,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1137,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1136,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"usr-list-page\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: /*#__PURE__*/_jsxDEV(TeamSettings, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1143,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1142,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1141,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1135,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1134,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1133,\n          columnNumber: 7\n        }, this) : null, location.pathname === \"/settings/domains\" && userType === \"admin\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-settings-page\",\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: /*#__PURE__*/_jsxDEV(PageWrapper, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-settings-smenu\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  id: \"settingMenuBox\",\n                  children: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1160,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1159,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1158,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"usr-list-page\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: /*#__PURE__*/_jsxDEV(DomainSettings, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1165,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1164,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1163,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1157,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1156,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1155,\n          columnNumber: 7\n        }, this) : null, location.pathname === \"/settings/rights\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-settings-page\",\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: /*#__PURE__*/_jsxDEV(PageWrapper, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-settings-smenu\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  id: \"settingMenuBox\",\n                  children: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1182,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1181,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1180,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"usr-list-page\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: /*#__PURE__*/_jsxDEV(RightSettings, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1187,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1186,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1185,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1179,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1178,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1177,\n          columnNumber: 7\n        }, this) : null, location.pathname === \"/settings/alerts\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-settings-page\",\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: /*#__PURE__*/_jsxDEV(PageWrapper, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-settings-smenu\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  id: \"settingMenuBox\",\n                  children: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1202,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1201,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1200,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"usr-list-page\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: /*#__PURE__*/_jsxDEV(AlertSettings, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1207,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1206,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1205,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1199,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1198,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1197,\n          columnNumber: 7\n        }, this) : null, location.pathname === \"/settings/billing\" && userType === \"admin\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-settings-page\",\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: /*#__PURE__*/_jsxDEV(PageWrapper, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-settings-smenu\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  id: \"settingMenuBox\",\n                  children: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1221,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1220,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1219,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"usr-list-page\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: /*#__PURE__*/_jsxDEV(BillingSettings, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1226,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1225,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1224,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1218,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1217,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1216,\n          columnNumber: 7\n        }, this) : null, location.pathname === \"/settings/install\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-settings-page\",\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: /*#__PURE__*/_jsxDEV(PageWrapper, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-settings-smenu\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  id: \"settingMenuBox\",\n                  children: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1240,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1239,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1238,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"usr-list-page\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: /*#__PURE__*/_jsxDEV(CodeInstall, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1245,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1244,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1243,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1237,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1236,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1235,\n          columnNumber: 7\n        }, this) : null, location.pathname === \"/settings/activitylog\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-settings-page\",\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: /*#__PURE__*/_jsxDEV(PageWrapper, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-settings-smenu\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  id: \"settingMenuBox\",\n                  children: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1260,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1259,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1258,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"usr-list-page\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: /*#__PURE__*/_jsxDEV(AuditLogList, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1265,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1264,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1263,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1257,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1256,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1255,\n          columnNumber: 7\n        }, this) : null, location.pathname === `/settings/agents` ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-settings-page\",\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: /*#__PURE__*/_jsxDEV(PageWrapper, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-settings-smenu\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  id: \"settingMenuBox\",\n                  children: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1280,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1279,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1278,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"usr-list-page\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: /*#__PURE__*/_jsxDEV(AgentsList, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1285,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1284,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1283,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1277,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1276,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1275,\n          columnNumber: 7\n        }, this) : null, location.pathname === `/settings/scripts` ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-settings-page\",\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: /*#__PURE__*/_jsxDEV(PageWrapper, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-settings-smenu\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  id: \"settingMenuBox\",\n                  children: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1301,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1300,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1299,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"usr-list-page\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: /*#__PURE__*/_jsxDEV(Scripts, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1306,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1305,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1304,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1298,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1297,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1296,\n          columnNumber: 7\n        }, this) : null, location.pathname === `/settings/scripthistory` ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-settings-page\",\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: /*#__PURE__*/_jsxDEV(PageWrapper, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-settings-smenu\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  id: \"settingMenuBox\",\n                  children: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1322,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1321,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1320,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"usr-list-page\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: /*#__PURE__*/_jsxDEV(ScriptHistory, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1327,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1326,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1325,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1319,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1318,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1317,\n          columnNumber: 7\n        }, this) : null, location.pathname === `/settings/scripthistoryviewer` ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-settings-page\",\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: /*#__PURE__*/_jsxDEV(PageWrapper, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-settings-smenu\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  id: \"settingMenuBox\",\n                  children: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1343,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1342,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1341,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"usr-list-page\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: /*#__PURE__*/_jsxDEV(ScriptHistoryViewer, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1348,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1347,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1346,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1340,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1339,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1338,\n          columnNumber: 7\n        }, this) : null, location.pathname === `/settings/training` ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-settings-page\",\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: /*#__PURE__*/_jsxDEV(PageWrapper, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-settings-smenu\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  id: \"settingMenuBox\",\n                  children: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1364,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1363,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1362,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"usr-list-page\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: /*#__PURE__*/_jsxDEV(Training, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1369,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1368,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1367,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1361,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1360,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1359,\n          columnNumber: 7\n        }, this) : null]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1009,\n        columnNumber: 5\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 952,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 565,\n    columnNumber: 3\n  }, this);\n};\n_s(OrgAdminMenu, \"wgJUISiW2LJBQwbARVzGxN6MKC8=\", false, function () {\n  return [useTranslation, useAuth, useNavigate, useLocation, useRtl];\n});\n_c = OrgAdminMenu;\nexport default OrgAdminMenu;\nexport { accountId };\nvar _c;\n$RefreshReg$(_c, \"OrgAdminMenu\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "startTransition", "useContext", "AccountCircleOutlinedIcon", "useNavigate", "i18n", "Popover", "Typography", "<PERSON><PERSON>", "Box", "initialsData", "initialssData", "SideMenu", "erroricon", "FormControl", "InputLabel", "InputAdornment", "MenuItem", "Select", "Translater", "useLocation", "LanguageProvider", "ProfileSettings", "Settings", "<PERSON><PERSON><PERSON><PERSON>", "getLanguages", "updateLanguage", "subscribe", "PageWrapper", "<PERSON><PERSON><PERSON>", "Logout", "logo", "global", "profile", "ArrowDown", "isSidebarOpen", "TeamSettings", "RightSettings", "UserList", "AccountList", "AgentsList", "<PERSON><PERSON><PERSON>", "ScriptHistory", "ScriptHistoryViewer", "Training", "DomainSettings", "AlertSettings", "BillingSettings", "CodeInstall", "useAuth", "AuditLogList", "ShareFeedbackPopup", "useTranslation", "GetAccountsByUser", "GetAccountsList", "AccountContext", "adminApiService", "userApiService", "getOrganizationById", "useRtl", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "accountId", "availableLanguages", "getLanguageDisplayName", "languageName", "languageCode", "displayNames", "Intl", "DisplayNames", "type", "nativeName", "of", "error", "console", "warn", "setBodyRtlAttribute", "rtlValue", "document", "body", "classList", "add", "setAttribute", "contains", "remove", "removeAttribute", "OrgAdminMenu", "_s", "_userDetails$UserType", "_userDetails$Organiza", "t", "translate", "isShareFeedbackPopup", "setIsShareFeedbackPopup", "openDropdown", "setOpenDropdown", "sidebarOpen", "setSidebarOpen", "openMenu", "setOpenMenu", "signOut", "userDetails", "userRoles", "userType", "setUserType", "UserType", "OrganizationId", "setOrganizationId", "user", "setUser", "anchorEl", "setAnchorEl", "openPopup", "set<PERSON>penPop<PERSON>", "sidebarLocalOpen", "setLocalSidebarOpen", "navigate", "toLanguage", "setToLanguage", "localStorage", "getItem", "Language", "<PERSON><PERSON><PERSON><PERSON>", "setTranslatedLabels", "location", "setAvailableLanguages", "loading", "setLoading", "selectedLanguages", "setSelectedLanguages", "languageSearchTerm", "setLanguageSearchTerm", "ORGANIZATION_ID", "accounts", "setAccounts", "selectedAccountId", "setSelectedAccountId", "accountsFetched", "setAccountsFetched", "setAccountId", "setRoles", "firstDropdownRef", "secondDropdownRef", "isRtl", "setRtl", "handleDropdownOpen", "dropdownName", "newState", "handleClickOutside", "event", "current", "target", "addEventListener", "removeEventListener", "organizationId", "fetchAccounts", "toLocaleLowerCase", "err", "handleAccountChange", "AccountId", "value", "rolesOfAccount", "setItem", "userInfoString", "userInfo", "JSON", "parse", "parsedUser", "_parsedUser$UserType$", "_parsedUser$UserType", "_parsedUser$Organizat", "OrgId", "_userDetails$UserType2", "_userDetails$UserType3", "_userDetails$Organiza2", "fetchSavedLanguages", "response", "filteredLanguages", "filter", "lang", "toLowerCase", "currentLanguageExists", "some", "fallbackLanguage", "ShareFeedbackClick", "closeShareFeedback", "storedSelectedLanguages", "getLanguageName", "language", "find", "LanguageCode", "handleToLanguageChange", "selectedLanguage", "log", "languageObj", "labelsNew", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fetchLabelsNew", "changeLanguage", "data", "languageKey", "parsed<PERSON><PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "for<PERSON>ach", "item", "LabelName", "undefined", "Object", "keys", "length", "addResourceBundle", "handleMenuClick", "prev", "unsubscribe", "handleHomeClick", "handleTogglePopover", "currentTarget", "handleOrgClick", "handleAccountClick", "handleGuideClick", "handleAccSeetingsClick", "handleTeamClick", "handleThemesClick", "handleBillingClick", "handleInstallClick", "handlenotifyClick", "handleProfileClick", "handleClick", "path", "handleQuickAdoptClick", "logedUsertype", "handleLogoutClick", "toggleSidebar", "fetchAndSetRtl", "urlParams", "URLSearchParams", "window", "search", "hasAutoLoginParams", "has", "isAutoLoginCompleted", "sessionStorage", "isFreeTrialToken", "hasValidUserInfo", "hasAutoLoginUserData", "parsedUserInfo", "e", "org", "orgRtl", "RTL", "accountRtl", "requestBody", "skip", "top", "filters", "order<PERSON><PERSON><PERSON><PERSON>s", "post", "results", "selectedAccount", "acc", "Rtl", "children", "className", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "style", "width", "variant", "onChange", "displayEmpty", "fullWidth", "startAdornment", "position", "endAdornment", "cursor", "zoom", "transform", "transition", "stopPropagation", "open", "onClose", "onOpen", "IconComponent", "sx", "display", "MenuProps", "PaperProps", "maxHeight", "overflowY", "marginTop", "fontSize", "whiteSpace", "wordBreak", "map", "account", "Account<PERSON><PERSON>", "disabled", "pathname", "id", "labelId", "backgroundColor", "borderBottom", "padding", "zIndex", "border", "borderRadius", "outline", "borderColor", "placeholder", "onKeyDown", "includes", "sort", "a", "b", "localeCompare", "LanguageId", "marginRight", "Boolean", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "height", "boxShadow", "flexDirection", "alignItems", "gap", "justifyContent", "textTransform", "fontFamily", "fontWeight", "lineHeight", "letterSpacing", "textAlign", "color", "selectedLanguageProp", "get", "status", "clear", "reload", "cookie", "split", "name", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/components/adminMenu/AdminMenu.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, startTransition, useContext } from \"react\";\r\nimport AccountCircleOutlinedIcon from '@mui/icons-material/AccountCircleOutlined';\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport i18n from '../multilingual/i18n';\r\nimport { SAinitialsData } from \"../login/Superadminloginpage\";\r\nimport { Popover, Typography, Button, Avatar, Box } from '@mui/material';\r\nimport { initialsData } from \"../auth/AuthProvider\";\r\nimport { initialssData } from \"../settings/ProfileSettings\";\r\nimport SideMenu from \"./sideMenu\";\r\nimport { erroricon, quickadopt } from \"../../assets/icons/icons\";\r\nimport AccountCircleIcon from '@mui/icons-material/AccountCircle';\r\nimport LogoutIcon from '@mui/icons-material/Logout';\r\nimport LocalActivityIcon from '@mui/icons-material/LocalActivity';\r\nimport Popup from \"../common/Popup\";\r\nimport { FormControl, InputLabel, InputAdornment, IconButton, MenuItem, Grid } from \"@mui/material\";\r\nimport Select from \"@mui/material/Select\";\r\nimport LanguageIcon from '@mui/icons-material/Language';\r\nimport { translateText } from \"../multilingual/Translator\";\r\nimport Translater from \"../multilingual/Multilingual\";\r\nimport { useLocation } from \"react-router-dom\";\r\nimport { LanguageProvider } from \"../multilingual/LanguageContext\";\r\nimport ProfileSettings from \"../settings/ProfileSettings\";\r\nimport Settings from \"../settings/Settings\";\r\nimport AccountSettings from \"../settings/AccountSettings\";\r\nimport { getLabels, getLanguages, updateLanguage } from \"../../services/MultilingualService\";\r\nimport { setSidebarOpen, subscribe } from \"../adminMenu/sidemenustate\";\r\nimport MenuOpenIcon from '@mui/icons-material/MenuOpen';\r\nimport MenuIcon from '@mui/icons-material/Menu';\r\nimport { Sidebar } from \"../common/Sidebar\";\r\nimport PageWrapper from \"../pagewrapper\";\r\nimport { Feedback, Logout, settings, logo, global, profile, ArrowDown } from \"../../assets/icons/icons\";\r\nimport { isSidebarOpen } from \"../adminMenu/sidemenustate\";\r\nimport TeamSettings from \"../settings/TeamSettings\";\r\nimport RightSettings from \"../settings/RightSettings\";\r\nimport UserList from \"../user/UserList\";\r\nimport LogoutPopup from \"./logoutpopup\";\r\nimport AccountList from \"../account/AccountList\";\r\nimport AgentsList from \"../agents/Agentslist\";\r\nimport Scripts from \"../agents/Scripts\";\r\nimport ScriptHistory from \"../agents/ScriptHistory\";\r\nimport ScriptHistoryViewer from \"../agents/ScriptHistoryViewer\";\r\nimport Training from \"../training/Training\";\r\nimport userManager from '../auth/UseAuth';\r\nimport DomainSettings from \"../settings/DomainSettings\";\r\nimport AlertSettings from \"../settings/AlertSettings\";\r\nimport BillingSettings from \"../settings/BillingSettings\";\r\nimport CodeInstall from \"../settings/InstallSettings\";\r\nimport { useAuth } from \"../auth/AuthProvider\";\r\nimport AuditLogList from \"../auditLog/AuditLogList\";\r\nimport { User } from \"../../models/User\";\r\nimport ShareFeedbackPopup from \"../feedback/ShareFeedbackPopup\";\r\nimport axios from \"axios\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport { GetAccountsByUser, GetAccountsList } from \"../../services/GuideService\";\r\nimport { AccountContext } from \"../account/AccountContext\";\r\nimport { adminApiService, userApiService, userUrl } from \"../../services/APIService\";\r\nimport { getOrganizationById } from \"../../services/OrganizationService\";\r\nimport { useRtl } from \"../../RtlContext\";\r\nlet accountId: string;\r\ninterface Account {\r\n\tAccountId: string;\r\n\tAccountName: string;\r\n}\r\n//let organizationLanguages : OrganizationLanguage[] = [];\r\nexport interface Language {\r\n\tLanguageId: string;\r\n\tLanguage: string;\r\n\tLanguageCode: string;\r\n\tFlagIcon: string;\r\n}\r\n\r\n// Complete list of 60 world languages (excluding English as primary language)\r\n// This will be replaced by dynamic data from API\r\nexport const availableLanguages: Language[] = [];\r\n\r\n// Helper function to get native language display name using Intl.DisplayNames API\r\nconst getLanguageDisplayName = (languageName: string, languageCode?: string): string => {\r\n\ttry {\r\n\t\tif (languageCode) {\r\n\t\t\tconst displayNames = new Intl.DisplayNames([languageCode], { type: 'language' });\r\n\t\t\tconst nativeName = displayNames.of(languageCode);\r\n\t\t\tif (nativeName && nativeName !== languageCode) {\r\n\t\t\t\treturn nativeName;\r\n\t\t\t}\r\n\t\t}\r\n\t\t// Fallback: return the original language name\r\n\t\treturn languageName;\r\n\t} catch (error) {\r\n\t\tconsole.warn(`Failed to get native name for language: ${languageName}`, error);\r\n\t\treturn languageName;\r\n\t}\r\n};\r\n\r\n\r\n// // Helper to set RTL attribute on body\r\nfunction setBodyRtlAttribute(rtlValue: boolean) {\r\n\tif (rtlValue) {\r\n\t\tdocument.body.classList.add('rtl');\r\n\t\tdocument.body.setAttribute('dir', 'rtl');\r\n\t}\r\n\telse {\r\n\t\tdocument.body.classList.contains('rtl') && document.body.classList.remove('rtl');\r\n\t\tdocument.body.removeAttribute('dir');\r\n\t}\r\n}\r\n\r\nconst OrgAdminMenu: React.FC = () => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst [isShareFeedbackPopup, setIsShareFeedbackPopup] = useState(false);\r\n\tconst [openDropdown, setOpenDropdown] = useState<string | null>(null);\r\n\r\n\tconst [sidebarOpen, setSidebarOpen] = useState(isSidebarOpen());\r\n\tconst [openMenu, setOpenMenu] = useState(false);\r\n\tconst { signOut, userDetails, userRoles } = useAuth();\r\n\tconst [userType, setUserType] = useState(userDetails?.UserType ?? \"\");\r\n\tconst [OrganizationId, setOrganizationId] = useState(userDetails?.OrganizationId ?? \"\");\r\n\tconst [user, setUser] = useState<User | null>(null);\r\n\tconst [anchorEl, setAnchorEl] = useState<HTMLDivElement | null>(null);\r\n\tconst [openPopup, setOpenPopup] = useState(false);\r\n\tconst [sidebarLocalOpen, setLocalSidebarOpen] = useState(isSidebarOpen());\r\n\tconst navigate = useNavigate();\r\n\tconst [toLanguage, setToLanguage] = useState<string>(() => {\r\n\t\treturn localStorage.getItem('selectedLanguage') || userDetails?.Language || 'English';\r\n\t});\r\n\tconst [translatedLabels, setTranslatedLabels] = useState<string[]>([]);\r\n\tconst location = useLocation();\r\n\tconst [availableLanguages, setAvailableLanguages] = useState<Language[]>([]);\r\n\tconst [loading, setLoading] = useState(true);\r\n\tconst [selectedLanguages, setSelectedLanguages] = useState<Language[]>([]);\r\n\tconst [languageSearchTerm, setLanguageSearchTerm] = useState('');\r\n\tconst ORGANIZATION_ID = userDetails?.OrganizationId;\r\n\tconst [accounts, setAccounts] = useState<Account[]>([]);\r\n\tconst [selectedAccountId, setSelectedAccountId] = useState('');\r\n\tconst [accountsFetched, setAccountsFetched] = useState(false);\r\n\tconst { setAccountId, setRoles } = useContext(AccountContext);\r\n\tconst firstDropdownRef = useRef<HTMLDivElement | null>(null);\r\n\tconst secondDropdownRef = useRef<HTMLDivElement | null>(null);\r\n\tconst { isRtl, setRtl } = useRtl();\r\n\r\n\tconst handleDropdownOpen = (dropdownName: string) => {\r\n\t\tconst newState = dropdownName === openDropdown ? null : dropdownName;\r\n\t\tsetOpenDropdown(newState);\r\n\r\n\t\tif (dropdownName === \"second\" && newState === null) {\r\n\t\t\tsetLanguageSearchTerm('');\r\n\t\t}\r\n\t};\r\n\r\n\r\n\tconst handleClickOutside = (event: MouseEvent) => {\r\n\t\tif (\r\n\t\t\tfirstDropdownRef.current && !firstDropdownRef.current.contains(event.target as Node) &&\r\n\t\t\tsecondDropdownRef.current && !secondDropdownRef.current.contains(event.target as Node)\r\n\t\t) {\r\n\t\t\tsetOpenDropdown(null);\r\n\t\t\tsetLanguageSearchTerm(''); // Reset search term when clicking outside\r\n\t\t}\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tdocument.addEventListener('mousedown', handleClickOutside);\r\n\t\treturn () => {\r\n\t\t\tdocument.removeEventListener('mousedown', handleClickOutside);\r\n\t\t};\r\n\t}, []);\r\n\tuseEffect(() => {\r\n\t\tconst organizationId = userDetails?.OrganizationId;\r\n\t\tconst userType = userDetails?.UserType;\r\n\r\n\t\tif (organizationId && userType && !accountsFetched) {\r\n\t\t\tconst fetchAccounts = async () => {\r\n\t\t\t\tsetLoading(true);\r\n\t\t\t\ttry {\r\n\t\t\t\t\tif (userType.toLocaleLowerCase() === 'admin') {\r\n\t\t\t\t\t\tawait GetAccountsList(setAccounts, setLoading, organizationId, -1, -1, \"\", \"\", \"\");\r\n\t\t\t\t\t} else if (userType.toLocaleLowerCase() === 'user') {\r\n\t\t\t\t\t\tawait GetAccountsByUser(setAccounts, setLoading, organizationId, -1, -1, \"\", \"\", \"\");\r\n\t\t\t\t\t}\r\n\t\t\t\t\tsetAccountsFetched(true);\r\n\t\t\t\t} catch (err) {\r\n\t\t\t\t\tconsole.error(err);\r\n\t\t\t\t} finally {\r\n\t\t\t\t\tsetLoading(false);\r\n\t\t\t\t}\r\n\t\t\t};\r\n\r\n\t\t\tfetchAccounts();\r\n\t\t}\r\n\t}, [userDetails?.OrganizationId, userDetails?.UserType, accountsFetched]);\r\n\r\n\tconst handleAccountChange = (event: any) => {\r\n\t\tconst AccountId: string = event.target.value;\r\n\t\tsetSelectedAccountId(AccountId);\r\n\t\tsetAccountId(AccountId);\r\n\t\tconst rolesOfAccount = userType.toLocaleLowerCase() == \"admin\" ? [\"Account Admin\"] : userRoles[AccountId];\r\n\t\tsetRoles(rolesOfAccount);\r\n\t\taccountId = AccountId;\r\n\t\tlocalStorage.setItem(\"CurrentAccountId\", accountId);\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tconst userInfoString = localStorage.getItem(\"userInfo\");\r\n\t\tif (userInfoString && userInfoString != '{}') {\r\n\t\t\ttry {\r\n\t\t\t\tconst userInfo = JSON.parse(userInfoString);\r\n\t\t\t\tif (userInfo['user']) {\r\n\t\t\t\t\tconst parsedUser = JSON.parse(userInfo['user']);\r\n\t\t\t\t\tsetUser(parsedUser);\r\n\t\t\t\t\tif (parsedUser) {\r\n\t\t\t\t\t\tsetUserType(parsedUser.UserType?.toLocaleLowerCase() ?? '');\r\n\t\t\t\t\t\tconst OrgId = parsedUser.OrganizationId ?? '';\r\n\t\t\t\t\t\tsetOrganizationId(OrgId);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(\"Error parsing userInfo: \", error);\r\n\t\t\t}\r\n\t\t}\r\n\t\telse if (userDetails) {\r\n\t\t\tsetUser(userDetails);\r\n\t\t\tif (userDetails) {\r\n\t\t\t\tsetUserType(userDetails.UserType?.toLocaleLowerCase() ?? '');\r\n\t\t\t\tconst OrgId = userDetails.OrganizationId ?? '';\r\n\t\t\t\tsetOrganizationId(OrgId);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [userDetails]);\r\n\t// Fetch saved languages from API (excluding English as it's the primary language)\r\n\tuseEffect(() => {\r\n\t\tconst fetchSavedLanguages = async () => {\r\n\t\t\ttry {\r\n\t\t\t\tif (accountId) {\r\n\t\t\t\t\tconst response = await getLanguages();\r\n\r\n\t\t\t\t\t// Set all available languages\r\n\t\t\t\t\tsetAvailableLanguages(response);\r\n\r\n\t\t\t\t\t// Filter out English since it's the primary language for selected languages\r\n\t\t\t\t\tconst filteredLanguages = response.filter((lang: Language) =>\r\n\t\t\t\t\t\tlang.Language.toLowerCase() !== 'english'\r\n\t\t\t\t\t);\r\n\t\t\t\t\tsetSelectedLanguages(filteredLanguages);\r\n\r\n\t\t\t\t\t// Only set language from userDetails on initial load, not on every change\r\n\t\t\t\t\tconst currentLanguageExists = filteredLanguages.some(lang =>\r\n\t\t\t\t\t\tlang.Language === toLanguage\r\n\t\t\t\t\t) || toLanguage === 'English';\r\n\r\n\t\t\t\t\t// If current language doesn't exist in available languages, fall back to English\r\n\t\t\t\t\tif (!currentLanguageExists) {\r\n\t\t\t\t\t\tconst fallbackLanguage = 'English';\r\n\t\t\t\t\t\tsetToLanguage(fallbackLanguage);\r\n\t\t\t\t\t\tlocalStorage.setItem('selectedLanguage', fallbackLanguage);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} catch (err) {\r\n\t\t\t\tconsole.error(\"Error fetching saved languages:\", err);\r\n\t\t\t\tsetSelectedLanguages([]);\r\n\t\t\t\tsetAvailableLanguages([]);\r\n\t\t\t\t// Reset to userDetails.Language or English if there's an error\r\n\t\t\t\tconst fallbackLanguage = userDetails?.Language || 'English';\r\n\t\t\t\tsetToLanguage(fallbackLanguage);\r\n\t\t\t\tlocalStorage.setItem('selectedLanguage', fallbackLanguage);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tfetchSavedLanguages();\r\n\t}, [accountId]); // Remove toLanguage and userDetails?.Language dependencies to prevent override\r\n\tconst ShareFeedbackClick = () => {\r\n\t\tsetIsShareFeedbackPopup(true);\r\n\r\n\t};\r\n\r\n\tconst closeShareFeedback = () => {\r\n\t\tsetIsShareFeedbackPopup(false);\r\n\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tconst storedSelectedLanguages = localStorage.getItem(\"selectedLanguages\");\r\n\t\tif (storedSelectedLanguages) {\r\n\t\t\t// This can be removed as we now fetch from API\r\n\t\t\t// setSelectedLanguages(JSON.parse(storedSelectedLanguages));\r\n\t\t}\r\n\t}, []);\r\n\r\n\r\n\tconst getLanguageName = (languageCode: string): string => {\r\n\t\tconst language = availableLanguages.find((lang) => lang.LanguageCode === languageCode);\r\n\t\treturn language ? language.Language : \"Unknown\";\r\n\t};\r\n\r\n\tconst handleToLanguageChange = async (event: any) => {\r\n\t\tconst selectedLanguage = event.target.value;\r\n\t\tconsole.log('Language change attempted:', selectedLanguage);\r\n\r\n\t\t// Immediately update the state\r\n\t\tsetToLanguage(selectedLanguage);\r\n\r\n\t\t// Find the language code from availableLanguages\r\n\t\tconst languageObj = availableLanguages.find(lang => lang.Language === selectedLanguage);\r\n\t\tconst languageCode = languageObj ? languageObj.LanguageCode : 'en';\r\n\r\n\t\tlocalStorage.setItem('selectedLanguage', selectedLanguage);\r\n\t\tlocalStorage.setItem('selectedLanguageCode', languageCode);\r\n\r\n\t\t// Call updateLanguage API with the language name\r\n\t\ttry {\r\n\t\t\tawait updateLanguage(selectedLanguage);\r\n\t\t\tconsole.log('Language updated successfully:', selectedLanguage);\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('Error updating language:', error);\r\n\t\t}\r\n\t};\r\n\r\n\tconst [labelsNew, setLabelsNew] = useState<any>({});\r\n\tuseEffect(() => {\r\n\t\tconst fetchLabelsNew = async (toLanguage: any) => {\r\n\t\t\tif (toLanguage === \"English\") {\r\n\t\t\t\ti18n.changeLanguage('en');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\ttry {\r\n\t\t\t\tsetLoading(true);\r\n\t\t\t\tconst data = await getLabels(toLanguage, false);\r\n\t\t\t\tif (data) {\r\n\t\t\t\t\t// Find the language code from selectedLanguages or availableLanguages\r\n\t\t\t\t\tconst languageObj = selectedLanguages.find(lang => lang.Language === toLanguage) ||\r\n\t\t\t\t\t\tavailableLanguages.find(lang => lang.Language === toLanguage);\r\n\t\t\t\t\tconst languageKey = languageObj ? languageObj.LanguageCode : '';\r\n\r\n\t\t\t\t\tif (languageKey) {\r\n\t\t\t\t\t\tlet parsedLabelsNew: any = {};\r\n\r\n\t\t\t\t\t\t// Handle new array format\r\n\t\t\t\t\t\tif (Array.isArray(data)) {\r\n\t\t\t\t\t\t\tdata.forEach((item: any) => {\r\n\t\t\t\t\t\t\t\tif (item.LabelName && item[toLanguage] !== undefined) {\r\n\t\t\t\t\t\t\t\t\tparsedLabelsNew[item.LabelName] = item[toLanguage];\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else if (data[languageKey]) {\r\n\t\t\t\t\t\t\t// Fallback for old object format\r\n\t\t\t\t\t\t\tparsedLabelsNew = data[languageKey];\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tif (Object.keys(parsedLabelsNew).length > 0) {\r\n\t\t\t\t\t\t\tsetLabelsNew(parsedLabelsNew);\r\n\t\t\t\t\t\t\ti18n.addResourceBundle(languageKey, 'translation', parsedLabelsNew, true, true);\r\n\t\t\t\t\t\t\ti18n.changeLanguage(languageKey);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} catch (err) {\r\n\t\t\t\tconsole.error('Error fetching labels:', err);\r\n\t\t\t} finally {\r\n\t\t\t\tsetLoading(false);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tfetchLabelsNew(toLanguage);\r\n\t}, [toLanguage, selectedLanguages, availableLanguages]);\r\n\r\n\r\n\tconst handleMenuClick = () => {\r\n\t\tsetOpenMenu((prev) => !prev);\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tconst unsubscribe = subscribe(setLocalSidebarOpen);\r\n\t\treturn () => unsubscribe();\r\n\t}, []);\r\n\r\n\tconst handleHomeClick = () => {\r\n\t\tstartTransition(() => {\r\n\t\t\tnavigate(\"/\");\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleTogglePopover = (event: any) => {\r\n\t\tif (anchorEl) {\r\n\t\t\t// Close the popover if it's open\r\n\t\t\tsetAnchorEl(null);\r\n\t\t} else {\r\n\t\t\t// Open the popover and set the anchor element\r\n\t\t\tsetAnchorEl(event.currentTarget);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleOrgClick = () => {\r\n\t\tstartTransition(() => {\r\n\t\t\tnavigate(\"/organizations\");\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleAccountClick = () => {\r\n\t\tstartTransition(() => {\r\n\t\t\tnavigate(\"/accounts\");\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleGuideClick = () => {\r\n\t\tstartTransition(() => {\r\n\t\t\tnavigate(\"/guide\");\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleAccSeetingsClick = () => {\r\n\t\tstartTransition(() => {\r\n\t\t\tnavigate(\"/settings/account\");\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleTeamClick = () => {\r\n\t\tstartTransition(() => {\r\n\t\t\tnavigate(\"/settings/team\");\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleThemesClick = () => {\r\n\t\tstartTransition(() => {\r\n\t\t\tnavigate(\"/theme\");\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleBillingClick = () => {\r\n\t\tstartTransition(() => {\r\n\t\t\tnavigate(\"/settings/billing\");\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleInstallClick = () => {\r\n\t\tstartTransition(() => {\r\n\t\t\tnavigate(\"/settings/install\");\r\n\t\t});\r\n\t};\r\n\r\n\tconst handlenotifyClick = () => {\r\n\t\tstartTransition(() => {\r\n\t\t\tnavigate(\"/notifications\");\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleProfileClick = () => {\r\n\r\n\t\tnavigate(\"/viewprofile\");\r\n\r\n\t\tsetAnchorEl(null);\r\n\t};\r\n\r\n\tconst handleClick = (path: string) => {\r\n\t\tswitch (path) {\r\n\t\t\tcase \"user\":\r\n\t\t\t\treturn navigate(\"/user\");\r\n\t\t\tcase \"account\":\r\n\t\t\t\treturn navigate(\"/settings/account\");\r\n\t\t\tcase \"settings\":\r\n\t\t\t\treturn navigate(\"/settings\");\r\n\t\t\tcase \"auditlogs\":\r\n\t\t\t\treturn navigate(\"/superadmin/auditlogs\");\r\n\t\t\tcase \"organization\":\r\n\t\t\t\treturn navigate(\"/superadmin/organizations\");\r\n\t\t\tcase \"multilingual\":\r\n\t\t\t\treturn navigate(\"/superadmin/Multilingual\");\r\n\t\t\tdefault:\r\n\t\t\t\treturn navigate(\"/\");\r\n\t\t}\r\n\t};\r\n\r\n\r\n\r\n\tconst handleQuickAdoptClick = () => {\r\n\t\tconst logedUsertype = localStorage.getItem('userType');\r\n\t\tif (logedUsertype !== \"SuperAdmin\") {\r\n\t\t\tstartTransition(() => {\r\n\t\t\t\tnavigate(\"/\");\r\n\t\t\t});\r\n\t\t}\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tif (accounts.length > 0) {\r\n\t\t\tsetSelectedAccountId(accounts[0].AccountId);\r\n\t\t\tsetAccountId(accounts[0].AccountId)\r\n\t\t\taccountId = accounts[0].AccountId\r\n\t\t\tlocalStorage.setItem(\"CurrentAccountId\", accountId);\r\n\t\t\tconst rolesOfAccount = userType.toLocaleLowerCase() == \"admin\" ? [\"Account Admin\"] : userRoles[accounts[0].AccountId];\r\n\t\t\tsetRoles(rolesOfAccount);\r\n\r\n\t\t}\r\n\t}, [accounts]);\r\n\tconst handleLogoutClick = () => {\r\n\t\tsetOpenPopup(true);\r\n\t\tsetAnchorEl(null);\r\n\t};\r\n\tconst toggleSidebar = () => {\r\n\t\tsetSidebarOpen(!sidebarLocalOpen);\r\n\t};\r\n// Fetch organization and account RTL and set body attribute\r\nuseEffect(() => {\r\n  async function fetchAndSetRtl() {\r\n    if (OrganizationId) {\r\n      // Check if we're in auto-login flow - skip API calls to avoid 401 errors\r\n      const urlParams = new URLSearchParams(window.location.search);\r\n      const hasAutoLoginParams = urlParams.has('token') && urlParams.has('user_id') && urlParams.has('org_id');\r\n      const isAutoLoginCompleted = sessionStorage.getItem('autoLoginCompleted') === 'true';\r\n      const isFreeTrialToken = sessionStorage.getItem('isFreeTrialToken') === 'true';\r\n\r\n      // Also check if we have valid userInfo indicating a recent auto-login\r\n      const userInfo = localStorage.getItem('userInfo');\r\n      const hasValidUserInfo = userInfo && userInfo !== '{}' && userInfo !== 'null';\r\n      let hasAutoLoginUserData = false;\r\n\r\n      if (hasValidUserInfo) {\r\n        try {\r\n          const parsedUserInfo = JSON.parse(userInfo);\r\n          hasAutoLoginUserData = parsedUserInfo['user'] && parsedUserInfo['oidc-info'];\r\n        } catch (e) {\r\n          // Ignore parse errors\r\n        }\r\n      }\r\n\r\n     // Skip API calls during auto-login flow or for free trial tokens to prevent 401 errors\r\n      if (hasAutoLoginParams  || isFreeTrialToken) {\r\n\r\n        // Use default RTL setting for now\r\n        setRtl(false);\r\n        return;\r\n      }\r\n\r\n      try {\r\n        // Fetch organization RTL\r\n        const org = await getOrganizationById(OrganizationId);\r\n        let orgRtl = org && typeof org.RTL !== 'undefined' ? org.RTL : false;\r\n\r\n        // Fetch accounts and get first account's Rtl if available\r\n        let accountRtl = false;\r\n        const requestBody = {\r\n          skip: -1,\r\n          top: -1,\r\n          filters: '',\r\n          orderByFields: '',\r\n        };\r\n\t\t const response = await adminApiService.post(`/Account/GetAccountsByOrgId`, requestBody);\r\n        const accounts = response.data.results;\r\n        if (Array.isArray(accounts) && accounts.length > 0) {\r\n          const selectedAccount = accounts.find((acc: any) => acc.AccountId === selectedAccountId);\r\n          if (selectedAccount && typeof selectedAccount.Rtl !== 'undefined') {\r\n            accountRtl = selectedAccount.Rtl;\r\n          }\r\n        }\r\n        setRtl(accountRtl); // Use context setter\r\n      } catch (e) {\r\n        console.error('Failed to fetch account for RTL:', e);\r\n        // Set default RTL on error\r\n        setRtl(false);\r\n      }\r\n\r\n    }\r\n  }\r\n  fetchAndSetRtl();\r\n}, [OrganizationId,selectedAccountId, setRtl]);\r\n\tuseEffect(() => {\r\n\t\tsetBodyRtlAttribute(isRtl);\r\n\t}, [isRtl]);\r\n\treturn (\r\n\t\t<div>\r\n\t\t\t<div className=\"qadpt-banner\">\r\n\t\t\t\t<div>\r\n\t\t\t\t\t<div className=\"adapat-banner-left\" >\r\n\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\tsrc={logo}\r\n\t\t\t\t\t\t\t\talt=\"QuickAdopt Logo\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-logo\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"adapat-banner-right\">\r\n\t\t\t\t\t{openMenu && userType.toLocaleLowerCase() === \"superadmin\" ? (\r\n\t\t\t\t\t\t<div className=\"qadpt-settings-container\">\r\n\t\t\t\t\t\t\t<a\r\n\t\t\t\t\t\t\t\tonClick={() => handleClick(\"organization\")}\r\n\t\t\t\t\t\t\t\tclassName=\"qadapt-link\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{translate('Organization')}\r\n\t\t\t\t\t\t\t</a>\r\n\t\t\t\t\t\t\t<a\r\n\t\t\t\t\t\t\t\tonClick={() => handleClick(\"auditlogs\")}\r\n\t\t\t\t\t\t\t\tclassName=\"qadapt-link\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{translate('Auditlogs')}\r\n\t\t\t\t\t\t\t</a>\r\n\r\n\t\t\t\t\t\t\t<a\r\n\t\t\t\t\t\t\t\tonClick={() => handleClick(\"multilingual\")}\r\n\t\t\t\t\t\t\t\tclassName=\"qadapt-link\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{translate('Multilingual')}\r\n\t\t\t\t\t\t\t</a>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t) : null}\r\n\t\t\t\t\t{userType.toLocaleLowerCase() === \"superadmin\" && (\r\n\t\t\t\t\t\t<i className=\"fal fa-admenu menu-icon\"\r\n\t\t\t\t\t\t\tdata-testid=\"cog-tci\"\r\n\t\t\t\t\t\t\tonClick={handleMenuClick}>\r\n\t\t\t\t\t\t</i>\r\n\t\t\t\t\t)}\r\n\t\t\t\t\t{/* <i className=\"fal fa-bell menu-icon\"></i> */}\r\n\t\t\t\t\t{userType !== \"superadmin\" ? (\r\n\r\n\t\t\t\t\t\t<div className=\"qxy-lang\" style={{ width: \"auto\" }} >\r\n\t\t\t\t\t\t\t<FormControl variant=\"standard\" className=\"lang-input\">\r\n\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\tvalue={selectedAccountId}\r\n\t\t\t\t\t\t\t\t\tonChange={handleAccountChange}\r\n\r\n\t\t\t\t\t\t\t\t\tdisplayEmpty\r\n\t\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\t\tstartAdornment={\r\n\t\t\t\t\t\t\t\t\t\t<InputAdornment position=\"start\">\r\n\t\t\t\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\t\t\t\tsrc={profile}\r\n\t\t\t\t\t\t\t\t\t\t\t\talt=\"profile Logo\"\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</InputAdornment>\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tendAdornment={\r\n\t\t\t\t\t\t\t\t\t\t<InputAdornment position=\"end\">\r\n\t\t\t\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\t\t\t\tsrc={ArrowDown}\r\n\t\t\t\t\t\t\t\t\t\t\t\talt=\"Arrowdown Logo\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcursor: 'pointer', zoom: \"0.9\", transform: openDropdown === \"first\" ? 'rotate(180deg)' : 'rotate(0deg)',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttransition: 'transform 0.3s ease'\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={(e) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\te.stopPropagation(); // Prevent default select behavior if needed\r\n\t\t\t\t\t\t\t\t\t\t\t\t\thandleDropdownOpen(\"first\"); // Trigger dropdown open\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</InputAdornment>\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\topen={openDropdown === \"first\"}\r\n\t\t\t\t\t\t\t\t\tonClose={() => setOpenDropdown(null)}\r\n\t\t\t\t\t\t\t\t\tonOpen={() => handleDropdownOpen(\"first\")}\r\n\t\t\t\t\t\t\t\t\tIconComponent={() => null}\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\"&:before, &:after\": {\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\tMenuProps={{\r\n\t\t\t\t\t\t\t\t\t\tPaperProps: {\r\n\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: '200px', // Dropdown width\r\n\t\t\t\t\t\t\t\t\t\t\t\tmaxHeight: '305px', // Maximum height for dropdown\r\n\t\t\t\t\t\t\t\t\t\t\t\toverflowY: 'auto', // Enable vertical scrolling\r\n\t\t\t\t\t\t\t\t\t\t\t\tmarginTop: '5px', // Small gap from select input\r\n\t\t\t\t\t\t\t\t\t\t\t\t'& .MuiMenuItem-root': {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: '14px', // Font size for dropdown items\r\n\t\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: 'normal', // Text wrapping\r\n\t\t\t\t\t\t\t\t\t\t\t\t\twordBreak: 'break-word', // Word breaking\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{accounts.length > 0 ? (\r\n\t\t\t\t\t\t\t\t\t\taccounts.map((account) => (\r\n\t\t\t\t\t\t\t\t\t\t\t<MenuItem key={account.AccountId} value={account.AccountId} >\r\n\t\t\t\t\t\t\t\t\t\t\t\t{account.AccountName}\r\n\t\t\t\t\t\t\t\t\t\t\t</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t))\r\n\t\t\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t\t\t<MenuItem disabled>No accounts available</MenuItem>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</Select>\r\n\t\t\t\t\t\t\t</FormControl>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t) : null}\r\n\r\n\r\n\t\t\t\t\t{location.pathname !== \"settings/multilingual\" && userType !== \"superadmin\" ? (\r\n\t\t\t\t\t\t<div className=\"qxy-lang\">\r\n\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t<FormControl variant=\"standard\" className=\"lang-input\">\r\n\t\t\t\t\t\t\t\t\t<InputLabel id=\"demo-simple-select\"></InputLabel>\r\n\t\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"lang-input-box\"\r\n\t\t\t\t\t\t\t\t\t\tlabelId=\"demo-simple-select\"\r\n\t\t\t\t\t\t\t\t\t\tid=\"demo-simple\"\r\n\t\t\t\t\t\t\t\t\t\tvalue={toLanguage} // Controlled component\r\n\t\t\t\t\t\t\t\t\t\tonChange={handleToLanguageChange}\r\n\t\t\t\t\t\t\t\t\t\tstartAdornment={\r\n\t\t\t\t\t\t\t\t\t\t\t<InputAdornment position=\"start\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<img src={global} alt=\"global Logo\" />\r\n\t\t\t\t\t\t\t\t\t\t\t</InputAdornment>\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tendAdornment={\r\n\t\t\t\t\t\t\t\t\t\t\t<InputAdornment position=\"end\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={ArrowDown}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\talt=\"Arrowdown Logo\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcursor: 'pointer',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tzoom: '0.9',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttransform: openDropdown === \"second\" ? 'rotate(180deg)' : 'rotate(0deg)',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttransition: 'transform 0.3s ease',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={(e) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\te.stopPropagation(); // Prevent default select behavior\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetOpenDropdown(openDropdown === \"second\" ? null : \"second\"); // Toggle logic\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t</InputAdornment>\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tIconComponent={() => null}\r\n\t\t\t\t\t\t\t\t\t\topen={openDropdown === \"second\"}\r\n\t\t\t\t\t\t\t\t\t\tonClose={() => {\r\n\t\t\t\t\t\t\t\t\t\t\tsetOpenDropdown(null);\r\n\t\t\t\t\t\t\t\t\t\t\tsetLanguageSearchTerm('');\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tonOpen={() => handleDropdownOpen(\"second\")}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\"&:before, &:after\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tMenuProps={{\r\n\t\t\t\t\t\t\t\t\t\t\tPaperProps: {\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\twidth: '250px',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tmaxHeight: '350px',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\toverflowY: 'auto',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tmarginTop: '5px',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t'& .MuiMenuItem-root': {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: '14px',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: 'normal',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\twordBreak: 'break-word',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t'& .search-container': {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tposition: 'sticky',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: 'white',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderBottom: '1px solid #e0e0e0',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: '8px 16px',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tzIndex: 1,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t'& .search-input': {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: '100%',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: '14px',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: '8px 12px',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder: '1px solid #ccc',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: '4px',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\toutline: 'none',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t'&:focus': {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderColor: '#1976d2',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<div className=\"search-container\">\r\n\t\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"search-input\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"Search languages...\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={languageSearchTerm}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\te.stopPropagation();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsetLanguageSearchTerm(e.target.value);\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonKeyDown={(e) => e.stopPropagation()}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={(e) => e.stopPropagation()}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<MenuItem key=\"en\" value=\"English\">\r\n\t\t\t\t\t\t\t\t\t\t\tEnglish\r\n\t\t\t\t\t\t\t\t\t\t</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t{selectedLanguages\r\n\t\t\t\t\t\t\t\t\t\t\t.filter((lang) =>\r\n\t\t\t\t\t\t\t\t\t\t\t\tlang.Language.toLowerCase().includes(languageSearchTerm.toLowerCase())\r\n\t\t\t\t\t\t\t\t\t\t\t)\r\n\t\t\t\t\t\t\t\t\t\t\t.sort((a, b) => a.Language.localeCompare(b.Language))\r\n\t\t\t\t\t\t\t\t\t\t\t.map((lang) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem key={lang.LanguageId} value={lang.Language} sx={{ fontSize: '14px' }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{getLanguageDisplayName(lang.Language, lang.LanguageCode)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t\t</Select>\r\n\t\t\t\t\t\t\t\t</FormControl>\r\n\r\n\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t) : null}\r\n\t\t\t\t\t<div\r\n\t\t\t\t\t\tclassName=\"qadpt-prof\"\r\n\t\t\t\t\t\tonClick={handleTogglePopover}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<span style={{ display: \"flex\", marginRight: \"0.5rem !important\" }}>\r\n\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t<div className=\"prof-div\">\r\n\t\t\t\t\t\t\t\t\t<span className=\"prof-span\">{initialssData ? initialssData : initialsData}</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t</span>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div className=\"profilepopup\">\r\n\t\t\t\t\t\t<Popover\r\n\t\t\t\t\t\t\topen={Boolean(anchorEl)}\r\n\t\t\t\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\t\t\t\t//onClose={() => setAnchorEl(null)}\r\n\t\t\t\t\t\t\tonClose={handleTogglePopover}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-prfpopup\"\r\n\t\t\t\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tPaperProps={{\r\n\t\t\t\t\t\t\t\tstyle: {\r\n\t\t\t\t\t\t\t\t\tmarginTop: '32px',\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\"& .MuiPaper-root\": {\r\n\t\t\t\t\t\t\t\t\twidth: \"195px\",\r\n\t\t\t\t\t\t\t\t\theight: \"auto\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"7px\",\r\n\t\t\t\t\t\t\t\t\tpadding: \"10px 12px\",\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\t\t\t\t\tboxShadow: \"0px 4px 8px rgba(0, 0, 0, 0.1)\",\r\n\t\t\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\twidth=\"100%\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{ userType.toLocaleLowerCase() !== \"superadmin\"  && (<>\r\n\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\t\t\tonClick={ShareFeedbackClick}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: '10px 0',\r\n\t\t\t\t\t\t\t\t\t\t\tgap: '10px',\r\n\t\t\t\t\t\t\t\t\t\t\tjustifyContent: 'flex-start',\r\n\t\t\t\t\t\t\t\t\t\t\ttextTransform: 'none',\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\t\t\tsrc={Feedback}\r\n\t\t\t\t\t\t\t\t\t\t\talt=\"Feedback Icon\"\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{ width: '18px', height: '18px', marginRight: '8px' }}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontFamily: 'Poppins',\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontSize: '14px',\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontWeight: 400,\r\n\t\t\t\t\t\t\t\t\t\t\t\tlineHeight: '21px',\r\n\t\t\t\t\t\t\t\t\t\t\t\tletterSpacing: '0.3px',\r\n\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: 'left',\r\n\t\t\t\t\t\t\t\t\t\t\t\tcolor: '#202224',\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate('Share Feedback')}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\t\t\tonClick={handleProfileClick}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: '10px 0',\r\n\t\t\t\t\t\t\t\t\t\t\tgap: '10px',\r\n\t\t\t\t\t\t\t\t\t\t\tjustifyContent: 'flex-start',\r\n\t\t\t\t\t\t\t\t\t\t\ttextTransform: 'none',\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<AccountCircleOutlinedIcon style={{ color: 'black' }} />\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontFamily: 'Poppins',\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontSize: '14px',\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontWeight: 400,\r\n\t\t\t\t\t\t\t\t\t\t\t\tlineHeight: '21px',\r\n\t\t\t\t\t\t\t\t\t\t\t\tletterSpacing: '0.3px',\r\n\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: 'left',\r\n\t\t\t\t\t\t\t\t\t\t\t\tcolor: '#202224',\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate('View Profile')}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t</Button></>)}\r\n\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\t\t\tonClick={handleLogoutClick}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: '10px 0',\r\n\t\t\t\t\t\t\t\t\t\t\tgap: '10px',\r\n\t\t\t\t\t\t\t\t\t\t\tjustifyContent: 'flex-start',\r\n\t\t\t\t\t\t\t\t\t\t\ttextTransform: 'none',\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-logout\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\t\t\tsrc={Logout}\r\n\t\t\t\t\t\t\t\t\t\t\talt=\"Logout Icon\"\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{ width: '18px', height: '18px', marginRight: '8px' }}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontFamily: 'Poppins',\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontSize: '14px',\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontWeight: 400,\r\n\t\t\t\t\t\t\t\t\t\t\t\tlineHeight: '21px',\r\n\t\t\t\t\t\t\t\t\t\t\t\tletterSpacing: '0.3px',\r\n\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: 'left',\r\n\t\t\t\t\t\t\t\t\t\t\t\tcolor: '#202224',\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate('Logout')}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Popover>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t<ShareFeedbackPopup\r\n\t\t\t\topen={isShareFeedbackPopup}\r\n\t\t\t\tonClose={closeShareFeedback}\r\n\t\t\t\tsetIsShareFeedbackPopup={setIsShareFeedbackPopup}\r\n\t\t\t/>\r\n\t\t\t<div className=\"qadpt-page-content\">\r\n\t\t\t\t{userType.toLocaleLowerCase() !== \"superadmin\" ? <SideMenu selectedLanguageProp={toLanguage} /> : null}\r\n\t\t\t\t{openPopup ? (\r\n\t\t\t\t\t<div className=\"qadpt-modal-overlay\">\r\n\t\t\t\t\t\t<div className=\"qadpt-usrconfirm-popup qadpt-success\">\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<img src={erroricon} />\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-popup-title\">{translate('Confirmation')}</div>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-warning\">\r\n\t\t\t\t\t\t\t\t{translate('Are you sure you want to Logout?')}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-buttons\">\r\n\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\tonClick={() => setOpenPopup(false)}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-cancel-button\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate('Cancel')}\r\n\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\tonClick={async () => {\r\n\t\t\t\t\t\t\t\t\t\tsetOpenPopup(false);\r\n\t\t\t\t\t\t\t\t\t\tif (userDetails?.UserType !== \"SuperAdmin\") {\r\n\t\t\t\t\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst response = await userApiService.get(`/User/Logout`);\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (response.status === 401) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tlocalStorage.clear();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsessionStorage.clear();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tnavigate(\"/login\");\r\n\t\t\t\t\t\t\t\t\t\t\t\t\twindow.location.reload();\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\tsignOut();\r\n\t\t\t\t\t\t\t\t\t\t\t\twindow.location.reload();\r\n\t\t\t\t\t\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t// Optional: handle error here\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\tlocalStorage.clear();\r\n\t\t\t\t\t\t\t\t\t\t\tsessionStorage.clear();\r\n\t\t\t\t\t\t\t\t\t\t\tdocument.cookie.split(\";\").forEach((cookie) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst [name] = cookie.split(\"=\");\r\n\t\t\t\t\t\t\t\t\t\t\t\tdocument.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\tnavigate(\"/admin/adminlogin\");\r\n\t\t\t\t\t\t\t\t\t\t\twindow.location.reload();\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-conform-button\"\r\n\t\t\t\t\t\t\t\t\ttype=\"button\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate('Logout')}\r\n\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t) : null}\r\n\r\n\t\t\t\t<div\r\n\t\t\t\t\tclassName={`qadpt-settings-content ${(\r\n\t\t\t\t\t\t\t[\r\n\t\t\t\t\t\t\t\t`/${userDetails?.OrganizationId}/accounts`,\r\n\t\t\t\t\t\t\t\t`/${userDetails?.OrganizationId}/accounts/free-trial`,\r\n\t\t\t\t\t\t\t\t`/${userDetails?.OrganizationId}/roles`,\r\n\t\t\t\t\t\t\t\t`/${userDetails?.OrganizationId}/team`,\r\n\t\t\t\t\t\t\t\t\"/settings/domains\",\r\n\t\t\t\t\t\t\t\t\"/settings/rights\",\r\n\t\t\t\t\t\t\t\t\"/settings/alerts\",\r\n\t\t\t\t\t\t\t\t\"/settings/billing\",\r\n\t\t\t\t\t\t\t\t\"/settings/install\",\r\n\t\t\t\t\t\t\t\t\"/settings/activitylog\",\r\n\t\t\t\t\t\t\t\t\"/settings/multilingual\",\r\n\t\t\t\t\t\t\t\t\"/settings/agents\",\r\n\t\t\t\t\t\t\t\t\"/settings/scripts\",\r\n\t\t\t\t\t\t\t\t\"/settings/scripthistory\",\r\n\t\t\t\t\t\t\t\t\"/settings/scripthistoryviewer\",\r\n\t\t\t\t\t\t\t\t\"/settings/training\",\r\n\t\t\t\t\t\t\t\t\"/viewprofile\"\r\n\t\t\t\t\t\t\t].includes(location.pathname)\r\n\t\t\t\t\t\t) ? '' : 'qadpt-hide-smenu'\r\n\t\t\t\t\t\t}\r\n   ${location.pathname === \"/viewprofile\" ? 'qadpt-viewprofile-page' : ''\r\n\t\t\t\t\t\t}`}\r\n\t\t\t\t>\r\n\t\t\t\t\t{location.pathname === `/${userDetails?.OrganizationId}/accounts` && userType === \"admin\" ? (\r\n\t\t\t\t\t\t<div className=\"qadpt-settings-page\">\r\n\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t<PageWrapper>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-settings-smenu\">\r\n\t\t\t\t\t\t\t\t\t\t<Box id=\"settingMenuBox\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Settings />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div className=\"usr-list-page\">\r\n\t\t\t\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t\t\t\t<AccountList />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t</PageWrapper>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t) : null}\r\n\r\n\t\t\t\t\t{location.pathname === `/${userDetails?.OrganizationId}/accounts/free-trial` && userType === \"admin\" ? (\r\n\t\t\t\t\t\t<div className=\"qadpt-settings-page\">\r\n\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t<PageWrapper>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-settings-smenu\">\r\n\t\t\t\t\t\t\t\t\t\t<Box id=\"settingMenuBox\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Settings />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div className=\"usr-list-page\">\r\n\t\t\t\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t\t\t\t<AccountList />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t</PageWrapper>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t) : null}\r\n\r\n\r\n\t\t\t\t\t{location.pathname === \"/viewprofile\" ? (\r\n\t\t\t\t\t\t<PageWrapper>\r\n\t\t\t\t\t\t\t<div style={{ display: 'flex', flexDirection: 'row', width: '100%' }}>\r\n\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t<ProfileSettings selectedLanguageProp={getLanguageName(toLanguage)} />\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</PageWrapper>\r\n\t\t\t\t\t) : null}\r\n\r\n\t\t\t\t\t{location.pathname === `/settings/multilingual` ? (\r\n\t\t\t\t\t\t<div className=\"qadpt-settings-page\">\r\n\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t<PageWrapper>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-settings-smenu\">\r\n\t\t\t\t\t\t\t\t\t\t<Box id=\"settingMenuBox\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Settings />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div className=\"usr-list-page\">\r\n\t\t\t\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t\t\t\t<LanguageProvider>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Translater />\r\n\t\t\t\t\t\t\t\t\t\t\t</LanguageProvider>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t</PageWrapper>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t) : null}\r\n\r\n\t\t\t\t\t{location.pathname === `/${userDetails?.OrganizationId}/team` && userType === \"admin\" ? (\r\n\t\t\t\t\t\t<div className=\"qadpt-settings-page\">\r\n\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t<PageWrapper>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-settings-smenu\">\r\n\t\t\t\t\t\t\t\t\t\t<Box id=\"settingMenuBox\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Settings />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div className=\"usr-list-page\">\r\n\t\t\t\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t\t\t\t<UserList />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t</PageWrapper>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t) : null}\r\n\r\n\t\t\t\t\t{location.pathname === `/${userDetails?.OrganizationId}/roles` && userType === \"admin\" ? (\r\n\t\t\t\t\t\t<div className=\"qadpt-settings-page\">\r\n\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t<PageWrapper>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-settings-smenu\">\r\n\t\t\t\t\t\t\t\t\t\t<Box id=\"settingMenuBox\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Settings />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div className=\"usr-list-page\">\r\n\t\t\t\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t\t\t\t<TeamSettings />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t</PageWrapper>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t) : null}\r\n\r\n\t\t\t\t\t{location.pathname === \"/settings/domains\" && userType === \"admin\" ? (\r\n\r\n\t\t\t\t\t\t<div className=\"qadpt-settings-page\">\r\n\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t<PageWrapper>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-settings-smenu\">\r\n\t\t\t\t\t\t\t\t\t\t<Box id=\"settingMenuBox\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Settings />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div className=\"usr-list-page\">\r\n\t\t\t\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t\t\t\t<DomainSettings />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t</PageWrapper>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</div>\r\n\r\n\r\n\t\t\t\t\t) : null}\r\n\t\t\t\t\t{location.pathname === \"/settings/rights\" ? (\r\n\r\n\t\t\t\t\t\t<div className=\"qadpt-settings-page\">\r\n\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t<PageWrapper>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-settings-smenu\">\r\n\t\t\t\t\t\t\t\t\t\t<Box id=\"settingMenuBox\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Settings />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div className=\"usr-list-page\">\r\n\t\t\t\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t\t\t\t<RightSettings />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t</PageWrapper>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t) : null}\r\n\r\n\t\t\t\t\t{location.pathname === \"/settings/alerts\" ? (\r\n\t\t\t\t\t\t<div className=\"qadpt-settings-page\">\r\n\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t<PageWrapper>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-settings-smenu\">\r\n\t\t\t\t\t\t\t\t\t\t<Box id=\"settingMenuBox\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Settings />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div className=\"usr-list-page\">\r\n\t\t\t\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t\t\t\t<AlertSettings />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t</PageWrapper>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t) : null}\r\n\t\t\t\t\t{location.pathname === \"/settings/billing\" && userType === \"admin\" ? (\r\n\t\t\t\t\t\t<div className=\"qadpt-settings-page\">\r\n\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t<PageWrapper>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-settings-smenu\">\r\n\t\t\t\t\t\t\t\t\t\t<Box id=\"settingMenuBox\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Settings />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div className=\"usr-list-page\">\r\n\t\t\t\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t\t\t\t<BillingSettings />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t</PageWrapper>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t) : null}\r\n\t\t\t\t\t{location.pathname === \"/settings/install\" ? (\r\n\t\t\t\t\t\t<div className=\"qadpt-settings-page\">\r\n\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t<PageWrapper>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-settings-smenu\">\r\n\t\t\t\t\t\t\t\t\t\t<Box id=\"settingMenuBox\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Settings />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div className=\"usr-list-page\">\r\n\t\t\t\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t\t\t\t<CodeInstall />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t</PageWrapper>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t) : null}\r\n\t\t\t\t\t{location.pathname === \"/settings/activitylog\" ? (\r\n\t\t\t\t\t\t<div className=\"qadpt-settings-page\">\r\n\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t<PageWrapper>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-settings-smenu\">\r\n\t\t\t\t\t\t\t\t\t\t<Box id=\"settingMenuBox\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Settings />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div className=\"usr-list-page\">\r\n\t\t\t\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t\t\t\t<AuditLogList />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t</PageWrapper>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t) : null}\r\n\t\t\t\t\t{location.pathname === `/settings/agents` ? (\r\n\t\t\t\t\t\t<div className=\"qadpt-settings-page\">\r\n\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t<PageWrapper>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-settings-smenu\">\r\n\t\t\t\t\t\t\t\t\t\t<Box id=\"settingMenuBox\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Settings />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div className=\"usr-list-page\">\r\n\t\t\t\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t\t\t\t<AgentsList />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t</PageWrapper>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t) : null}\r\n\r\n\t\t\t\t\t{location.pathname === `/settings/scripts` ? (\r\n\t\t\t\t\t\t<div className=\"qadpt-settings-page\">\r\n\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t<PageWrapper>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-settings-smenu\">\r\n\t\t\t\t\t\t\t\t\t\t<Box id=\"settingMenuBox\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Settings />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div className=\"usr-list-page\">\r\n\t\t\t\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t\t\t\t<Scripts />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t</PageWrapper>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t) : null}\r\n\r\n\t\t\t\t\t{location.pathname === `/settings/scripthistory` ? (\r\n\t\t\t\t\t\t<div className=\"qadpt-settings-page\">\r\n\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t<PageWrapper>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-settings-smenu\">\r\n\t\t\t\t\t\t\t\t\t\t<Box id=\"settingMenuBox\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Settings />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div className=\"usr-list-page\">\r\n\t\t\t\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t\t\t\t<ScriptHistory />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t</PageWrapper>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t) : null}\r\n\r\n\t\t\t\t\t{location.pathname === `/settings/scripthistoryviewer` ? (\r\n\t\t\t\t\t\t<div className=\"qadpt-settings-page\">\r\n\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t<PageWrapper>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-settings-smenu\">\r\n\t\t\t\t\t\t\t\t\t\t<Box id=\"settingMenuBox\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Settings />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div className=\"usr-list-page\">\r\n\t\t\t\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t\t\t\t<ScriptHistoryViewer />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t</PageWrapper>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t) : null}\r\n\r\n\t\t\t\t\t{location.pathname === `/settings/training` ? (\r\n\t\t\t\t\t\t<div className=\"qadpt-settings-page\">\r\n\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t<PageWrapper>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-settings-smenu\">\r\n\t\t\t\t\t\t\t\t\t\t<Box id=\"settingMenuBox\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Settings />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div className=\"usr-list-page\">\r\n\t\t\t\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t\t\t\t<Training />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t</PageWrapper>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t) : null}\r\n\r\n\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t);\r\n};\r\n\r\nexport default OrgAdminMenu;\r\n\r\nexport { accountId }"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,eAAe,EAAEC,UAAU,QAAQ,OAAO;AACvF,OAAOC,yBAAyB,MAAM,2CAA2C;AACjF,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,IAAI,MAAM,sBAAsB;AAEvC,SAASC,OAAO,EAAEC,UAAU,EAAEC,MAAM,EAAUC,GAAG,QAAQ,eAAe;AACxE,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,SAAS,QAAoB,0BAA0B;AAKhE,SAASC,WAAW,EAAEC,UAAU,EAAEC,cAAc,EAAcC,QAAQ,QAAc,eAAe;AACnG,OAAOC,MAAM,MAAM,sBAAsB;AAGzC,OAAOC,UAAU,MAAM,8BAA8B;AACrD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,QAAQ,MAAM,sBAAsB;AAE3C,SAASC,SAAS,EAAEC,YAAY,EAAEC,cAAc,QAAQ,oCAAoC;AAC5F,SAAyBC,SAAS,QAAQ,4BAA4B;AAItE,OAAOC,WAAW,MAAM,gBAAgB;AACxC,SAASC,QAAQ,EAAEC,MAAM,EAAYC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,SAAS,QAAQ,0BAA0B;AACvG,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,QAAQ,MAAM,kBAAkB;AAEvC,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,UAAU,MAAM,sBAAsB;AAC7C,OAAOC,OAAO,MAAM,mBAAmB;AACvC,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,mBAAmB,MAAM,+BAA+B;AAC/D,OAAOC,QAAQ,MAAM,sBAAsB;AAE3C,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,WAAW,MAAM,6BAA6B;AACrD,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,OAAOC,YAAY,MAAM,0BAA0B;AAEnD,OAAOC,kBAAkB,MAAM,gCAAgC;AAE/D,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,iBAAiB,EAAEC,eAAe,QAAQ,6BAA6B;AAChF,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,eAAe,EAAEC,cAAc,QAAiB,2BAA2B;AACpF,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,MAAM,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAC1C,IAAIC,SAAiB;;AAKrB;;AAQA;AACA;AACA,OAAO,MAAMC,kBAA8B,GAAG,EAAE;;AAEhD;AACA,MAAMC,sBAAsB,GAAGA,CAACC,YAAoB,EAAEC,YAAqB,KAAa;EACvF,IAAI;IACH,IAAIA,YAAY,EAAE;MACjB,MAAMC,YAAY,GAAG,IAAIC,IAAI,CAACC,YAAY,CAAC,CAACH,YAAY,CAAC,EAAE;QAAEI,IAAI,EAAE;MAAW,CAAC,CAAC;MAChF,MAAMC,UAAU,GAAGJ,YAAY,CAACK,EAAE,CAACN,YAAY,CAAC;MAChD,IAAIK,UAAU,IAAIA,UAAU,KAAKL,YAAY,EAAE;QAC9C,OAAOK,UAAU;MAClB;IACD;IACA;IACA,OAAON,YAAY;EACpB,CAAC,CAAC,OAAOQ,KAAK,EAAE;IACfC,OAAO,CAACC,IAAI,CAAC,2CAA2CV,YAAY,EAAE,EAAEQ,KAAK,CAAC;IAC9E,OAAOR,YAAY;EACpB;AACD,CAAC;;AAGD;AACA,SAASW,mBAAmBA,CAACC,QAAiB,EAAE;EAC/C,IAAIA,QAAQ,EAAE;IACbC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,KAAK,CAAC;IAClCH,QAAQ,CAACC,IAAI,CAACG,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC;EACzC,CAAC,MACI;IACJJ,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACG,QAAQ,CAAC,KAAK,CAAC,IAAIL,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACI,MAAM,CAAC,KAAK,CAAC;IAChFN,QAAQ,CAACC,IAAI,CAACM,eAAe,CAAC,KAAK,CAAC;EACrC;AACD;AAEA,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA;EACpC,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGzC,cAAc,CAAC,CAAC;EACzC,MAAM,CAAC0C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGjG,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACkG,YAAY,EAAEC,eAAe,CAAC,GAAGnG,QAAQ,CAAgB,IAAI,CAAC;EAErE,MAAM,CAACoG,WAAW,EAAEC,cAAc,CAAC,GAAGrG,QAAQ,CAACqC,aAAa,CAAC,CAAC,CAAC;EAC/D,MAAM,CAACiE,QAAQ,EAAEC,WAAW,CAAC,GAAGvG,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM;IAAEwG,OAAO;IAAEC,WAAW;IAAEC;EAAU,CAAC,GAAGvD,OAAO,CAAC,CAAC;EACrD,MAAM,CAACwD,QAAQ,EAAEC,WAAW,CAAC,GAAG5G,QAAQ,EAAA4F,qBAAA,GAACa,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,QAAQ,cAAAjB,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;EACrE,MAAM,CAACkB,cAAc,EAAEC,iBAAiB,CAAC,GAAG/G,QAAQ,EAAA6F,qBAAA,GAACY,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEK,cAAc,cAAAjB,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;EACvF,MAAM,CAACmB,IAAI,EAAEC,OAAO,CAAC,GAAGjH,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACkH,QAAQ,EAAEC,WAAW,CAAC,GAAGnH,QAAQ,CAAwB,IAAI,CAAC;EACrE,MAAM,CAACoH,SAAS,EAAEC,YAAY,CAAC,GAAGrH,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACsH,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvH,QAAQ,CAACqC,aAAa,CAAC,CAAC,CAAC;EACzE,MAAMmF,QAAQ,GAAGlH,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmH,UAAU,EAAEC,aAAa,CAAC,GAAG1H,QAAQ,CAAS,MAAM;IAC1D,OAAO2H,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC,KAAInB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEoB,QAAQ,KAAI,SAAS;EACtF,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/H,QAAQ,CAAW,EAAE,CAAC;EACtE,MAAMgI,QAAQ,GAAG1G,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC6C,kBAAkB,EAAE8D,qBAAqB,CAAC,GAAGjI,QAAQ,CAAa,EAAE,CAAC;EAC5E,MAAM,CAACkI,OAAO,EAAEC,UAAU,CAAC,GAAGnI,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoI,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrI,QAAQ,CAAa,EAAE,CAAC;EAC1E,MAAM,CAACsI,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvI,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAMwI,eAAe,GAAG/B,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEK,cAAc;EACnD,MAAM,CAAC2B,QAAQ,EAAEC,WAAW,CAAC,GAAG1I,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAAC2I,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5I,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC6I,eAAe,EAAEC,kBAAkB,CAAC,GAAG9I,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM;IAAE+I,YAAY;IAAEC;EAAS,CAAC,GAAG5I,UAAU,CAACqD,cAAc,CAAC;EAC7D,MAAMwF,gBAAgB,GAAG/I,MAAM,CAAwB,IAAI,CAAC;EAC5D,MAAMgJ,iBAAiB,GAAGhJ,MAAM,CAAwB,IAAI,CAAC;EAC7D,MAAM;IAAEiJ,KAAK;IAAEC;EAAO,CAAC,GAAGvF,MAAM,CAAC,CAAC;EAElC,MAAMwF,kBAAkB,GAAIC,YAAoB,IAAK;IACpD,MAAMC,QAAQ,GAAGD,YAAY,KAAKpD,YAAY,GAAG,IAAI,GAAGoD,YAAY;IACpEnD,eAAe,CAACoD,QAAQ,CAAC;IAEzB,IAAID,YAAY,KAAK,QAAQ,IAAIC,QAAQ,KAAK,IAAI,EAAE;MACnDhB,qBAAqB,CAAC,EAAE,CAAC;IAC1B;EACD,CAAC;EAGD,MAAMiB,kBAAkB,GAAIC,KAAiB,IAAK;IACjD,IACCR,gBAAgB,CAACS,OAAO,IAAI,CAACT,gBAAgB,CAACS,OAAO,CAACnE,QAAQ,CAACkE,KAAK,CAACE,MAAc,CAAC,IACpFT,iBAAiB,CAACQ,OAAO,IAAI,CAACR,iBAAiB,CAACQ,OAAO,CAACnE,QAAQ,CAACkE,KAAK,CAACE,MAAc,CAAC,EACrF;MACDxD,eAAe,CAAC,IAAI,CAAC;MACrBoC,qBAAqB,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5B;EACD,CAAC;EACDtI,SAAS,CAAC,MAAM;IACfiF,QAAQ,CAAC0E,gBAAgB,CAAC,WAAW,EAAEJ,kBAAkB,CAAC;IAC1D,OAAO,MAAM;MACZtE,QAAQ,CAAC2E,mBAAmB,CAAC,WAAW,EAAEL,kBAAkB,CAAC;IAC9D,CAAC;EACF,CAAC,EAAE,EAAE,CAAC;EACNvJ,SAAS,CAAC,MAAM;IACf,MAAM6J,cAAc,GAAGrD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEK,cAAc;IAClD,MAAMH,QAAQ,GAAGF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,QAAQ;IAEtC,IAAIiD,cAAc,IAAInD,QAAQ,IAAI,CAACkC,eAAe,EAAE;MACnD,MAAMkB,aAAa,GAAG,MAAAA,CAAA,KAAY;QACjC5B,UAAU,CAAC,IAAI,CAAC;QAChB,IAAI;UACH,IAAIxB,QAAQ,CAACqD,iBAAiB,CAAC,CAAC,KAAK,OAAO,EAAE;YAC7C,MAAMxG,eAAe,CAACkF,WAAW,EAAEP,UAAU,EAAE2B,cAAc,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;UACnF,CAAC,MAAM,IAAInD,QAAQ,CAACqD,iBAAiB,CAAC,CAAC,KAAK,MAAM,EAAE;YACnD,MAAMzG,iBAAiB,CAACmF,WAAW,EAAEP,UAAU,EAAE2B,cAAc,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;UACrF;UACAhB,kBAAkB,CAAC,IAAI,CAAC;QACzB,CAAC,CAAC,OAAOmB,GAAG,EAAE;UACbnF,OAAO,CAACD,KAAK,CAACoF,GAAG,CAAC;QACnB,CAAC,SAAS;UACT9B,UAAU,CAAC,KAAK,CAAC;QAClB;MACD,CAAC;MAED4B,aAAa,CAAC,CAAC;IAChB;EACD,CAAC,EAAE,CAACtD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEK,cAAc,EAAEL,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,QAAQ,EAAEgC,eAAe,CAAC,CAAC;EAEzE,MAAMqB,mBAAmB,GAAIT,KAAU,IAAK;IAC3C,MAAMU,SAAiB,GAAGV,KAAK,CAACE,MAAM,CAACS,KAAK;IAC5CxB,oBAAoB,CAACuB,SAAS,CAAC;IAC/BpB,YAAY,CAACoB,SAAS,CAAC;IACvB,MAAME,cAAc,GAAG1D,QAAQ,CAACqD,iBAAiB,CAAC,CAAC,IAAI,OAAO,GAAG,CAAC,eAAe,CAAC,GAAGtD,SAAS,CAACyD,SAAS,CAAC;IACzGnB,QAAQ,CAACqB,cAAc,CAAC;IACxBnG,SAAS,GAAGiG,SAAS;IACrBxC,YAAY,CAAC2C,OAAO,CAAC,kBAAkB,EAAEpG,SAAS,CAAC;EACpD,CAAC;EAEDjE,SAAS,CAAC,MAAM;IACf,MAAMsK,cAAc,GAAG5C,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IACvD,IAAI2C,cAAc,IAAIA,cAAc,IAAI,IAAI,EAAE;MAC7C,IAAI;QACH,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACH,cAAc,CAAC;QAC3C,IAAIC,QAAQ,CAAC,MAAM,CAAC,EAAE;UACrB,MAAMG,UAAU,GAAGF,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,MAAM,CAAC,CAAC;UAC/CvD,OAAO,CAAC0D,UAAU,CAAC;UACnB,IAAIA,UAAU,EAAE;YAAA,IAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA;YACflE,WAAW,EAAAgE,qBAAA,IAAAC,oBAAA,GAACF,UAAU,CAAC9D,QAAQ,cAAAgE,oBAAA,uBAAnBA,oBAAA,CAAqBb,iBAAiB,CAAC,CAAC,cAAAY,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;YAC3D,MAAMG,KAAK,IAAAD,qBAAA,GAAGH,UAAU,CAAC7D,cAAc,cAAAgE,qBAAA,cAAAA,qBAAA,GAAI,EAAE;YAC7C/D,iBAAiB,CAACgE,KAAK,CAAC;UACzB;QACD;MACD,CAAC,CAAC,OAAOlG,KAAK,EAAE;QACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MACjD;IACD,CAAC,MACI,IAAI4B,WAAW,EAAE;MACrBQ,OAAO,CAACR,WAAW,CAAC;MACpB,IAAIA,WAAW,EAAE;QAAA,IAAAuE,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;QAChBtE,WAAW,EAAAoE,sBAAA,IAAAC,sBAAA,GAACxE,WAAW,CAACI,QAAQ,cAAAoE,sBAAA,uBAApBA,sBAAA,CAAsBjB,iBAAiB,CAAC,CAAC,cAAAgB,sBAAA,cAAAA,sBAAA,GAAI,EAAE,CAAC;QAC5D,MAAMD,KAAK,IAAAG,sBAAA,GAAGzE,WAAW,CAACK,cAAc,cAAAoE,sBAAA,cAAAA,sBAAA,GAAI,EAAE;QAC9CnE,iBAAiB,CAACgE,KAAK,CAAC;MACzB;IACD;EACD,CAAC,EAAE,CAACtE,WAAW,CAAC,CAAC;EACjB;EACAxG,SAAS,CAAC,MAAM;IACf,MAAMkL,mBAAmB,GAAG,MAAAA,CAAA,KAAY;MACvC,IAAI;QACH,IAAIjH,SAAS,EAAE;UACd,MAAMkH,QAAQ,GAAG,MAAMzJ,YAAY,CAAC,CAAC;;UAErC;UACAsG,qBAAqB,CAACmD,QAAQ,CAAC;;UAE/B;UACA,MAAMC,iBAAiB,GAAGD,QAAQ,CAACE,MAAM,CAAEC,IAAc,IACxDA,IAAI,CAAC1D,QAAQ,CAAC2D,WAAW,CAAC,CAAC,KAAK,SACjC,CAAC;UACDnD,oBAAoB,CAACgD,iBAAiB,CAAC;;UAEvC;UACA,MAAMI,qBAAqB,GAAGJ,iBAAiB,CAACK,IAAI,CAACH,IAAI,IACxDA,IAAI,CAAC1D,QAAQ,KAAKJ,UACnB,CAAC,IAAIA,UAAU,KAAK,SAAS;;UAE7B;UACA,IAAI,CAACgE,qBAAqB,EAAE;YAC3B,MAAME,gBAAgB,GAAG,SAAS;YAClCjE,aAAa,CAACiE,gBAAgB,CAAC;YAC/BhE,YAAY,CAAC2C,OAAO,CAAC,kBAAkB,EAAEqB,gBAAgB,CAAC;UAC3D;QACD;MACD,CAAC,CAAC,OAAO1B,GAAG,EAAE;QACbnF,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEoF,GAAG,CAAC;QACrD5B,oBAAoB,CAAC,EAAE,CAAC;QACxBJ,qBAAqB,CAAC,EAAE,CAAC;QACzB;QACA,MAAM0D,gBAAgB,GAAG,CAAAlF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEoB,QAAQ,KAAI,SAAS;QAC3DH,aAAa,CAACiE,gBAAgB,CAAC;QAC/BhE,YAAY,CAAC2C,OAAO,CAAC,kBAAkB,EAAEqB,gBAAgB,CAAC;MAC3D;IACD,CAAC;IAEDR,mBAAmB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACjH,SAAS,CAAC,CAAC,CAAC,CAAC;EACjB,MAAM0H,kBAAkB,GAAGA,CAAA,KAAM;IAChC3F,uBAAuB,CAAC,IAAI,CAAC;EAE9B,CAAC;EAED,MAAM4F,kBAAkB,GAAGA,CAAA,KAAM;IAChC5F,uBAAuB,CAAC,KAAK,CAAC;EAE/B,CAAC;EACDhG,SAAS,CAAC,MAAM;IACf,MAAM6L,uBAAuB,GAAGnE,YAAY,CAACC,OAAO,CAAC,mBAAmB,CAAC;IACzE,IAAIkE,uBAAuB,EAAE;MAC5B;MACA;IAAA;EAEF,CAAC,EAAE,EAAE,CAAC;EAGN,MAAMC,eAAe,GAAIzH,YAAoB,IAAa;IACzD,MAAM0H,QAAQ,GAAG7H,kBAAkB,CAAC8H,IAAI,CAAEV,IAAI,IAAKA,IAAI,CAACW,YAAY,KAAK5H,YAAY,CAAC;IACtF,OAAO0H,QAAQ,GAAGA,QAAQ,CAACnE,QAAQ,GAAG,SAAS;EAChD,CAAC;EAED,MAAMsE,sBAAsB,GAAG,MAAO1C,KAAU,IAAK;IACpD,MAAM2C,gBAAgB,GAAG3C,KAAK,CAACE,MAAM,CAACS,KAAK;IAC3CtF,OAAO,CAACuH,GAAG,CAAC,4BAA4B,EAAED,gBAAgB,CAAC;;IAE3D;IACA1E,aAAa,CAAC0E,gBAAgB,CAAC;;IAE/B;IACA,MAAME,WAAW,GAAGnI,kBAAkB,CAAC8H,IAAI,CAACV,IAAI,IAAIA,IAAI,CAAC1D,QAAQ,KAAKuE,gBAAgB,CAAC;IACvF,MAAM9H,YAAY,GAAGgI,WAAW,GAAGA,WAAW,CAACJ,YAAY,GAAG,IAAI;IAElEvE,YAAY,CAAC2C,OAAO,CAAC,kBAAkB,EAAE8B,gBAAgB,CAAC;IAC1DzE,YAAY,CAAC2C,OAAO,CAAC,sBAAsB,EAAEhG,YAAY,CAAC;;IAE1D;IACA,IAAI;MACH,MAAM1C,cAAc,CAACwK,gBAAgB,CAAC;MACtCtH,OAAO,CAACuH,GAAG,CAAC,gCAAgC,EAAED,gBAAgB,CAAC;IAChE,CAAC,CAAC,OAAOvH,KAAK,EAAE;MACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IACjD;EACD,CAAC;EAED,MAAM,CAAC0H,SAAS,EAAEC,YAAY,CAAC,GAAGxM,QAAQ,CAAM,CAAC,CAAC,CAAC;EACnDC,SAAS,CAAC,MAAM;IACf,MAAMwM,cAAc,GAAG,MAAOhF,UAAe,IAAK;MACjD,IAAIA,UAAU,KAAK,SAAS,EAAE;QAC7BlH,IAAI,CAACmM,cAAc,CAAC,IAAI,CAAC;QACzB;MACD;MAEA,IAAI;QACHvE,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMwE,IAAI,GAAG,MAAMjL,SAAS,CAAC+F,UAAU,EAAE,KAAK,CAAC;QAC/C,IAAIkF,IAAI,EAAE;UACT;UACA,MAAML,WAAW,GAAGlE,iBAAiB,CAAC6D,IAAI,CAACV,IAAI,IAAIA,IAAI,CAAC1D,QAAQ,KAAKJ,UAAU,CAAC,IAC/EtD,kBAAkB,CAAC8H,IAAI,CAACV,IAAI,IAAIA,IAAI,CAAC1D,QAAQ,KAAKJ,UAAU,CAAC;UAC9D,MAAMmF,WAAW,GAAGN,WAAW,GAAGA,WAAW,CAACJ,YAAY,GAAG,EAAE;UAE/D,IAAIU,WAAW,EAAE;YAChB,IAAIC,eAAoB,GAAG,CAAC,CAAC;;YAE7B;YACA,IAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC,EAAE;cACxBA,IAAI,CAACK,OAAO,CAAEC,IAAS,IAAK;gBAC3B,IAAIA,IAAI,CAACC,SAAS,IAAID,IAAI,CAACxF,UAAU,CAAC,KAAK0F,SAAS,EAAE;kBACrDN,eAAe,CAACI,IAAI,CAACC,SAAS,CAAC,GAAGD,IAAI,CAACxF,UAAU,CAAC;gBACnD;cACD,CAAC,CAAC;YACH,CAAC,MAAM,IAAIkF,IAAI,CAACC,WAAW,CAAC,EAAE;cAC7B;cACAC,eAAe,GAAGF,IAAI,CAACC,WAAW,CAAC;YACpC;YAEA,IAAIQ,MAAM,CAACC,IAAI,CAACR,eAAe,CAAC,CAACS,MAAM,GAAG,CAAC,EAAE;cAC5Cd,YAAY,CAACK,eAAe,CAAC;cAC7BtM,IAAI,CAACgN,iBAAiB,CAACX,WAAW,EAAE,aAAa,EAAEC,eAAe,EAAE,IAAI,EAAE,IAAI,CAAC;cAC/EtM,IAAI,CAACmM,cAAc,CAACE,WAAW,CAAC;YACjC;UACD;QACD;MACD,CAAC,CAAC,OAAO3C,GAAG,EAAE;QACbnF,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEoF,GAAG,CAAC;MAC7C,CAAC,SAAS;QACT9B,UAAU,CAAC,KAAK,CAAC;MAClB;IACD,CAAC;IAEDsE,cAAc,CAAChF,UAAU,CAAC;EAC3B,CAAC,EAAE,CAACA,UAAU,EAAEW,iBAAiB,EAAEjE,kBAAkB,CAAC,CAAC;EAGvD,MAAMqJ,eAAe,GAAGA,CAAA,KAAM;IAC7BjH,WAAW,CAAEkH,IAAI,IAAK,CAACA,IAAI,CAAC;EAC7B,CAAC;EAEDxN,SAAS,CAAC,MAAM;IACf,MAAMyN,WAAW,GAAG7L,SAAS,CAAC0F,mBAAmB,CAAC;IAClD,OAAO,MAAMmG,WAAW,CAAC,CAAC;EAC3B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC7BxN,eAAe,CAAC,MAAM;MACrBqH,QAAQ,CAAC,GAAG,CAAC;IACd,CAAC,CAAC;EACH,CAAC;EAED,MAAMoG,mBAAmB,GAAInE,KAAU,IAAK;IAC3C,IAAIvC,QAAQ,EAAE;MACb;MACAC,WAAW,CAAC,IAAI,CAAC;IAClB,CAAC,MAAM;MACN;MACAA,WAAW,CAACsC,KAAK,CAACoE,aAAa,CAAC;IACjC;EACD,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC5B3N,eAAe,CAAC,MAAM;MACrBqH,QAAQ,CAAC,gBAAgB,CAAC;IAC3B,CAAC,CAAC;EACH,CAAC;EAED,MAAMuG,kBAAkB,GAAGA,CAAA,KAAM;IAChC5N,eAAe,CAAC,MAAM;MACrBqH,QAAQ,CAAC,WAAW,CAAC;IACtB,CAAC,CAAC;EACH,CAAC;EAED,MAAMwG,gBAAgB,GAAGA,CAAA,KAAM;IAC9B7N,eAAe,CAAC,MAAM;MACrBqH,QAAQ,CAAC,QAAQ,CAAC;IACnB,CAAC,CAAC;EACH,CAAC;EAED,MAAMyG,sBAAsB,GAAGA,CAAA,KAAM;IACpC9N,eAAe,CAAC,MAAM;MACrBqH,QAAQ,CAAC,mBAAmB,CAAC;IAC9B,CAAC,CAAC;EACH,CAAC;EAED,MAAM0G,eAAe,GAAGA,CAAA,KAAM;IAC7B/N,eAAe,CAAC,MAAM;MACrBqH,QAAQ,CAAC,gBAAgB,CAAC;IAC3B,CAAC,CAAC;EACH,CAAC;EAED,MAAM2G,iBAAiB,GAAGA,CAAA,KAAM;IAC/BhO,eAAe,CAAC,MAAM;MACrBqH,QAAQ,CAAC,QAAQ,CAAC;IACnB,CAAC,CAAC;EACH,CAAC;EAED,MAAM4G,kBAAkB,GAAGA,CAAA,KAAM;IAChCjO,eAAe,CAAC,MAAM;MACrBqH,QAAQ,CAAC,mBAAmB,CAAC;IAC9B,CAAC,CAAC;EACH,CAAC;EAED,MAAM6G,kBAAkB,GAAGA,CAAA,KAAM;IAChClO,eAAe,CAAC,MAAM;MACrBqH,QAAQ,CAAC,mBAAmB,CAAC;IAC9B,CAAC,CAAC;EACH,CAAC;EAED,MAAM8G,iBAAiB,GAAGA,CAAA,KAAM;IAC/BnO,eAAe,CAAC,MAAM;MACrBqH,QAAQ,CAAC,gBAAgB,CAAC;IAC3B,CAAC,CAAC;EACH,CAAC;EAED,MAAM+G,kBAAkB,GAAGA,CAAA,KAAM;IAEhC/G,QAAQ,CAAC,cAAc,CAAC;IAExBL,WAAW,CAAC,IAAI,CAAC;EAClB,CAAC;EAED,MAAMqH,WAAW,GAAIC,IAAY,IAAK;IACrC,QAAQA,IAAI;MACX,KAAK,MAAM;QACV,OAAOjH,QAAQ,CAAC,OAAO,CAAC;MACzB,KAAK,SAAS;QACb,OAAOA,QAAQ,CAAC,mBAAmB,CAAC;MACrC,KAAK,UAAU;QACd,OAAOA,QAAQ,CAAC,WAAW,CAAC;MAC7B,KAAK,WAAW;QACf,OAAOA,QAAQ,CAAC,uBAAuB,CAAC;MACzC,KAAK,cAAc;QAClB,OAAOA,QAAQ,CAAC,2BAA2B,CAAC;MAC7C,KAAK,cAAc;QAClB,OAAOA,QAAQ,CAAC,0BAA0B,CAAC;MAC5C;QACC,OAAOA,QAAQ,CAAC,GAAG,CAAC;IACtB;EACD,CAAC;EAID,MAAMkH,qBAAqB,GAAGA,CAAA,KAAM;IACnC,MAAMC,aAAa,GAAGhH,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IACtD,IAAI+G,aAAa,KAAK,YAAY,EAAE;MACnCxO,eAAe,CAAC,MAAM;QACrBqH,QAAQ,CAAC,GAAG,CAAC;MACd,CAAC,CAAC;IACH;EACD,CAAC;EACDvH,SAAS,CAAC,MAAM;IACf,IAAIwI,QAAQ,CAAC6E,MAAM,GAAG,CAAC,EAAE;MACxB1E,oBAAoB,CAACH,QAAQ,CAAC,CAAC,CAAC,CAAC0B,SAAS,CAAC;MAC3CpB,YAAY,CAACN,QAAQ,CAAC,CAAC,CAAC,CAAC0B,SAAS,CAAC;MACnCjG,SAAS,GAAGuE,QAAQ,CAAC,CAAC,CAAC,CAAC0B,SAAS;MACjCxC,YAAY,CAAC2C,OAAO,CAAC,kBAAkB,EAAEpG,SAAS,CAAC;MACnD,MAAMmG,cAAc,GAAG1D,QAAQ,CAACqD,iBAAiB,CAAC,CAAC,IAAI,OAAO,GAAG,CAAC,eAAe,CAAC,GAAGtD,SAAS,CAAC+B,QAAQ,CAAC,CAAC,CAAC,CAAC0B,SAAS,CAAC;MACrHnB,QAAQ,CAACqB,cAAc,CAAC;IAEzB;EACD,CAAC,EAAE,CAAC5B,QAAQ,CAAC,CAAC;EACd,MAAMmG,iBAAiB,GAAGA,CAAA,KAAM;IAC/BvH,YAAY,CAAC,IAAI,CAAC;IAClBF,WAAW,CAAC,IAAI,CAAC;EAClB,CAAC;EACD,MAAM0H,aAAa,GAAGA,CAAA,KAAM;IAC3BxI,cAAc,CAAC,CAACiB,gBAAgB,CAAC;EAClC,CAAC;EACF;EACArH,SAAS,CAAC,MAAM;IACd,eAAe6O,cAAcA,CAAA,EAAG;MAC9B,IAAIhI,cAAc,EAAE;QAClB;QACA,MAAMiI,SAAS,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACjH,QAAQ,CAACkH,MAAM,CAAC;QAC7D,MAAMC,kBAAkB,GAAGJ,SAAS,CAACK,GAAG,CAAC,OAAO,CAAC,IAAIL,SAAS,CAACK,GAAG,CAAC,SAAS,CAAC,IAAIL,SAAS,CAACK,GAAG,CAAC,QAAQ,CAAC;QACxG,MAAMC,oBAAoB,GAAGC,cAAc,CAAC1H,OAAO,CAAC,oBAAoB,CAAC,KAAK,MAAM;QACpF,MAAM2H,gBAAgB,GAAGD,cAAc,CAAC1H,OAAO,CAAC,kBAAkB,CAAC,KAAK,MAAM;;QAE9E;QACA,MAAM4C,QAAQ,GAAG7C,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;QACjD,MAAM4H,gBAAgB,GAAGhF,QAAQ,IAAIA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,MAAM;QAC7E,IAAIiF,oBAAoB,GAAG,KAAK;QAEhC,IAAID,gBAAgB,EAAE;UACpB,IAAI;YACF,MAAME,cAAc,GAAGjF,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC;YAC3CiF,oBAAoB,GAAGC,cAAc,CAAC,MAAM,CAAC,IAAIA,cAAc,CAAC,WAAW,CAAC;UAC9E,CAAC,CAAC,OAAOC,CAAC,EAAE;YACV;UAAA;QAEJ;;QAED;QACC,IAAIR,kBAAkB,IAAKI,gBAAgB,EAAE;UAE3C;UACAnG,MAAM,CAAC,KAAK,CAAC;UACb;QACF;QAEA,IAAI;UACF;UACA,MAAMwG,GAAG,GAAG,MAAMhM,mBAAmB,CAACkD,cAAc,CAAC;UACrD,IAAI+I,MAAM,GAAGD,GAAG,IAAI,OAAOA,GAAG,CAACE,GAAG,KAAK,WAAW,GAAGF,GAAG,CAACE,GAAG,GAAG,KAAK;;UAEpE;UACA,IAAIC,UAAU,GAAG,KAAK;UACtB,MAAMC,WAAW,GAAG;YAClBC,IAAI,EAAE,CAAC,CAAC;YACRC,GAAG,EAAE,CAAC,CAAC;YACPC,OAAO,EAAE,EAAE;YACXC,aAAa,EAAE;UACjB,CAAC;UACN,MAAMhF,QAAQ,GAAG,MAAM1H,eAAe,CAAC2M,IAAI,CAAC,6BAA6B,EAAEL,WAAW,CAAC;UAClF,MAAMvH,QAAQ,GAAG2C,QAAQ,CAACuB,IAAI,CAAC2D,OAAO;UACtC,IAAIxD,KAAK,CAACC,OAAO,CAACtE,QAAQ,CAAC,IAAIA,QAAQ,CAAC6E,MAAM,GAAG,CAAC,EAAE;YAClD,MAAMiD,eAAe,GAAG9H,QAAQ,CAACwD,IAAI,CAAEuE,GAAQ,IAAKA,GAAG,CAACrG,SAAS,KAAKxB,iBAAiB,CAAC;YACxF,IAAI4H,eAAe,IAAI,OAAOA,eAAe,CAACE,GAAG,KAAK,WAAW,EAAE;cACjEV,UAAU,GAAGQ,eAAe,CAACE,GAAG;YAClC;UACF;UACArH,MAAM,CAAC2G,UAAU,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,OAAOJ,CAAC,EAAE;UACV7K,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAE8K,CAAC,CAAC;UACpD;UACAvG,MAAM,CAAC,KAAK,CAAC;QACf;MAEF;IACF;IACA0F,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAChI,cAAc,EAAC6B,iBAAiB,EAAES,MAAM,CAAC,CAAC;EAC7CnJ,SAAS,CAAC,MAAM;IACf+E,mBAAmB,CAACmE,KAAK,CAAC;EAC3B,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACX,oBACCpF,OAAA;IAAA2M,QAAA,gBACC3M,OAAA;MAAK4M,SAAS,EAAC,cAAc;MAAAD,QAAA,gBAC5B3M,OAAA;QAAA2M,QAAA,eACC3M,OAAA;UAAK4M,SAAS,EAAC,oBAAoB;UAAAD,QAAA,eAClC3M,OAAA;YAAA2M,QAAA,eACC3M,OAAA;cACC6M,GAAG,EAAE3O,IAAK;cACV4O,GAAG,EAAC,iBAAiB;cACrBF,SAAS,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEF,CAAC,eACNlN,OAAA;QAAK4M,SAAS,EAAC,qBAAqB;QAAAD,QAAA,GAClCpK,QAAQ,IAAIK,QAAQ,CAACqD,iBAAiB,CAAC,CAAC,KAAK,YAAY,gBACzDjG,OAAA;UAAK4M,SAAS,EAAC,0BAA0B;UAAAD,QAAA,gBACxC3M,OAAA;YACCmN,OAAO,EAAEA,CAAA,KAAM1C,WAAW,CAAC,cAAc,CAAE;YAC3CmC,SAAS,EAAC,aAAa;YAAAD,QAAA,EAEtB3K,SAAS,CAAC,cAAc;UAAC;YAAA+K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACJlN,OAAA;YACCmN,OAAO,EAAEA,CAAA,KAAM1C,WAAW,CAAC,WAAW,CAAE;YACxCmC,SAAS,EAAC,aAAa;YAAAD,QAAA,EAEtB3K,SAAS,CAAC,WAAW;UAAC;YAAA+K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eAEJlN,OAAA;YACCmN,OAAO,EAAEA,CAAA,KAAM1C,WAAW,CAAC,cAAc,CAAE;YAC3CmC,SAAS,EAAC,aAAa;YAAAD,QAAA,EAEtB3K,SAAS,CAAC,cAAc;UAAC;YAAA+K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,GACH,IAAI,EACPtK,QAAQ,CAACqD,iBAAiB,CAAC,CAAC,KAAK,YAAY,iBAC7CjG,OAAA;UAAG4M,SAAS,EAAC,yBAAyB;UACrC,eAAY,SAAS;UACrBO,OAAO,EAAE1D;QAAgB;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CACH,EAEAtK,QAAQ,KAAK,YAAY,gBAEzB5C,OAAA;UAAK4M,SAAS,EAAC,UAAU;UAACQ,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,eAClD3M,OAAA,CAAC/C,WAAW;YAACqQ,OAAO,EAAC,UAAU;YAACV,SAAS,EAAC,YAAY;YAAAD,QAAA,eACrD3M,OAAA,CAAC3C,MAAM;cACNgJ,KAAK,EAAEzB,iBAAkB;cACzB2I,QAAQ,EAAEpH,mBAAoB;cAE9BqH,YAAY;cACZC,SAAS;cACTC,cAAc,eACb1N,OAAA,CAAC7C,cAAc;gBAACwQ,QAAQ,EAAC,OAAO;gBAAAhB,QAAA,eAC/B3M,OAAA;kBACC6M,GAAG,EAAEzO,OAAQ;kBACb0O,GAAG,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAElB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACa,CAChB;cACDU,YAAY,eACX5N,OAAA,CAAC7C,cAAc;gBAACwQ,QAAQ,EAAC,KAAK;gBAAAhB,QAAA,eAC7B3M,OAAA;kBACC6M,GAAG,EAAExO,SAAU;kBACfyO,GAAG,EAAC,gBAAgB;kBACpBM,KAAK,EAAE;oBACNS,MAAM,EAAE,SAAS;oBAAEC,IAAI,EAAE,KAAK;oBAAEC,SAAS,EAAE5L,YAAY,KAAK,OAAO,GAAG,gBAAgB,GAAG,cAAc;oBACvG6L,UAAU,EAAE;kBACb,CAAE;kBACFb,OAAO,EAAGvB,CAAC,IAAK;oBACfA,CAAC,CAACqC,eAAe,CAAC,CAAC,CAAC,CAAC;oBACrB3I,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC;kBAC9B;gBAAE;kBAAAyH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACa,CAChB;cACDgB,IAAI,EAAE/L,YAAY,KAAK,OAAQ;cAC/BgM,OAAO,EAAEA,CAAA,KAAM/L,eAAe,CAAC,IAAI,CAAE;cACrCgM,MAAM,EAAEA,CAAA,KAAM9I,kBAAkB,CAAC,OAAO,CAAE;cAC1C+I,aAAa,EAAEA,CAAA,KAAM,IAAK;cAC1BC,EAAE,EAAE;gBACH,mBAAmB,EAAE;kBACpBC,OAAO,EAAE;gBACV;cACD,CAAE;cACFC,SAAS,EAAE;gBACVC,UAAU,EAAE;kBACXH,EAAE,EAAE;oBACHjB,KAAK,EAAE,OAAO;oBAAE;oBAChBqB,SAAS,EAAE,OAAO;oBAAE;oBACpBC,SAAS,EAAE,MAAM;oBAAE;oBACnBC,SAAS,EAAE,KAAK;oBAAE;oBAClB,qBAAqB,EAAE;sBACtBC,QAAQ,EAAE,MAAM;sBAAE;sBAClBC,UAAU,EAAE,QAAQ;sBAAE;sBACtBC,SAAS,EAAE,YAAY,CAAE;oBAC1B;kBACD;gBACD;cACD,CAAE;cAAApC,QAAA,EAEDjI,QAAQ,CAAC6E,MAAM,GAAG,CAAC,GACnB7E,QAAQ,CAACsK,GAAG,CAAEC,OAAO,iBACpBjP,OAAA,CAAC5C,QAAQ;gBAAyBiJ,KAAK,EAAE4I,OAAO,CAAC7I,SAAU;gBAAAuG,QAAA,EACzDsC,OAAO,CAACC;cAAW,GADND,OAAO,CAAC7I,SAAS;gBAAA2G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEtB,CACV,CAAC,gBAEFlN,OAAA,CAAC5C,QAAQ;gBAAC+R,QAAQ;gBAAAxC,QAAA,EAAC;cAAqB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU;YAClD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,GACH,IAAI,EAGPjJ,QAAQ,CAACmL,QAAQ,KAAK,uBAAuB,IAAIxM,QAAQ,KAAK,YAAY,gBAC1E5C,OAAA;UAAK4M,SAAS,EAAC,UAAU;UAAAD,QAAA,eACxB3M,OAAA,CAACpD,GAAG;YAAA+P,QAAA,eACH3M,OAAA,CAAC/C,WAAW;cAACqQ,OAAO,EAAC,UAAU;cAACV,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACrD3M,OAAA,CAAC9C,UAAU;gBAACmS,EAAE,EAAC;cAAoB;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjDlN,OAAA,CAAC3C,MAAM;gBACNuP,SAAS,EAAC,gBAAgB;gBAC1B0C,OAAO,EAAC,oBAAoB;gBAC5BD,EAAE,EAAC,aAAa;gBAChBhJ,KAAK,EAAE3C,UAAW,CAAC;gBAAA;gBACnB6J,QAAQ,EAAEnF,sBAAuB;gBACjCsF,cAAc,eACb1N,OAAA,CAAC7C,cAAc;kBAACwQ,QAAQ,EAAC,OAAO;kBAAAhB,QAAA,eAC/B3M,OAAA;oBAAK6M,GAAG,EAAE1O,MAAO;oBAAC2O,GAAG,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAChB;gBACDU,YAAY,eACX5N,OAAA,CAAC7C,cAAc;kBAACwQ,QAAQ,EAAC,KAAK;kBAAAhB,QAAA,eAC7B3M,OAAA;oBACC6M,GAAG,EAAExO,SAAU;oBACfyO,GAAG,EAAC,gBAAgB;oBACpBM,KAAK,EAAE;sBACNS,MAAM,EAAE,SAAS;sBACjBC,IAAI,EAAE,KAAK;sBACXC,SAAS,EAAE5L,YAAY,KAAK,QAAQ,GAAG,gBAAgB,GAAG,cAAc;sBACxE6L,UAAU,EAAE;oBACb,CAAE;oBACFb,OAAO,EAAGvB,CAAC,IAAK;sBACfA,CAAC,CAACqC,eAAe,CAAC,CAAC,CAAC,CAAC;sBACrB7L,eAAe,CAACD,YAAY,KAAK,QAAQ,GAAG,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC;oBAC/D;kBAAE;oBAAA4K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACa,CAChB;gBACDmB,aAAa,EAAEA,CAAA,KAAM,IAAK;gBAC1BH,IAAI,EAAE/L,YAAY,KAAK,QAAS;gBAChCgM,OAAO,EAAEA,CAAA,KAAM;kBACd/L,eAAe,CAAC,IAAI,CAAC;kBACrBoC,qBAAqB,CAAC,EAAE,CAAC;gBAC1B,CAAE;gBACF4J,MAAM,EAAEA,CAAA,KAAM9I,kBAAkB,CAAC,QAAQ,CAAE;gBAC3CgJ,EAAE,EAAE;kBACH,mBAAmB,EAAE;oBACpBC,OAAO,EAAE;kBACV;gBACD,CAAE;gBACFC,SAAS,EAAE;kBACVC,UAAU,EAAE;oBACXH,EAAE,EAAE;sBACHjB,KAAK,EAAE,OAAO;sBACdqB,SAAS,EAAE,OAAO;sBAClBC,SAAS,EAAE,MAAM;sBACjBC,SAAS,EAAE,KAAK;sBAChB,qBAAqB,EAAE;wBACtBC,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE,QAAQ;wBACpBC,SAAS,EAAE;sBACZ,CAAC;sBACD,qBAAqB,EAAE;wBACtBpB,QAAQ,EAAE,QAAQ;wBAClBxB,GAAG,EAAE,CAAC;wBACNoD,eAAe,EAAE,OAAO;wBACxBC,YAAY,EAAE,mBAAmB;wBACjCC,OAAO,EAAE,UAAU;wBACnBC,MAAM,EAAE;sBACT,CAAC;sBACD,iBAAiB,EAAE;wBAClBrC,KAAK,EAAE,MAAM;wBACbwB,QAAQ,EAAE,MAAM;wBAChBY,OAAO,EAAE,UAAU;wBACnBE,MAAM,EAAE,gBAAgB;wBACxBC,YAAY,EAAE,KAAK;wBACnBC,OAAO,EAAE,MAAM;wBACf,SAAS,EAAE;0BACVC,WAAW,EAAE;wBACd;sBACD;oBACD;kBACD;gBACD,CAAE;gBAAAnD,QAAA,gBAEF3M,OAAA;kBAAK4M,SAAS,EAAC,kBAAkB;kBAAAD,QAAA,eAChC3M,OAAA;oBACCW,IAAI,EAAC,MAAM;oBACXiM,SAAS,EAAC,cAAc;oBACxBmD,WAAW,EAAC,qBAAqB;oBACjC1J,KAAK,EAAE9B,kBAAmB;oBAC1BgJ,QAAQ,EAAG3B,CAAC,IAAK;sBAChBA,CAAC,CAACqC,eAAe,CAAC,CAAC;sBACnBzJ,qBAAqB,CAACoH,CAAC,CAAChG,MAAM,CAACS,KAAK,CAAC;oBACtC,CAAE;oBACF2J,SAAS,EAAGpE,CAAC,IAAKA,CAAC,CAACqC,eAAe,CAAC,CAAE;oBACtCd,OAAO,EAAGvB,CAAC,IAAKA,CAAC,CAACqC,eAAe,CAAC;kBAAE;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNlN,OAAA,CAAC5C,QAAQ;kBAAUiJ,KAAK,EAAC,SAAS;kBAAAsG,QAAA,EAAC;gBAEnC,GAFc,IAAI;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAER,CAAC,EACV7I,iBAAiB,CAChBkD,MAAM,CAAEC,IAAI,IACZA,IAAI,CAAC1D,QAAQ,CAAC2D,WAAW,CAAC,CAAC,CAACwI,QAAQ,CAAC1L,kBAAkB,CAACkD,WAAW,CAAC,CAAC,CACtE,CAAC,CACAyI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACrM,QAAQ,CAACuM,aAAa,CAACD,CAAC,CAACtM,QAAQ,CAAC,CAAC,CACpDkL,GAAG,CAAExH,IAAI,iBACTxH,OAAA,CAAC5C,QAAQ;kBAAuBiJ,KAAK,EAAEmB,IAAI,CAAC1D,QAAS;kBAACwK,EAAE,EAAE;oBAAEO,QAAQ,EAAE;kBAAO,CAAE;kBAAAlC,QAAA,EAC7EtM,sBAAsB,CAACmH,IAAI,CAAC1D,QAAQ,EAAE0D,IAAI,CAACW,YAAY;gBAAC,GAD3CX,IAAI,CAAC8I,UAAU;kBAAAvD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEpB,CACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEF,CAAC,GACH,IAAI,eACRlN,OAAA;UACC4M,SAAS,EAAC,YAAY;UACtBO,OAAO,EAAEtD,mBAAoB;UAAA8C,QAAA,eAE7B3M,OAAA;YAAMoN,KAAK,EAAE;cAAEmB,OAAO,EAAE,MAAM;cAAEgC,WAAW,EAAE;YAAoB,CAAE;YAAA5D,QAAA,eAClE3M,OAAA;cAAA2M,QAAA,eACC3M,OAAA;gBAAK4M,SAAS,EAAC,UAAU;gBAAAD,QAAA,eACxB3M,OAAA;kBAAM4M,SAAS,EAAC,WAAW;kBAAAD,QAAA,EAAE7P,aAAa,GAAGA,aAAa,GAAGD;gBAAY;kBAAAkQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlN,OAAA;UAAK4M,SAAS,EAAC,cAAc;UAAAD,QAAA,eAC5B3M,OAAA,CAACvD,OAAO;YACPyR,IAAI,EAAEsC,OAAO,CAACrN,QAAQ,CAAE;YACxBA,QAAQ,EAAEA;YACV;YAAA;YACAgL,OAAO,EAAEtE,mBAAoB;YAC7B+C,SAAS,EAAC,gBAAgB;YAC1B6D,YAAY,EAAE;cACbC,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACb,CAAE;YACFC,eAAe,EAAE;cAChBF,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACb,CAAE;YACFlC,UAAU,EAAE;cACXrB,KAAK,EAAE;gBACNwB,SAAS,EAAE;cACZ;YACD,CAAE;YACFN,EAAE,EAAE;cACH,kBAAkB,EAAE;gBACnBjB,KAAK,EAAE,OAAO;gBACdwD,MAAM,EAAE,MAAM;gBACdjB,YAAY,EAAE,KAAK;gBACnBH,OAAO,EAAE,WAAW;gBACpBF,eAAe,EAAE,SAAS;gBAC1BuB,SAAS,EAAE,gCAAgC;gBAC3CpB,MAAM,EAAE;cACT;YACD,CAAE;YAAA/C,QAAA,eAEF3M,OAAA,CAACpD,GAAG;cACH2R,OAAO,EAAC,MAAM;cACdwC,aAAa,EAAC,QAAQ;cACtBC,UAAU,EAAC,QAAQ;cAAArE,QAAA,eAEnB3M,OAAA,CAACpD,GAAG;gBACHyQ,KAAK,EAAC,MAAM;gBAAAV,QAAA,GAEV/J,QAAQ,CAACqD,iBAAiB,CAAC,CAAC,KAAK,YAAY,iBAAMjG,OAAA,CAAAE,SAAA;kBAAAyM,QAAA,gBACrD3M,OAAA,CAACrD,MAAM;oBACN8Q,SAAS;oBACTN,OAAO,EAAEtF,kBAAmB;oBAC5ByG,EAAE,EAAE;sBAEHmB,OAAO,EAAE,QAAQ;sBACjBwB,GAAG,EAAE,MAAM;sBACXC,cAAc,EAAE,YAAY;sBAC5BC,aAAa,EAAE;oBAChB,CAAE;oBAAAxE,QAAA,gBAEF3M,OAAA;sBACC6M,GAAG,EAAE7O,QAAS;sBACd8O,GAAG,EAAC,eAAe;sBACnBM,KAAK,EAAE;wBAAEC,KAAK,EAAE,MAAM;wBAAEwD,MAAM,EAAE,MAAM;wBAAEN,WAAW,EAAE;sBAAM;oBAAE;sBAAAxD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7D,CAAC,eACFlN,OAAA,CAACtD,UAAU;sBACV4R,EAAE,EAAE;wBACH8C,UAAU,EAAE,SAAS;wBACrBvC,QAAQ,EAAE,MAAM;wBAChBwC,UAAU,EAAE,GAAG;wBACfC,UAAU,EAAE,MAAM;wBAClBC,aAAa,EAAE,OAAO;wBACtBC,SAAS,EAAE,MAAM;wBACjBC,KAAK,EAAE;sBACR,CAAE;sBAAA9E,QAAA,EAED3K,SAAS,CAAC,gBAAgB;oBAAC;sBAAA+K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACTlN,OAAA,CAACrD,MAAM;oBACN8Q,SAAS;oBACTN,OAAO,EAAE3C,kBAAmB;oBAC5B8D,EAAE,EAAE;sBAEHmB,OAAO,EAAE,QAAQ;sBACjBwB,GAAG,EAAE,MAAM;sBACXC,cAAc,EAAE,YAAY;sBAC5BC,aAAa,EAAE;oBAChB,CAAE;oBAAAxE,QAAA,gBAEF3M,OAAA,CAAC1D,yBAAyB;sBAAC8Q,KAAK,EAAE;wBAAEqE,KAAK,EAAE;sBAAQ;oBAAE;sBAAA1E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACxDlN,OAAA,CAACtD,UAAU;sBACV4R,EAAE,EAAE;wBACH8C,UAAU,EAAE,SAAS;wBACrBvC,QAAQ,EAAE,MAAM;wBAChBwC,UAAU,EAAE,GAAG;wBACfC,UAAU,EAAE,MAAM;wBAClBC,aAAa,EAAE,OAAO;wBACtBC,SAAS,EAAE,MAAM;wBACjBC,KAAK,EAAE;sBACR,CAAE;sBAAA9E,QAAA,EAED3K,SAAS,CAAC,cAAc;oBAAC;sBAAA+K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA,eAAE,CAAE,eACblN,OAAA,CAACrD,MAAM;kBACN8Q,SAAS;kBACTN,OAAO,EAAEtC,iBAAkB;kBAC3ByD,EAAE,EAAE;oBAEHmB,OAAO,EAAE,QAAQ;oBACjBwB,GAAG,EAAE,MAAM;oBACXC,cAAc,EAAE,YAAY;oBAC5BC,aAAa,EAAE;kBAChB,CAAE;kBACFvE,SAAS,EAAC,cAAc;kBAAAD,QAAA,gBAExB3M,OAAA;oBACC6M,GAAG,EAAE5O,MAAO;oBACZ6O,GAAG,EAAC,aAAa;oBACjBM,KAAK,EAAE;sBAAEC,KAAK,EAAE,MAAM;sBAAEwD,MAAM,EAAE,MAAM;sBAAEN,WAAW,EAAE;oBAAM;kBAAE;oBAAAxD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACFlN,OAAA,CAACtD,UAAU;oBACV4R,EAAE,EAAE;sBACH8C,UAAU,EAAE,SAAS;sBACrBvC,QAAQ,EAAE,MAAM;sBAChBwC,UAAU,EAAE,GAAG;sBACfC,UAAU,EAAE,MAAM;sBAClBC,aAAa,EAAE,OAAO;sBACtBC,SAAS,EAAE,MAAM;sBACjBC,KAAK,EAAE;oBACR,CAAE;oBAAA9E,QAAA,EAED3K,SAAS,CAAC,QAAQ;kBAAC;oBAAA+K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACNlN,OAAA,CAACV,kBAAkB;MAClB4O,IAAI,EAAEjM,oBAAqB;MAC3BkM,OAAO,EAAErG,kBAAmB;MAC5B5F,uBAAuB,EAAEA;IAAwB;MAAA6K,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC,eACFlN,OAAA;MAAK4M,SAAS,EAAC,oBAAoB;MAAAD,QAAA,GACjC/J,QAAQ,CAACqD,iBAAiB,CAAC,CAAC,KAAK,YAAY,gBAAGjG,OAAA,CAACjD,QAAQ;QAAC2U,oBAAoB,EAAEhO;MAAW;QAAAqJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GAAG,IAAI,EACrG7J,SAAS,gBACTrD,OAAA;QAAK4M,SAAS,EAAC,qBAAqB;QAAAD,QAAA,eACnC3M,OAAA;UAAK4M,SAAS,EAAC,sCAAsC;UAAAD,QAAA,gBACpD3M,OAAA;YAAA2M,QAAA,eACC3M,OAAA;cAAK6M,GAAG,EAAE7P;YAAU;cAAA+P,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACNlN,OAAA;YAAK4M,SAAS,EAAC,mBAAmB;YAAAD,QAAA,EAAE3K,SAAS,CAAC,cAAc;UAAC;YAAA+K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpElN,OAAA;YAAK4M,SAAS,EAAC,eAAe;YAAAD,QAAA,EAC5B3K,SAAS,CAAC,kCAAkC;UAAC;YAAA+K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACNlN,OAAA;YAAK4M,SAAS,EAAC,eAAe;YAAAD,QAAA,gBAC7B3M,OAAA;cACCmN,OAAO,EAAEA,CAAA,KAAM7J,YAAY,CAAC,KAAK,CAAE;cACnCsJ,SAAS,EAAC,qBAAqB;cAAAD,QAAA,EAE9B3K,SAAS,CAAC,QAAQ;YAAC;cAAA+K,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACTlN,OAAA;cACCmN,OAAO,EAAE,MAAAA,CAAA,KAAY;gBACpB7J,YAAY,CAAC,KAAK,CAAC;gBACnB,IAAI,CAAAZ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,QAAQ,MAAK,YAAY,EAAE;kBAC3C,IAAI;oBACH,MAAMuE,QAAQ,GAAG,MAAMzH,cAAc,CAAC+R,GAAG,CAAC,cAAc,CAAC;oBACzD,IAAItK,QAAQ,CAACuK,MAAM,KAAK,GAAG,EAAE;sBAC5BhO,YAAY,CAACiO,KAAK,CAAC,CAAC;sBACpBtG,cAAc,CAACsG,KAAK,CAAC,CAAC;sBACtBpO,QAAQ,CAAC,QAAQ,CAAC;sBAClByH,MAAM,CAACjH,QAAQ,CAAC6N,MAAM,CAAC,CAAC;oBACzB;oBACArP,OAAO,CAAC,CAAC;oBACTyI,MAAM,CAACjH,QAAQ,CAAC6N,MAAM,CAAC,CAAC;kBACzB,CAAC,CAAC,OAAOhR,KAAK,EAAE;oBACf;kBAAA;gBAEF,CAAC,MAAM;kBACN8C,YAAY,CAACiO,KAAK,CAAC,CAAC;kBACpBtG,cAAc,CAACsG,KAAK,CAAC,CAAC;kBACtB1Q,QAAQ,CAAC4Q,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC/I,OAAO,CAAE8I,MAAM,IAAK;oBAC9C,MAAM,CAACE,IAAI,CAAC,GAAGF,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC;oBAChC7Q,QAAQ,CAAC4Q,MAAM,GAAG,GAAGE,IAAI,mDAAmD;kBAC7E,CAAC,CAAC;kBACFxO,QAAQ,CAAC,mBAAmB,CAAC;kBAC7ByH,MAAM,CAACjH,QAAQ,CAAC6N,MAAM,CAAC,CAAC;gBACzB;cACD,CAAE;cACFlF,SAAS,EAAC,sBAAsB;cAChCjM,IAAI,EAAC,QAAQ;cAAAgM,QAAA,EAEZ3K,SAAS,CAAC,QAAQ;YAAC;cAAA+K,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,GACH,IAAI,eAERlN,OAAA;QACC4M,SAAS,EAAE,0BACT,CACC,IAAIlK,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEK,cAAc,WAAW,EAC1C,IAAIL,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEK,cAAc,sBAAsB,EACrD,IAAIL,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEK,cAAc,QAAQ,EACvC,IAAIL,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEK,cAAc,OAAO,EACtC,mBAAmB,EACnB,kBAAkB,EAClB,kBAAkB,EAClB,mBAAmB,EACnB,mBAAmB,EACnB,uBAAuB,EACvB,wBAAwB,EACxB,kBAAkB,EAClB,mBAAmB,EACnB,yBAAyB,EACzB,+BAA+B,EAC/B,oBAAoB,EACpB,cAAc,CACd,CAACkN,QAAQ,CAAChM,QAAQ,CAACmL,QAAQ,CAAC,GAC1B,EAAE,GAAG,kBAAkB;AACjC,KACKnL,QAAQ,CAACmL,QAAQ,KAAK,cAAc,GAAG,wBAAwB,GAAG,EAAE,EAChE;QAAAzC,QAAA,GAEH1I,QAAQ,CAACmL,QAAQ,KAAK,IAAI1M,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEK,cAAc,WAAW,IAAIH,QAAQ,KAAK,OAAO,gBACxF5C,OAAA;UAAK4M,SAAS,EAAC,qBAAqB;UAAAD,QAAA,eACnC3M,OAAA,CAACpD,GAAG;YAAA+P,QAAA,eACH3M,OAAA,CAACjC,WAAW;cAAA4O,QAAA,gBACX3M,OAAA;gBAAK4M,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,eACpC3M,OAAA,CAACpD,GAAG;kBAACyS,EAAE,EAAC,gBAAgB;kBAAA1C,QAAA,eACvB3M,OAAA,CAACtC,QAAQ;oBAAAqP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNlN,OAAA;gBAAK4M,SAAS,EAAC,eAAe;gBAAAD,QAAA,eAC7B3M,OAAA,CAACpD,GAAG;kBAAA+P,QAAA,eACH3M,OAAA,CAACtB,WAAW;oBAAAqO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,GAEH,IAAI,EAEPjJ,QAAQ,CAACmL,QAAQ,KAAK,IAAI1M,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEK,cAAc,sBAAsB,IAAIH,QAAQ,KAAK,OAAO,gBACnG5C,OAAA;UAAK4M,SAAS,EAAC,qBAAqB;UAAAD,QAAA,eACnC3M,OAAA,CAACpD,GAAG;YAAA+P,QAAA,eACH3M,OAAA,CAACjC,WAAW;cAAA4O,QAAA,gBACX3M,OAAA;gBAAK4M,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,eACpC3M,OAAA,CAACpD,GAAG;kBAACyS,EAAE,EAAC,gBAAgB;kBAAA1C,QAAA,eACvB3M,OAAA,CAACtC,QAAQ;oBAAAqP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNlN,OAAA;gBAAK4M,SAAS,EAAC,eAAe;gBAAAD,QAAA,eAC7B3M,OAAA,CAACpD,GAAG;kBAAA+P,QAAA,eACH3M,OAAA,CAACtB,WAAW;oBAAAqO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,GAEH,IAAI,EAGPjJ,QAAQ,CAACmL,QAAQ,KAAK,cAAc,gBACpCpP,OAAA,CAACjC,WAAW;UAAA4O,QAAA,eACX3M,OAAA;YAAKoN,KAAK,EAAE;cAAEmB,OAAO,EAAE,MAAM;cAAEwC,aAAa,EAAE,KAAK;cAAE1D,KAAK,EAAE;YAAO,CAAE;YAAAV,QAAA,eACpE3M,OAAA;cAAA2M,QAAA,eACC3M,OAAA,CAACvC,eAAe;gBAACiU,oBAAoB,EAAE1J,eAAe,CAACtE,UAAU;cAAE;gBAAAqJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,GACX,IAAI,EAEPjJ,QAAQ,CAACmL,QAAQ,KAAK,wBAAwB,gBAC9CpP,OAAA;UAAK4M,SAAS,EAAC,qBAAqB;UAAAD,QAAA,eACnC3M,OAAA,CAACpD,GAAG;YAAA+P,QAAA,eACH3M,OAAA,CAACjC,WAAW;cAAA4O,QAAA,gBACX3M,OAAA;gBAAK4M,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,eACpC3M,OAAA,CAACpD,GAAG;kBAACyS,EAAE,EAAC,gBAAgB;kBAAA1C,QAAA,eACvB3M,OAAA,CAACtC,QAAQ;oBAAAqP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNlN,OAAA;gBAAK4M,SAAS,EAAC,eAAe;gBAAAD,QAAA,eAC7B3M,OAAA,CAACpD,GAAG;kBAAA+P,QAAA,eACH3M,OAAA,CAACxC,gBAAgB;oBAAAmP,QAAA,eAChB3M,OAAA,CAAC1C,UAAU;sBAAAyP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,GAEH,IAAI,EAEPjJ,QAAQ,CAACmL,QAAQ,KAAK,IAAI1M,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEK,cAAc,OAAO,IAAIH,QAAQ,KAAK,OAAO,gBACpF5C,OAAA;UAAK4M,SAAS,EAAC,qBAAqB;UAAAD,QAAA,eACnC3M,OAAA,CAACpD,GAAG;YAAA+P,QAAA,eACH3M,OAAA,CAACjC,WAAW;cAAA4O,QAAA,gBACX3M,OAAA;gBAAK4M,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,eACpC3M,OAAA,CAACpD,GAAG;kBAACyS,EAAE,EAAC,gBAAgB;kBAAA1C,QAAA,eACvB3M,OAAA,CAACtC,QAAQ;oBAAAqP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNlN,OAAA;gBAAK4M,SAAS,EAAC,eAAe;gBAAAD,QAAA,eAC7B3M,OAAA,CAACpD,GAAG;kBAAA+P,QAAA,eACH3M,OAAA,CAACvB,QAAQ;oBAAAsO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,GAEH,IAAI,EAEPjJ,QAAQ,CAACmL,QAAQ,KAAK,IAAI1M,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEK,cAAc,QAAQ,IAAIH,QAAQ,KAAK,OAAO,gBACrF5C,OAAA;UAAK4M,SAAS,EAAC,qBAAqB;UAAAD,QAAA,eACnC3M,OAAA,CAACpD,GAAG;YAAA+P,QAAA,eACH3M,OAAA,CAACjC,WAAW;cAAA4O,QAAA,gBACX3M,OAAA;gBAAK4M,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,eACpC3M,OAAA,CAACpD,GAAG;kBAACyS,EAAE,EAAC,gBAAgB;kBAAA1C,QAAA,eACvB3M,OAAA,CAACtC,QAAQ;oBAAAqP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNlN,OAAA;gBAAK4M,SAAS,EAAC,eAAe;gBAAAD,QAAA,eAC7B3M,OAAA,CAACpD,GAAG;kBAAA+P,QAAA,eACH3M,OAAA,CAACzB,YAAY;oBAAAwO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,GAEH,IAAI,EAEPjJ,QAAQ,CAACmL,QAAQ,KAAK,mBAAmB,IAAIxM,QAAQ,KAAK,OAAO,gBAEjE5C,OAAA;UAAK4M,SAAS,EAAC,qBAAqB;UAAAD,QAAA,eACnC3M,OAAA,CAACpD,GAAG;YAAA+P,QAAA,eACH3M,OAAA,CAACjC,WAAW;cAAA4O,QAAA,gBACX3M,OAAA;gBAAK4M,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,eACpC3M,OAAA,CAACpD,GAAG;kBAACyS,EAAE,EAAC,gBAAgB;kBAAA1C,QAAA,eACvB3M,OAAA,CAACtC,QAAQ;oBAAAqP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNlN,OAAA;gBAAK4M,SAAS,EAAC,eAAe;gBAAAD,QAAA,eAC7B3M,OAAA,CAACpD,GAAG;kBAAA+P,QAAA,eACH3M,OAAA,CAAChB,cAAc;oBAAA+N,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,GAGH,IAAI,EACPjJ,QAAQ,CAACmL,QAAQ,KAAK,kBAAkB,gBAExCpP,OAAA;UAAK4M,SAAS,EAAC,qBAAqB;UAAAD,QAAA,eACnC3M,OAAA,CAACpD,GAAG;YAAA+P,QAAA,eACH3M,OAAA,CAACjC,WAAW;cAAA4O,QAAA,gBACX3M,OAAA;gBAAK4M,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,eACpC3M,OAAA,CAACpD,GAAG;kBAACyS,EAAE,EAAC,gBAAgB;kBAAA1C,QAAA,eACvB3M,OAAA,CAACtC,QAAQ;oBAAAqP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNlN,OAAA;gBAAK4M,SAAS,EAAC,eAAe;gBAAAD,QAAA,eAC7B3M,OAAA,CAACpD,GAAG;kBAAA+P,QAAA,eACH3M,OAAA,CAACxB,aAAa;oBAAAuO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,GACH,IAAI,EAEPjJ,QAAQ,CAACmL,QAAQ,KAAK,kBAAkB,gBACxCpP,OAAA;UAAK4M,SAAS,EAAC,qBAAqB;UAAAD,QAAA,eACnC3M,OAAA,CAACpD,GAAG;YAAA+P,QAAA,eACH3M,OAAA,CAACjC,WAAW;cAAA4O,QAAA,gBACX3M,OAAA;gBAAK4M,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,eACpC3M,OAAA,CAACpD,GAAG;kBAACyS,EAAE,EAAC,gBAAgB;kBAAA1C,QAAA,eACvB3M,OAAA,CAACtC,QAAQ;oBAAAqP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNlN,OAAA;gBAAK4M,SAAS,EAAC,eAAe;gBAAAD,QAAA,eAC7B3M,OAAA,CAACpD,GAAG;kBAAA+P,QAAA,eACH3M,OAAA,CAACf,aAAa;oBAAA8N,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,GACH,IAAI,EACPjJ,QAAQ,CAACmL,QAAQ,KAAK,mBAAmB,IAAIxM,QAAQ,KAAK,OAAO,gBACjE5C,OAAA;UAAK4M,SAAS,EAAC,qBAAqB;UAAAD,QAAA,eACnC3M,OAAA,CAACpD,GAAG;YAAA+P,QAAA,eACH3M,OAAA,CAACjC,WAAW;cAAA4O,QAAA,gBACX3M,OAAA;gBAAK4M,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,eACpC3M,OAAA,CAACpD,GAAG;kBAACyS,EAAE,EAAC,gBAAgB;kBAAA1C,QAAA,eACvB3M,OAAA,CAACtC,QAAQ;oBAAAqP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNlN,OAAA;gBAAK4M,SAAS,EAAC,eAAe;gBAAAD,QAAA,eAC7B3M,OAAA,CAACpD,GAAG;kBAAA+P,QAAA,eACH3M,OAAA,CAACd,eAAe;oBAAA6N,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,GACH,IAAI,EACPjJ,QAAQ,CAACmL,QAAQ,KAAK,mBAAmB,gBACzCpP,OAAA;UAAK4M,SAAS,EAAC,qBAAqB;UAAAD,QAAA,eACnC3M,OAAA,CAACpD,GAAG;YAAA+P,QAAA,eACH3M,OAAA,CAACjC,WAAW;cAAA4O,QAAA,gBACX3M,OAAA;gBAAK4M,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,eACpC3M,OAAA,CAACpD,GAAG;kBAACyS,EAAE,EAAC,gBAAgB;kBAAA1C,QAAA,eACvB3M,OAAA,CAACtC,QAAQ;oBAAAqP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNlN,OAAA;gBAAK4M,SAAS,EAAC,eAAe;gBAAAD,QAAA,eAC7B3M,OAAA,CAACpD,GAAG;kBAAA+P,QAAA,eACH3M,OAAA,CAACb,WAAW;oBAAA4N,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,GAEH,IAAI,EACPjJ,QAAQ,CAACmL,QAAQ,KAAK,uBAAuB,gBAC7CpP,OAAA;UAAK4M,SAAS,EAAC,qBAAqB;UAAAD,QAAA,eACnC3M,OAAA,CAACpD,GAAG;YAAA+P,QAAA,eACH3M,OAAA,CAACjC,WAAW;cAAA4O,QAAA,gBACX3M,OAAA;gBAAK4M,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,eACpC3M,OAAA,CAACpD,GAAG;kBAACyS,EAAE,EAAC,gBAAgB;kBAAA1C,QAAA,eACvB3M,OAAA,CAACtC,QAAQ;oBAAAqP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNlN,OAAA;gBAAK4M,SAAS,EAAC,eAAe;gBAAAD,QAAA,eAC7B3M,OAAA,CAACpD,GAAG;kBAAA+P,QAAA,eACH3M,OAAA,CAACX,YAAY;oBAAA0N,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,GAEH,IAAI,EACPjJ,QAAQ,CAACmL,QAAQ,KAAK,kBAAkB,gBACxCpP,OAAA;UAAK4M,SAAS,EAAC,qBAAqB;UAAAD,QAAA,eACnC3M,OAAA,CAACpD,GAAG;YAAA+P,QAAA,eACH3M,OAAA,CAACjC,WAAW;cAAA4O,QAAA,gBACX3M,OAAA;gBAAK4M,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,eACpC3M,OAAA,CAACpD,GAAG;kBAACyS,EAAE,EAAC,gBAAgB;kBAAA1C,QAAA,eACvB3M,OAAA,CAACtC,QAAQ;oBAAAqP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNlN,OAAA;gBAAK4M,SAAS,EAAC,eAAe;gBAAAD,QAAA,eAC7B3M,OAAA,CAACpD,GAAG;kBAAA+P,QAAA,eACH3M,OAAA,CAACrB,UAAU;oBAAAoO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,GAEH,IAAI,EAEPjJ,QAAQ,CAACmL,QAAQ,KAAK,mBAAmB,gBACzCpP,OAAA;UAAK4M,SAAS,EAAC,qBAAqB;UAAAD,QAAA,eACnC3M,OAAA,CAACpD,GAAG;YAAA+P,QAAA,eACH3M,OAAA,CAACjC,WAAW;cAAA4O,QAAA,gBACX3M,OAAA;gBAAK4M,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,eACpC3M,OAAA,CAACpD,GAAG;kBAACyS,EAAE,EAAC,gBAAgB;kBAAA1C,QAAA,eACvB3M,OAAA,CAACtC,QAAQ;oBAAAqP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNlN,OAAA;gBAAK4M,SAAS,EAAC,eAAe;gBAAAD,QAAA,eAC7B3M,OAAA,CAACpD,GAAG;kBAAA+P,QAAA,eACH3M,OAAA,CAACpB,OAAO;oBAAAmO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,GAEH,IAAI,EAEPjJ,QAAQ,CAACmL,QAAQ,KAAK,yBAAyB,gBAC/CpP,OAAA;UAAK4M,SAAS,EAAC,qBAAqB;UAAAD,QAAA,eACnC3M,OAAA,CAACpD,GAAG;YAAA+P,QAAA,eACH3M,OAAA,CAACjC,WAAW;cAAA4O,QAAA,gBACX3M,OAAA;gBAAK4M,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,eACpC3M,OAAA,CAACpD,GAAG;kBAACyS,EAAE,EAAC,gBAAgB;kBAAA1C,QAAA,eACvB3M,OAAA,CAACtC,QAAQ;oBAAAqP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNlN,OAAA;gBAAK4M,SAAS,EAAC,eAAe;gBAAAD,QAAA,eAC7B3M,OAAA,CAACpD,GAAG;kBAAA+P,QAAA,eACH3M,OAAA,CAACnB,aAAa;oBAAAkO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,GAEH,IAAI,EAEPjJ,QAAQ,CAACmL,QAAQ,KAAK,+BAA+B,gBACrDpP,OAAA;UAAK4M,SAAS,EAAC,qBAAqB;UAAAD,QAAA,eACnC3M,OAAA,CAACpD,GAAG;YAAA+P,QAAA,eACH3M,OAAA,CAACjC,WAAW;cAAA4O,QAAA,gBACX3M,OAAA;gBAAK4M,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,eACpC3M,OAAA,CAACpD,GAAG;kBAACyS,EAAE,EAAC,gBAAgB;kBAAA1C,QAAA,eACvB3M,OAAA,CAACtC,QAAQ;oBAAAqP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNlN,OAAA;gBAAK4M,SAAS,EAAC,eAAe;gBAAAD,QAAA,eAC7B3M,OAAA,CAACpD,GAAG;kBAAA+P,QAAA,eACH3M,OAAA,CAAClB,mBAAmB;oBAAAiO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,GAEH,IAAI,EAEPjJ,QAAQ,CAACmL,QAAQ,KAAK,oBAAoB,gBAC1CpP,OAAA;UAAK4M,SAAS,EAAC,qBAAqB;UAAAD,QAAA,eACnC3M,OAAA,CAACpD,GAAG;YAAA+P,QAAA,eACH3M,OAAA,CAACjC,WAAW;cAAA4O,QAAA,gBACX3M,OAAA;gBAAK4M,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,eACpC3M,OAAA,CAACpD,GAAG;kBAACyS,EAAE,EAAC,gBAAgB;kBAAA1C,QAAA,eACvB3M,OAAA,CAACtC,QAAQ;oBAAAqP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNlN,OAAA;gBAAK4M,SAAS,EAAC,eAAe;gBAAAD,QAAA,eAC7B3M,OAAA,CAACpD,GAAG;kBAAA+P,QAAA,eACH3M,OAAA,CAACjB,QAAQ;oBAAAgO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,GAEH,IAAI;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAER,CAAC;AAACtL,EAAA,CA7vCID,YAAsB;EAAA,QACFpC,cAAc,EAMKH,OAAO,EAOlC7C,WAAW,EAKXgB,WAAW,EAYFuC,MAAM;AAAA;AAAAoS,EAAA,GA/B3BvQ,YAAsB;AA+vC5B,eAAeA,YAAY;AAE3B,SAASxB,SAAS;AAAE,IAAA+R,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}