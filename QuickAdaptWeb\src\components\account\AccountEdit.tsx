import React, { useState, useEffect, ChangeEvent } from "react";
import { getAllOrganizations } from "../../services/OrganizationService";
import { SubmitAccountDetails, fetchAccountsById } from "../../services/AccountService";
import { 
	TextField, 
	Button, 
  Radio,
  RadioGroup,
  FormControlLabel,
  Grid,
  FormControl,
} from "@mui/material";
import { useSnackbar } from "../../SnackbarContext";
import { useAuth } from "../auth/AuthProvider";
import { User } from "../../models/User";
import { useTranslation } from "react-i18next";

type InputFields = {
	AccountName: string;
	DomainUrl: string;
};
type ErrorFields = Partial<InputFields>;

const EditAccount = (props: any) => {
	const {
		showEditPopup,
		setShowEditPopup,
		accountidedit,
		setModels,
		setLoading,
		setTotalcount,
		orderByField,
		filters,
	} = props;
	const [organizations, setOrganizations] = useState<any[]>([]);
	const [snackbarOpen, setSnackbarOpen] = useState(false);
	const [snackbarMessage, setSnackbarMessage] = useState("");
	const [snackbarSeverity, setSnackbarSeverity] = useState<"success" | "error">("success");
	const [skip] = useState("0");
	const [top] = useState("30");
	const { openSnackbar } = useSnackbar();
	const { userDetails } = useAuth();
	const [OrganizationId, setOrganizationId] = useState(userDetails?.OrganizationId ?? "");
	const [, setUser] = useState<User | null>(null);
	const [isValid, setIsValid] = useState(true);
	const [hasChanges, setHasChanges] = useState(false);
	//const [totalcount, setTotalcount] = useState(0);
	// const [paginationModel, setPaginationModel] = useState<GridPaginationModel>({
	// 	page: 0,
	// 	pageSize: 10,
	// });
	const [errors, setErrors] = useState<ErrorFields>({
		AccountName: "",
		DomainUrl: ""
	});

	const [originalAccountDetails, setOriginalAccountDetails] = useState({
		AccountId: "",
		AccountName: "",
		AccountType: "",
		CreatedBy: "",
		CreatedDate: "",
		OrganizationId: "",
		UpdatedBy: userDetails?.UserName,
		UpdatedDate: "",
		Active: Boolean(true),
		DomainUrl: "",
		Rtl: false,
		IsAIEnabled: false
	});

	const [AccountDetails, setAccountDetails] = useState({
		AccountId: "",
		AccountName: "",
		AccountType: "",
		CreatedBy: "",
		CreatedDate: "",
		OrganizationId: "",
		UpdatedBy: userDetails?.UserName,
		UpdatedDate: "",
		Active: Boolean(true),
		DomainUrl: "",
		IsAIEnabled: false,
		Rtl: false
	});

	const { t: translate } = useTranslation();


	useEffect(() => {
		if (showEditPopup) {
			fetchAccountDetails(accountidedit);
		}
	}, [showEditPopup]);
	// Validate inputs whenever AccountDetails change
	useEffect(() => {
		const newErrors: ErrorFields = {};

		// Account Name Validation
		if (AccountDetails.AccountName) {
			const trimmedAccountName = AccountDetails.AccountName.trim();
			if (!trimmedAccountName) {
				newErrors.AccountName = translate("Account Name cannot be only spaces.");
			} else if (trimmedAccountName.length < 3) {
				newErrors.AccountName = translate("Account Name must be at least 3 characters.");
			} else if (trimmedAccountName.length > 50) {
				newErrors.AccountName = translate("Account Name cannot exceed 50 characters.");
			} else if (/[^a-zA-Z\s]/g.test(trimmedAccountName)) {
				newErrors.AccountName = translate("Account Name can only contain letters and spaces.");
			} else {
				newErrors.AccountName = "";
			}
		}

		// Domain URL Validation
		if (AccountDetails.DomainUrl) {
			const trimmedDomainUrl = AccountDetails.DomainUrl.trim();
			const domainPattern = /^(https?:\/\/)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/)?$/;
			if (!domainPattern.test(trimmedDomainUrl)) {
				newErrors.DomainUrl = translate("Please enter a valid full domain URL (e.g., https://example.com).");
			} else {
				newErrors.DomainUrl = "";
			}
		}

		setErrors(prev => ({ ...prev, ...newErrors }));
	}, [AccountDetails]);

	useEffect(() => {
		const userInfoString = localStorage.getItem("userInfo");	
		if (userInfoString) { 
			try {
				const userInfo = JSON.parse(userInfoString);	
				if (userInfo['user']) {
					const parsedUser = JSON.parse(userInfo['user']);
					setUser(parsedUser);	
					if (parsedUser) {
						const OrgId = parsedUser.OrganizationId ?? '';
						setOrganizationId(OrgId);
					}
				}
			} catch (error) {
				console.error("Error parsing userInfo: ", error);
			}
		}
		else if (userDetails) {
			setUser(userDetails);	
			if (userDetails) {
				const OrgId = userDetails.OrganizationId ?? '';
				setOrganizationId(OrgId);
			}
		}
	}, []);


	// Function to check if current account details differ from original data
	const checkForChanges = (currentDetails: any) => {
		const fieldsToCompare = ['AccountName', 'DomainUrl', 'IsAIEnabled', 'Rtl'] as const;
		return fieldsToCompare.some(field => currentDetails[field] !== originalAccountDetails[field]);
	};

	const fetchAccountDetails = async (id: any) => {
		try {
			const responseData = await fetchAccountsById(id);

			if (!responseData) {
				throw new Error("Network response was not ok");
			}

			const accountData = {
				AccountId: responseData.AccountId,
				AccountName: responseData.AccountName,
				AccountType: responseData.AccountType,
				CreatedBy: responseData.CreatedBy,
				CreatedDate: responseData.CreatedDate,
				OrganizationId: responseData.OrganizationId,
				UpdatedBy: userDetails?.UserName,
				UpdatedDate: new Date().toUTCString(),
				Active: true,
				DomainUrl: responseData.DomainUrl,
				IsAIEnabled: responseData.IsAIEnabled,
				Rtl: responseData.Rtl || false
			};

			// Set both original and current data
			setOriginalAccountDetails(accountData);
			setAccountDetails(accountData);

			// Reset change tracking
			setHasChanges(false);
		} catch (error) {
			console.error("Failed to fetch user details:", error);
		}
	};
	const handleOrganizationDropdownOpen = async () => {
		try {
			const response = await getAllOrganizations(setOrganizations, setLoading);
		} catch (error) {
			console.error("Error fetching organizations:", error);
		}
	};
	const alphanumericRegex = /^[a-zA-Z0-9]*$/;
	const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
		const { name, value } = event.target;
		let error = "";
		let processedValue = value;

		if (name === "AccountType") {
			const selectedOrganization = organizations.find((org) => org.Name === value);
			if (selectedOrganization) {
				const newAccountDetails = {
					...AccountDetails,
					AccountType: value,
					OrganizationId: OrganizationId
				};
				setAccountDetails(newAccountDetails);
				setHasChanges(checkForChanges(newAccountDetails));
				return;
			}
		} else if (name === "AccountName") {
			// Allow only letters and spaces, remove special characters and numbers
			processedValue = value.replace(/[^a-zA-Z\s]/g, "");

			// Validate trimmed value but keep original spacing for user experience
			const trimmedValue = processedValue.trim();
			if (trimmedValue.length < 3) {
				error = translate("Account Name must be at least 3 characters.");
			} else if (trimmedValue.length > 50) {
				error = translate("Account Name cannot exceed 50 characters.");
			} else if (trimmedValue === "") {
				error = translate("Account Name cannot be only spaces.");
			}
		} else if (name === "DomainUrl") {
			// Trim spaces for domain URL
			processedValue = value.trim();
			const domainPattern = /^(https?:\/\/)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/)?$/;
			if (processedValue && !domainPattern.test(processedValue)) {
				error = translate("Please enter a valid full domain URL (e.g., https://example.com).");
			}
		}

		const newAccountDetails = { ...AccountDetails, [name]: processedValue };
		setAccountDetails(newAccountDetails);
		setErrors((prev) => ({ ...prev, [name]: error }));

		// Check for changes after updating account details
		setHasChanges(checkForChanges(newAccountDetails));
	};

	const handleSwitchChange = (event: ChangeEvent<HTMLInputElement>) => {
		const { name, checked } = event.target;
		const newAccountDetails = { ...AccountDetails, [name]: checked };
		setAccountDetails(newAccountDetails);
		setHasChanges(checkForChanges(newAccountDetails));
	};

	// const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
	// 	const { name, value } = event.target;
	// 	const alphanumericWithSpacesRegex = /^[a-zA-Z0-9\s]*$/; // Allow letters, numbers, and spaces
	// 	let error = "";
	// 	let processedValue = value;

	// 	if (name === "AccountType") {
	// 		const selectedOrganization = organizations.find((org) => org.Name === value);
	// 		if (selectedOrganization) {
	// 			setAccountDetails((values) => ({
	// 				...values,
	// 				AccountType: value,
	// 				OrganizationId: "********-*********-134dc53c-f123-4655-aa39-0529fa976863", // Placeholder ID
	// 			}));
	// 		}
	// 	} else if (name === "AccountName") {
	// 		// Remove special characters
	// 		processedValue = value.replace(/[^a-zA-Z0-9\s]/g, "");
	// 	}

	// 	setAccountDetails((prev) => ({ ...prev, [name]: processedValue }));

	// 	if (name === "AccountName" && processedValue.length < 5) {
	// 		error = "Account Name must be at least 5 characters.";
	// 	} else {
	// 		setAccountDetails((values) => ({ ...values, [name]: value }));
	// 	}
	// 	setErrors((prev) => ({ ...prev, [name]: error }));
	// };

	const handleSubmit = async (e: any) => {
		e.preventDefault();

		// Trim leading and trailing spaces from AccountName and DomainUrl before validation and submission
		const trimmedAccountName = AccountDetails.AccountName.trim();
		const trimmedDomainUrl = AccountDetails.DomainUrl.trim();
		const updatedAccountDetails = {
			...AccountDetails,
			AccountName: trimmedAccountName,
			DomainUrl: trimmedDomainUrl
		};

		const newErrors: ErrorFields = {};
		let isValid = true;

		// Validate AccountName
		if (!trimmedAccountName) {
			newErrors.AccountName = translate("Account Name is required.");
			isValid = false;
		} else if (trimmedAccountName.length < 3) {
			newErrors.AccountName = translate("Account Name must be at least 3 characters.");
			isValid = false;
		} else if (trimmedAccountName.length > 50) {
			newErrors.AccountName = translate("Account Name cannot exceed 50 characters.");
			isValid = false;
		} else if (/[^a-zA-Z\s]/g.test(trimmedAccountName)) {
			newErrors.AccountName = translate("Account Name can only contain letters and spaces.");
			isValid = false;
		}

		// Validate DomainUrl
		if (!trimmedDomainUrl) {
			newErrors.DomainUrl = translate("Domain Url is required.");
			isValid = false;
		} else {
			const domainPattern = /^(https?:\/\/)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/)?$/;
			if (!domainPattern.test(trimmedDomainUrl)) {
				newErrors.DomainUrl = translate("Please enter a valid full domain URL (e.g., https://example.com).");
				isValid = false;
			}
		}

		setErrors(newErrors);

		if (!isValid) {
			// Show first error in snackbar
			const firstError = newErrors.AccountName || newErrors.DomainUrl;
			if (firstError) {
				setSnackbarMessage(firstError);
				setSnackbarSeverity("error");
				setSnackbarOpen(true);
			}
			return;
		}

		setLoading(true);
		try {
			await SubmitAccountDetails(
				setLoading,
				setModels,
				setShowEditPopup,
				updatedAccountDetails,
				OrganizationId,
				skip,
				top,
				setTotalcount,
				openSnackbar,
				orderByField,
				filters
			);
		} catch (error) {
			// Handle error
		}
	};
	const handleSnackbarClose = () => {
		setSnackbarOpen(false);
	};

	return (
		showEditPopup && (
			<div className="qadpt-modal-overlay">
  <div className="qadpt-accountcreatepopup">
    <div className="qadpt-title-sec">
      <div className="qadpt-title">{translate('Edit Account')}</div>
      <svg
        onClick={() => setShowEditPopup(false)}
        className="qadpt-closeicon"
        xmlns="http://www.w3.org/2000/svg"
        x="0px"
        y="0px"
        width="24"
        height="24"
        viewBox="0 0 50 50"
      >
        <path d="M 7.71875 6.28125 L 6.28125 7.71875 L 23.5625 25 L 6.28125 42.28125 L 7.71875 43.71875 L 25 26.4375 L 42.28125 43.71875 L 43.71875 42.28125 L 26.4375 25 L 43.71875 7.71875 L 42.28125 6.28125 L 25 23.5625 Z"></path>
      </svg>
    </div>

    <form onSubmit={handleSubmit}>
      <div className="qadpt-accountcreatefield">
        <Grid container>
          <Grid item>
            <FormControl fullWidth required>
              <label htmlFor="edit-account-name">{translate("Account Name")}</label>
              <TextField
                id="edit-account-name"
                name="AccountName"
                value={AccountDetails.AccountName}
                onChange={handleChange}
                placeholder={translate("Enter Account Name")}
                helperText={errors.AccountName && translate(errors.AccountName)}
											variant="outlined"
											inputProps={{ maxLength: 60 }}
											error={!!errors.AccountName}
                className={`qadpt-acctfield ${errors.AccountName ? 'qadpt-error' : ''}`}
              />
            </FormControl>

            <FormControl fullWidth required>
              <label htmlFor="edit-account-domain">{translate("Domain Url")}</label>
              <TextField
                id="edit-account-domain"
                name="DomainUrl"
                value={AccountDetails.DomainUrl}
                onChange={handleChange}
                placeholder={translate("Enter Domain Url")}
                helperText={errors.DomainUrl && translate(errors.DomainUrl)}
                variant="outlined"
                error={!!errors.DomainUrl}
                inputProps={{ maxLength: 50 }}
                className={`qadpt-acctfield ${errors.DomainUrl ? 'qadpt-error' : ''}`}
              />
            </FormControl>

            <div className="qadpt-txtfld">
              <div style={{ display: "flex", justifyContent: "space-between", margin: "10px 0" }}>
                <span style={{ marginLeft: '10px' }}>
                  {translate("RTL")}
                </span>
                <label className="toggle-switch">
                  <input
                    type="checkbox"
                    checked={AccountDetails.Rtl}
                    onChange={(e) => {
                      const newValue = e.target.checked;
                      setAccountDetails(prev => ({
                        ...prev,
                        Rtl: newValue
                      }));
                      setHasChanges(checkForChanges({ ...AccountDetails, Rtl: newValue }));
                    }}
                  />
                  <span className="slider"></span>
                </label>
              </div>
            </div>

            <div className="qadpt-txtfld">
              <div style={{ display: "flex", justifyContent: "space-between", margin: "10px 0" }}>
                <span style={{ marginLeft: '10px' }}>
                  {translate("Dona")}
                </span>
                <label className="toggle-switch">
                  <input
                    type="checkbox"
                    checked={AccountDetails.IsAIEnabled}
                    onChange={(e) => {
                    const newAccountDetails = {
                        ...AccountDetails,
                        IsAIEnabled: e.target.checked
                    };
                    setAccountDetails(newAccountDetails);
                    setHasChanges(checkForChanges(newAccountDetails));
                    }}
                  />
                  <span className="slider"></span>
                </label>
              </div>
            </div>

          </Grid>
        </Grid>
      </div>

      <div className="qadpt-account-buttons">
        <Button
          className="qadpt-save-btn"
          type="submit"
          disabled={!hasChanges || Object.values(errors).some(error => error !== "")}
          sx={{
            '&.Mui-disabled': {
              opacity: 0.5
            }
          }}
        >
          {translate("Save")}
        </Button>
      </div>
    </form>
  </div>
</div>
		)
	);
};

export default EditAccount;
