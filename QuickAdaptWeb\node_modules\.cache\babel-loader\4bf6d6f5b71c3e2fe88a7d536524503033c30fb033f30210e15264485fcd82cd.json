{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptWeb\\\\src\\\\components\\\\login\\\\login.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Box, Typography, TextField, Button, Link, IconButton, InputAdornment } from '@mui/material';\nimport { useAuth } from '../auth/AuthProvider';\nimport { LoginService } from \"../../services/LoginService\";\nimport { GetUserDetails } from '../../services/UserService';\nimport { JSEncrypt } from 'jsencrypt';\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport jwt_decode from \"jwt-decode\";\nimport { FormHelperText } from '@mui/material';\nimport { getOrganizationById } from '../../services/OrganizationService';\nimport { QuickAdopttext } from \"../../assets/icons/icons\";\nimport { checkSessionExpired } from '../../services/APIService';\nimport { Alert, CircularProgress } from '@mui/material';\n//import { useAuth } from '../auth/AuthProvider';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nlet userLocalData = {};\nlet SAinitialsData;\nlet userDetails;\nconst Login = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  let UserId;\n  let OrganizationId;\n  const location = useLocation();\n  const [showSessionExpiredAlert, setShowSessionExpiredAlert] = useState(false);\n  const [isAutoLoggingIn, setIsAutoLoggingIn] = useState(() => {\n    // Check if we have auto-login parameters immediately\n    const urlParams = new URLSearchParams(location.search);\n    const autoLogin = urlParams.get('auto_login');\n    const token = urlParams.get('token');\n    const userId = urlParams.get('user_id');\n    const orgId = urlParams.get('org_id');\n\n    // If we have auto-login parameters, start in loading state\n    return Boolean(autoLogin === 'true' && token && userId && orgId);\n  });\n\n  // Check if auto-login was already completed in this session\n  const [autoLoginCompleted, setAutoLoginCompleted] = useState(() => {\n    return sessionStorage.getItem('autoLoginCompleted') === 'true';\n  });\n\n  // Utility function to safely clear localStorage only when not in auto-login flow\n  const safeLocalStorageClear = () => {\n    const urlParams = new URLSearchParams(location.search);\n    const hasAutoLoginParams = urlParams.has('token') && urlParams.has('userId') && urlParams.has('orgId');\n    const isAutoLoginCompleted = sessionStorage.getItem('autoLoginCompleted') === 'true';\n    if (!hasAutoLoginParams && !isAutoLoginCompleted && !isAutoLoggingIn) {\n      localStorage.clear();\n    }\n  };\n  useEffect(() => {\n    // Check if user was redirected due to session expiration\n    const sessionExpired = checkSessionExpired();\n    if (sessionExpired) {\n      setShowSessionExpiredAlert(true);\n\n      // Auto-hide the alert after 5 seconds\n      const timer = setTimeout(() => {\n        setShowSessionExpiredAlert(false);\n      }, 5000);\n      return () => clearTimeout(timer);\n    }\n  }, []);\n  const [showPassword, setShowPassword] = useState(false);\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [users, setUser] = useState(null);\n  const [error, setError] = useState(null);\n  const [loginUserInfo, setLoginUserInfo] = useState(undefined);\n  const [response, setresponse] = useState('');\n  const [userIds, setuserId] = useState(\"\");\n  const [organizationDetails, setOrganizationDetails] = useState(null);\n  const [loginUserDetails, setUserDetails] = useState(null);\n  const handleClickShowPassword = () => {\n    setShowPassword(!showPassword);\n  };\n  const {\n    signOut,\n    loggedOut\n  } = useAuth();\n  const handleEmailChange = event => {\n    setEmail(event.target.value);\n  };\n  const handlePasswordChange = event => {\n    setPassword(event.target.value);\n  };\n  const navigate = useNavigate();\n  useEffect(() => {\n    const hasSessionExpired = checkSessionExpired();\n    const token = localStorage.getItem(\"access_token\");\n\n    // Check if we're in auto-login flow\n    const urlParams = new URLSearchParams(location.search);\n    const hasAutoLoginParams = urlParams.has('token') && urlParams.has('user_id') && urlParams.has('org_id');\n\n    // Don't redirect if we're in auto-login flow and it hasn't completed yet\n    if (hasAutoLoginParams && !autoLoginCompleted) {\n      return;\n    }\n    if (user && token && !hasSessionExpired) {\n      navigate(\"/\", {\n        replace: true\n      });\n    }\n  }, [user, autoLoginCompleted, location.search]);\n\n  // Handle automatic login from free trial success\n  useEffect(() => {\n    const handleAutoLogin = async () => {\n      const urlParams = new URLSearchParams(location.search);\n      const autoLogin = urlParams.get('auto_login');\n      const token = urlParams.get('token');\n      const email = urlParams.get('email');\n      const userId = urlParams.get('user_id');\n      const orgId = urlParams.get('org_id');\n      const orgName = urlParams.get('org_name');\n      if (autoLogin === 'true' && token && email && userId && orgId) {\n        setIsAutoLoggingIn(true);\n        try {\n          // Create user object for localStorage - must match User interface\n          const userData = {\n            UserId: userId,\n            EmailId: email,\n            OrganizationId: orgId,\n            FirstName: email.split('@')[0],\n            // Extract first name from email\n            LastName: '',\n            UserType: 'Admin',\n            UserName: email,\n            Password: '',\n            ContactNumber: '',\n            Gender: '',\n            DateofBirth: '',\n            AdminDeactivated: false,\n            EmailConfirmed: true,\n            LoginType: 'Admin',\n            ProfilePhoto: '',\n            RTL: false,\n            TimeZone: ''\n          };\n\n          // Create OIDC info object\n          const oidcInfo = {\n            access_token: token,\n            token_type: 'Bearer',\n            expires_in: 86400,\n            // 24 hours\n            scope: 'openid profile api1'\n          };\n\n          // Store authentication data\n          localStorage.setItem(\"access_token\", token);\n          localStorage.setItem(\"userType\", \"Admin\");\n\n          // Check if this is a free trial token (non-JWT format)\n          let isFreeTrialToken = false;\n          try {\n            // Try to decode as JWT\n            jwt_decode(token);\n          } catch (e) {\n            // Not a JWT token, likely a free trial token\n            isFreeTrialToken = true;\n          }\n\n          // Store free trial token flag for API service to use\n          if (isFreeTrialToken) {\n            sessionStorage.setItem('isFreeTrialToken', 'true');\n          }\n          const userLocalData = {};\n          userLocalData[\"oidc-info\"] = JSON.stringify(oidcInfo);\n          userLocalData[\"user\"] = JSON.stringify(userData);\n\n          // For auto-login, skip the organization details API call to avoid 401 errors\n          // The organization details can be fetched later after the user is fully logged in\n          userLocalData[\"orgDetails\"] = JSON.stringify({\n            OrganizationId: orgId,\n            OrganizationName: orgName || 'Organization'\n          });\n          localStorage.setItem(\"userInfo\", JSON.stringify(userLocalData));\n\n          // Set the global userDetails variable\n          userDetails = userData;\n\n          // Mark auto-login as completed and persist in sessionStorage\n          setAutoLoginCompleted(true);\n          sessionStorage.setItem('autoLoginCompleted', 'true');\n          setIsAutoLoggingIn(false);\n\n          // Clear URL parameters\n          const newUrl = window.location.pathname;\n          window.history.replaceState({}, document.title, newUrl);\n\n          // Add a small delay to ensure all localStorage operations are completed\n          // before navigation to prevent race conditions\n          setTimeout(() => {\n            // Redirect to the accounts page with free-trial suffix for free trial users\n            navigate(`/${orgId}/accounts/free-trial`, {\n              replace: true\n            });\n          }, 100);\n        } catch (error) {\n          console.error('Auto login failed:', error);\n          setError('Automatic login failed. Please try logging in manually.');\n          setIsAutoLoggingIn(false);\n          setAutoLoginCompleted(true);\n        }\n      } else {\n        // No auto-login parameters, mark as completed and persist\n        setAutoLoginCompleted(true);\n        sessionStorage.setItem('autoLoginCompleted', 'true');\n      }\n    };\n    handleAutoLogin();\n  }, [location.search, navigate]);\n\n  // Cleanup sessionStorage on component unmount\n  useEffect(() => {\n    return () => {\n      // Only clear if we're actually leaving the login page (not just re-rendering)\n      if (!location.pathname.includes('/login')) {\n        sessionStorage.removeItem('autoLoginCompleted');\n      }\n    };\n  }, [location.pathname]);\n  const handleSubmit = async () => {\n    try {\n      const organizationId = \"1\";\n      const rememberLogin = true;\n      const returnUrl = \"\";\n      const authType = \"admin\";\n      const tenantId = \"web\";\n      if (password === '' || password == null) {\n        setError('password should not be empty');\n      } else if (email === '' || email == null) {\n        setError('email should not be empty');\n      } else {\n        const isEncryptionEnabled = process.env.REACT_APP_ENABLE_ENCRYPTION === 'true';\n        const publicKey = process.env.REACT_APP_PUBLIC_ENCRYPT_KEY || '';\n        const encryptor = new JSEncrypt();\n        encryptor.setPublicKey(publicKey);\n        const now = new Date().toISOString();\n        const encryptedPassword = encryptor.encrypt(password + '|' + now.trim()).toString();\n        if (!encryptedPassword) {\n          console.error(\"Encryption failed\");\n          return;\n        }\n        const response = await LoginService(email, isEncryptionEnabled ? encryptedPassword : password, organizationId, rememberLogin, returnUrl, authType, tenantId);\n        if (response.access_token) {\n          var _userResponse$data$Us, _userResponse$data, _userResponse$data$Or, _userResponse$data2;\n          setresponse(response);\n          userLocalData[\"oidc-info\"] = JSON.stringify(response);\n          localStorage.setItem(\"access_token\", response.access_token);\n          const userResponse = await GetUserDetails();\n          setUserDetails(userResponse ? userResponse.data : null);\n          const firstNameInitials = userResponse !== null && userResponse !== void 0 && userResponse.data.FirstName && userResponse !== null && userResponse !== void 0 && userResponse.data.FirstName ? userResponse === null || userResponse === void 0 ? void 0 : userResponse.data.FirstName.substring(0, 1).toUpperCase() : '';\n          const lastNameinitials = userResponse !== null && userResponse !== void 0 && userResponse.data && userResponse !== null && userResponse !== void 0 && userResponse.data.LastName ? userResponse === null || userResponse === void 0 ? void 0 : userResponse.data.LastName.substring(0, 1).toUpperCase() : '';\n          const finalData = firstNameInitials + lastNameinitials;\n          SAinitialsData = finalData;\n          localStorage.setItem(\"userType\", (_userResponse$data$Us = userResponse === null || userResponse === void 0 ? void 0 : (_userResponse$data = userResponse.data) === null || _userResponse$data === void 0 ? void 0 : _userResponse$data.UserType) !== null && _userResponse$data$Us !== void 0 ? _userResponse$data$Us : \"\");\n          userLocalData[\"user\"] = JSON.stringify(userResponse === null || userResponse === void 0 ? void 0 : userResponse.data);\n          const orgDetails = await getOrganizationById((_userResponse$data$Or = userResponse === null || userResponse === void 0 ? void 0 : (_userResponse$data2 = userResponse.data) === null || _userResponse$data2 === void 0 ? void 0 : _userResponse$data2.OrganizationId) !== null && _userResponse$data$Or !== void 0 ? _userResponse$data$Or : \"\");\n          userLocalData[\"orgDetails\"] = JSON.stringify(orgDetails);\n          localStorage.setItem(\"userInfo\", JSON.stringify(userLocalData));\n          navigate(\"/\");\n        } else {\n          setError(response.error_description);\n        }\n      }\n    } catch (error) {\n      console.error('Login failed:');\n      setError('An unexpected error occurred.'); // Handle unexpected errors\n    }\n  };\n  useEffect(() => {\n    var _userDetails, _userDetails2, _userDetails3, _userDetails4, _userDetails5, _userDetails6;\n    const firstNameInitials = (_userDetails = userDetails) !== null && _userDetails !== void 0 && _userDetails.FirstName && (_userDetails2 = userDetails) !== null && _userDetails2 !== void 0 && _userDetails2.FirstName ? (_userDetails3 = userDetails) === null || _userDetails3 === void 0 ? void 0 : _userDetails3.FirstName.substring(0, 1).toUpperCase() : '';\n    const lastNameinitials = (_userDetails4 = userDetails) !== null && _userDetails4 !== void 0 && _userDetails4.LastName && (_userDetails5 = userDetails) !== null && _userDetails5 !== void 0 && _userDetails5.LastName ? (_userDetails6 = userDetails) === null || _userDetails6 === void 0 ? void 0 : _userDetails6.LastName.substring(0, 1).toUpperCase() : '';\n    const finalData = firstNameInitials + lastNameinitials;\n    SAinitialsData = finalData;\n  }, [userDetails]);\n  async function GetLoginUserInfo(userResponse) {\n    try {\n      var _userResponse$UserTyp;\n      const firstNameInitials = userResponse !== null && userResponse !== void 0 && userResponse.FirstName && userResponse !== null && userResponse !== void 0 && userResponse.FirstName ? userResponse === null || userResponse === void 0 ? void 0 : userResponse.FirstName.substring(0, 1).toUpperCase() : '';\n      const lastNameinitials = userResponse && userResponse !== null && userResponse !== void 0 && userResponse.LastName ? userResponse === null || userResponse === void 0 ? void 0 : userResponse.LastName.substring(0, 1).toUpperCase() : '';\n      const finalData = firstNameInitials + lastNameinitials;\n      SAinitialsData = finalData;\n      localStorage.setItem(\"userType\", (_userResponse$UserTyp = userResponse === null || userResponse === void 0 ? void 0 : userResponse.UserType) !== null && _userResponse$UserTyp !== void 0 ? _userResponse$UserTyp : \"\");\n    } catch (error) {\n      console.error('Error fetching user or organization details', error);\n    }\n  }\n  useEffect(() => {\n    // Skip token validation if we're in the middle of auto-login or auto-login hasn't completed yet\n    if (isAutoLoggingIn || !autoLoginCompleted) {\n      return;\n    }\n\n    // Also skip if we have auto-login URL parameters (to prevent interference)\n    const urlParams = new URLSearchParams(location.search);\n    const hasAutoLoginParams = urlParams.has('token') && urlParams.has('user_id') && urlParams.has('org_id');\n    if (hasAutoLoginParams) {\n      return;\n    }\n\n    // Skip if we're not on the login page (token validation should only run on login page)\n    if (!location.pathname.includes('/login')) {\n      return;\n    }\n\n    // Skip token validation if auto-login is in progress or completed\n    if (isAutoLoggingIn || autoLoginCompleted) {\n      return;\n    }\n    let token = localStorage.getItem(\"access_token\");\n    const userInfo = JSON.parse(localStorage.getItem(\"userInfo\") || '{}');\n    let storedUserDetails = null;\n    if (userInfo['oidc-info'] && userInfo['user']) {\n      storedUserDetails = JSON.parse(userInfo['user']);\n      userDetails = storedUserDetails; // Update global variable\n      try {\n        const oidcInfo = JSON.parse(userInfo['oidc-info']);\n        token = oidcInfo.access_token;\n      } catch (error) {\n        console.error('Error parsing oidc-info:', error);\n      }\n    } else if (userInfo['user'] && !userInfo['oidc-info']) {\n      // Handle auto-login case where we have user data but no oidc-info\n      storedUserDetails = JSON.parse(userInfo['user']);\n      userDetails = storedUserDetails;\n    }\n    if (token) {\n      try {\n        // Try to decode as JWT first (for regular login tokens)\n        const loggedinUserInfo = jwt_decode(token);\n        setLoginUserInfo(loggedinUserInfo);\n        // Only call GetLoginUserInfo if userDetails is available\n        if (userDetails) {\n          GetLoginUserInfo(userDetails);\n        }\n        UserId = loggedinUserInfo.UserId;\n      } catch (error) {\n        // If JWT decode fails, check if we have userDetails from localStorage (auto-login flow)\n        if (storedUserDetails) {\n          // Create LoginUserInfo from stored userDetails for auto-login flow\n          const autoLoginUserInfo = {\n            EmailId: storedUserDetails.EmailId,\n            Name: storedUserDetails.FirstName + ' ' + storedUserDetails.LastName,\n            OrganizationId: storedUserDetails.OrganizationId,\n            UserId: storedUserDetails.UserId,\n            UserName: storedUserDetails.UserName,\n            UserType: storedUserDetails.UserType,\n            auth_time: Math.floor(Date.now() / 1000),\n            role: storedUserDetails.UserType\n          };\n          setLoginUserInfo(autoLoginUserInfo);\n          GetLoginUserInfo(storedUserDetails);\n          UserId = storedUserDetails.UserId;\n        } else {\n          console.error('Token decode failed and no stored userDetails available:', error);\n          signOut();\n        }\n      }\n    } else if (!storedUserDetails) {\n      // Only sign out if we don't have stored user details (auto-login case)\n      signOut();\n    }\n  }, [user, isAutoLoggingIn, autoLoginCompleted, location.search, location.pathname]);\n\n  // Show loading state during auto login\n  if (isAutoLoggingIn) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"sm\",\n      className: \"qadpt-superadminlogin\",\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        mb: 4,\n        className: \"qadpt-brand-logo\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: QuickAdopttext,\n          alt: \"QuickAdopt Logo\",\n          className: \"qadpt-brand-logo-img\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        className: \"qadpt-welcome-message\",\n        style: {\n          textAlign: 'center',\n          padding: '40px 0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 60,\n          style: {\n            marginBottom: '20px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          className: \"qadpt-welcome-message-text\",\n          children: \"Welcome to QuickAdopt!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          style: {\n            marginTop: '10px',\n            color: '#6b7280'\n          },\n          children: \"Setting up your free trial workspace...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 394,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"sm\",\n    className: \"qadpt-superadminlogin\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 4,\n      className: \"qadpt-brand-logo\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: QuickAdopttext,\n        alt: \"QuickAdopt Logo\",\n        className: \"qadpt-brand-logo-img\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 419,\n      columnNumber: 13\n    }, this), showSessionExpiredAlert && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        marginBottom: -5,\n        width: '50%',\n        position: 'relative',\n        alignContent: 'center',\n        textAlign: 'center',\n        // centers the text\n        display: 'flex',\n        justifyContent: 'center'\n      },\n      children: \"Your session has expired. Please log in again.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 428,\n      columnNumber: 11\n    }, this), showSessionExpiredAlert && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        marginBottom: -5,\n        width: '50%',\n        position: 'relative',\n        alignContent: 'center',\n        textAlign: 'center',\n        // centers the text\n        display: 'flex',\n        justifyContent: 'center'\n      },\n      children: \"Your session has expired. Please log in again.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 446,\n      columnNumber: 11\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      className: \"qadpt-welcome-message\",\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        className: \"qadpt-welcome-message-text\",\n        children: \"Welcome back\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 464,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 463,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      className: \"qadpt-login-form\",\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        className: \"qadpt-form-label\",\n        children: \"Email\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 470,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TextField, {\n        required: true,\n        fullWidth: true,\n        type: \"email\",\n        id: \"email\",\n        name: \"Email\",\n        autoComplete: \"Email\",\n        autoFocus: true,\n        value: email,\n        onChange: handleEmailChange,\n        placeholder: \"eg, <EMAIL>\",\n        className: \"qadpt-custom-input\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 473,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        className: \"qadpt-form-label\",\n        children: \"Password\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 487,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TextField, {\n        required: true,\n        fullWidth: true,\n        type: showPassword ? \"text\" : \"password\",\n        id: \"password\",\n        name: \"password\",\n        autoComplete: \"password\",\n        value: password,\n        onChange: handlePasswordChange,\n        placeholder: \"Enter your password\",\n        className: \"qadpt-custom-input\",\n        InputProps: {\n          endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n            position: \"end\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              \"aria-label\": \"toggle password visibility\",\n              onClick: handleClickShowPassword,\n              edge: \"end\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: `fal ${showPassword ? \"fa-eye\" : \"fa-eye-slash\"}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 29\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 29\n          }, this)\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 490,\n        columnNumber: 17\n      }, this), error && /*#__PURE__*/_jsxDEV(FormHelperText, {\n        error: true,\n        className: \"qadpt-text-danger\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 518,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-form-label\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          onClick: () => window.open(`/forgotpassword`, ''),\n          children: \"Forgot password?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 523,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"button\",\n        fullWidth: true,\n        variant: \"contained\",\n        className: \"qadpt-btn-default\",\n        onClick: handleSubmit,\n        children: \"Continue\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 528,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 469,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      mt: 12,\n      className: \"qadpt-login-footer\",\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        className: \"qadpt-footer-text\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          sx: {\n            cursor: \"pointer\"\n          },\n          className: \"qadpt-footer-link\",\n          children: \"Terms of use\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 13\n        }, this), \" |\", /*#__PURE__*/_jsxDEV(Link, {\n          sx: {\n            cursor: \"pointer\"\n          },\n          className: \"qadpt-footer-link\",\n          children: \"Privacy Policy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 540,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 539,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 418,\n    columnNumber: 9\n  }, this);\n};\n_s(Login, \"3eNpe0pBDoD/P93xxfLLqbHzLms=\", false, function () {\n  return [useAuth, useLocation, useAuth, useNavigate];\n});\n_c = Login;\nexport default Login;\n\n//Code based login changes\n\n//   const { signIn ,signOut,loggedOut} = useAuth();\n//   useEffect(() => {\n//     // Get the user from userManager\n//     userManager.getUser().then(user => {\n//       if (!user || user.expired) {\n//         // If the user is not authenticated or the token is expired, redirect to the identity server login page\n//         loggedOut ? signOut() :userManager.signinRedirect() ;        \n//       }\n//       else {        \n//         userManager.signinRedirect();\n//       }\n//     });\n//   }, []);\n\n//   return null;\n// };\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Box", "Typography", "TextField", "<PERSON><PERSON>", "Link", "IconButton", "InputAdornment", "useAuth", "LoginService", "GetUserDetails", "JSEncrypt", "useNavigate", "useLocation", "jwt_decode", "FormHelperText", "getOrganizationById", "QuickAdopttext", "checkSessionExpired", "<PERSON><PERSON>", "CircularProgress", "jsxDEV", "_jsxDEV", "userLocalData", "SAinitialsData", "userDetails", "<PERSON><PERSON>", "_s", "user", "UserId", "OrganizationId", "location", "showSessionExpiredAlert", "setShowSessionExpiredAlert", "isAutoLoggingIn", "setIsAutoLoggingIn", "urlParams", "URLSearchParams", "search", "autoLogin", "get", "token", "userId", "orgId", "Boolean", "autoLoginCompleted", "setAutoLoginCompleted", "sessionStorage", "getItem", "safeLocalStorageClear", "hasAutoLoginParams", "has", "isAutoLoginCompleted", "localStorage", "clear", "sessionExpired", "timer", "setTimeout", "clearTimeout", "showPassword", "setShowPassword", "email", "setEmail", "password", "setPassword", "users", "setUser", "error", "setError", "loginUserInfo", "setLoginUserInfo", "undefined", "response", "setresponse", "userIds", "setuserId", "organizationDetails", "setOrganizationDetails", "loginUserDetails", "setUserDetails", "handleClickShowPassword", "signOut", "loggedOut", "handleEmailChange", "event", "target", "value", "handlePasswordChange", "navigate", "hasSessionExpired", "replace", "handleAutoLogin", "orgName", "userData", "EmailId", "FirstName", "split", "LastName", "UserType", "UserName", "Password", "ContactNumber", "Gender", "DateofBirth", "AdminDeactivated", "EmailConfirmed", "LoginType", "ProfilePhoto", "RTL", "TimeZone", "oidcInfo", "access_token", "token_type", "expires_in", "scope", "setItem", "isFreeTrialToken", "e", "JSON", "stringify", "OrganizationName", "newUrl", "window", "pathname", "history", "replaceState", "document", "title", "console", "includes", "removeItem", "handleSubmit", "organizationId", "<PERSON><PERSON><PERSON><PERSON>", "returnUrl", "authType", "tenantId", "isEncryptionEnabled", "process", "env", "REACT_APP_ENABLE_ENCRYPTION", "public<PERSON>ey", "REACT_APP_PUBLIC_ENCRYPT_KEY", "encryptor", "setPublicKey", "now", "Date", "toISOString", "encryptedPassword", "encrypt", "trim", "toString", "_userResponse$data$Us", "_userResponse$data", "_userResponse$data$Or", "_userResponse$data2", "userResponse", "data", "firstNameInitials", "substring", "toUpperCase", "lastNameinitials", "finalData", "orgDetails", "error_description", "_userDetails", "_userDetails2", "_userDetails3", "_userDetails4", "_userDetails5", "_userDetails6", "GetLoginUserInfo", "_userResponse$UserTyp", "userInfo", "parse", "storedUserDetails", "loggedinUserInfo", "autoLoginUserInfo", "Name", "auth_time", "Math", "floor", "role", "max<PERSON><PERSON><PERSON>", "className", "children", "mb", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "textAlign", "padding", "size", "marginBottom", "variant", "marginTop", "color", "severity", "sx", "width", "position", "align<PERSON><PERSON><PERSON>", "display", "justifyContent", "required", "fullWidth", "type", "id", "name", "autoComplete", "autoFocus", "onChange", "placeholder", "InputProps", "endAdornment", "onClick", "edge", "open", "mt", "cursor", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/components/login/login.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Con<PERSON>er, Box, Typography, TextField, Button, Link, IconButton, InputAdornment } from '@mui/material';\r\nimport Visibility from '@mui/icons-material/Visibility';\r\nimport { useAuth } from '../auth/AuthProvider';\r\nimport VisibilityOff from '@mui/icons-material/VisibilityOff';\r\nimport { LoginService } from \"../../services/LoginService\";\r\nimport { GetUserDetails, encryptPassword } from '../../services/UserService';\r\nimport { JSEncrypt } from 'jsencrypt';\r\nimport { useNavigate, useLocation } from \"react-router-dom\";\r\nimport { GetUserDetailsById } from '../../services/UserService';\r\nimport { LoginUserInfo } from '../../models/LoginUserInfo';\r\nimport jwt_decode from \"jwt-decode\";\r\nimport { getAllUsers } from '../../services/UserService';\r\nimport { Organization } from \"../../models/Organization\";\r\nimport { User } from \"../../models/User\";\r\nimport { User as Users, UserManager } from 'oidc-client-ts';\r\nimport { FormHelperText } from '@mui/material';\r\nimport { getOrganizationById } from '../../services/OrganizationService';\r\nimport { QuickAdopttext } from \"../../assets/icons/icons\";\r\nimport { checkSessionExpired } from '../../services/APIService';\r\nimport { Alert, CircularProgress } from '@mui/material';\r\n//import { useAuth } from '../auth/AuthProvider';\r\nlet userLocalData: { [key: string]: any } = {}\r\nlet SAinitialsData: string;\r\nlet userDetails: User;\r\nconst Login: React.FC = () => {\r\n  const { user } = useAuth();\r\n    let UserId: string;\r\n    let OrganizationId: string;\r\n    const location = useLocation();\r\n    const [showSessionExpiredAlert, setShowSessionExpiredAlert] = useState(false);\r\n    const [isAutoLoggingIn, setIsAutoLoggingIn] = useState<boolean>(() => {\r\n    // Check if we have auto-login parameters immediately\r\n    const urlParams = new URLSearchParams(location.search);\r\n    const autoLogin = urlParams.get('auto_login');\r\n    const token = urlParams.get('token');\r\n    const userId = urlParams.get('user_id');\r\n    const orgId = urlParams.get('org_id');\r\n\r\n    // If we have auto-login parameters, start in loading state\r\n    return Boolean(autoLogin === 'true' && token && userId && orgId);\r\n  });\r\n\r\n  // Check if auto-login was already completed in this session\r\n  const [autoLoginCompleted, setAutoLoginCompleted] = useState(() => {\r\n    return sessionStorage.getItem('autoLoginCompleted') === 'true';\r\n  });\r\n\r\n  // Utility function to safely clear localStorage only when not in auto-login flow\r\n  const safeLocalStorageClear = () => {\r\n    const urlParams = new URLSearchParams(location.search);\r\n    const hasAutoLoginParams = urlParams.has('token') && urlParams.has('userId') && urlParams.has('orgId');\r\n    const isAutoLoginCompleted = sessionStorage.getItem('autoLoginCompleted') === 'true';\r\n\r\n    if (!hasAutoLoginParams && !isAutoLoginCompleted && !isAutoLoggingIn) {\r\n      localStorage.clear();\r\n    }\r\n  };\r\n\r\n    useEffect(() => {\r\n        // Check if user was redirected due to session expiration\r\n        const sessionExpired = checkSessionExpired();\r\n        if (sessionExpired) {\r\n          setShowSessionExpiredAlert(true);\r\n          \r\n          // Auto-hide the alert after 5 seconds\r\n          const timer = setTimeout(() => {\r\n            setShowSessionExpiredAlert(false);\r\n          }, 5000);\r\n          \r\n          return () => clearTimeout(timer);\r\n        }\r\n    }, []);\r\n    \r\n    const [showPassword, setShowPassword] = useState(false);\r\n    const [email, setEmail] = useState('');\r\n    const [password, setPassword] = useState('');\r\n    const [users, setUser] = useState<Users | null>(null);\r\n    const [error, setError] = useState<string | null>(null);\r\n    const [loginUserInfo, setLoginUserInfo] = useState<LoginUserInfo | undefined>(undefined);\r\n    const [response, setresponse] = useState('');\r\n    const [userIds, setuserId] = useState(\"\");\r\n    const [organizationDetails, setOrganizationDetails] = useState<Organization | null>(null);\r\n    const [loginUserDetails, setUserDetails] = useState<User | null>(null);\r\n    const handleClickShowPassword = () => {\r\n        setShowPassword(!showPassword);\r\n    };\r\n    const { signOut, loggedOut } = useAuth();\r\n\r\n    const handleEmailChange = (event: any) => {\r\n        setEmail(event.target.value);\r\n    };\r\n\r\n    const handlePasswordChange = (event: any) => {\r\n        setPassword(event.target.value);\r\n    };\r\n    const navigate = useNavigate();\r\n\r\nuseEffect(() => {\r\n  const hasSessionExpired = checkSessionExpired();\r\n  const token = localStorage.getItem(\"access_token\");\r\n\r\n  // Check if we're in auto-login flow\r\n  const urlParams = new URLSearchParams(location.search);\r\n  const hasAutoLoginParams = urlParams.has('token') && urlParams.has('user_id') && urlParams.has('org_id');\r\n\r\n  // Don't redirect if we're in auto-login flow and it hasn't completed yet\r\n  if (hasAutoLoginParams && !autoLoginCompleted) {\r\n    return;\r\n  }\r\n\r\n  if (user && token && !hasSessionExpired) {\r\n    navigate(\"/\", { replace: true });\r\n  }\r\n}, [user, autoLoginCompleted, location.search]);\r\n\r\n// Handle automatic login from free trial success\r\nuseEffect(() => {\r\n  const handleAutoLogin = async () => {\r\n    const urlParams = new URLSearchParams(location.search);\r\n    const autoLogin = urlParams.get('auto_login');\r\n    const token = urlParams.get('token');\r\n    const email = urlParams.get('email');\r\n    const userId = urlParams.get('user_id');\r\n    const orgId = urlParams.get('org_id');\r\n    const orgName = urlParams.get('org_name');\r\n\r\n\r\n\r\n    if (autoLogin === 'true' && token && email && userId && orgId) {\r\n      setIsAutoLoggingIn(true);\r\n\r\n      try {\r\n        // Create user object for localStorage - must match User interface\r\n        const userData: User = {\r\n          UserId: userId,\r\n          EmailId: email,\r\n          OrganizationId: orgId,\r\n          FirstName: email.split('@')[0], // Extract first name from email\r\n          LastName: '',\r\n          UserType: 'Admin',\r\n          UserName: email,\r\n          Password: '',\r\n          ContactNumber: '',\r\n          Gender: '',\r\n          DateofBirth: '',\r\n          AdminDeactivated: false,\r\n          EmailConfirmed: true,\r\n          LoginType: 'Admin',\r\n          ProfilePhoto: '',\r\n          RTL: false,\r\n          TimeZone: ''\r\n        };\r\n\r\n        // Create OIDC info object\r\n        const oidcInfo = {\r\n          access_token: token,\r\n          token_type: 'Bearer',\r\n          expires_in: 86400, // 24 hours\r\n          scope: 'openid profile api1'\r\n        };\r\n\r\n        // Store authentication data\r\n        localStorage.setItem(\"access_token\", token);\r\n        localStorage.setItem(\"userType\", \"Admin\");\r\n\r\n        // Check if this is a free trial token (non-JWT format)\r\n        let isFreeTrialToken = false;\r\n        try {\r\n          // Try to decode as JWT\r\n          jwt_decode(token);\r\n        } catch (e) {\r\n          // Not a JWT token, likely a free trial token\r\n          isFreeTrialToken = true;\r\n        }\r\n\r\n        // Store free trial token flag for API service to use\r\n        if (isFreeTrialToken) {\r\n          sessionStorage.setItem('isFreeTrialToken', 'true');\r\n        }\r\n\r\n        const userLocalData: { [key: string]: any } = {};\r\n        userLocalData[\"oidc-info\"] = JSON.stringify(oidcInfo);\r\n        userLocalData[\"user\"] = JSON.stringify(userData);\r\n\r\n        // For auto-login, skip the organization details API call to avoid 401 errors\r\n        // The organization details can be fetched later after the user is fully logged in\r\n        userLocalData[\"orgDetails\"] = JSON.stringify({\r\n          OrganizationId: orgId,\r\n          OrganizationName: orgName || 'Organization'\r\n        });\r\n\r\n        localStorage.setItem(\"userInfo\", JSON.stringify(userLocalData));\r\n\r\n        // Set the global userDetails variable\r\n        userDetails = userData;\r\n\r\n        // Mark auto-login as completed and persist in sessionStorage\r\n        setAutoLoginCompleted(true);\r\n        sessionStorage.setItem('autoLoginCompleted', 'true');\r\n        setIsAutoLoggingIn(false);\r\n\r\n        // Clear URL parameters\r\n        const newUrl = window.location.pathname;\r\n        window.history.replaceState({}, document.title, newUrl);\r\n\r\n        // Add a small delay to ensure all localStorage operations are completed\r\n        // before navigation to prevent race conditions\r\n        setTimeout(() => {\r\n          // Redirect to the accounts page with free-trial suffix for free trial users\r\n          navigate(`/${orgId}/accounts/free-trial`, { replace: true });\r\n        }, 100);\r\n\r\n      } catch (error) {\r\n        console.error('Auto login failed:', error);\r\n        setError('Automatic login failed. Please try logging in manually.');\r\n        setIsAutoLoggingIn(false);\r\n        setAutoLoginCompleted(true);\r\n      }\r\n    } else {\r\n      // No auto-login parameters, mark as completed and persist\r\n      setAutoLoginCompleted(true);\r\n      sessionStorage.setItem('autoLoginCompleted', 'true');\r\n    }\r\n  };\r\n\r\n  handleAutoLogin();\r\n}, [location.search, navigate]);\r\n\r\n  // Cleanup sessionStorage on component unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      // Only clear if we're actually leaving the login page (not just re-rendering)\r\n      if (!location.pathname.includes('/login')) {\r\n        sessionStorage.removeItem('autoLoginCompleted');\r\n      }\r\n    };\r\n  }, [location.pathname]);\r\n    const handleSubmit = async () => {\r\n        try {\r\n            const organizationId = \"1\";\r\n            const rememberLogin = true;\r\n            const returnUrl = \"\"\r\n            const authType = \"admin\"\r\n            const tenantId = \"web\"\r\n            if (password === '' || password == null) {\r\n                setError('password should not be empty');\r\n            }\r\n            else if (email === '' || email == null) {\r\n                setError('email should not be empty');\r\n            }\r\n            else {\r\n                const isEncryptionEnabled = process.env.REACT_APP_ENABLE_ENCRYPTION === 'true';\r\n                const publicKey = process.env.REACT_APP_PUBLIC_ENCRYPT_KEY || '';\r\n                const encryptor = new JSEncrypt();\r\n                encryptor.setPublicKey(publicKey);\r\n                const now = new Date().toISOString();\r\n                const encryptedPassword = encryptor.encrypt(password + '|' + now.trim()).toString();\r\n                if (!encryptedPassword) {\r\n                  console.error(\"Encryption failed\");\r\n                  return; \r\n                }\r\n                const response = await LoginService(email, isEncryptionEnabled ? encryptedPassword : password, organizationId, rememberLogin, returnUrl, authType, tenantId);\r\n                if (response.access_token) {\r\n                    setresponse(response);\r\n                    userLocalData[\"oidc-info\"] = JSON.stringify(response)\r\n                    localStorage.setItem(\"access_token\", response.access_token)\r\n                    const userResponse = await GetUserDetails();\r\n                    setUserDetails(userResponse ? userResponse.data : null);\r\n                    const firstNameInitials = userResponse?.data.FirstName && userResponse?.data.FirstName ? userResponse?.data.FirstName.substring(0, 1).toUpperCase() : '';\r\n                    const lastNameinitials = userResponse?.data && userResponse?.data.LastName ? userResponse?.data.LastName.substring(0, 1).toUpperCase() : '';\r\n                    const finalData = firstNameInitials + lastNameinitials;\r\n                    SAinitialsData = finalData;\r\n                    localStorage.setItem(\"userType\", userResponse?.data?.UserType ?? \"\");\r\n                    userLocalData[\"user\"] = JSON.stringify(userResponse?.data);\r\n                    const orgDetails = await getOrganizationById(userResponse?.data?.OrganizationId ?? \"\");\r\n                    userLocalData[\"orgDetails\"] = JSON.stringify(orgDetails);\r\n\r\n                    localStorage.setItem(\"userInfo\", JSON.stringify(userLocalData))\r\n                    navigate(\"/\");\r\n                } else {\r\n                    setError(response.error_description);\r\n                }\r\n            }\r\n        }\r\n        catch (error) {\r\n            console.error('Login failed:');\r\n            setError('An unexpected error occurred.'); // Handle unexpected errors\r\n        }\r\n    };\r\n    useEffect(() =>\r\n    {\r\n        const firstNameInitials = userDetails?.FirstName &&  userDetails?.FirstName ? userDetails?.FirstName.substring(0, 1).toUpperCase() : '';\r\n        const lastNameinitials =  userDetails?.LastName &&  userDetails?.LastName ? userDetails?.LastName.substring(0, 1).toUpperCase() : '';\r\n        const finalData = firstNameInitials + lastNameinitials;\r\n        SAinitialsData = finalData;\r\n    }, [userDetails])\r\n    \r\n    async function GetLoginUserInfo(userResponse : User) {\r\n        try {\r\n            const firstNameInitials =  userResponse?.FirstName &&  userResponse?.FirstName ? userResponse?.FirstName.substring(0, 1).toUpperCase() : '';\r\n            const lastNameinitials =  userResponse &&  userResponse?.LastName ? userResponse?.LastName.substring(0, 1).toUpperCase() : '';\r\n            const finalData = firstNameInitials + lastNameinitials;\r\n            SAinitialsData = finalData;\r\n            localStorage.setItem(\"userType\", userResponse?.UserType ?? \"\");\r\n        } catch (error) {\r\n            console.error('Error fetching user or organization details', error);\r\n        }\r\n    }\r\n    useEffect(() => {\r\n        // Skip token validation if we're in the middle of auto-login or auto-login hasn't completed yet\r\n        if (isAutoLoggingIn || !autoLoginCompleted) {\r\n            return;\r\n        }\r\n\r\n        // Also skip if we have auto-login URL parameters (to prevent interference)\r\n        const urlParams = new URLSearchParams(location.search);\r\n        const hasAutoLoginParams = urlParams.has('token') && urlParams.has('user_id') && urlParams.has('org_id');\r\n        if (hasAutoLoginParams) {\r\n            return;\r\n        }\r\n\r\n        // Skip if we're not on the login page (token validation should only run on login page)\r\n        if (!location.pathname.includes('/login')) {\r\n            return;\r\n        }\r\n\r\n        // Skip token validation if auto-login is in progress or completed\r\n        if (isAutoLoggingIn || autoLoginCompleted) {\r\n            return;\r\n        }\r\n\r\n        let token = localStorage.getItem(\"access_token\");\r\n\t\tconst userInfo = JSON.parse(localStorage.getItem(\"userInfo\") || '{}');\r\n\t\tlet storedUserDetails = null;\r\n\r\n\t\tif (userInfo['oidc-info'] && userInfo['user']) {\r\n\t\t\tstoredUserDetails = JSON.parse(userInfo['user']);\r\n\t\t\tuserDetails = storedUserDetails; // Update global variable\r\n\t\t\ttry {\r\n\t\t\t\tconst oidcInfo = JSON.parse(userInfo['oidc-info']);\r\n\t\t\t\ttoken = oidcInfo.access_token;\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('Error parsing oidc-info:', error);\r\n\t\t\t}\r\n\t\t} else if (userInfo['user'] && !userInfo['oidc-info']) {\r\n\t\t\t// Handle auto-login case where we have user data but no oidc-info\r\n\t\t\tstoredUserDetails = JSON.parse(userInfo['user']);\r\n\t\t\tuserDetails = storedUserDetails;\r\n\t\t}\r\n        if (token) {\r\n            try {\r\n                // Try to decode as JWT first (for regular login tokens)\r\n                const loggedinUserInfo = jwt_decode<LoginUserInfo>(token);\r\n                setLoginUserInfo(loggedinUserInfo);\r\n                // Only call GetLoginUserInfo if userDetails is available\r\n                if (userDetails) {\r\n                    GetLoginUserInfo(userDetails);\r\n                }\r\n                UserId = loggedinUserInfo.UserId;\r\n            } catch (error) {\r\n                // If JWT decode fails, check if we have userDetails from localStorage (auto-login flow)\r\n                if (storedUserDetails) {\r\n                    // Create LoginUserInfo from stored userDetails for auto-login flow\r\n                    const autoLoginUserInfo: LoginUserInfo = {\r\n                        EmailId: storedUserDetails.EmailId,\r\n                        Name: storedUserDetails.FirstName + ' ' + storedUserDetails.LastName,\r\n                        OrganizationId: storedUserDetails.OrganizationId,\r\n                        UserId: storedUserDetails.UserId,\r\n                        UserName: storedUserDetails.UserName,\r\n                        UserType: storedUserDetails.UserType,\r\n                        auth_time: Math.floor(Date.now() / 1000),\r\n                        role: storedUserDetails.UserType\r\n                    };\r\n                    setLoginUserInfo(autoLoginUserInfo);\r\n                    GetLoginUserInfo(storedUserDetails);\r\n                    UserId = storedUserDetails.UserId;\r\n                } else {\r\n                    console.error('Token decode failed and no stored userDetails available:', error);\r\n                    signOut();\r\n                }\r\n            }\r\n        }\r\n        else if (!storedUserDetails) {\r\n            // Only sign out if we don't have stored user details (auto-login case)\r\n            signOut();\r\n        }\r\n\r\n    }, [user, isAutoLoggingIn, autoLoginCompleted, location.search, location.pathname]);\r\n\r\n    // Show loading state during auto login\r\n    if (isAutoLoggingIn) {\r\n        return (\r\n            <Container maxWidth=\"sm\" className=\"qadpt-superadminlogin\">\r\n                <Box mb={4} className=\"qadpt-brand-logo\">\r\n                    <img\r\n                        src={QuickAdopttext}\r\n                        alt=\"QuickAdopt Logo\"\r\n                        className=\"qadpt-brand-logo-img\"\r\n                    />\r\n                </Box>\r\n                <Box className=\"qadpt-welcome-message\" style={{ textAlign: 'center', padding: '40px 0' }}>\r\n                    <CircularProgress size={60} style={{ marginBottom: '20px' }} />\r\n                    <Typography variant=\"h5\" className=\"qadpt-welcome-message-text\">\r\n                        Welcome to QuickAdopt!\r\n                    </Typography>\r\n                    <Typography variant=\"body1\" style={{ marginTop: '10px', color: '#6b7280' }}>\r\n                        Setting up your free trial workspace...\r\n                    </Typography>\r\n                </Box>\r\n            </Container>\r\n        );\r\n    }\r\n\r\n    return (\r\n\r\n\r\n        <Container maxWidth=\"sm\" className=\"qadpt-superadminlogin\">\r\n            <Box mb={4} className=\"qadpt-brand-logo\">\r\n    <img\r\n        src={QuickAdopttext}\r\n        alt=\"QuickAdopt Logo\"\r\n        className=\"qadpt-brand-logo-img\"\r\n    />\r\n            </Box>\r\n\r\n            {showSessionExpiredAlert && (\r\n          <Alert \r\n            severity=\"error\" \r\n            sx={{ \r\n              marginBottom: -5,\r\n                width: '50%',\r\n                position: 'relative',\r\n                alignContent: 'center',\r\n                textAlign: 'center', // centers the text\r\n                display: 'flex',\r\n                justifyContent: 'center',\r\n              \r\n            }}\r\n          >\r\n            Your session has expired. Please log in again.\r\n          </Alert>\r\n        )}\r\n\r\n            {showSessionExpiredAlert && (\r\n          <Alert \r\n            severity=\"error\" \r\n            sx={{ \r\n              marginBottom: -5,\r\n                width: '50%',\r\n                position: 'relative',\r\n                alignContent: 'center',\r\n                textAlign: 'center', // centers the text\r\n                display: 'flex',\r\n                justifyContent: 'center',\r\n              \r\n            }}\r\n          >\r\n            Your session has expired. Please log in again.\r\n          </Alert>\r\n        )}\r\n\r\n            <Box className=\"qadpt-welcome-message\">\r\n                <Typography variant=\"h4\" className=\"qadpt-welcome-message-text\">\r\n                    Welcome back\r\n                </Typography>\r\n            </Box>\r\n\r\n            <Box className=\"qadpt-login-form\">\r\n                <Typography className=\"qadpt-form-label\">\r\n                    Email\r\n                </Typography>\r\n                <TextField\r\n                    required\r\n                    fullWidth\r\n                    type=\"email\"\r\n                    id=\"email\"\r\n                    name=\"Email\"\r\n                    autoComplete=\"Email\"\r\n                    autoFocus\r\n                    value={email}\r\n                    onChange={handleEmailChange}\r\n                    placeholder=\"eg, <EMAIL>\"\r\n                    className=\"qadpt-custom-input\"\r\n                />\r\n\r\n                <Typography className=\"qadpt-form-label\">\r\n                    Password\r\n                </Typography>\r\n                <TextField\r\n                    required\r\n                    fullWidth\r\n                    type={showPassword ? \"text\" : \"password\"}\r\n                    id=\"password\"\r\n                    name=\"password\"\r\n                    autoComplete=\"password\"                    \r\n                    value={password}\r\n                    onChange={handlePasswordChange}\r\n                    placeholder=\"Enter your password\"\r\n                    className=\"qadpt-custom-input\"\r\n                    InputProps={{\r\n                        endAdornment: (\r\n                            <InputAdornment position=\"end\">\r\n                                <IconButton\r\n                                    aria-label=\"toggle password visibility\"\r\n                                    onClick={handleClickShowPassword}\r\n                                    edge=\"end\"\r\n                                >\r\n                                    {/* {showPassword ? <VisibilityOff /> : <Visibility />} */}\r\n                            <i className={`fal ${showPassword ? \"fa-eye\" : \"fa-eye-slash\"}`}></i>\r\n                                </IconButton>\r\n                            </InputAdornment>\r\n                        ),\r\n                    }}\r\n                   \r\n                />\r\n                 {error && (\r\n                    <FormHelperText error className=\"qadpt-text-danger\">\r\n                        {error}\r\n                    </FormHelperText>\r\n                )}\r\n\r\n                <div className=\"qadpt-form-label\">\r\n                    <span onClick={() => window.open(`/forgotpassword`, '')}>\r\n                        Forgot password?\r\n                    </span>\r\n                </div>\r\n                <Button\r\n                    type=\"button\"\r\n                    fullWidth\r\n                    variant=\"contained\"\r\n                    className=\"qadpt-btn-default\"\r\n                    onClick={handleSubmit}\r\n                > \r\n                        Continue\r\n                </Button>\r\n            </Box>\r\n\r\n            <Box mt={12} className=\"qadpt-login-footer\">\r\n                <Typography variant=\"body2\" className=\"qadpt-footer-text\">\r\n            <Link sx={{ cursor: \"pointer\" }} className=\"qadpt-footer-link\">Terms of use</Link> |\r\n            <Link sx={{ cursor: \"pointer\" }} className=\"qadpt-footer-link\">Privacy Policy</Link>\r\n                </Typography>\r\n            </Box>\r\n            </Container>\r\n      \r\n    );\r\n\r\n\r\n}\r\nexport default Login;\r\n\r\n\r\n\r\n\r\n\r\n\r\n//Code based login changes\r\n\r\n//   const { signIn ,signOut,loggedOut} = useAuth();\r\n//   useEffect(() => {\r\n//     // Get the user from userManager\r\n//     userManager.getUser().then(user => {\r\n//       if (!user || user.expired) {\r\n//         // If the user is not authenticated or the token is expired, redirect to the identity server login page\r\n//         loggedOut ? signOut() :userManager.signinRedirect() ;        \r\n//       }\r\n//       else {        \r\n//         userManager.signinRedirect();\r\n//       }\r\n//     });\r\n//   }, []);\r\n\r\n//   return null;\r\n// };"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAEC,IAAI,EAAEC,UAAU,EAAEC,cAAc,QAAQ,eAAe;AAE/G,SAASC,OAAO,QAAQ,sBAAsB;AAE9C,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,cAAc,QAAyB,4BAA4B;AAC5E,SAASC,SAAS,QAAQ,WAAW;AACrC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAG3D,OAAOC,UAAU,MAAM,YAAY;AAKnC,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,mBAAmB,QAAQ,2BAA2B;AAC/D,SAASC,KAAK,EAAEC,gBAAgB,QAAQ,eAAe;AACvD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,IAAIC,aAAqC,GAAG,CAAC,CAAC;AAC9C,IAAIC,cAAsB;AAC1B,IAAIC,WAAiB;AACrB,MAAMC,KAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM;IAAEC;EAAK,CAAC,GAAGpB,OAAO,CAAC,CAAC;EACxB,IAAIqB,MAAc;EAClB,IAAIC,cAAsB;EAC1B,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmB,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC7E,MAAM,CAACoC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrC,QAAQ,CAAU,MAAM;IACtE;IACA,MAAMsC,SAAS,GAAG,IAAIC,eAAe,CAACN,QAAQ,CAACO,MAAM,CAAC;IACtD,MAAMC,SAAS,GAAGH,SAAS,CAACI,GAAG,CAAC,YAAY,CAAC;IAC7C,MAAMC,KAAK,GAAGL,SAAS,CAACI,GAAG,CAAC,OAAO,CAAC;IACpC,MAAME,MAAM,GAAGN,SAAS,CAACI,GAAG,CAAC,SAAS,CAAC;IACvC,MAAMG,KAAK,GAAGP,SAAS,CAACI,GAAG,CAAC,QAAQ,CAAC;;IAErC;IACA,OAAOI,OAAO,CAACL,SAAS,KAAK,MAAM,IAAIE,KAAK,IAAIC,MAAM,IAAIC,KAAK,CAAC;EAClE,CAAC,CAAC;;EAEF;EACA,MAAM,CAACE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhD,QAAQ,CAAC,MAAM;IACjE,OAAOiD,cAAc,CAACC,OAAO,CAAC,oBAAoB,CAAC,KAAK,MAAM;EAChE,CAAC,CAAC;;EAEF;EACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMb,SAAS,GAAG,IAAIC,eAAe,CAACN,QAAQ,CAACO,MAAM,CAAC;IACtD,MAAMY,kBAAkB,GAAGd,SAAS,CAACe,GAAG,CAAC,OAAO,CAAC,IAAIf,SAAS,CAACe,GAAG,CAAC,QAAQ,CAAC,IAAIf,SAAS,CAACe,GAAG,CAAC,OAAO,CAAC;IACtG,MAAMC,oBAAoB,GAAGL,cAAc,CAACC,OAAO,CAAC,oBAAoB,CAAC,KAAK,MAAM;IAEpF,IAAI,CAACE,kBAAkB,IAAI,CAACE,oBAAoB,IAAI,CAAClB,eAAe,EAAE;MACpEmB,YAAY,CAACC,KAAK,CAAC,CAAC;IACtB;EACF,CAAC;EAECvD,SAAS,CAAC,MAAM;IACZ;IACA,MAAMwD,cAAc,GAAGrC,mBAAmB,CAAC,CAAC;IAC5C,IAAIqC,cAAc,EAAE;MAClBtB,0BAA0B,CAAC,IAAI,CAAC;;MAEhC;MACA,MAAMuB,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BxB,0BAA0B,CAAC,KAAK,CAAC;MACnC,CAAC,EAAE,IAAI,CAAC;MAER,OAAO,MAAMyB,YAAY,CAACF,KAAK,CAAC;IAClC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM,CAACG,YAAY,EAAEC,eAAe,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC+D,KAAK,EAAEC,QAAQ,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiE,QAAQ,EAAEC,WAAW,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmE,KAAK,EAAEC,OAAO,CAAC,GAAGpE,QAAQ,CAAe,IAAI,CAAC;EACrD,MAAM,CAACqE,KAAK,EAAEC,QAAQ,CAAC,GAAGtE,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACuE,aAAa,EAAEC,gBAAgB,CAAC,GAAGxE,QAAQ,CAA4ByE,SAAS,CAAC;EACxF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC4E,OAAO,EAAEC,SAAS,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EACzC,MAAM,CAAC8E,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/E,QAAQ,CAAsB,IAAI,CAAC;EACzF,MAAM,CAACgF,gBAAgB,EAAEC,cAAc,CAAC,GAAGjF,QAAQ,CAAc,IAAI,CAAC;EACtE,MAAMkF,uBAAuB,GAAGA,CAAA,KAAM;IAClCpB,eAAe,CAAC,CAACD,YAAY,CAAC;EAClC,CAAC;EACD,MAAM;IAAEsB,OAAO;IAAEC;EAAU,CAAC,GAAG1E,OAAO,CAAC,CAAC;EAExC,MAAM2E,iBAAiB,GAAIC,KAAU,IAAK;IACtCtB,QAAQ,CAACsB,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EAChC,CAAC;EAED,MAAMC,oBAAoB,GAAIH,KAAU,IAAK;IACzCpB,WAAW,CAACoB,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACnC,CAAC;EACD,MAAME,QAAQ,GAAG5E,WAAW,CAAC,CAAC;EAElCb,SAAS,CAAC,MAAM;IACd,MAAM0F,iBAAiB,GAAGvE,mBAAmB,CAAC,CAAC;IAC/C,MAAMuB,KAAK,GAAGY,YAAY,CAACL,OAAO,CAAC,cAAc,CAAC;;IAElD;IACA,MAAMZ,SAAS,GAAG,IAAIC,eAAe,CAACN,QAAQ,CAACO,MAAM,CAAC;IACtD,MAAMY,kBAAkB,GAAGd,SAAS,CAACe,GAAG,CAAC,OAAO,CAAC,IAAIf,SAAS,CAACe,GAAG,CAAC,SAAS,CAAC,IAAIf,SAAS,CAACe,GAAG,CAAC,QAAQ,CAAC;;IAExG;IACA,IAAID,kBAAkB,IAAI,CAACL,kBAAkB,EAAE;MAC7C;IACF;IAEA,IAAIjB,IAAI,IAAIa,KAAK,IAAI,CAACgD,iBAAiB,EAAE;MACvCD,QAAQ,CAAC,GAAG,EAAE;QAAEE,OAAO,EAAE;MAAK,CAAC,CAAC;IAClC;EACF,CAAC,EAAE,CAAC9D,IAAI,EAAEiB,kBAAkB,EAAEd,QAAQ,CAACO,MAAM,CAAC,CAAC;;EAE/C;EACAvC,SAAS,CAAC,MAAM;IACd,MAAM4F,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,MAAMvD,SAAS,GAAG,IAAIC,eAAe,CAACN,QAAQ,CAACO,MAAM,CAAC;MACtD,MAAMC,SAAS,GAAGH,SAAS,CAACI,GAAG,CAAC,YAAY,CAAC;MAC7C,MAAMC,KAAK,GAAGL,SAAS,CAACI,GAAG,CAAC,OAAO,CAAC;MACpC,MAAMqB,KAAK,GAAGzB,SAAS,CAACI,GAAG,CAAC,OAAO,CAAC;MACpC,MAAME,MAAM,GAAGN,SAAS,CAACI,GAAG,CAAC,SAAS,CAAC;MACvC,MAAMG,KAAK,GAAGP,SAAS,CAACI,GAAG,CAAC,QAAQ,CAAC;MACrC,MAAMoD,OAAO,GAAGxD,SAAS,CAACI,GAAG,CAAC,UAAU,CAAC;MAIzC,IAAID,SAAS,KAAK,MAAM,IAAIE,KAAK,IAAIoB,KAAK,IAAInB,MAAM,IAAIC,KAAK,EAAE;QAC7DR,kBAAkB,CAAC,IAAI,CAAC;QAExB,IAAI;UACF;UACA,MAAM0D,QAAc,GAAG;YACrBhE,MAAM,EAAEa,MAAM;YACdoD,OAAO,EAAEjC,KAAK;YACd/B,cAAc,EAAEa,KAAK;YACrBoD,SAAS,EAAElC,KAAK,CAACmC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAAE;YAChCC,QAAQ,EAAE,EAAE;YACZC,QAAQ,EAAE,OAAO;YACjBC,QAAQ,EAAEtC,KAAK;YACfuC,QAAQ,EAAE,EAAE;YACZC,aAAa,EAAE,EAAE;YACjBC,MAAM,EAAE,EAAE;YACVC,WAAW,EAAE,EAAE;YACfC,gBAAgB,EAAE,KAAK;YACvBC,cAAc,EAAE,IAAI;YACpBC,SAAS,EAAE,OAAO;YAClBC,YAAY,EAAE,EAAE;YAChBC,GAAG,EAAE,KAAK;YACVC,QAAQ,EAAE;UACZ,CAAC;;UAED;UACA,MAAMC,QAAQ,GAAG;YACfC,YAAY,EAAEtE,KAAK;YACnBuE,UAAU,EAAE,QAAQ;YACpBC,UAAU,EAAE,KAAK;YAAE;YACnBC,KAAK,EAAE;UACT,CAAC;;UAED;UACA7D,YAAY,CAAC8D,OAAO,CAAC,cAAc,EAAE1E,KAAK,CAAC;UAC3CY,YAAY,CAAC8D,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC;;UAEzC;UACA,IAAIC,gBAAgB,GAAG,KAAK;UAC5B,IAAI;YACF;YACAtG,UAAU,CAAC2B,KAAK,CAAC;UACnB,CAAC,CAAC,OAAO4E,CAAC,EAAE;YACV;YACAD,gBAAgB,GAAG,IAAI;UACzB;;UAEA;UACA,IAAIA,gBAAgB,EAAE;YACpBrE,cAAc,CAACoE,OAAO,CAAC,kBAAkB,EAAE,MAAM,CAAC;UACpD;UAEA,MAAM5F,aAAqC,GAAG,CAAC,CAAC;UAChDA,aAAa,CAAC,WAAW,CAAC,GAAG+F,IAAI,CAACC,SAAS,CAACT,QAAQ,CAAC;UACrDvF,aAAa,CAAC,MAAM,CAAC,GAAG+F,IAAI,CAACC,SAAS,CAAC1B,QAAQ,CAAC;;UAEhD;UACA;UACAtE,aAAa,CAAC,YAAY,CAAC,GAAG+F,IAAI,CAACC,SAAS,CAAC;YAC3CzF,cAAc,EAAEa,KAAK;YACrB6E,gBAAgB,EAAE5B,OAAO,IAAI;UAC/B,CAAC,CAAC;UAEFvC,YAAY,CAAC8D,OAAO,CAAC,UAAU,EAAEG,IAAI,CAACC,SAAS,CAAChG,aAAa,CAAC,CAAC;;UAE/D;UACAE,WAAW,GAAGoE,QAAQ;;UAEtB;UACA/C,qBAAqB,CAAC,IAAI,CAAC;UAC3BC,cAAc,CAACoE,OAAO,CAAC,oBAAoB,EAAE,MAAM,CAAC;UACpDhF,kBAAkB,CAAC,KAAK,CAAC;;UAEzB;UACA,MAAMsF,MAAM,GAAGC,MAAM,CAAC3F,QAAQ,CAAC4F,QAAQ;UACvCD,MAAM,CAACE,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAACC,KAAK,EAAEN,MAAM,CAAC;;UAEvD;UACA;UACAhE,UAAU,CAAC,MAAM;YACf;YACA+B,QAAQ,CAAC,IAAI7C,KAAK,sBAAsB,EAAE;cAAE+C,OAAO,EAAE;YAAK,CAAC,CAAC;UAC9D,CAAC,EAAE,GAAG,CAAC;QAET,CAAC,CAAC,OAAOvB,KAAK,EAAE;UACd6D,OAAO,CAAC7D,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;UAC1CC,QAAQ,CAAC,yDAAyD,CAAC;UACnEjC,kBAAkB,CAAC,KAAK,CAAC;UACzBW,qBAAqB,CAAC,IAAI,CAAC;QAC7B;MACF,CAAC,MAAM;QACL;QACAA,qBAAqB,CAAC,IAAI,CAAC;QAC3BC,cAAc,CAACoE,OAAO,CAAC,oBAAoB,EAAE,MAAM,CAAC;MACtD;IACF,CAAC;IAEDxB,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAAC5D,QAAQ,CAACO,MAAM,EAAEkD,QAAQ,CAAC,CAAC;;EAE7B;EACAzF,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX;MACA,IAAI,CAACgC,QAAQ,CAAC4F,QAAQ,CAACM,QAAQ,CAAC,QAAQ,CAAC,EAAE;QACzClF,cAAc,CAACmF,UAAU,CAAC,oBAAoB,CAAC;MACjD;IACF,CAAC;EACH,CAAC,EAAE,CAACnG,QAAQ,CAAC4F,QAAQ,CAAC,CAAC;EACrB,MAAMQ,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACA,MAAMC,cAAc,GAAG,GAAG;MAC1B,MAAMC,aAAa,GAAG,IAAI;MAC1B,MAAMC,SAAS,GAAG,EAAE;MACpB,MAAMC,QAAQ,GAAG,OAAO;MACxB,MAAMC,QAAQ,GAAG,KAAK;MACtB,IAAIzE,QAAQ,KAAK,EAAE,IAAIA,QAAQ,IAAI,IAAI,EAAE;QACrCK,QAAQ,CAAC,8BAA8B,CAAC;MAC5C,CAAC,MACI,IAAIP,KAAK,KAAK,EAAE,IAAIA,KAAK,IAAI,IAAI,EAAE;QACpCO,QAAQ,CAAC,2BAA2B,CAAC;MACzC,CAAC,MACI;QACD,MAAMqE,mBAAmB,GAAGC,OAAO,CAACC,GAAG,CAACC,2BAA2B,KAAK,MAAM;QAC9E,MAAMC,SAAS,GAAGH,OAAO,CAACC,GAAG,CAACG,4BAA4B,IAAI,EAAE;QAChE,MAAMC,SAAS,GAAG,IAAIpI,SAAS,CAAC,CAAC;QACjCoI,SAAS,CAACC,YAAY,CAACH,SAAS,CAAC;QACjC,MAAMI,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACpC,MAAMC,iBAAiB,GAAGL,SAAS,CAACM,OAAO,CAACtF,QAAQ,GAAG,GAAG,GAAGkF,GAAG,CAACK,IAAI,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACnF,IAAI,CAACH,iBAAiB,EAAE;UACtBpB,OAAO,CAAC7D,KAAK,CAAC,mBAAmB,CAAC;UAClC;QACF;QACA,MAAMK,QAAQ,GAAG,MAAM/D,YAAY,CAACoD,KAAK,EAAE4E,mBAAmB,GAAGW,iBAAiB,GAAGrF,QAAQ,EAAEqE,cAAc,EAAEC,aAAa,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,CAAC;QAC5J,IAAIhE,QAAQ,CAACuC,YAAY,EAAE;UAAA,IAAAyC,qBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,mBAAA;UACvBlF,WAAW,CAACD,QAAQ,CAAC;UACrBjD,aAAa,CAAC,WAAW,CAAC,GAAG+F,IAAI,CAACC,SAAS,CAAC/C,QAAQ,CAAC;UACrDnB,YAAY,CAAC8D,OAAO,CAAC,cAAc,EAAE3C,QAAQ,CAACuC,YAAY,CAAC;UAC3D,MAAM6C,YAAY,GAAG,MAAMlJ,cAAc,CAAC,CAAC;UAC3CqE,cAAc,CAAC6E,YAAY,GAAGA,YAAY,CAACC,IAAI,GAAG,IAAI,CAAC;UACvD,MAAMC,iBAAiB,GAAGF,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEC,IAAI,CAAC9D,SAAS,IAAI6D,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEC,IAAI,CAAC9D,SAAS,GAAG6D,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEC,IAAI,CAAC9D,SAAS,CAACgE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG,EAAE;UACxJ,MAAMC,gBAAgB,GAAGL,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEC,IAAI,IAAID,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEC,IAAI,CAAC5D,QAAQ,GAAG2D,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEC,IAAI,CAAC5D,QAAQ,CAAC8D,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG,EAAE;UAC3I,MAAME,SAAS,GAAGJ,iBAAiB,GAAGG,gBAAgB;UACtDzI,cAAc,GAAG0I,SAAS;UAC1B7G,YAAY,CAAC8D,OAAO,CAAC,UAAU,GAAAqC,qBAAA,GAAEI,YAAY,aAAZA,YAAY,wBAAAH,kBAAA,GAAZG,YAAY,CAAEC,IAAI,cAAAJ,kBAAA,uBAAlBA,kBAAA,CAAoBvD,QAAQ,cAAAsD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;UACpEjI,aAAa,CAAC,MAAM,CAAC,GAAG+F,IAAI,CAACC,SAAS,CAACqC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEC,IAAI,CAAC;UAC1D,MAAMM,UAAU,GAAG,MAAMnJ,mBAAmB,EAAA0I,qBAAA,GAACE,YAAY,aAAZA,YAAY,wBAAAD,mBAAA,GAAZC,YAAY,CAAEC,IAAI,cAAAF,mBAAA,uBAAlBA,mBAAA,CAAoB7H,cAAc,cAAA4H,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;UACtFnI,aAAa,CAAC,YAAY,CAAC,GAAG+F,IAAI,CAACC,SAAS,CAAC4C,UAAU,CAAC;UAExD9G,YAAY,CAAC8D,OAAO,CAAC,UAAU,EAAEG,IAAI,CAACC,SAAS,CAAChG,aAAa,CAAC,CAAC;UAC/DiE,QAAQ,CAAC,GAAG,CAAC;QACjB,CAAC,MAAM;UACHpB,QAAQ,CAACI,QAAQ,CAAC4F,iBAAiB,CAAC;QACxC;MACJ;IACJ,CAAC,CACD,OAAOjG,KAAK,EAAE;MACV6D,OAAO,CAAC7D,KAAK,CAAC,eAAe,CAAC;MAC9BC,QAAQ,CAAC,+BAA+B,CAAC,CAAC,CAAC;IAC/C;EACJ,CAAC;EACDrE,SAAS,CAAC,MACV;IAAA,IAAAsK,YAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA;IACI,MAAMZ,iBAAiB,GAAG,CAAAO,YAAA,GAAA5I,WAAW,cAAA4I,YAAA,eAAXA,YAAA,CAAatE,SAAS,KAAAuE,aAAA,GAAK7I,WAAW,cAAA6I,aAAA,eAAXA,aAAA,CAAavE,SAAS,IAAAwE,aAAA,GAAG9I,WAAW,cAAA8I,aAAA,uBAAXA,aAAA,CAAaxE,SAAS,CAACgE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG,EAAE;IACvI,MAAMC,gBAAgB,GAAI,CAAAO,aAAA,GAAA/I,WAAW,cAAA+I,aAAA,eAAXA,aAAA,CAAavE,QAAQ,KAAAwE,aAAA,GAAKhJ,WAAW,cAAAgJ,aAAA,eAAXA,aAAA,CAAaxE,QAAQ,IAAAyE,aAAA,GAAGjJ,WAAW,cAAAiJ,aAAA,uBAAXA,aAAA,CAAazE,QAAQ,CAAC8D,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG,EAAE;IACpI,MAAME,SAAS,GAAGJ,iBAAiB,GAAGG,gBAAgB;IACtDzI,cAAc,GAAG0I,SAAS;EAC9B,CAAC,EAAE,CAACzI,WAAW,CAAC,CAAC;EAEjB,eAAekJ,gBAAgBA,CAACf,YAAmB,EAAE;IACjD,IAAI;MAAA,IAAAgB,qBAAA;MACA,MAAMd,iBAAiB,GAAIF,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAE7D,SAAS,IAAK6D,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAE7D,SAAS,GAAG6D,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE7D,SAAS,CAACgE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG,EAAE;MAC3I,MAAMC,gBAAgB,GAAIL,YAAY,IAAKA,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAE3D,QAAQ,GAAG2D,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE3D,QAAQ,CAAC8D,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG,EAAE;MAC7H,MAAME,SAAS,GAAGJ,iBAAiB,GAAGG,gBAAgB;MACtDzI,cAAc,GAAG0I,SAAS;MAC1B7G,YAAY,CAAC8D,OAAO,CAAC,UAAU,GAAAyD,qBAAA,GAAEhB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE1D,QAAQ,cAAA0E,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;IAClE,CAAC,CAAC,OAAOzG,KAAK,EAAE;MACZ6D,OAAO,CAAC7D,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;IACvE;EACJ;EACApE,SAAS,CAAC,MAAM;IACZ;IACA,IAAImC,eAAe,IAAI,CAACW,kBAAkB,EAAE;MACxC;IACJ;;IAEA;IACA,MAAMT,SAAS,GAAG,IAAIC,eAAe,CAACN,QAAQ,CAACO,MAAM,CAAC;IACtD,MAAMY,kBAAkB,GAAGd,SAAS,CAACe,GAAG,CAAC,OAAO,CAAC,IAAIf,SAAS,CAACe,GAAG,CAAC,SAAS,CAAC,IAAIf,SAAS,CAACe,GAAG,CAAC,QAAQ,CAAC;IACxG,IAAID,kBAAkB,EAAE;MACpB;IACJ;;IAEA;IACA,IAAI,CAACnB,QAAQ,CAAC4F,QAAQ,CAACM,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACvC;IACJ;;IAEA;IACA,IAAI/F,eAAe,IAAIW,kBAAkB,EAAE;MACvC;IACJ;IAEA,IAAIJ,KAAK,GAAGY,YAAY,CAACL,OAAO,CAAC,cAAc,CAAC;IACtD,MAAM6H,QAAQ,GAAGvD,IAAI,CAACwD,KAAK,CAACzH,YAAY,CAACL,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC;IACrE,IAAI+H,iBAAiB,GAAG,IAAI;IAE5B,IAAIF,QAAQ,CAAC,WAAW,CAAC,IAAIA,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC9CE,iBAAiB,GAAGzD,IAAI,CAACwD,KAAK,CAACD,QAAQ,CAAC,MAAM,CAAC,CAAC;MAChDpJ,WAAW,GAAGsJ,iBAAiB,CAAC,CAAC;MACjC,IAAI;QACH,MAAMjE,QAAQ,GAAGQ,IAAI,CAACwD,KAAK,CAACD,QAAQ,CAAC,WAAW,CAAC,CAAC;QAClDpI,KAAK,GAAGqE,QAAQ,CAACC,YAAY;MAC9B,CAAC,CAAC,OAAO5C,KAAK,EAAE;QACf6D,OAAO,CAAC7D,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MACjD;IACD,CAAC,MAAM,IAAI0G,QAAQ,CAAC,MAAM,CAAC,IAAI,CAACA,QAAQ,CAAC,WAAW,CAAC,EAAE;MACtD;MACAE,iBAAiB,GAAGzD,IAAI,CAACwD,KAAK,CAACD,QAAQ,CAAC,MAAM,CAAC,CAAC;MAChDpJ,WAAW,GAAGsJ,iBAAiB;IAChC;IACM,IAAItI,KAAK,EAAE;MACP,IAAI;QACA;QACA,MAAMuI,gBAAgB,GAAGlK,UAAU,CAAgB2B,KAAK,CAAC;QACzD6B,gBAAgB,CAAC0G,gBAAgB,CAAC;QAClC;QACA,IAAIvJ,WAAW,EAAE;UACbkJ,gBAAgB,CAAClJ,WAAW,CAAC;QACjC;QACAI,MAAM,GAAGmJ,gBAAgB,CAACnJ,MAAM;MACpC,CAAC,CAAC,OAAOsC,KAAK,EAAE;QACZ;QACA,IAAI4G,iBAAiB,EAAE;UACnB;UACA,MAAME,iBAAgC,GAAG;YACrCnF,OAAO,EAAEiF,iBAAiB,CAACjF,OAAO;YAClCoF,IAAI,EAAEH,iBAAiB,CAAChF,SAAS,GAAG,GAAG,GAAGgF,iBAAiB,CAAC9E,QAAQ;YACpEnE,cAAc,EAAEiJ,iBAAiB,CAACjJ,cAAc;YAChDD,MAAM,EAAEkJ,iBAAiB,CAAClJ,MAAM;YAChCsE,QAAQ,EAAE4E,iBAAiB,CAAC5E,QAAQ;YACpCD,QAAQ,EAAE6E,iBAAiB,CAAC7E,QAAQ;YACpCiF,SAAS,EAAEC,IAAI,CAACC,KAAK,CAACnC,IAAI,CAACD,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;YACxCqC,IAAI,EAAEP,iBAAiB,CAAC7E;UAC5B,CAAC;UACD5B,gBAAgB,CAAC2G,iBAAiB,CAAC;UACnCN,gBAAgB,CAACI,iBAAiB,CAAC;UACnClJ,MAAM,GAAGkJ,iBAAiB,CAAClJ,MAAM;QACrC,CAAC,MAAM;UACHmG,OAAO,CAAC7D,KAAK,CAAC,0DAA0D,EAAEA,KAAK,CAAC;UAChFc,OAAO,CAAC,CAAC;QACb;MACJ;IACJ,CAAC,MACI,IAAI,CAAC8F,iBAAiB,EAAE;MACzB;MACA9F,OAAO,CAAC,CAAC;IACb;EAEJ,CAAC,EAAE,CAACrD,IAAI,EAAEM,eAAe,EAAEW,kBAAkB,EAAEd,QAAQ,CAACO,MAAM,EAAEP,QAAQ,CAAC4F,QAAQ,CAAC,CAAC;;EAEnF;EACA,IAAIzF,eAAe,EAAE;IACjB,oBACIZ,OAAA,CAACtB,SAAS;MAACuL,QAAQ,EAAC,IAAI;MAACC,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBACtDnK,OAAA,CAACrB,GAAG;QAACyL,EAAE,EAAE,CAAE;QAACF,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eACpCnK,OAAA;UACIqK,GAAG,EAAE1K,cAAe;UACpB2K,GAAG,EAAC,iBAAiB;UACrBJ,SAAS,EAAC;QAAsB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN1K,OAAA,CAACrB,GAAG;QAACuL,SAAS,EAAC,uBAAuB;QAACS,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAS,CAAE;QAAAV,QAAA,gBACrFnK,OAAA,CAACF,gBAAgB;UAACgL,IAAI,EAAE,EAAG;UAACH,KAAK,EAAE;YAAEI,YAAY,EAAE;UAAO;QAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/D1K,OAAA,CAACpB,UAAU;UAACoM,OAAO,EAAC,IAAI;UAACd,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAEhE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb1K,OAAA,CAACpB,UAAU;UAACoM,OAAO,EAAC,OAAO;UAACL,KAAK,EAAE;YAAEM,SAAS,EAAE,MAAM;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAf,QAAA,EAAC;QAE5E;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEpB;EAEA,oBAGI1K,OAAA,CAACtB,SAAS;IAACuL,QAAQ,EAAC,IAAI;IAACC,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBACtDnK,OAAA,CAACrB,GAAG;MAACyL,EAAE,EAAE,CAAE;MAACF,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAChDnK,OAAA;QACIqK,GAAG,EAAE1K,cAAe;QACpB2K,GAAG,EAAC,iBAAiB;QACrBJ,SAAS,EAAC;MAAsB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACW,CAAC,EAELhK,uBAAuB,iBAC1BV,OAAA,CAACH,KAAK;MACJsL,QAAQ,EAAC,OAAO;MAChBC,EAAE,EAAE;QACFL,YAAY,EAAE,CAAC,CAAC;QACdM,KAAK,EAAE,KAAK;QACZC,QAAQ,EAAE,UAAU;QACpBC,YAAY,EAAE,QAAQ;QACtBX,SAAS,EAAE,QAAQ;QAAE;QACrBY,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE;MAEpB,CAAE;MAAAtB,QAAA,EACH;IAED;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACR,EAEIhK,uBAAuB,iBAC1BV,OAAA,CAACH,KAAK;MACJsL,QAAQ,EAAC,OAAO;MAChBC,EAAE,EAAE;QACFL,YAAY,EAAE,CAAC,CAAC;QACdM,KAAK,EAAE,KAAK;QACZC,QAAQ,EAAE,UAAU;QACpBC,YAAY,EAAE,QAAQ;QACtBX,SAAS,EAAE,QAAQ;QAAE;QACrBY,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE;MAEpB,CAAE;MAAAtB,QAAA,EACH;IAED;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACR,eAEG1K,OAAA,CAACrB,GAAG;MAACuL,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eAClCnK,OAAA,CAACpB,UAAU;QAACoM,OAAO,EAAC,IAAI;QAACd,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAEhE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC,eAEN1K,OAAA,CAACrB,GAAG;MAACuL,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC7BnK,OAAA,CAACpB,UAAU;QAACsL,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAEzC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb1K,OAAA,CAACnB,SAAS;QACN6M,QAAQ;QACRC,SAAS;QACTC,IAAI,EAAC,OAAO;QACZC,EAAE,EAAC,OAAO;QACVC,IAAI,EAAC,OAAO;QACZC,YAAY,EAAC,OAAO;QACpBC,SAAS;QACThI,KAAK,EAAEzB,KAAM;QACb0J,QAAQ,EAAEpI,iBAAkB;QAC5BqI,WAAW,EAAC,sBAAsB;QAClChC,SAAS,EAAC;MAAoB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eAEF1K,OAAA,CAACpB,UAAU;QAACsL,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAEzC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb1K,OAAA,CAACnB,SAAS;QACN6M,QAAQ;QACRC,SAAS;QACTC,IAAI,EAAEvJ,YAAY,GAAG,MAAM,GAAG,UAAW;QACzCwJ,EAAE,EAAC,UAAU;QACbC,IAAI,EAAC,UAAU;QACfC,YAAY,EAAC,UAAU;QACvB/H,KAAK,EAAEvB,QAAS;QAChBwJ,QAAQ,EAAEhI,oBAAqB;QAC/BiI,WAAW,EAAC,qBAAqB;QACjChC,SAAS,EAAC,oBAAoB;QAC9BiC,UAAU,EAAE;UACRC,YAAY,eACRpM,OAAA,CAACf,cAAc;YAACqM,QAAQ,EAAC,KAAK;YAAAnB,QAAA,eAC1BnK,OAAA,CAAChB,UAAU;cACP,cAAW,4BAA4B;cACvCqN,OAAO,EAAE3I,uBAAwB;cACjC4I,IAAI,EAAC,KAAK;cAAAnC,QAAA,eAGlBnK,OAAA;gBAAGkK,SAAS,EAAE,OAAO7H,YAAY,GAAG,QAAQ,GAAG,cAAc;cAAG;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAExB;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEL,CAAC,EACA7H,KAAK,iBACH7C,OAAA,CAACP,cAAc;QAACoD,KAAK;QAACqH,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAC9CtH;MAAK;QAAA0H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CACnB,eAED1K,OAAA;QAAKkK,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC7BnK,OAAA;UAAMqM,OAAO,EAAEA,CAAA,KAAMjG,MAAM,CAACmG,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAE;UAAApC,QAAA,EAAC;QAEzD;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN1K,OAAA,CAAClB,MAAM;QACH8M,IAAI,EAAC,QAAQ;QACbD,SAAS;QACTX,OAAO,EAAC,WAAW;QACnBd,SAAS,EAAC,mBAAmB;QAC7BmC,OAAO,EAAExF,YAAa;QAAAsD,QAAA,EACzB;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAEN1K,OAAA,CAACrB,GAAG;MAAC6N,EAAE,EAAE,EAAG;MAACtC,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eACvCnK,OAAA,CAACpB,UAAU;QAACoM,OAAO,EAAC,OAAO;QAACd,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAC7DnK,OAAA,CAACjB,IAAI;UAACqM,EAAE,EAAE;YAAEqB,MAAM,EAAE;UAAU,CAAE;UAACvC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAAY;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,MAClF,eAAA1K,OAAA,CAACjB,IAAI;UAACqM,EAAE,EAAE;YAAEqB,MAAM,EAAE;UAAU,CAAE;UAACvC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAKxB,CAAC;AAAArK,EAAA,CA5gBKD,KAAe;EAAA,QACFlB,OAAO,EAGLK,WAAW,EA0DGL,OAAO,EASrBI,WAAW;AAAA;AAAAoN,EAAA,GAvE1BtM,KAAe;AA6gBrB,eAAeA,KAAK;;AAOpB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AAAA,IAAAsM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}