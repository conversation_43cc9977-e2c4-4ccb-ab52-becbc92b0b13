import React from 'react';
import { Box, Typography } from '@mui/material';

const AIPerformanceTab: React.FC = () => {

  return (
    <>
      {/* Header Section */}
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        mb: 3,
        p: 2.5,
        backgroundColor: '#f8fafc',
        borderRadius: '8px',
        border: '1px solid #e2e8f0'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Box sx={{
            width: 32,
            height: 32,
            borderRadius: '50%',
            backgroundColor: '#8b5cf6',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Typography sx={{ fontSize: '14px', color: 'white', fontWeight: 'bold' }}>AI</Typography>
          </Box>
          <Box>
            <Typography variant="h6" fontWeight="bold" sx={{ color: '#1e293b', fontSize: '16px' }}>
              AI Agent Specialization Dashboard
            </Typography>
            <Typography variant="body2" sx={{ color: '#64748b', fontSize: '12px' }}>
              Q&A, Tour Creation, and Automation specialists performance metrics
            </Typography>
          </Box>
        </Box>
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          px: 1.5,
          py: 0.5,
          backgroundColor: '#dcfce7',
          borderRadius: '6px',
          border: '1px solid #bbf7d0'
        }}>
          <Box sx={{
            width: 6,
            height: 6,
            borderRadius: '50%',
            backgroundColor: '#16a34a'
          }} />
          <Typography variant="caption" sx={{ color: '#16a34a', fontWeight: 'medium', fontSize: '11px' }}>
            3 Specialists Active
          </Typography>
        </Box>
      </Box>

      {/* Agent Specialization Cards */}
      <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: 2, mb: 3 }}>
        {/* Dona - Q&A Specialist */}
        <Box sx={{
          p: 2.5,
          backgroundColor: 'white',
          borderRadius: '8px',
          border: '1px solid #e2e8f0',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, mb: 1.5 }}>
            <Box sx={{
              width: 32,
              height: 32,
              borderRadius: '6px',
              backgroundColor: '#8b5cf6',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Typography sx={{ fontSize: '16px' }}>💬</Typography>
            </Box>
            <Box>
              <Typography variant="body1" fontWeight="bold" sx={{ color: '#1e293b', fontSize: '14px' }}>
                Dona
              </Typography>
              <Typography variant="caption" sx={{ color: '#8b5cf6', fontWeight: 'medium', fontSize: '11px' }}>
                Q&A Specialist AI
              </Typography>
              <Typography variant="caption" sx={{ display: 'block', color: '#64748b', fontSize: '10px' }}>
                Expert
              </Typography>
            </Box>
          </Box>

         

          {/* Metrics */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1.5 }}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" fontWeight="bold" sx={{ color: '#3b82f6', fontSize: '16px' }}>8,136</Typography>
              <Typography variant="caption" sx={{ color: '#64748b', fontSize: '10px' }}>Q&A Responses</Typography>
            </Box>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" fontWeight="bold" sx={{ color: '#10b981', fontSize: '16px' }}>98%</Typography>
              <Typography variant="caption" sx={{ color: '#64748b', fontSize: '10px' }}>Success Rate</Typography>
            </Box>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" fontWeight="bold" sx={{ color: '#ef4444', fontSize: '16px' }}>2%</Typography>
              <Typography variant="caption" sx={{ color: '#64748b', fontSize: '10px' }}>Failure Rate</Typography>
            </Box>
          </Box>

          {/* Quality Score */}
          <Box sx={{ mb: 1.5 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
              <Typography variant="caption" fontWeight="medium" sx={{ fontSize: '10px' }}>Quality Score</Typography>
              <Typography variant="caption" fontWeight="bold" sx={{ fontSize: '10px' }}>98%</Typography>
            </Box>
            <Box sx={{
              width: '100%',
              height: 4,
              backgroundColor: '#e2e8f0',
              borderRadius: '2px',
              overflow: 'hidden'
            }}>
              <Box sx={{
                width: '98%',
                height: '100%',
                backgroundColor: '#10b981',
                borderRadius: '2px'
              }} />
            </Box>
          </Box>

          
        </Box>

        {/* Rookie - Tour Creation Specialist */}
        <Box sx={{
          p: 2.5,
          backgroundColor: 'white',
          borderRadius: '8px',
          border: '1px solid #e2e8f0',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, mb: 1.5 }}>
            <Box sx={{
              width: 32,
              height: 32,
              borderRadius: '6px',
              backgroundColor: '#10b981',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Typography sx={{ fontSize: '16px' }}>👁️</Typography>
            </Box>
            <Box>
              <Typography variant="body1" fontWeight="bold" sx={{ color: '#1e293b', fontSize: '14px' }}>
                Rookie
              </Typography>
              <Typography variant="caption" sx={{ color: '#10b981', fontWeight: 'medium', fontSize: '11px' }}>
                Tour Creation Specialist
              </Typography>
              <Typography variant="caption" sx={{ display: 'block', color: '#64748b', fontSize: '10px' }}>
                Intermediate
              </Typography>
            </Box>
          </Box>

          {/* Metrics */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1.5 }}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" fontWeight="bold" sx={{ color: '#3b82f6', fontSize: '16px' }}>1,360</Typography>
              <Typography variant="caption" sx={{ color: '#64748b', fontSize: '10px' }}>Tours Created</Typography>
            </Box>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" fontWeight="bold" sx={{ color: '#10b981', fontSize: '16px' }}>94%</Typography>
              <Typography variant="caption" sx={{ color: '#64748b', fontSize: '10px' }}>Success Rate</Typography>
            </Box>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" fontWeight="bold" sx={{ color: '#ef4444', fontSize: '16px' }}>6%</Typography>
              <Typography variant="caption" sx={{ color: '#64748b', fontSize: '10px' }}>Failure Rate</Typography>
            </Box>
          </Box>

          {/* Quality Score */}
          <Box sx={{ mb: 1.5 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
              <Typography variant="caption" fontWeight="medium" sx={{ fontSize: '10px' }}>Quality Score</Typography>
              <Typography variant="caption" fontWeight="bold" sx={{ fontSize: '10px' }}>91%</Typography>
            </Box>
            <Box sx={{
              width: '100%',
              height: 4,
              backgroundColor: '#e2e8f0',
              borderRadius: '2px',
              overflow: 'hidden'
            }}>
              <Box sx={{
                width: '91%',
                height: '100%',
                backgroundColor: '#10b981',
                borderRadius: '2px'
              }} />
            </Box>
          </Box>

         
        </Box>

        {/* Work Agent - Automation Specialist */}
        <Box sx={{
          p: 2.5,
          backgroundColor: 'white',
          borderRadius: '8px',
          border: '1px solid #e2e8f0',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, mb: 1.5 }}>
            <Box sx={{
              width: 32,
              height: 32,
              borderRadius: '6px',
              backgroundColor: '#3b82f6',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Typography sx={{ fontSize: '16px' }}>⚙️</Typography>
            </Box>
            <Box>
              <Typography variant="body1" fontWeight="bold" sx={{ color: '#1e293b', fontSize: '14px' }}>
                Work Agent
              </Typography>
              <Typography variant="caption" sx={{ color: '#3b82f6', fontWeight: 'medium', fontSize: '11px' }}>
                Automation Specialist
              </Typography>
              <Typography variant="caption" sx={{ display: 'block', color: '#64748b', fontSize: '10px' }}>
                Advanced
              </Typography>
            </Box>
          </Box>

          {/* Metrics */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1.5 }}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" fontWeight="bold" sx={{ color: '#3b82f6', fontSize: '16px' }}>711</Typography>
              <Typography variant="caption" sx={{ color: '#64748b', fontSize: '10px' }}>Tasks Automated</Typography>
            </Box>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" fontWeight="bold" sx={{ color: '#10b981', fontSize: '16px' }}>97%</Typography>
              <Typography variant="caption" sx={{ color: '#64748b', fontSize: '10px' }}>Success Rate</Typography>
            </Box>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" fontWeight="bold" sx={{ color: '#ef4444', fontSize: '16px' }}>3%</Typography>
              <Typography variant="caption" sx={{ color: '#64748b', fontSize: '10px' }}>Failure Rate</Typography>
            </Box>
          </Box>

          {/* Quality Score */}
          <Box sx={{ mb: 1.5 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
              <Typography variant="caption" fontWeight="medium" sx={{ fontSize: '10px' }}>Quality Score</Typography>
              <Typography variant="caption" fontWeight="bold" sx={{ fontSize: '10px' }}>95%</Typography>
            </Box>
            <Box sx={{
              width: '100%',
              height: 4,
              backgroundColor: '#e2e8f0',
              borderRadius: '2px',
              overflow: 'hidden'
            }}>
              <Box sx={{
                width: '95%',
                height: '100%',
                backgroundColor: '#10b981',
                borderRadius: '2px'
              }} />
            </Box>
          </Box>

         
        </Box>
      </Box>

      {/* Main Content Grid */}
      <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2, mb: 3 }}>
        {/* Left Side - Success Rate Performance Trends */}
        <Box sx={{
          p: 2.5,
          backgroundColor: 'white',
          borderRadius: '8px',
          border: '1px solid #e2e8f0',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography sx={{ fontSize: '16px' }}>📈</Typography>
              <Typography variant="h6" fontWeight="bold" sx={{ color: '#1e293b', fontSize: '14px' }}>
                Success Rate Performance Trends
              </Typography>
            </Box>
            <Typography variant="caption" sx={{ color: '#8b5cf6', fontSize: '11px', fontWeight: 'medium' }}>
              More
            </Typography>
          </Box>

          <Box sx={{ height: '240px', position: 'relative', backgroundColor: 'white', borderRadius: '6px' }}>
            {/* Line Chart for Success Rates */}
            <svg width="100%" height="220" viewBox="0 0 400 180" style={{ overflow: 'visible' }}>
              {/* Grid lines */}
              <defs>
                <pattern id="successGrid" width="66.67" height="30" patternUnits="userSpaceOnUse">
                  <path d="M 66.67 0 L 0 0 0 30" fill="none" stroke="#f1f5f9" strokeWidth="1"/>
                </pattern>
              </defs>
              <rect width="100%" height="150" fill="url(#successGrid)" />

              {/* Y-axis */}
              <line x1="40" y1="15" x2="40" y2="150" stroke="#e2e8f0" strokeWidth="1"/>
              {/* X-axis */}
              <line x1="40" y1="150" x2="360" y2="150" stroke="#e2e8f0" strokeWidth="1"/>

              {/* Y-axis labels */}
              <text x="35" y="20" fontSize="9" fill="#64748b" textAnchor="end">100</text>
              <text x="35" y="50" fontSize="9" fill="#64748b" textAnchor="end">95</text>
              <text x="35" y="80" fontSize="9" fill="#64748b" textAnchor="end">90</text>
              <text x="35" y="110" fontSize="9" fill="#64748b" textAnchor="end">85</text>
              <text x="35" y="140" fontSize="9" fill="#64748b" textAnchor="end">80</text>

              {/* X-axis labels */}
              <text x="80" y="165" fontSize="9" fill="#64748b" textAnchor="middle">Jan 24</text>
              <text x="160" y="165" fontSize="9" fill="#64748b" textAnchor="middle">Feb 24</text>
              <text x="240" y="165" fontSize="9" fill="#64748b" textAnchor="middle">Mar 24</text>
              <text x="320" y="165" fontSize="9" fill="#64748b" textAnchor="middle">Apr 24</text>

              {/* Dona Q&A Success Rate Line (Purple) - 98% trend */}
              <path d="M 80 18 L 160 16 L 240 15 L 320 14" fill="none" stroke="#8b5cf6" strokeWidth="2" strokeLinecap="round"/>
              <circle cx="80" cy="18" r="3" fill="#8b5cf6"/>
              <circle cx="160" cy="16" r="3" fill="#8b5cf6"/>
              <circle cx="240" cy="15" r="3" fill="#8b5cf6"/>
              <circle cx="320" cy="14" r="3" fill="#8b5cf6"/>

              {/* Rookie Tour Success Rate Line (Green) - 94% trend */}
              <path d="M 80 33 L 160 31 L 240 30 L 320 28" fill="none" stroke="#10b981" strokeWidth="2" strokeLinecap="round"/>
              <circle cx="80" cy="33" r="3" fill="#10b981"/>
              <circle cx="160" cy="31" r="3" fill="#10b981"/>
              <circle cx="240" cy="30" r="3" fill="#10b981"/>
              <circle cx="320" cy="28" r="3" fill="#10b981"/>

              {/* Work Agent Automation Success Rate Line (Blue) - 97% trend */}
              <path d="M 80 21 L 160 19 L 240 18 L 320 16" fill="none" stroke="#3b82f6" strokeWidth="2" strokeLinecap="round"/>
              <circle cx="80" cy="21" r="3" fill="#3b82f6"/>
              <circle cx="160" cy="19" r="3" fill="#3b82f6"/>
              <circle cx="240" cy="18" r="3" fill="#3b82f6"/>
              <circle cx="320" cy="16" r="3" fill="#3b82f6"/>
            </svg>

            {/* Legend */}
            <Box sx={{
              position: 'absolute',
              bottom: 5,
              left: '50%',
              transform: 'translateX(-50%)',
              display: 'flex',
              gap: 2,
              backgroundColor: 'rgba(255,255,255,0.95)',
              padding: '4px 8px',
              borderRadius: '4px',
              border: '1px solid #e2e8f0'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <Box sx={{ width: 8, height: 8, backgroundColor: '#8b5cf6', borderRadius: '50%' }} />
                <Typography variant="caption" sx={{ fontSize: '9px', color: '#64748b' }}>Dona Q&A Success Rate</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <Box sx={{ width: 8, height: 8, backgroundColor: '#10b981', borderRadius: '50%' }} />
                <Typography variant="caption" sx={{ fontSize: '9px', color: '#64748b' }}>Rookie Tour Success Rate</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <Box sx={{ width: 8, height: 8, backgroundColor: '#3b82f6', borderRadius: '50%' }} />
                <Typography variant="caption" sx={{ fontSize: '9px', color: '#64748b' }}>Work Agent Automation Success Rate</Typography>
              </Box>
            </Box>
          </Box>

          {/* Chart Description */}
          <Typography variant="body2" sx={{ color: '#64748b', mt: 1.5, fontSize: '10px' }}>
            Success rate performance trends over time for all 3 specialists showing reliability and improvement patterns
          </Typography>
        </Box>

        {/* Right Side - Dona Capabilities */}
        <Box sx={{
          p: 2.5,
          backgroundColor: 'white',
          borderRadius: '8px',
          border: '1px solid #e2e8f0',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          {/* Header */}
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
              <Box sx={{
                width: 24,
                height: 24,
                borderRadius: '4px',
                backgroundColor: '#8b5cf6',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <Typography sx={{ fontSize: '12px' }}>💬</Typography>
              </Box>
              <Typography variant="h6" fontWeight="bold" sx={{ color: '#1e293b', fontSize: '14px' }}>
                Dona Capabilities
              </Typography>
            </Box>
            <Box sx={{
              px: 1.5,
              py: 0.25,
              backgroundColor: '#dcfce7',
              color: '#16a34a',
              borderRadius: '6px',
              fontSize: '10px',
              fontWeight: 'medium'
            }}>
              Active
            </Box>
          </Box>

          {/* Q&A and Knowledge Management Summary */}
          <Box sx={{ mb: 2, p: 1.5, backgroundColor: '#f8fafc', borderRadius: '6px' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
              <Typography variant="body1" fontWeight="bold" sx={{ color: '#1e293b', fontSize: '12px' }}>
                Q&A and Knowledge Management
              </Typography>
              <Typography variant="caption" sx={{
                px: 1,
                py: 0.25,
                backgroundColor: '#e0e7ff',
                color: '#3730a3',
                borderRadius: '4px',
                fontSize: '9px',
                fontWeight: 'medium'
              }}>
                Expert
              </Typography>
            </Box>
           
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <Typography sx={{ fontSize: '10px' }}>⏱️</Typography>
                <Typography variant="body2" fontWeight="medium" sx={{ fontSize: '10px' }}>Avg: 0.4s</Typography>
              </Box>
              <Box sx={{
                px: 1,
                py: 0.25,
                backgroundColor: '#dcfce7',
                color: '#16a34a',
                borderRadius: '6px',
                fontSize: '9px',
                fontWeight: 'medium'
              }}>
                98% Success
              </Box>
            </Box>
          </Box>

          {/* Scrollable Capabilities Container */}
          <Box sx={{
            height: '180px',
            overflowY: 'auto',
            pr: 0.5,
            '&::-webkit-scrollbar': {
              width: '4px',
            },
            '&::-webkit-scrollbar-track': {
              backgroundColor: '#f1f5f9',
              borderRadius: '2px',
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: '#cbd5e1',
              borderRadius: '2px',
              '&:hover': {
                backgroundColor: '#94a3b8',
              },
            },
          }}>

            {/* FAQ Responses Box */}
            <Box sx={{
              p: 2,
              mb: 2,
              backgroundColor: 'white',
              border: '1px solid #e2e8f0',
              borderRadius: '6px',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1.5 }}>
                <Box sx={{
                  width: 16,
                  height: 16,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <Typography sx={{ fontSize: '12px' }}>📄</Typography>
                </Box>
                <Typography variant="body1" fontWeight="bold" sx={{ color: '#1e293b', fontSize: '12px' }}>
                  Faq Responses
                </Typography>
              </Box>

              <Typography variant="body2" sx={{ color: '#64748b', mb: 2, fontSize: '10px', lineHeight: 1.4 }}>
                Provides instant, accurate responses to frequently asked questions with context awareness
              </Typography>

              {/* Metrics */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" fontWeight="bold" sx={{ color: '#3b82f6', mb: 0.25, fontSize: '14px' }}>2,847</Typography>
                  <Typography variant="caption" sx={{ color: '#64748b', fontSize: '9px' }}>Completed</Typography>
                </Box>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" fontWeight="bold" sx={{ color: '#10b981', mb: 0.25, fontSize: '14px' }}>99%</Typography>
                  <Typography variant="caption" sx={{ color: '#64748b', fontSize: '9px' }}>Success</Typography>
                </Box>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" fontWeight="bold" sx={{ color: '#8b5cf6', mb: 0.25, fontSize: '14px' }}>0.3s</Typography>
                  <Typography variant="caption" sx={{ color: '#64748b', fontSize: '9px' }}>Avg Time</Typography>
                </Box>
              </Box>

              {/* Key Features */}
              <Typography variant="body2" fontWeight="medium" sx={{ mb: 1, color: '#1e293b', fontSize: '10px' }}>Key Features:</Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2 }}>
                <Box sx={{
                  px: 1,
                  py: 0.5,
                  backgroundColor: '#f8fafc',
                  color: '#475569',
                  borderRadius: '4px',
                  fontSize: '9px',
                  fontWeight: 'medium',
                  border: '1px solid #e2e8f0'
                }}>
                  Instant responses
                </Box>
                <Box sx={{
                  px: 1,
                  py: 0.5,
                  backgroundColor: '#f8fafc',
                  color: '#475569',
                  borderRadius: '4px',
                  fontSize: '9px',
                  fontWeight: 'medium',
                  border: '1px solid #e2e8f0'
                }}>
                  Context awareness
                </Box>
                <Box sx={{
                  px: 1,
                  py: 0.5,
                  backgroundColor: '#f8fafc',
                  color: '#475569',
                  borderRadius: '4px',
                  fontSize: '9px',
                  fontWeight: 'medium',
                  border: '1px solid #e2e8f0'
                }}>
                  Smart suggestions
                </Box>
                <Box sx={{
                  px: 1,
                  py: 0.5,
                  backgroundColor: '#f8fafc',
                  color: '#475569',
                  borderRadius: '4px',
                  fontSize: '9px',
                  fontWeight: 'medium',
                  border: '1px solid #e2e8f0'
                }}>
                  Auto-categorization
                </Box>
              </Box>

              {/* No Known Issues */}
              <Box sx={{
                p: 1.5,
                backgroundColor: '#f0fdf4',
                borderRadius: '4px',
                border: '1px solid #bbf7d0'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Box sx={{
                    width: 12,
                    height: 12,
                    backgroundColor: '#16a34a',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Typography sx={{ fontSize: '8px', color: 'white' }}>✓</Typography>
                  </Box>
                  <Typography variant="body2" sx={{ color: '#16a34a', fontWeight: 'medium', fontSize: '10px' }}>
                    No known issues
                  </Typography>
                </Box>
              </Box>
            </Box>

            {/* Troubleshooting */}
            <Box sx={{
              p: 2,
              mb: 2,
              backgroundColor: 'white',
              border: '1px solid #e2e8f0',
              borderRadius: '6px',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1.5 }}>
                <Typography sx={{ fontSize: '12px' }}>🔧</Typography>
                <Typography variant="body1" fontWeight="bold" sx={{ color: '#1e293b', fontSize: '12px' }}>
                  Troubleshooting
                </Typography>
              </Box>

              <Typography variant="body2" sx={{ color: '#64748b', mb: 2, fontSize: '10px' }}>
                Provides systematic troubleshooting guidance with diagnostic workflows and escalation
              </Typography>

              {/* Metrics */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" fontWeight="bold" sx={{ color: '#3b82f6', fontSize: '14px' }}>163</Typography>
                  <Typography variant="caption" sx={{ color: '#64748b', fontSize: '9px' }}>Completed</Typography>
                </Box>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" fontWeight="bold" sx={{ color: '#10b981', fontSize: '14px' }}>89%</Typography>
                  <Typography variant="caption" sx={{ color: '#64748b', fontSize: '9px' }}>Success</Typography>
                </Box>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" fontWeight="bold" sx={{ color: '#8b5cf6', fontSize: '14px' }}>1.2s</Typography>
                  <Typography variant="caption" sx={{ color: '#64748b', fontSize: '9px' }}>Avg Time</Typography>
                </Box>
              </Box>

              {/* Key Features */}
              <Typography variant="body2" fontWeight="medium" sx={{ mb: 1, color: '#1e293b', fontSize: '10px' }}>Key Features:</Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                <Box sx={{ px: 1, py: 0.5, backgroundColor: '#f0f9ff', color: '#0369a1', borderRadius: '4px', fontSize: '9px' }}>
                  Diagnostic workflows
                </Box>
                <Box sx={{ px: 1, py: 0.5, backgroundColor: '#f0f9ff', color: '#0369a1', borderRadius: '4px', fontSize: '9px' }}>
                  Step-by-step solutions
                </Box>
                <Box sx={{ px: 1, py: 0.5, backgroundColor: '#f0f9ff', color: '#0369a1', borderRadius: '4px', fontSize: '9px' }}>
                  Error pattern recognition
                </Box>
                <Box sx={{ px: 1, py: 0.5, backgroundColor: '#f0f9ff', color: '#0369a1', borderRadius: '4px', fontSize: '9px' }}>
                  Escalation paths
                </Box>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>

      {/* Bottom Metrics Overview Cards */}
      <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: 2, mb: 3 }}>
        {/* Q&A Responses */}
        <Box sx={{
          p: 2.5,
          backgroundColor: '#faf5ff',
          borderRadius: '8px',
          border: '1px solid #e9d5ff',
          textAlign: 'center'
        }}>
          <Box sx={{
            width: 32,
            height: 32,
            backgroundColor: '#a855f7',
            borderRadius: '6px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            mx: 'auto',
            mb: 1.5
          }}>
            <Typography sx={{ fontSize: '16px', color: 'white' }}>💬</Typography>
          </Box>
          <Typography variant="h5" fontWeight="bold" sx={{ color: '#7c3aed', mb: 0.5, fontSize: '20px' }}>
            8,136
          </Typography>
          <Typography variant="body2" sx={{ color: '#374151', fontWeight: 'medium', mb: 0.5, fontSize: '12px' }}>
            Q&A Responses
          </Typography>
          <Typography variant="caption" sx={{ color: '#9ca3af', fontSize: '10px' }}>
            by Dona
          </Typography>
        </Box>

        {/* Tours Created */}
        <Box sx={{
          p: 2.5,
          backgroundColor: '#f0fdf4',
          borderRadius: '8px',
          border: '1px solid #bbf7d0',
          textAlign: 'center'
        }}>
          <Box sx={{
            width: 32,
            height: 32,
            backgroundColor: '#22c55e',
            borderRadius: '6px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            mx: 'auto',
            mb: 1.5
          }}>
            <Typography sx={{ fontSize: '16px', color: 'white' }}>👁️</Typography>
          </Box>
          <Typography variant="h5" fontWeight="bold" sx={{ color: '#16a34a', mb: 0.5, fontSize: '20px' }}>
            1,360
          </Typography>
          <Typography variant="body2" sx={{ color: '#374151', fontWeight: 'medium', mb: 0.5, fontSize: '12px' }}>
            Tours Created
          </Typography>
          <Typography variant="caption" sx={{ color: '#9ca3af', fontSize: '10px' }}>
            by Rookie
          </Typography>
        </Box>

        {/* Tasks Automated */}
        <Box sx={{
          p: 2.5,
          backgroundColor: '#eff6ff',
          borderRadius: '8px',
          border: '1px solid #bfdbfe',
          textAlign: 'center'
        }}>
          <Box sx={{
            width: 32,
            height: 32,
            backgroundColor: '#3b82f6',
            borderRadius: '6px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            mx: 'auto',
            mb: 1.5
          }}>
            <Typography sx={{ fontSize: '16px', color: 'white' }}>⚙️</Typography>
          </Box>
          <Typography variant="h5" fontWeight="bold" sx={{ color: '#2563eb', mb: 0.5, fontSize: '20px' }}>
            711
          </Typography>
          <Typography variant="body2" sx={{ color: '#374151', fontWeight: 'medium', mb: 0.5, fontSize: '12px' }}>
            Tasks Automated
          </Typography>
          <Typography variant="caption" sx={{ color: '#9ca3af', fontSize: '10px' }}>
            by Work Agent
          </Typography>
        </Box>

        {/* Average Quality */}
        <Box sx={{
          p: 2.5,
          backgroundColor: '#fff7ed',
          borderRadius: '8px',
          border: '1px solid #fed7aa',
          textAlign: 'center'
        }}>
          <Box sx={{
            width: 32,
            height: 32,
            backgroundColor: '#f97316',
            borderRadius: '6px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            mx: 'auto',
            mb: 1.5
          }}>
            <Typography sx={{ fontSize: '16px', color: 'white' }}>📊</Typography>
          </Box>
          <Typography variant="h5" fontWeight="bold" sx={{ color: '#ea580c', mb: 0.5, fontSize: '20px' }}>
            94.7%
          </Typography>
          <Typography variant="body2" sx={{ color: '#374151', fontWeight: 'medium', mb: 0.5, fontSize: '12px' }}>
            Average Quality
          </Typography>
          <Typography variant="caption" sx={{ color: '#9ca3af', fontSize: '10px' }}>
            across all specialists
          </Typography>
        </Box>
      </Box>

      {/* Agent Collaboration Flow */}
      <Box sx={{
        p: 3,
        backgroundColor: '#f8fafc',
        borderRadius: '8px',
        border: '1px solid #e2e8f0'
      }}>
        <Typography variant="h6" fontWeight="bold" sx={{
          color: '#1e293b',
          textAlign: 'center',
          mb: 3,
          fontSize: '14px'
        }}>
          Agent Collaboration Flow
        </Typography>

        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: 3,
          mb: 2
        }}>
          {/* Dona */}
          <Box sx={{ textAlign: 'center', minWidth: '80px' }}>
            <Box sx={{
              width: 40,
              height: 40,
              backgroundColor: '#a855f7',
              borderRadius: '6px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mx: 'auto',
              mb: 1.5
            }}>
              <Typography sx={{ fontSize: '16px', color: 'white' }}>💬</Typography>
            </Box>
            <Typography variant="body1" fontWeight="bold" sx={{ color: '#1e293b', mb: 0.5, fontSize: '12px' }}>
              Dona
            </Typography>
            <Typography variant="body2" sx={{ color: '#64748b', mb: 0.5, fontSize: '10px' }}>
              Answers Questions
            </Typography>
            <Typography variant="caption" sx={{
              color: '#7c3aed',
              fontWeight: 'medium',
              fontSize: '9px'
            }}>
              8,136 responses
            </Typography>
          </Box>

          {/* Arrow */}
          <Box sx={{
            color: '#94a3b8',
            fontSize: '16px',
            display: 'flex',
            alignItems: 'center',
            mt: -1
          }}>
            →
          </Box>

          {/* Rookie */}
          <Box sx={{ textAlign: 'center', minWidth: '80px' }}>
            <Box sx={{
              width: 40,
              height: 40,
              backgroundColor: '#22c55e',
              borderRadius: '6px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mx: 'auto',
              mb: 1.5
            }}>
              <Typography sx={{ fontSize: '16px', color: 'white' }}>👁️</Typography>
            </Box>
            <Typography variant="body1" fontWeight="bold" sx={{ color: '#1e293b', mb: 0.5, fontSize: '12px' }}>
              Rookie
            </Typography>
            <Typography variant="body2" sx={{ color: '#64748b', mb: 0.5, fontSize: '10px' }}>
              Creates Tours
            </Typography>
            <Typography variant="caption" sx={{
              color: '#16a34a',
              fontWeight: 'medium',
              fontSize: '9px'
            }}>
              1,360 tours
            </Typography>
          </Box>

          {/* Arrow */}
          <Box sx={{
            color: '#94a3b8',
            fontSize: '16px',
            display: 'flex',
            alignItems: 'center',
            mt: -1
          }}>
            →
          </Box>

          {/* Work Agent */}
          <Box sx={{ textAlign: 'center', minWidth: '80px' }}>
            <Box sx={{
              width: 40,
              height: 40,
              backgroundColor: '#3b82f6',
              borderRadius: '6px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mx: 'auto',
              mb: 1.5
            }}>
              <Typography sx={{ fontSize: '16px', color: 'white' }}>⚙️</Typography>
            </Box>
            <Typography variant="body1" fontWeight="bold" sx={{ color: '#1e293b', mb: 0.5, fontSize: '12px' }}>
              Work Agent
            </Typography>
            <Typography variant="body2" sx={{ color: '#64748b', mb: 0.5, fontSize: '10px' }}>
              Automates Rookie
            </Typography>
            <Typography variant="caption" sx={{
              color: '#2563eb',
              fontWeight: 'medium',
              fontSize: '9px'
            }}>
              711 automations
            </Typography>
          </Box>
        </Box>

        {/* Description */}
        <Typography variant="body2" sx={{
          color: '#64748b',
          textAlign: 'center',
          fontSize: '10px',
          maxWidth: '400px',
          mx: 'auto'
        }}>
          Work Agent optimizes Rookie's tour creation process, improving efficiency by 50%
        </Typography>
      </Box>
    </>
  );
};

export default AIPerformanceTab;
