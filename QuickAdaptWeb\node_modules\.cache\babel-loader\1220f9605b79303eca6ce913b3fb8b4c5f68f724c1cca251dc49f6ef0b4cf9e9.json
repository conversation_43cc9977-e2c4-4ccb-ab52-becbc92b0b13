{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptWeb\\\\src\\\\components\\\\organization\\\\OrganizationList.tsx\",\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport Switch from '@mui/material/Switch';\nimport 'moment-timezone';\nimport { useNavigate } from \"react-router-dom\";\nimport { Snackbar } from '@mui/material';\n// import { GridColDef, GridRenderCellParams, GridValueGetterParams } from '@mui/x-data-grid';\nimport { DataGrid } from \"@mui/x-data-grid\";\nimport { FormControlLabel, IconButton, Tooltip, Alert } from \"@mui/material\";\nimport EditIcon from \"@mui/icons-material/Edit\";\nimport loader from \"../../assets/loader.gif\";\n//import EditOrganization from \"./OrganizationEdit\";\n\nimport EditOrganization from \"./OrganizationEdit\";\nimport { activateOrganization, createOrganization, deactivateOrganization, fetchOrganizations, getOrganizations, updateOrganization } from \"../../services/OrganizationService\";\nimport { formatDateTime } from \"../common/TimeZoneConversion\";\nimport OrganizationCustomColumnMenu from \"./OrganizationCustomColumnMenu\";\nimport WarningAmberIcon from '@mui/icons-material/WarningAmber';\nimport CorporateFareIcon from '@mui/icons-material/CorporateFare';\nimport CreditScoreIcon from '@mui/icons-material/CreditScore';\nimport EditSubscription from \"./EditSubscription\";\n// interface State extends SnackbarOrigin {\n// \topen: boolean;\n//   }\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst OrganizationList = () => {\n  _s2();\n  var _s = $RefreshSig$();\n  const [menuVisible, setMenuVisible] = useState(false);\n  const [searchText, setSearchText] = useState(\"\");\n  const [gridHeight, setGridHeight] = useState(500);\n  const [models, setModels] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [activePlan, setActivePlan] = useState(\"\");\n  const [idDelete, setIdDelete] = useState(\"\");\n  const [orgIdEdit, setOrgIdEdit] = useState(\"\");\n  const [logoFile, setLogoFile] = useState(null);\n  const [organizationName, setOrganizationName] = useState('');\n  const [orgNameError, setOrgNameError] = useState('');\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [logoError, setLogoError] = useState('');\n  const [inputs, setInputs] = useState({}); //useState<DataModel>() // //\n  const [organizationId, setOrganizationId] = useState(null);\n  const [activeId, setActiveId] = useState(null);\n  const [filteredUsers, setFilteredUsers] = useState([]);\n  const [users, setUsers] = useState([]);\n  const [filters, setFilters] = useState([]);\n  const [orderByFields, setOrderByFields] = useState(\"\");\n  const [isRTL, setIsRTL] = useState(false);\n  const [dateFormat, setDateFormat] = useState(\"dd-MM-yyyy\");\n  const [type, setType] = useState('Client');\n  const [logo, setLogo] = useState(null);\n  const [logoUrl, setLogoUrl] = useState(null);\n  const [timezoneError, setTimezoneError] = useState('');\n  const [dateFormatError, setDateFormatError] = useState('');\n  const [currentDate, setCurrentDate] = useState(\"\");\n  const [typeError, setTypeError] = useState('');\n  const [selectedTimezone, setSelectedTimezone] = useState(\"Asia/Kolkata\"); // default value\n  const [switchStates, setSwitchStates] = useState({});\n  const [snackbarOpen, setSnackbarOpen] = useState(false);\n  const [snackbarMessage, setSnackbarMessage] = useState(\"\");\n  const [snackbarSeverity, setSnackbarSeverity] = useState(\"success\");\n  const [skip, setskip] = useState(0);\n  const [top, settop] = useState(15);\n  const [totalcount, setTotalcount] = useState(0);\n  const [paginationModel, setPaginationModel] = useState({\n    page: 0,\n    pageSize: 15\n  });\n  const [isPaginationResetDone, setIsPaginationResetDone] = useState(false);\n  const [planType, setPlanType] = useState('Free Trail');\n  const [planTypeError, setPlanTypeError] = useState(\"\");\n  const [createddate, setCreatedDate] = useState(\"\");\n  const paginationReset = isFilter => {\n    setPaginationModel(prev => ({\n      ...prev,\n      page: 0\n    }));\n    setIsPaginationResetDone(isFilter);\n  };\n  const handleTimezoneChange = event => {\n    setSelectedTimezone(event.target.value);\n  };\n  const handleSnackbarClose = () => {\n    setSnackbarOpen(false);\n  };\n  const [pendingSwitchState, setPendingSwitchState] = useState(null);\n  const [checkedOne, setCheckedOne] = useState(true);\n  const [IsActive, setIsActive] = useState(true);\n  const MatEdit = number => {\n    const handleMailClick = type => {};\n    const handleDeleteClick = organizationid => {\n      setShowDeletePopup(true);\n      setIdDelete(organizationid);\n    };\n    const handleSwitchClick = (organizationid, currentStatus) => {\n      setShowActivatePopup(false);\n      setShowDeactivatePopup(false);\n      setIdDelete(organizationid);\n      setPendingSwitchState(!currentStatus);\n      if (!currentStatus) {\n        setShowActivatePopup(true);\n      } else {\n        setShowDeactivatePopup(true);\n      }\n    };\n    const handleeditclick = organizationid => {\n      setShowEditPopup(true);\n      setOrgIdEdit(organizationid);\n    };\n    const handleClose = () => {\n      setAnchorEl(null);\n      setShowDeletePopup(false);\n    };\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Edit Organization\",\n        arrow: true,\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          className: \"qadpt-grdicon\",\n          \"aria-label\": \"edit\",\n          onClick: () => handleeditclick(number.organizationid),\n          children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 8\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Edit Subscirption\",\n        arrow: true,\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          className: \"qadpt-grdicon\",\n          \"aria-label\": \"edit\",\n          onClick: () => openSubscribe(number),\n          children: /*#__PURE__*/_jsxDEV(CreditScoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 16\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 8\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 6\n      }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n        className: \"qadpt-grdicon\",\n        children: /*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"toggle-switch\",\n          style: {\n            margin: 0,\n            cursor: 'pointer'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: switchStates[number.organizationid] || false,\n            onChange: () => handleSwitchClick(number.organizationid, switchStates[number.organizationid]),\n            name: `switch-${number.organizationid}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 5\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"slider\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 5\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 3\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 5\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 4\n    }, this);\n  };\n  const handleChangeDate = event => {\n    const newFormat = event.target.value;\n    setDateFormat(newFormat);\n    updateCurrentDate(newFormat);\n  };\n  const handleSearch = value => {\n    // This should only update filters, not setModels directly\n    // The effect on filters will fetch filtered data from backend\n    setFilters(value && value.length ? [{\n      FieldName: 'Name',\n      ElementType: 'string',\n      Condition: 'in',\n      Value: value.join(','),\n      IsCustomField: false\n    }] : []);\n  };\n  const columns = [{\n    field: \"Name\",\n    headerName: \"Organization Name\",\n    width: 200,\n    flex: 1,\n    filterable: false\n  },\n  //{ field: \"DateFormat\", headerName: \"Date Format\", width: 200 ,flex:1,disableColumnMenu: true,sortable:false},\n  {\n    field: \"Plan\",\n    headerName: \"Organization Plan\",\n    width: 200,\n    flex: 1,\n    filterable: false,\n    disableColumnMenu: true\n  }, {\n    field: \"CreatedDate\",\n    headerName: \"Created Date\",\n    width: 200,\n    flex: 1,\n    filterable: false,\n    sortable: true,\n    disableColumnMenu: true,\n    renderCell: params => {\n      const dateString = params.row.CreatedDate;\n      const formatStr = params.row.DateFormat;\n      return formatDateTime(dateString, formatStr);\n    }\n  }, {\n    field: \"Type\",\n    headerName: \"Type\",\n    width: 200,\n    flex: 1,\n    filterable: false\n  }, {\n    field: \"actions\",\n    headerName: \"Actions\",\n    sortable: false,\n    disableColumnMenu: true,\n    width: 300,\n    flex: 1,\n    renderCell: params => {\n      const organizationid = params.row.OrganizationId || false;\n      setOrganizationId(organizationid);\n      const date = params.row.CreatedDate || false;\n      setCreatedDate(date);\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(MatEdit, {\n          organizationid: organizationid,\n          index: params.row.id\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 6\n      }, this);\n    }\n  }];\n  const [selectedValue, setSelectedValue] = useState(\"option1\");\n  const handleRadioChange = value => {\n    setSelectedValue(value);\n  };\n  const onPageChange = newPage => {};\n  const onPageSizeChange = newPageSize => {};\n  const planEditClick = type => {\n    setActivePlan(type);\n    setShowSubscribe(true);\n  };\n  const [organizations, setOrganizations] = useState([]);\n  const [sortModel, setSortModel] = useState([{\n    field: 'CreatedDate',\n    sort: 'desc'\n  }]);\n  const [filterModel, setFilterModel] = useState({\n    items: []\n  });\n  const setModelsandOptions = modelOptions => {\n    setModels(modelOptions);\n    setOptionsModel(modelOptions);\n  };\n  useEffect(() => {\n    const fetchData = async () => {\n      const skipcount = paginationModel.pageSize || 10;\n      const limitcount = paginationModel.page * skipcount;\n      const skips = limitcount;\n      const top = paginationModel.pageSize;\n      setskip(skips);\n      settop(top);\n      if (filters.length > 0) {\n        await getOrganizations(setModels, setLoading, skips, top, setTotalcount, sortModel, filters);\n      } else {\n        await getOrganizations(setModelsandOptions, setLoading, skips, top, setTotalcount, sortModel, filters);\n      }\n    };\n    if (isPaginationResetDone) {\n      setIsPaginationResetDone(false); //Here to skip the effect after paginationReset it has been, called\n    } else {\n      fetchData();\n    }\n    //setOptionsModel(models);\n  }, [paginationModel, filters, sortModel]);\n  useEffect(() => {\n    const fetchData = async () => {\n      const skipcount = paginationModel.pageSize || 10;\n      const limitcount = paginationModel.page * skipcount;\n      const skips = limitcount;\n      const top = paginationModel.pageSize;\n      setskip(skips);\n      settop(top);\n      if (filters.length > 0) {\n        await getOrganizations(setModels, setLoading, skips, top, setTotalcount, sortModel, filters);\n      } else {\n        await getOrganizations(setModelsandOptions, setLoading, skips, top, setTotalcount, sortModel, filters);\n      }\n    };\n    fetchData();\n  }, [sortModel]);\n  useEffect(() => {\n    paginationReset(filters.length > 0);\n  }, [filters]);\n  const [modelsData, setModelsData] = useState([]);\n  useEffect(() => {\n    const fetchAllData = async () => {\n      try {\n        await fetchOrganizations(setModelsData, setLoading);\n      } catch (error) {} finally {\n        setLoading(false);\n      }\n    };\n    fetchAllData();\n  }, []);\n  const handleSortModelChange = model => {\n    setSortModel(model);\n    setLoading(false);\n  };\n  const filteredRows = models.filter(row => {\n    const emailId = row.EmailId || \"\";\n    const contactnumber = row.ContactNumber || \"\";\n    const userName = row.UserName || \"\";\n    return emailId.toLowerCase().includes(searchText.toLowerCase()) || contactnumber.toLowerCase().includes(searchText.toLowerCase()) || userName.toLowerCase().includes(searchText.toLowerCase());\n  });\n  useEffect(() => {\n    setSwitchStates(models.reduce((acc, org) => {\n      acc[org.OrganizationId] = org.IsActive;\n      return acc;\n    }, {}));\n  }, [models]);\n  const [showPopup, setShowPopup] = useState(false);\n  const [showeditPopup, setShowEditPopup] = useState(false);\n  const [showSubscribe, setShowSubscribe] = useState(false);\n  const [showDeletePopup, setShowDeletePopup] = useState(false);\n  const [showDeactivatePopup, setShowDeactivatePopup] = useState(false);\n  const [showActivatePopup, setShowActivatePopup] = useState(false);\n  const [optionsModel, setOptionsModel] = useState([]);\n  const navigate = useNavigate();\n  const openPopup = () => {\n    setShowPopup(true);\n  };\n  const openSubscribe = number => {\n    setOrgIdEdit(number.organizationid);\n    setShowSubscribe(true);\n  };\n  const closeSubscribe = () => {\n    setShowSubscribe(false);\n  };\n  const CustomToolbar = () => {\n    _s();\n    const [anchorEl, setAnchorEl] = useState(null);\n    const handleExportMenuClick = event => {\n      setAnchorEl(event.currentTarget);\n    };\n    const handleExportMenuClose = () => {\n      setAnchorEl(null);\n    };\n    const handleDownloadExcelClick = () => {\n      handleExportMenuClose();\n    };\n    return /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 419,\n      columnNumber: 4\n    }, this);\n  };\n  _s(CustomToolbar, \"+aMDa7FPcESUyQDF1vq0RSMn4/k=\");\n  const handleSwitchChange = (organizationid, newStatus) => {\n    setSwitchStates(prevStates => ({\n      ...prevStates,\n      [organizationid]: newStatus\n    }));\n  };\n  const handleActivateClick = async () => {\n    if (idDelete !== null) {\n      await activateOrganization(idDelete, checkedOne, setShowActivatePopup, setModels, setLoading);\n      handleSwitchChange(idDelete, true);\n      await getOrganizations(setModels, setLoading, skip, top, setTotalcount, sortModel, filters);\n      setShowActivatePopup(false);\n      setSnackbarMessage(\"Organization Activated successfully\");\n      setSnackbarSeverity(\"success\");\n      setSnackbarOpen(true);\n      setTimeout(() => {\n        setSnackbarOpen(false);\n      }, 2000);\n    }\n  };\n  const handleDeactivateClick = async () => {\n    if (idDelete !== null) {\n      await deactivateOrganization(idDelete, checkedOne, setShowDeactivatePopup, setModels, setLoading);\n      handleSwitchChange(idDelete, false);\n      await getOrganizations(setModels, setLoading, skip, top, setTotalcount, sortModel, filters);\n      setShowDeactivatePopup(false);\n      setSnackbarMessage(\"Organization Deactivated successfully\");\n      setSnackbarSeverity(\"success\");\n      setSnackbarOpen(true);\n      setTimeout(() => {\n        setSnackbarOpen(false);\n      }, 2000);\n    }\n  };\n  let logoFileName = '';\n  const handleChange = e => {\n    const {\n      name,\n      value,\n      checked\n    } = e.target;\n    switch (name) {\n      case 'Name':\n        setOrganizationName(value);\n        validateOrganizationName(value);\n        break;\n      case 'isRTL':\n        setIsRTL(checked);\n        break;\n      case 'Type':\n        setType(value);\n        break;\n      case 'Plan Type':\n        setPlanType(value);\n        break;\n      default:\n        break;\n    }\n  };\n  const updateCurrentDate = format => {\n    const date = new Date();\n    let formattedDate = \"\";\n    switch (format) {\n      case \"yyyy-MM-dd\":\n        formattedDate = date.toISOString().split(\"T\")[0];\n        break;\n      case \"dd-MM-yyyy\":\n        formattedDate = `${String(date.getDate()).padStart(2, \"0\")}-${String(date.getMonth() + 1).padStart(2, \"0\")}-${date.getFullYear()}`;\n        break;\n      case \"MM-dd-yyyy\":\n        formattedDate = `${String(date.getMonth() + 1).padStart(2, \"0\")}-${String(date.getDate()).padStart(2, \"0\")}-${date.getFullYear()}`;\n        break;\n      case \"dd/mm/yyyy\":\n        formattedDate = `${String(date.getDate()).padStart(2, \"0\")}/${String(date.getMonth() + 1).padStart(2, \"0\")}/${date.getFullYear()}`;\n        break;\n      case \"mm/dd/yyyy\":\n        formattedDate = `${String(date.getMonth() + 1).padStart(2, \"0\")}/${String(date.getDate()).padStart(2, \"0\")}/${date.getFullYear()}`;\n        break;\n      case \"yyyy/mm/dd\":\n        formattedDate = `${date.getFullYear()}/${String(date.getMonth() + 1).padStart(2, \"0\")}/${String(date.getDate()).padStart(2, \"0\")}`;\n        break;\n      default:\n        formattedDate = date.toISOString().split(\"T\")[0];\n    }\n    setCurrentDate(formattedDate);\n  };\n  useEffect(() => {\n    updateCurrentDate(dateFormat);\n  }, []);\n  const validateOrganizationName = name => {\n    setOrgNameError('');\n    const trimmedName = name.trim();\n    if (trimmedName === '') {\n      setOrgNameError('Organization Name is required');\n      return;\n    }\n    if ((trimmedName.length < 5 || trimmedName.length > 50) && !/^[a-zA-Z0-9 ]+$/.test(trimmedName)) {\n      setOrgNameError('Organization Name must be between 5 and 50 characters.Special characters are not allowed');\n      return;\n    }\n    if (trimmedName.length < 5 || trimmedName.length > 50) {\n      setOrgNameError('Organization Name must be between 5 and 50 characters');\n      return;\n    }\n    if (!/^[a-zA-Z0-9 ]+$/.test(trimmedName)) {\n      setOrgNameError('Special characters are not allowed');\n      return;\n    }\n    const isDuplicateName = modelsData.some(org => org.Name === trimmedName);\n    if (isDuplicateName) {\n      setOrgNameError('Organization Name already exists');\n      return;\n    }\n    setOrgNameError('');\n  };\n  const handleClose = () => {\n    setAnchorEl(null);\n    setShowDeletePopup(false);\n    setOrganizationName(\"\");\n    setLogoFile(null);\n    setShowPopup(false);\n    setShowEditPopup(false);\n    setShowSubscribe(false);\n    setOrgNameError('');\n    setLogoError(\"\");\n  };\n  const toBase64 = file => {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => resolve(reader.result);\n      reader.onerror = error => reject(error);\n    });\n  };\n  const handleFileChange = event => {\n    if (event.target.files && event.target.files[0]) {\n      const file = event.target.files[0];\n      setLogoFile(file);\n      setLogoUrl(URL.createObjectURL(file));\n      logoFileName = file.name;\n      setLogoError('');\n    }\n  };\n  const handleSubmitOrganization = async event => {\n    event.preventDefault();\n    setOrgNameError('');\n    setDateFormatError('');\n    setTypeError('');\n    setLogoError('');\n    let hasError = false;\n    const trimmedNameNew = organizationName.trim();\n    validateOrganizationName(trimmedNameNew);\n    if (orgNameError) {\n      hasError = true;\n    }\n\n    // if (!dateFormat) {\n    // \tsetDateFormatError('Date format is required.');\n    // \thasError = true;\n    // }\n\n    if (!type) {\n      setTypeError('Type is required.');\n      hasError = true;\n    }\n    if (!planType) {\n      setPlanType(\"Plan is Requried\");\n      hasError = true;\n    }\n\n    // if (!logoFile) {\n    // \tsetLogoError('Logo is required');\n    // \thasError = true;\n    // } else {\n    // \tsetLogoError('');\n    // \tlogoFileName = logoFile.name; \n    // }\n\n    if (hasError) {\n      return;\n    }\n    const reqObj = {\n      Name: organizationName,\n      Logo: logoFileName,\n      TimeZone: selectedTimezone,\n      DateFormat: dateFormat,\n      Type: type,\n      RTL: isRTL,\n      Plan: planType\n    };\n    createOrganization(setLoading, setShowPopup, setModels, setShowEditPopup, reqObj, setSnackbarMessage, setSnackbarSeverity, setSnackbarOpen, organizationId => {\n      setOrgNameError('');\n      setOrganizationName('');\n      setLogoFile(null);\n      setLogoError('');\n      const redirectUrl = `/superadmin/${organizationId}/createadmin`;\n      setTimeout(() => {\n        navigate(redirectUrl);\n      }, 3000);\n    });\n  };\n  const updateOrganizationDetails = async orgDetails => {\n    await updateOrganization(setLoading, setModels, setShowEditPopup, orgDetails, setSnackbarMessage, setSnackbarSeverity, setSnackbarOpen, () => {\n      setShowEditPopup(false);\n      getOrganizations(setModelsandOptions, setLoading, skip, top, setTotalcount, sortModel, []);\n      fetchOrganizations(setModelsData, setLoading);\n    });\n    setShowSubscribe(false);\n    setShowEditPopup(false);\n  };\n  const validateLogo = () => {\n    if (!logoFile) {\n      setLogoError('Logo is required');\n    } else {\n      setLogoError('');\n    }\n  };\n  const [columnMenuApi, setColumnMenuApi] = useState(null); // Store the API instance\n\n  const handleColumnMenuClose = () => {\n    if (columnMenuApi) {\n      columnMenuApi.hideColumnMenu(); // Close the column menu\n    }\n  };\n  const isFormValid = !orgNameError;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-head\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-title-sec\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-title\",\n          children: \"Organization List\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 715,\n          columnNumber: 8\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 714,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-right-part\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: openPopup,\n          className: \"qadpt-memberButton\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fal fa-add-plus\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 722,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Create Organization\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 723,\n            columnNumber: 9\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 718,\n          columnNumber: 5\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 717,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 713,\n      columnNumber: 4\n    }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"Loaderstyles\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: loader,\n        alt: \"Spinner\",\n        className: \"LoaderSpinnerStyles\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 731,\n        columnNumber: 6\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 730,\n      columnNumber: 6\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(DataGrid, {\n        className: \"qadpt-org-grd\"\n        //   sx={{\n        //     borderColor: \"black\",\n        //     \"& .MuiDataGrid-columnHeaders\": {\n        //       backgroundImage: \"linear-gradient(to right, rgb(30, 138, 201), rgba(62, 200, 241, 0.7))\",\n        //     },\n        //     \"& .MuiDataGrid-columnHeaderTitle\": {\n        //       fontWeight: \"bold\",\n        //       color: \"white\"\n        //     },\n        //     \"& .MuiDataGrid-columnHeader\": {\n        //       backgroundImage: \"linear-gradient(to right, rgb(30, 138, 201), rgba(62, 200, 241, 0.7))\",\n        //       borderBottom: \"2px solid #ddd\",\n        //       color: \"white\",\n        //     },\n        //     \"& .MuiDataGrid-columnHeader--alignLeft\": {\n        //       backgroundImage: \"linear-gradient(to right, rgb(30, 138, 201), rgba(62, 200, 241, 0.7))\",\n        //       color: \"white\",\n        //     },\n        //     \"& .MuiDataGrid-cell\": {\n        //       borderBottom: \"2px solid #ddd\",\n        //       borderRight: \"2px solid #ddd\",\n        //     },\n        //     \"& .MuiDataGrid-columnHeader, .MuiDataGrid-cell\": {\n        //       borderRight: \"2px solid #ddd\",\n        //     },\n        //     \"& .MuiDataGrid-row\": {\n        //       \"&:last-child .MuiDataGrid-cell\": {\n        //         borderBottom: \"none\",\n        //       },\n        //     },\n        //     \"& .MuiDataGrid-menuIconButton\": {\n        //       visibility: \"visible\",\n        //       opacity: 1,\n        //     },\n        //   }}\n        ,\n        rows: models,\n        columns: columns,\n        getRowId: row => row.OrganizationId,\n        paginationModel: paginationModel,\n        onPaginationModelChange: model => {\n          setPaginationModel(model);\n          setLoading(false);\n        },\n        pagination: true,\n        paginationMode: \"server\",\n        rowCount: totalcount,\n        pageSizeOptions: [15, 25, 50, 100],\n        localeText: {\n          MuiTablePagination: {\n            labelRowsPerPage: \"Records Per Page\"\n          }\n        },\n        disableColumnSelector: true,\n        loading: loading,\n        disableRowSelectionOnClick: false,\n        sortModel: sortModel,\n        onSortModelChange: handleSortModelChange,\n        slots: {\n          columnMenu: menuProps => {\n            if (menuProps.colDef.field === \"Name\" || menuProps.colDef.field === \"Type\") {\n              return /*#__PURE__*/_jsxDEV(OrganizationCustomColumnMenu, {\n                column: menuProps.colDef.field,\n                setModels: setModels,\n                setLoading: setLoading,\n                skip: skip,\n                top: top,\n                OrganizationId: organizationId,\n                sortModel: sortModel,\n                setTotalcount: setTotalcount,\n                orderByFields: orderByFields,\n                filters: filters,\n                models: models,\n                modelsData: modelsData,\n                setFilters: setFilters,\n                ...menuProps,\n                optionsModel: optionsModel,\n                options: modelsData.map(model => model[menuProps.colDef.field] || \"\"),\n                onSearch: handleSearch,\n                hideMenu: menuProps.hideMenu,\n                paginationModel: paginationModel\n              }, modelsData.map(m => m.OrganizationId).join(\",\"), false, {\n                fileName: _jsxFileName,\n                lineNumber: 802,\n                columnNumber: 11\n              }, this);\n            }\n            return null;\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 740,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 738,\n      columnNumber: 6\n    }, this), showPopup ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-popup\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Create Organization\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 841,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n          onClick: () => handleClose(),\n          className: \"close-icon\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          x: \"0px\",\n          y: \"0px\",\n          width: \"24\",\n          height: \"24\",\n          viewBox: \"0 0 50 50\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M 7.71875 6.28125 L 6.28125 7.71875 L 23.5625 25 L 6.28125 42.28125 L 7.71875 43.71875 L 25 26.4375 L 42.28125 43.71875 L 43.71875 42.28125 L 26.4375 25 L 43.71875 7.71875 L 42.28125 6.28125 L 25 23.5625 Z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 852,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 842,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 840,\n        columnNumber: 6\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-usrform\",\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmitOrganization,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-txtfld\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"organizationname\",\n              className: \"qadpt-txtlabel\",\n              children: \"Organization Name*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 858,\n              columnNumber: 6\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              className: `qadpt-txtinp ${orgNameError ? \"error-input\" : \"\"}`,\n              type: \"text\",\n              name: \"Name\",\n              value: organizationName,\n              onChange: handleChange\n              // onFocus={() => validateOrganizationName(organizationName)}\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 859,\n              columnNumber: 7\n            }, this), orgNameError && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"error\",\n              children: orgNameError\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 867,\n              columnNumber: 24\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 857,\n            columnNumber: 8\n          }, this), logoError && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"error\",\n            children: logoError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 889,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-txtfld\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"type\",\n              className: \"qadpt-txtlabel\",\n              children: \"Type* \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 941,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"type\",\n              name: \"Type\",\n              value: type,\n              onChange: handleChange,\n              className: typeError ? \"error-input\" : \"qadpt-txtinp\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Client\",\n                children: \"Client\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 949,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Testing\",\n                children: \"Testing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 950,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"POC\",\n                children: \"POC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 951,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Prospects\",\n                children: \"Prospects\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 952,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 942,\n              columnNumber: 7\n            }, this), typeError && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"error\",\n              children: typeError\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 954,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 940,\n            columnNumber: 6\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-txtfld\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"type\",\n              className: \"qadpt-txtlabel\",\n              children: \"Plan \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 957,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"plan\",\n              name: \"Plan Type\",\n              value: planType,\n              onChange: handleChange,\n              className: planTypeError ? \"error-input\" : \"qadpt-txtinp\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Free Trail\",\n                children: \"Free Trail\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 965,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Basic\",\n                children: \"Basic\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 966,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Advanced\",\n                children: \"Advanced\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 967,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 958,\n              columnNumber: 7\n            }, this), planTypeError && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"error\",\n              children: planTypeError\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 969,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 956,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-txtfld qadpt-switch\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"qadpt-txtlabel\",\n              children: \"RTL \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 980,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              control: /*#__PURE__*/_jsxDEV(Switch, {\n                id: \"isRTL\",\n                name: \"isRTL\",\n                checked: isRTL,\n                onChange: handleChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 984,\n                columnNumber: 7\n              }, this),\n              label: \"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 981,\n              columnNumber: 7\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 979,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 998,\n            columnNumber: 6\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 856,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 855,\n        columnNumber: 6\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-button\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleSubmitOrganization,\n          className: isFormValid ? \"qadpt-enab\" : \"qadpt-disab\",\n          disabled: !isFormValid,\n          children: \"Save\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1003,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1002,\n        columnNumber: 6\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 839,\n      columnNumber: 5\n    }, this) : \"\", showeditPopup ? /*#__PURE__*/_jsxDEV(EditOrganization, {\n      modelsData: modelsData,\n      setModelsData: setModelsData,\n      showEditPopup: showeditPopup,\n      setShowEditPopup: setShowEditPopup,\n      OrganizationId: orgIdEdit,\n      sortModel: sortModel,\n      filters: filters,\n      getOrganizations: getOrganizations,\n      setModels: setModels,\n      models: models,\n      setLoading: setLoading,\n      handleClose: handleClose,\n      skip: skip,\n      top: top,\n      setTotalcount: setTotalcount,\n      updateOrganizationDetails: updateOrganizationDetails\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1017,\n      columnNumber: 5\n    }, this) : \"\", showActivatePopup ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-usrconfirm-popup qadpt-success\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-icon\",\n            children: /*#__PURE__*/_jsxDEV(CorporateFareIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1045,\n              columnNumber: 6\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1044,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1043,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-popup-title\",\n          children: \"Activate Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1048,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-warning\",\n          children: \"Are you sure you want to Activate Organization\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1049,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowActivatePopup(false),\n            className: \"qadpt-cancel-button\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1053,\n            columnNumber: 6\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleActivateClick,\n            className: \"qadpt-conform-button\",\n            type: \"submit\",\n            children: \"Activate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1059,\n            columnNumber: 6\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1052,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1042,\n        columnNumber: 6\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1041,\n      columnNumber: 18\n    }, this) : \"\", showDeactivatePopup ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-usrconfirm-popup qadpt-danger\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-icon\",\n            children: /*#__PURE__*/_jsxDEV(WarningAmberIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1079,\n              columnNumber: 7\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1078,\n            columnNumber: 5\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1077,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-popup-title\",\n          children: \"Deactivate Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1082,\n          columnNumber: 5\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-warning\",\n          children: \"Are you sure you want to deactivate this organization? This action cannot be undone.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1083,\n          columnNumber: 5\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowDeactivatePopup(false),\n            className: \"qadpt-cancel-button\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1087,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleDeactivateClick,\n            className: \"qadpt-conform-button\",\n            type: \"submit\",\n            children: \"Deactivate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1093,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1086,\n          columnNumber: 5\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1076,\n        columnNumber: 6\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1075,\n      columnNumber: 12\n    }, this) : \"\", /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: snackbarOpen,\n      autoHideDuration: 6000,\n      onClose: handleSnackbarClose,\n      anchorOrigin: {\n        vertical: \"top\",\n        horizontal: \"center\"\n      },\n      sx: {\n        zIndex: 10000,\n        marginTop: 4\n      } // Optionally adjust the zIndex if needed\n      ,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleSnackbarClose,\n        severity: snackbarSeverity,\n        sx: {\n          width: \"100%\"\n        },\n        children: snackbarMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1118,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1111,\n      columnNumber: 4\n    }, this), showSubscribe ? /*#__PURE__*/_jsxDEV(EditSubscription, {\n      closeSubscribe: closeSubscribe,\n      showSubscribe: showSubscribe,\n      organizationId: orgIdEdit,\n      setSnackbarOpen: setSnackbarOpen,\n      setSnackbarSeverity: setSnackbarSeverity,\n      setSnackbarMessage: setSnackbarMessage,\n      updateOrganizationDetails: updateOrganizationDetails\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1139,\n      columnNumber: 5\n    }, this) : \"\"]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 712,\n    columnNumber: 3\n  }, this);\n};\n_s2(OrganizationList, \"tdMiT7JFyMpOquZzy+szR3kBA9o=\", false, function () {\n  return [useNavigate];\n});\n_c = OrganizationList;\nexport default OrganizationList;\nvar _c;\n$RefreshReg$(_c, \"OrganizationList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Switch", "useNavigate", "Snackbar", "DataGrid", "FormControlLabel", "IconButton", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "EditIcon", "loader", "EditOrganization", "activateOrganization", "createOrganization", "deactivateOrganization", "fetchOrganizations", "getOrganizations", "updateOrganization", "formatDateTime", "OrganizationCustomColumnMenu", "WarningAmberIcon", "CorporateFareIcon", "CreditScoreIcon", "EditSubscription", "jsxDEV", "_jsxDEV", "OrganizationList", "_s2", "_s", "$RefreshSig$", "menuVisible", "setMenuVisible", "searchText", "setSearchText", "gridHeight", "setGridHeight", "models", "setModels", "loading", "setLoading", "activePlan", "setActivePlan", "idDelete", "setIdDelete", "orgIdEdit", "setOrgIdEdit", "logoFile", "setLogoFile", "organizationName", "setOrganizationName", "orgNameError", "setOrgNameError", "anchorEl", "setAnchorEl", "logoError", "setLogoError", "inputs", "setInputs", "organizationId", "setOrganizationId", "activeId", "setActiveId", "filteredUsers", "setFilteredUsers", "users", "setUsers", "filters", "setFilters", "order<PERSON><PERSON><PERSON><PERSON>s", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isRTL", "setIsRTL", "dateFormat", "setDateFormat", "type", "setType", "logo", "set<PERSON><PERSON>", "logoUrl", "setLogoUrl", "timezoneError", "setTimezoneError", "dateFormatError", "setDateFormatError", "currentDate", "setCurrentDate", "typeError", "setTypeError", "selectedTimezone", "setSelectedTimezone", "switchStates", "setSwitchStates", "snackbarOpen", "setSnackbarOpen", "snackbarMessage", "setSnackbarMessage", "snackbarSeverity", "setSnackbarSeverity", "skip", "setskip", "top", "settop", "totalcount", "setTotalcount", "paginationModel", "setPaginationModel", "page", "pageSize", "isPaginationResetDone", "setIsPaginationResetDone", "planType", "setPlanType", "planTypeError", "setPlanTypeError", "createddate", "setCreatedDate", "paginationReset", "isFilter", "prev", "handleTimezoneChange", "event", "target", "value", "handleSnackbarClose", "pendingSwitchState", "setPendingSwitchState", "checkedOne", "setCheckedOne", "IsActive", "setIsActive", "MatEdit", "number", "handleMailClick", "handleDeleteClick", "organizationid", "setShowDeletePopup", "handleSwitchClick", "currentStatus", "setShowActivatePopup", "setShowDeactivatePopup", "handleeditclick", "setShowEditPopup", "handleClose", "children", "title", "arrow", "className", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "openSubscribe", "style", "margin", "cursor", "checked", "onChange", "name", "handleChangeDate", "newFormat", "updateCurrentDate", "handleSearch", "length", "FieldName", "ElementType", "Condition", "Value", "join", "IsCustomField", "columns", "field", "headerName", "width", "flex", "filterable", "disableColumnMenu", "sortable", "renderCell", "params", "dateString", "row", "CreatedDate", "formatStr", "DateFormat", "OrganizationId", "date", "index", "id", "selected<PERSON><PERSON><PERSON>", "setSelectedValue", "handleRadioChange", "onPageChange", "newPage", "onPageSizeChange", "newPageSize", "planEditClick", "setShowSubscribe", "organizations", "setOrganizations", "sortModel", "setSortModel", "sort", "filterModel", "setFilterModel", "items", "setModelsandOptions", "modelOptions", "setOptionsModel", "fetchData", "skipcount", "limitcount", "skips", "modelsData", "setModelsData", "fetchAllData", "error", "handleSortModelChange", "model", "filteredRows", "filter", "emailId", "EmailId", "contactnumber", "ContactNumber", "userName", "UserName", "toLowerCase", "includes", "reduce", "acc", "org", "showPopup", "setShowPopup", "showeditPopup", "showSubscribe", "showDeletePopup", "showDeactivatePopup", "showActivatePopup", "optionsModel", "navigate", "openPopup", "closeSubscribe", "CustomToolbar", "handleExportMenuClick", "currentTarget", "handleExportMenuClose", "handleDownloadExcelClick", "handleSwitchChange", "newStatus", "prevStates", "handleActivateClick", "setTimeout", "handleDeactivateClick", "logoFileName", "handleChange", "e", "validateOrganizationName", "format", "Date", "formattedDate", "toISOString", "split", "String", "getDate", "padStart", "getMonth", "getFullYear", "trimmedName", "trim", "test", "isDuplicateName", "some", "Name", "toBase64", "file", "Promise", "resolve", "reject", "reader", "FileReader", "readAsDataURL", "onload", "result", "onerror", "handleFileChange", "files", "URL", "createObjectURL", "handleSubmitOrganization", "preventDefault", "<PERSON><PERSON><PERSON><PERSON>", "trimmedNameNew", "req<PERSON>bj", "Logo", "TimeZone", "Type", "RTL", "Plan", "redirectUrl", "updateOrganizationDetails", "orgDetails", "validate<PERSON><PERSON>", "columnMenuApi", "setColumnMenuApi", "handleColumnMenuClose", "hideColumnMenu", "isFormValid", "src", "alt", "rows", "getRowId", "onPaginationModelChange", "pagination", "paginationMode", "rowCount", "pageSizeOptions", "localeText", "MuiTablePagination", "labelRowsPerPage", "disableColumnSelector", "disableRowSelectionOnClick", "onSortModelChange", "slots", "columnMenu", "menuProps", "colDef", "column", "options", "map", "onSearch", "hideMenu", "m", "xmlns", "x", "y", "height", "viewBox", "d", "onSubmit", "htmlFor", "control", "label", "disabled", "showEditPopup", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "sx", "zIndex", "marginTop", "severity", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/components/organization/OrganizationList.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\"\r\nimport GetAppTwoToneIcon from '@mui/icons-material/GetAppTwoTone';\r\nimport Switch from '@mui/material/Switch';\r\nimport { format } from \"date-fns\";\r\nimport moment from 'moment';\r\nimport 'moment-timezone';\r\nimport styles from \"./OrganizationStyles.module.scss\"\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { <PERSON>nackbar, Typography } from '@mui/material';\r\n// import { GridColDef, GridRenderCellParams, GridValueGetterParams } from '@mui/x-data-grid';\r\nimport {\r\n\tDataGrid,\r\n\tGridColDef,\r\n\tGridColumnMenuProps,\r\n\tGridRenderCellParams,\r\n\tGridPaginationModel,\r\n\tGridSortModel\r\n\r\n} from \"@mui/x-data-grid\"\r\nimport { Button, Menu, MenuItem, FormControlLabel, IconButton, Tooltip, FormGroup, Select, Box, Alert } from \"@mui/material\"\r\nimport EditIcon from \"@mui/icons-material/Edit\"\r\nimport DeleteIcon from \"@mui/icons-material/Delete\"\r\nimport MailIcon from \"@mui/icons-material/Mail\"\r\nimport SaveAltIcon from \"@mui/icons-material/SaveAlt\"\r\nimport loader from \"../../assets/loader.gif\"\r\nimport { organizationsList } from \"./orgData\"\r\nimport CustomGrid from \"../common/Grid\"\r\nimport axios from \"axios\";\r\n//import EditOrganization from \"./OrganizationEdit\";\r\n\r\nimport EditOrganization from \"./OrganizationEdit\";\r\nimport { User } from \"../../models/User\";\r\nimport { Organization } from \"../../models/Organization\";\r\nimport { activateOrganization, createOrganization, deactivateOrganization, fetchOrganizations, getOrganizations, getOrganizationsData,updateOrganization } from \"../../services/OrganizationService\";\r\n\r\nimport { AnyAaaaRecord } from \"dns\";\r\nimport { timezones } from \"../../timezones\";\r\nimport { fetchUserDataFromApi } from \"../../services/UserService\";\r\nimport { formatDateTime } from \"../common/TimeZoneConversion\";\r\nimport OrganizationCustomColumnMenu from \"./OrganizationCustomColumnMenu\";\r\nimport WarningAmberIcon from '@mui/icons-material/WarningAmber';\r\nimport CorporateFareIcon from '@mui/icons-material/CorporateFare';\r\nimport CreditScoreIcon from '@mui/icons-material/CreditScore';\r\nimport EditSubscription from \"./EditSubscription\";\r\n// interface State extends SnackbarOrigin {\r\n// \topen: boolean;\r\n//   }\r\n\r\ninterface CustomDataGridProps extends React.ComponentProps<typeof DataGrid> {\r\n\tcomponents?: {\r\n\t\tcolumnMenu?: React.ComponentType<GridColumnMenuProps>;\r\n\t};\r\n}\r\nconst OrganizationList: React.FC = () => {\r\n\tconst [menuVisible, setMenuVisible] = useState(false);\r\n\tconst [searchText, setSearchText] = useState(\"\");\r\n\tconst [gridHeight, setGridHeight] = useState(500);\r\n\tconst [models, setModels] = useState<Organization[]>([])\r\n\tconst [loading, setLoading] = useState(true)\r\n\tconst [activePlan, setActivePlan] = useState(\"\")\r\n\tconst [idDelete, setIdDelete] = useState(\"\");\r\n\tconst [orgIdEdit, setOrgIdEdit] = useState(\"\")\r\n\tconst [logoFile, setLogoFile] = useState<File | null>(null);\r\n\tconst [organizationName, setOrganizationName] = useState('');\r\n\tconst [orgNameError, setOrgNameError] = useState('');\r\n\tconst [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);\r\n\tconst [logoError, setLogoError] = useState('');\r\n\tconst [inputs, setInputs] = useState({}) //useState<DataModel>() // //\r\n\tconst [organizationId, setOrganizationId] = useState<number | null>(null);\r\n\tconst [activeId, setActiveId] = useState<boolean | null>(null);\r\n\tconst [filteredUsers, setFilteredUsers] = useState<User[]>([]);\r\n\tconst [users, setUsers] = useState<User[]>([]);\r\n\t\r\n\ttype Filter = {\r\n\t\tFieldName: string;\r\n\t\tElementType: string;\r\n\t\tCondition: string;\r\n\t\tValue: string;\r\n\t\tIsCustomField: boolean;\r\n\t};\r\n\tconst [filters, setFilters] = useState<Filter[]>([]);\r\n\tconst [orderByFields, setOrderByFields] = useState(\"\");\r\n\tconst [isRTL, setIsRTL] = useState(false);\r\n\tconst [dateFormat, setDateFormat] = useState(\"dd-MM-yyyy\");\r\n\tconst [type, setType] = useState('Client');\r\n\tconst [logo, setLogo] = useState(null);\r\n\tconst [logoUrl, setLogoUrl] = useState<string | null>(null);\r\n\r\n\tconst [timezoneError, setTimezoneError] = useState('');\r\n\tconst [dateFormatError, setDateFormatError] = useState('');\r\n\tconst [currentDate, setCurrentDate] = useState(\"\");\r\n\tconst [typeError, setTypeError] = useState('');\r\n\tconst [selectedTimezone, setSelectedTimezone] = useState<string>(\"Asia/Kolkata\"); // default value\r\n\tconst [switchStates, setSwitchStates] = useState<{ [key: string]: boolean }>({});\r\n\tconst [snackbarOpen, setSnackbarOpen] = useState(false);\r\n\tconst [snackbarMessage, setSnackbarMessage] = useState(\"\");\r\n\tconst [snackbarSeverity, setSnackbarSeverity] = useState<\"success\" | \"error\">(\"success\");\r\n\tconst [skip, setskip] = useState(0);\r\n\tconst [top, settop] = useState(15);\r\n\tconst [totalcount, setTotalcount] = useState(0);\r\n\tconst [paginationModel, setPaginationModel] = useState<GridPaginationModel>({\r\n\t\tpage: 0,\r\n\t\tpageSize: 15,\r\n\t});\r\n\tconst [isPaginationResetDone, setIsPaginationResetDone] = useState(false);\r\n\tconst [planType, setPlanType] = useState('Free Trail');\r\n\tconst [planTypeError, setPlanTypeError] = useState(\"\");\r\n\r\n\tconst [createddate, setCreatedDate] = useState<string>(\"\")\r\n\tconst paginationReset = (isFilter : boolean) => {\r\n\t\tsetPaginationModel( (prev) => ({ ...prev, page: 0 }));\r\n\t\tsetIsPaginationResetDone(isFilter);\r\n\t}\r\n\r\n\r\n\tconst handleTimezoneChange = (event: any) => {\r\n\t\tsetSelectedTimezone(event.target.value);\r\n\t};\r\n\tconst handleSnackbarClose = () => {\r\n\t\tsetSnackbarOpen(false);\r\n\t};\r\n\tconst [pendingSwitchState, setPendingSwitchState] = useState<boolean | null>(null);\r\n\tconst [checkedOne, setCheckedOne] = useState(true)\r\n\tconst [IsActive, setIsActive] = useState<boolean>(true);\r\n\tconst MatEdit = (number: any) => {\r\n\r\n\t\tconst handleMailClick = (type: any) => {\r\n\r\n\t\t};\r\n\t\tconst handleDeleteClick = (organizationid: string) => {\r\n\t\t\tsetShowDeletePopup(true);\r\n\t\t\tsetIdDelete(organizationid);\r\n\t\t};\r\n\r\n\r\n\t\tconst handleSwitchClick = (organizationid: string, currentStatus: boolean) => {\r\n\t\t\tsetShowActivatePopup(false);\r\n\t\t\tsetShowDeactivatePopup(false);\r\n\t\t\tsetIdDelete(organizationid);\r\n\t\t\tsetPendingSwitchState(!currentStatus);\r\n\r\n\t\t\tif (!currentStatus) {\r\n\t\t\t\tsetShowActivatePopup(true);\r\n\t\t\t} else {\r\n\t\t\t\tsetShowDeactivatePopup(true);\r\n\t\t\t}\r\n\t\t};\r\n\t\tconst handleeditclick = (organizationid: string) => {\r\n\t\t\tsetShowEditPopup(true);\r\n\t\t\tsetOrgIdEdit(organizationid);\r\n\t\t};\r\n\t\tconst handleClose = () => {\r\n\t\t\tsetAnchorEl(null);\r\n\t\t\tsetShowDeletePopup(false);\r\n\t\t};\r\n\t\treturn (\r\n\t\t\t<div>\r\n\t\t\t\t\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t{/* <Tooltip title=\"Mail\">\r\n\t\t\t\t\t\t\t<IconButton\r\n              className=\"qadpt-grdicon\"\r\n\t\t\t\t\t\t\t\taria-label=\"mail\"\r\n\t\t\t\t\t\t\t\tonClick={() => handleMailClick(\"mail\")}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<MailIcon />\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t</Tooltip> */}\r\n\t\t\t\t\t \r\n\t\t\t\t\r\n\t\t\t\t\t\t<Tooltip title=\"Edit Organization\" arrow>\r\n\t\t\t\t\t\t\t<IconButton\r\n              className=\"qadpt-grdicon\"\r\n\t\t\t\t\t\t\t\taria-label=\"edit\"\r\n\t\t\t\t\t\t\t\tonClick={() => handleeditclick(number.organizationid)}\r\n\t\t\t\t\t\t\t>\r\n              <EditIcon/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t</Tooltip>\r\n\t\t\t\t\t<Tooltip title=\"Edit Subscirption\" arrow>\r\n\t\t\t\t\t\t\t<IconButton\r\n              className=\"qadpt-grdicon\"\r\n\t\t\t\t\t\t\t\taria-label=\"edit\"\r\n\t\t\t\t\t\t\t\t\tonClick={() => openSubscribe(number)}\r\n\t\t\t\t\t\t\t>\r\n               <CreditScoreIcon/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\r\n\r\n\t\t\t\r\n\t\t\t\t<IconButton className=\"qadpt-grdicon\">\r\n  <label className=\"toggle-switch\" style={{ margin: 0, cursor: 'pointer' }}>\r\n    <input\r\n      type=\"checkbox\"\r\n      checked={switchStates[number.organizationid] || false}\r\n      onChange={() =>\r\n        handleSwitchClick(number.organizationid, switchStates[number.organizationid])\r\n      }\r\n      name={`switch-${number.organizationid}`}\r\n    />\r\n    <span className=\"slider\"></span>\r\n  </label>\r\n</IconButton>\r\n\r\n\r\n\r\n\r\n\t\t\t\t\t\r\n\t\t\t\t\r\n\r\n\t\t\t</div>\r\n\t\t);\r\n\t};\r\n\r\n\tconst handleChangeDate = (event: any) => {\r\n\t\tconst newFormat = event.target.value;\r\n\t\tsetDateFormat(newFormat);\r\n\t\tupdateCurrentDate(newFormat);\r\n\t};\r\n\tconst handleSearch = (value: string[]) => {\r\n\t\t// This should only update filters, not setModels directly\r\n\t\t// The effect on filters will fetch filtered data from backend\r\n\t\tsetFilters(value && value.length ? [{ FieldName: 'Name', ElementType: 'string', Condition: 'in', Value: value.join(','), IsCustomField: false }] : []);\r\n\t};\r\n\tconst columns: GridColDef[] = [\r\n\r\n\t\t\t{ field: \"Name\", headerName: \"Organization Name\", width: 200,flex:1,filterable:false },\r\n\t\t\t//{ field: \"DateFormat\", headerName: \"Date Format\", width: 200 ,flex:1,disableColumnMenu: true,sortable:false},\r\n\t\t\t{ field: \"Plan\", headerName: \"Organization Plan\", width: 200,flex:1,filterable:false, disableColumnMenu: true},\r\n\t\t{\r\n\t\t\tfield: \"CreatedDate\",\r\n\t\t\theaderName: \"Created Date\",\r\n\t\t\twidth: 200,\r\n\t\t\tflex: 1,\r\n\t\t\tfilterable: false,\r\n\t\t\tsortable: true,\r\n\t\t\tdisableColumnMenu: true,\r\n\t\t\trenderCell: (params) => {\r\n\t\t\t\tconst dateString = params.row.CreatedDate;\r\n\t\t\t\tconst formatStr = params.row.DateFormat as\r\n\t\t\t\t\t'dd-MM-yyyy' |\r\n\t\t\t\t\t'MM-dd-yyyy' |\r\n\t\t\t\t\t'yyyy-MM-dd' |\r\n\t\t\t\t\t'dd/mm/yyyy' |\r\n\t\t\t\t\t'mm/dd/yyyy' |\r\n\t\t\t\t\t'yyyy/mm/dd';\r\n\t\t\t\treturn formatDateTime(dateString, formatStr);\r\n\t\t\t},\r\n\t\t},\r\n\r\n\r\n\t\t\t{ field: \"Type\", headerName: \"Type\", width: 200,flex:1,filterable:false},\r\n\t\t{\r\n\t\t\tfield: \"actions\",\r\n\t\t\theaderName: \"Actions\",\r\n\t\t\tsortable: false,\r\n\t\t\tdisableColumnMenu: true,\r\n\t\t\twidth: 300,\r\n\t\t\tflex: 1,\r\n\t\t\trenderCell: (params) => {\r\n\t\t\t\tconst organizationid = params.row.OrganizationId || false;\r\n\t\t\t\tsetOrganizationId(organizationid);\r\n\t\t\t\tconst date = params.row.CreatedDate || false;\r\n\t\t\t\tsetCreatedDate(date);\r\n\r\n\r\n\r\n\t\t\t\treturn (\r\n\t\t\t  <div>\r\n\t\t\t\t\t\t<MatEdit\r\n\r\n\t\t\t\t\t\t\torganizationid={organizationid}\r\n\t\t\t\t\t\t\tindex={params.row.id}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t);\r\n\t\t\t},\r\n\t\t},\r\n\t];\r\n\r\n\tconst [selectedValue, setSelectedValue] = useState(\"option1\")\r\n\r\n\tconst handleRadioChange = (value: any) => {\r\n\t\tsetSelectedValue(value)\r\n\t}\r\n\tconst onPageChange = (newPage: number) => {\r\n\t}\r\n\tconst onPageSizeChange = (newPageSize: number) => {\r\n\t}\r\n\tconst planEditClick = (type: any) => {\r\n\t\tsetActivePlan(type)\r\n\t\tsetShowSubscribe(true)\r\n\t}\r\n\tconst [organizations, setOrganizations] = useState<Organization[]>([]);\r\n\tconst [sortModel, setSortModel] = useState<GridSortModel>([{ field: 'CreatedDate', sort: 'desc' }]); \r\n\tconst [filterModel, setFilterModel] = useState({ items: [] });\r\n\tconst setModelsandOptions = (modelOptions: any) => {\r\n\t\tsetModels(modelOptions);\r\n\t\tsetOptionsModel(modelOptions)\r\n\t}\r\n\tuseEffect(() => {\r\n\t\tconst fetchData = async () => {\r\n\t\t\tconst skipcount = paginationModel.pageSize || 10;\r\n\t\t\tconst limitcount = paginationModel.page * skipcount;\r\n\t\t\tconst skips = limitcount;\r\n\t\t\tconst top = paginationModel.pageSize;\r\n\t\t\tsetskip(skips);\r\n\t\t\tsettop(top);\r\n\t\t\tif (filters.length > 0) {\r\n\t\t\t\tawait getOrganizations(setModels, setLoading, skips, top, setTotalcount, sortModel, filters);\r\n\t\t\t} else {\r\n\t\t\t\tawait getOrganizations(setModelsandOptions, setLoading, skips, top, setTotalcount, sortModel, filters);\r\n\t\t\t}\r\n\t\t};\r\n\t\tif (isPaginationResetDone) {\r\n\t\t\tsetIsPaginationResetDone(false);//Here to skip the effect after paginationReset it has been, called\r\n\t\t} else {\r\n\t\t\tfetchData();\r\n\t\t}\r\n\t\t//setOptionsModel(models);\r\n\t}, [paginationModel, filters, sortModel]);\r\n\t\r\n\tuseEffect(() => {\r\n\t\tconst fetchData = async () => {\r\n\t\t\t\r\n\t\t\tconst skipcount = paginationModel.pageSize || 10;\r\n\t\t\tconst limitcount = paginationModel.page * skipcount;\r\n\t\t\tconst skips = limitcount;\r\n\t\t\tconst top = paginationModel.pageSize;\r\n\t\t\tsetskip(skips);\r\n\t\t\tsettop(top);\r\n\t\t\t\r\n\t\t\tif (filters.length > 0) {\r\n\t\t\t\tawait getOrganizations(setModels, setLoading, skips, top, setTotalcount, sortModel, filters);\r\n\t\t\t} else {\r\n\t\t\t\tawait getOrganizations(setModelsandOptions, setLoading, skips, top, setTotalcount, sortModel, filters);\r\n\t\t\t}\r\n\t\t};\r\n\t\tfetchData();\r\n\t},[sortModel]);\r\n\r\n\tuseEffect(() => {\r\n\t\tpaginationReset(filters.length>0);\r\n\t}, [filters]);\r\n\r\n\tconst [modelsData, setModelsData] = useState<Organization[]>([])\r\n\r\n\tuseEffect(() => {\r\n\t\tconst fetchAllData = async () => {\r\n\t\t\ttry {\r\n\t\t\t\tawait fetchOrganizations(setModelsData, setLoading);\r\n\t\t\t} catch (error) {\r\n\t\t\t} finally {\r\n\t\t\t\tsetLoading(false);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tfetchAllData();\r\n\t}, []);\r\n\r\n\tconst handleSortModelChange = (model: any) => {\r\n\t\tsetSortModel(model);\r\n\t\tsetLoading(false);\r\n\t};\r\n\tconst filteredRows = models.filter((row: any) => {\r\n\t\tconst emailId = row.EmailId || \"\";\r\n\t\tconst contactnumber = row.ContactNumber || \"\";\r\n\t\tconst userName = row.UserName || \"\";\r\n\r\n\t\treturn (\r\n\t\t\temailId.toLowerCase().includes(searchText.toLowerCase()) ||\r\n\t\t\tcontactnumber.toLowerCase().includes(searchText.toLowerCase()) ||\r\n\t\t\tuserName.toLowerCase().includes(searchText.toLowerCase())\r\n\t\t);\r\n\t});\r\n\tuseEffect(() => {\r\n\t\tsetSwitchStates(models.reduce((acc, org) => {\r\n\t\t\tacc[org.OrganizationId] = org.IsActive;\r\n\t\t\treturn acc;\r\n\t\t}, {} as { [key: string]: boolean }));\r\n\t}, [models])\r\n\tconst [showPopup, setShowPopup] = useState(false)\r\n\tconst [showeditPopup, setShowEditPopup] = useState(false);\r\n\tconst [showSubscribe, setShowSubscribe] = useState(false)\r\n\tconst [showDeletePopup, setShowDeletePopup] = useState(false);\r\n\tconst [showDeactivatePopup, setShowDeactivatePopup] = useState(false);\r\n\tconst [showActivatePopup, setShowActivatePopup] = useState(false);\r\n\tconst [optionsModel, setOptionsModel] = useState<Organization[]>([]);\r\n\tconst navigate = useNavigate();\r\n\tconst openPopup = () => {\r\n\t\tsetShowPopup(true)\r\n\t}\r\n\tconst openSubscribe = (number:any) => {\r\n\t\tsetOrgIdEdit(number.organizationid);\r\n\t\tsetShowSubscribe(true);\r\n\t}\r\n\tconst closeSubscribe = () => {\r\n\t\tsetShowSubscribe(false)\r\n\t}\r\n\tconst CustomToolbar: React.FC<any> = () => {\r\n\t\tconst [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)\r\n\r\n\t\tconst handleExportMenuClick = (event: React.MouseEvent<HTMLButtonElement>) => {\r\n\t\t\tsetAnchorEl(event.currentTarget)\r\n\t\t}\r\n\r\n\t\tconst handleExportMenuClose = () => {\r\n\t\t\tsetAnchorEl(null)\r\n\t\t}\r\n\r\n\t\tconst handleDownloadExcelClick = () => {\r\n\t\t\thandleExportMenuClose()\r\n\t\t}\r\n\r\n\r\n\t\treturn (\r\n\t\t\t<div>\r\n\r\n\t\t\t\t{/* <Button\r\n\t\t\t\t\taria-controls=\"export-menu\"\r\n\t\t\t\t\taria-haspopup=\"true\"\r\n\t\t\t\t\tonClick={handleExportMenuClick}\r\n\t\t\t\t\tstyle={{ marginLeft: \"10px\" }}\r\n\t\t\t\t\tstartIcon={<SaveAltIcon />}\r\n\t\t\t\t>\r\n\t\t\t\t\tExport\r\n\t\t\t\t</Button> */}\r\n\t\t\t\t{/* <Menu\r\n\t\t\t\t\tid=\"export-menu\"\r\n\t\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\t\tkeepMounted\r\n\t\t\t\t\topen={Boolean(anchorEl)}\r\n\t\t\t\t\tonClose={handleExportMenuClose}\r\n\t\t\t\t>\r\n\t\t\t\t\t<MenuItem\r\n\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\thandleDownloadExcel()\r\n\t\t\t\t\t\t\thandleExportMenuClose()\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\tDownload Excel\r\n\t\t\t\t\t</MenuItem>\r\n\t\t\t\t</Menu> */}\r\n\t\t\t</div>\r\n\t\t)\r\n\t}\r\n\r\n\r\n\tconst handleSwitchChange = (organizationid: any, newStatus: boolean) => {\r\n\t\tsetSwitchStates(prevStates => ({\r\n\t\t\t...prevStates,\r\n\t\t\t[organizationid]: newStatus\r\n\t\t}));\r\n\t};\r\n\r\n\tconst handleActivateClick = async () => {\r\n\t\tif (idDelete !== null) {\r\n\t\t\tawait activateOrganization(idDelete, checkedOne, setShowActivatePopup, setModels, setLoading);\r\n\t\t\thandleSwitchChange(idDelete, true); \r\n\t\t\tawait getOrganizations(setModels, setLoading, skip, top, setTotalcount, sortModel, filters);\r\n\r\n\t\t\tsetShowActivatePopup(false);\r\n\t\t\tsetSnackbarMessage(\"Organization Activated successfully\");\r\n\t\t\tsetSnackbarSeverity(\"success\");\r\n\t\t\tsetSnackbarOpen(true);\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tsetSnackbarOpen(false);\r\n\t\t\t}, 2000);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleDeactivateClick = async () => {\r\n\t\tif (idDelete !== null) {\r\n\t\t\tawait deactivateOrganization(idDelete, checkedOne, setShowDeactivatePopup, setModels, setLoading);\r\n\t\t\thandleSwitchChange(idDelete, false); \r\n\t\t\tawait getOrganizations(setModels, setLoading, skip, top, setTotalcount, sortModel, filters);\r\n\r\n\t\t\tsetShowDeactivatePopup(false);\r\n\t\t\tsetSnackbarMessage(\"Organization Deactivated successfully\");\r\n\t\t\tsetSnackbarSeverity(\"success\");\r\n\t\t\tsetSnackbarOpen(true);\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tsetSnackbarOpen(false);\r\n\t\t\t}, 2000);\r\n\t\t}\r\n\t};\r\n\tlet logoFileName = '';\r\n\tconst handleChange = (e: any) => {\r\n\t\tconst { name, value, checked } = e.target;\r\n\t\tswitch (name) {\r\n\t\t\tcase 'Name':\r\n\t\t\t\tsetOrganizationName(value);\r\n\t\t\t\tvalidateOrganizationName(value)\r\n\t\t\t\tbreak;\r\n\t\t\tcase 'isRTL':\r\n\t\t\t\tsetIsRTL(checked);\r\n\t\t\t\tbreak;\r\n\t\t\tcase 'Type':\r\n\t\t\t\tsetType(value);\r\n\t\t\t\tbreak;\r\n\t\t\tcase 'Plan Type':\r\n\t\t\t\tsetPlanType(value);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tdefault:\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\t};\r\n\r\n\r\n\tconst updateCurrentDate = (format: any) => {\r\n\t\tconst date = new Date();\r\n\t\tlet formattedDate = \"\";\r\n\r\n\t\tswitch (format) {\r\n\t\t\tcase \"yyyy-MM-dd\":\r\n\t\t\t\tformattedDate = date.toISOString().split(\"T\")[0];\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"dd-MM-yyyy\":\r\n\t\t\t\tformattedDate = `${String(date.getDate()).padStart(2, \"0\")}-${String(date.getMonth() + 1).padStart(2, \"0\")}-${date.getFullYear()}`;\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"MM-dd-yyyy\":\r\n\t\t\t\tformattedDate = `${String(date.getMonth() + 1).padStart(2, \"0\")}-${String(date.getDate()).padStart(2, \"0\")}-${date.getFullYear()}`;\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"dd/mm/yyyy\":\r\n\t\t\t\tformattedDate = `${String(date.getDate()).padStart(2, \"0\")}/${String(date.getMonth() + 1).padStart(2, \"0\")}/${date.getFullYear()}`;\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"mm/dd/yyyy\":\r\n\t\t\t\tformattedDate = `${String(date.getMonth() + 1).padStart(2, \"0\")}/${String(date.getDate()).padStart(2, \"0\")}/${date.getFullYear()}`;\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"yyyy/mm/dd\":\r\n\t\t\t\tformattedDate = `${date.getFullYear()}/${String(date.getMonth() + 1).padStart(2, \"0\")}/${String(date.getDate()).padStart(2, \"0\")}`;\r\n\t\t\t\tbreak;\r\n\t\t\tdefault:\r\n\t\t\t\tformattedDate = date.toISOString().split(\"T\")[0];\r\n\t\t}\r\n\r\n\t\tsetCurrentDate(formattedDate);\r\n\t};\r\n\r\n\r\n\tuseEffect(() => {\r\n\t\tupdateCurrentDate(dateFormat);\r\n\t}, []);\r\n\tconst validateOrganizationName = (name: string) => {\r\n\t\tsetOrgNameError('');\r\n\t\tconst trimmedName = name.trim();\r\n\t\tif (trimmedName === '') {\r\n\t\t\tsetOrgNameError('Organization Name is required');\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tif ((trimmedName.length < 5 || trimmedName.length > 50) && (!/^[a-zA-Z0-9 ]+$/.test(trimmedName))) {\r\n\t\t\tsetOrgNameError('Organization Name must be between 5 and 50 characters.Special characters are not allowed');\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tif (trimmedName.length < 5 || trimmedName.length > 50) {\r\n\t\t\tsetOrgNameError('Organization Name must be between 5 and 50 characters');\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tif (!/^[a-zA-Z0-9 ]+$/.test(trimmedName)) {\r\n\t\t\tsetOrgNameError('Special characters are not allowed');\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tconst isDuplicateName = modelsData.some(org => org.Name === trimmedName);\r\n\t\tif (isDuplicateName) {\r\n\t\t\tsetOrgNameError('Organization Name already exists');\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tsetOrgNameError('');\r\n\t};\r\n\r\n\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetAnchorEl(null);\r\n\t\tsetShowDeletePopup(false);\r\n\t\tsetOrganizationName(\"\");\r\n\t\tsetLogoFile(null);\r\n\t\tsetShowPopup(false);\r\n\t\tsetShowEditPopup(false);\r\n\t\tsetShowSubscribe(false);\r\n\t\tsetOrgNameError('');\r\n\t\tsetLogoError(\"\");\r\n\t};\r\n\r\n\tconst toBase64 = (file: File): Promise<string> => {\r\n\t\treturn new Promise((resolve, reject) => {\r\n\t\t\tconst reader = new FileReader();\r\n\t\t\treader.readAsDataURL(file);\r\n\t\t\treader.onload = () => resolve(reader.result as string);\r\n\t\t\treader.onerror = error => reject(error);\r\n\t\t});\r\n\t};\r\n\tconst handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tif (event.target.files && event.target.files[0]) {\r\n\t\t\tconst file = event.target.files[0];\r\n\t\t\tsetLogoFile(file);\r\n\t\t\tsetLogoUrl(URL.createObjectURL(file));\r\n\t\t\tlogoFileName = file.name;\r\n\t\t\tsetLogoError('')\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleSubmitOrganization = async (event: any) => {\r\n\t\tevent.preventDefault();\r\n\t\tsetOrgNameError('');\r\n\t\tsetDateFormatError('');\r\n\t\tsetTypeError('');\r\n\t\tsetLogoError('');\r\n\t\tlet hasError = false;\r\n\t\tconst trimmedNameNew = organizationName.trim();\r\n\t\tvalidateOrganizationName(trimmedNameNew);\r\n\r\n\t\tif (orgNameError) {\r\n\t\t\thasError = true;\r\n\t\t}\r\n\r\n\t\t// if (!dateFormat) {\r\n\t\t// \tsetDateFormatError('Date format is required.');\r\n\t\t// \thasError = true;\r\n\t\t// }\r\n\r\n\t\tif (!type) {\r\n\t\t\tsetTypeError('Type is required.');\r\n\t\t\thasError = true;\r\n\t\t}\r\n\r\n\t\tif (!planType) {\r\n\t\t\tsetPlanType(\"Plan is Requried\");\r\n\t\t\thasError = true;\r\n\t\t}\r\n\r\n\t\t// if (!logoFile) {\r\n\t\t// \tsetLogoError('Logo is required');\r\n\t\t// \thasError = true;\r\n\t\t// } else {\r\n\t\t// \tsetLogoError('');\r\n\t\t// \tlogoFileName = logoFile.name; \r\n\t\t// }\r\n\r\n\t\tif (hasError) {\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tconst reqObj = {\r\n\t\t\tName: organizationName,\r\n\t\t\tLogo: logoFileName, \r\n\t\t\tTimeZone: selectedTimezone,\r\n\t\t\tDateFormat: dateFormat,\r\n\t\t\tType: type,\r\n\t\t\tRTL: isRTL,\r\n\t\t\tPlan: planType,\r\n\t\t};\r\n\t\tcreateOrganization(\r\n\t\t\tsetLoading,\r\n\t\t\tsetShowPopup,\r\n\t\t\tsetModels,\r\n\t\t\tsetShowEditPopup,\r\n\t\t\treqObj,\r\n\t\t\tsetSnackbarMessage,\r\n\t\t\tsetSnackbarSeverity,\r\n\t\t\tsetSnackbarOpen,\r\n\t\t\t(organizationId: string) => {\r\n\t\t\t\tsetOrgNameError('');\r\n\t\t\t\tsetOrganizationName('');\r\n\t\t\t\tsetLogoFile(null);\r\n\t\t\t\tsetLogoError('');\r\n\t\t\t\tconst redirectUrl = `/superadmin/${organizationId}/createadmin`;\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tnavigate(redirectUrl);\r\n\t\t\t\t}, 3000);\r\n\t\t\t}\r\n\t\t);\r\n\t};\r\n\r\n\tconst updateOrganizationDetails = async (orgDetails:any)=>{\r\n\t\tawait updateOrganization(\r\n\t\t\tsetLoading,\r\n\t\t\tsetModels,\r\n\t\t\tsetShowEditPopup,\r\n\t\t\torgDetails,\r\n\t\t\tsetSnackbarMessage,\r\n\t\t\tsetSnackbarSeverity,\r\n\t\t\tsetSnackbarOpen,\r\n\t\t\t() => {\r\n\t\t\t  setShowEditPopup(false);\r\n\t\t\t  getOrganizations(setModelsandOptions, setLoading, skip, top, setTotalcount, sortModel, []);\r\n\t\t\t  fetchOrganizations(setModelsData, setLoading);\r\n\t\t\t}\r\n\t\t);\r\n\t\tsetShowSubscribe(false);\r\n\t\tsetShowEditPopup(false);\r\n\t}\r\n\r\n\tconst validateLogo = () => {\r\n\t\tif (!logoFile) {\r\n\t\t\tsetLogoError('Logo is required');\r\n\t\t} else {\r\n\t\t\tsetLogoError('');\r\n\t\t}\r\n\t};\r\n\tconst [columnMenuApi, setColumnMenuApi] = useState<any>(null); // Store the API instance\r\n\r\n\tconst handleColumnMenuClose = () => {\r\n\t\tif (columnMenuApi) {\r\n\t\t\tcolumnMenuApi.hideColumnMenu(); // Close the column menu\r\n\t\t}\r\n\t};\r\n\tconst isFormValid = !orgNameError ;\r\n\treturn (\r\n\t\t<div>\r\n\t\t\t<div className=\"qadpt-head\">\r\n\t\t\t\t\t\t<div className=\"qadpt-title-sec\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-title\">Organization List</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div className=\"qadpt-right-part\">\r\n\t\t\t\t<button\r\n\t\t\t\t\tonClick={openPopup}\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-memberButton\"\r\n\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<i className=\"fal fa-add-plus\"></i>\r\n\t\t\t\t\t\t\t\t<span>Create Organization</span>\r\n\t\t\t\t</button>\r\n\t\t\t</div>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t{loading ? (\r\n\t\t\t\t <div className=\"Loaderstyles\">\r\n\t\t\t\t <img\r\n\t\t\t\t src={loader}\r\n\t\t\t\t\t   alt=\"Spinner\"\r\n\t\t\t\t\t   className=\"LoaderSpinnerStyles\"\r\n\t\t\t\t />\r\n\t\t\t  </div>\r\n\t\t\t) : (\r\n\t\t\t\t\t<div>\r\n\r\n\t\t\t\t<DataGrid className=\"qadpt-org-grd\"\r\n//   sx={{\r\n//     borderColor: \"black\",\r\n//     \"& .MuiDataGrid-columnHeaders\": {\r\n//       backgroundImage: \"linear-gradient(to right, rgb(30, 138, 201), rgba(62, 200, 241, 0.7))\",\r\n//     },\r\n//     \"& .MuiDataGrid-columnHeaderTitle\": {\r\n//       fontWeight: \"bold\",\r\n//       color: \"white\"\r\n//     },\r\n//     \"& .MuiDataGrid-columnHeader\": {\r\n//       backgroundImage: \"linear-gradient(to right, rgb(30, 138, 201), rgba(62, 200, 241, 0.7))\",\r\n//       borderBottom: \"2px solid #ddd\",\r\n//       color: \"white\",\r\n//     },\r\n//     \"& .MuiDataGrid-columnHeader--alignLeft\": {\r\n//       backgroundImage: \"linear-gradient(to right, rgb(30, 138, 201), rgba(62, 200, 241, 0.7))\",\r\n//       color: \"white\",\r\n//     },\r\n//     \"& .MuiDataGrid-cell\": {\r\n//       borderBottom: \"2px solid #ddd\",\r\n//       borderRight: \"2px solid #ddd\",\r\n//     },\r\n//     \"& .MuiDataGrid-columnHeader, .MuiDataGrid-cell\": {\r\n//       borderRight: \"2px solid #ddd\",\r\n//     },\r\n//     \"& .MuiDataGrid-row\": {\r\n//       \"&:last-child .MuiDataGrid-cell\": {\r\n//         borderBottom: \"none\",\r\n//       },\r\n//     },\r\n//     \"& .MuiDataGrid-menuIconButton\": {\r\n//       visibility: \"visible\",\r\n//       opacity: 1,\r\n//     },\r\n//   }}\r\n\t\t\t\t\t\trows={models}\r\n\t\t\t\t\t\tcolumns={columns}\r\n\t\t\t\t\t\tgetRowId={(row) => row.OrganizationId}\r\n\t\t\t\t\t\tpaginationModel={paginationModel}\r\n\t\t\t\t\t\tonPaginationModelChange={(model) => {\r\n\t\t\t\t\t\t\tsetPaginationModel(model);\r\n\t\t\t\t\t\t\tsetLoading(false);\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\tpagination\r\n\t\t\t\t\t\tpaginationMode=\"server\"\r\n\t\t\t\t\t\trowCount={totalcount}\r\n\t\t\t\t\t\tpageSizeOptions={[15, 25, 50, 100]}\r\n\t\t\t\t\t\tlocaleText={{\r\n\t\t\t\t\t\t\tMuiTablePagination: {\r\n\t\t\t\t\t\t\t\tlabelRowsPerPage: \"Records Per Page\",\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\tdisableColumnSelector\r\n\t\t\t\t\t\tloading={loading}\r\n\t\t\t\t\t\tdisableRowSelectionOnClick={false}\r\n\t\t\t\t\t\tsortModel={sortModel}\r\n\t\t\t\t\t\tonSortModelChange={handleSortModelChange}\r\n\t\t\t\t\t\tslots={{\r\n\t\t\t\t\t\t\tcolumnMenu: (menuProps) => {\r\n\t\t\t\t\t\t\t\tif (menuProps.colDef.field === \"Name\" || menuProps.colDef.field === \"Type\") {\r\n\t\t\t\t\t\t\t\t\treturn (\r\n\t\t\t\t\t\t\t\t\t\t<OrganizationCustomColumnMenu\r\n\t\t\t\t\t\t\t\t\t\t\tkey={modelsData.map(m => m.OrganizationId).join(\",\")}\r\n\t\t\t\t\t\t\t\t\t\t\tcolumn={menuProps.colDef.field}\r\n\t\t\t\t\t\t\t\t\t\t\tsetModels={setModels}\r\n\t\t\t\t\t\t\t\t\t\t\tsetLoading={setLoading}\r\n\t\t\t\t\t\t\t\t\t\t\tskip={skip}\r\n\t\t\t\t\t\t\t\t\t\t\ttop={top}\r\n\t\t\t\t\t\t\t\t\t\t\tOrganizationId={organizationId}\r\n\t\t\t\t\t\t\t\t\t\t\tsortModel={sortModel}\r\n\t\t\t\t\t\t\t\t\t\t\tsetTotalcount={setTotalcount}\r\n\t\t\t\t\t\t\t\t\t\t\torderByFields={orderByFields}\r\n\t\t\t\t\t\t\t\t\t\t\tfilters={filters}\r\n\t\t\t\t\t\t\t\t\t\t\tmodels={models}\r\n\t\t\t\t\t\t\t\t\t\t\tmodelsData={modelsData}\r\n\t\t\t\t\t\t\t\t\t\t\tsetFilters={setFilters}\r\n\t\t\t\t\t\t\t\t\t\t\t{...menuProps}\r\n\t\t\t\t\t\t\t\t\t\t\toptionsModel={optionsModel}\r\n\t\t\t\t\t\t\t\t\t\t\toptions={modelsData.map((model: any) => model[menuProps.colDef.field] || \"\")}\r\n\t\t\t\t\t\t\t\t\t\t\tonSearch={handleSearch}\r\n\t\t\t\t\t\t\t\t\t\t\thideMenu={menuProps.hideMenu}\r\n\t\t\t\t\t\t\t\t\t\t\tpaginationModel={paginationModel}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\treturn null;\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t}}\r\n\r\n\t\t\t\t\t/>\r\n\r\n\r\n\t\t\t\t</div>\r\n\t\t\t)}\r\n\r\n\r\n\r\n\t\t\t{showPopup ? (\r\n\t\t\t\t<div className=\"user-popup\">\r\n\t\t\t\t\t<div className='qadpt-header'>\r\n\t\t\t\t\t\t<span>Create Organization</span>\r\n\t\t\t\t\t\t<svg\r\n\t\t\t\t\t\t\tonClick={() => handleClose()}\r\n\t\t\t\t\t\t\tclassName=\"close-icon\"\r\n\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\r\n\t\t\t\t\t\t\tx=\"0px\"\r\n\t\t\t\t\t\t\ty=\"0px\"\r\n\t\t\t\t\t\t\twidth=\"24\"\r\n\t\t\t\t\t\t\theight=\"24\"\r\n\t\t\t\t\t\t\tviewBox=\"0 0 50 50\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<path d=\"M 7.71875 6.28125 L 6.28125 7.71875 L 23.5625 25 L 6.28125 42.28125 L 7.71875 43.71875 L 25 26.4375 L 42.28125 43.71875 L 43.71875 42.28125 L 26.4375 25 L 43.71875 7.71875 L 42.28125 6.28125 L 25 23.5625 Z\"></path>\r\n\t\t\t\t\t\t</svg>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div className=\"qadpt-usrform\">\r\n\t\t\t\t\t\t<form onSubmit={handleSubmitOrganization}>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-txtfld\">\r\n\t\t\t\t\t<label htmlFor=\"organizationname\" className=\"qadpt-txtlabel\">Organization Name*</label>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\tclassName={`qadpt-txtinp ${orgNameError ? \"error-input\" : \"\"}`}\r\n\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\t\tname=\"Name\"\r\n\t\t\t\t\t\t\tvalue={organizationName}\r\n\t\t\t\t\t\t\t\t\tonChange={handleChange}\r\n\t\t\t\t\t\t// onFocus={() => validateOrganizationName(organizationName)}\r\n\t\t\t\t\t\t></input>\r\n\t\t\t\t\t\t{orgNameError && <span className=\"error\">{orgNameError}</span>}\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t{/* <div className=\"qadpt-txtfld\">\r\n\t\t\t\t\t\t<label htmlFor=\"logo\" className=\"qadpt-txtlabel\"> Logo*</label>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\tclassName={logoError ? \"error-input\" : \"qadpt-txtinp\"}\r\n\t\t\t\t\t\t\ttype=\"file\"\r\n\t\t\t\t\t\t\tname=\"logo\"\r\n\t\t\t\t\t\t\taccept=\"image/*\"\r\n\t\t\t\t\t\t\tonChange={handleFileChange} */}\r\n\t\t\t\t\t\t{/* // onFocus={validateLogo} // Validate on focus */}\r\n\t\t\t\t\t\t{/* ></input> */}\r\n\t\t\t\t\t\t{/* {logoError && <span className=\"error\">{logoError}</span>} */}\r\n\r\n\r\n\r\n\t\t\t\t\t\t{/* {logoUrl && (\r\n                <div>\r\n                    <img src={logoUrl} alt=\"Logo\" style={{ marginTop: 10, maxWidth: '200px', maxHeight: '200px' }} />\r\n                </div>\r\n            )} */}\r\n\t\t\t\t\t\t{logoError && <span className=\"error\">{logoError}</span>}\r\n\t\t\t\t\t\t{/* </div> */}\r\n\t\t\t\t\t{/* <div className=\"qadpt-txtfld\">\r\n\t\t\t\t\t\r\n\t\t\t\t\t<label htmlFor=\"timezone\" className=\"qadpt-txtlabel\">Timezone*</label>\r\n\r\n\t\t\t\t\t\t<select\r\n\r\n\t\t\t\t\t\t\tvalue={selectedTimezone}\r\n\t\t\t\t\t\t\t\t\tonChange={handleTimezoneChange}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-txtinp\"\r\n\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{timezones.map((timezone: any) => (\r\n\t\t\t\t\t\t\t\t<option key={timezone.Id} value={timezone.Id}>\r\n\t\t\t\t\t\t\t\t\t{timezone.DisplayName}\r\n\t\t\t\t\t\t\t\t</option>\r\n\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t</select>\r\n\t\t\t\t\t\t{timezoneError && <span className=\"error\">{timezoneError}</span>}\r\n\t\t\t\t\t</div> */}\r\n\t\t\t\t\t{/* <div>\r\n\t\t\t\t\t  <label className='class-labels'>RTL</label>\r\n          <FormGroup>\r\n            <FormControlLabel control={<Switch />} label=\"disabled\" />\r\n          </FormGroup>\r\n        </div> */}\r\n\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t{/* <div className=\"qadpt-txtfld\">\r\n\t\t\t\t\t<label htmlFor=\"dateFormat\" className=\"qadpt-txtlabel\">Date Format*</label>\r\n\t\t\t\t\t\t<select\r\n\t\t\t\t\t\t\tid=\"dateFormat\"\r\n\t\t\t\t\t\t\tname=\"DateFormat\"\r\n\t\t\t\t\t\t\tvalue={dateFormat}\r\n\t\t\t\t\t\t\tonChange={handleChangeDate}\r\n\t\t\t\t\t\t\tclassName={dateFormatError ? \"error-input\" : \"qadpt-txtinp\"}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<option value=\"dd-MM-yyyy\">dd-MM-yyyy</option>\r\n\t\t\t\t\t\t\t<option value=\"MM-dd-yyyy\">MM-dd-yyyy</option>\r\n\t\t\t\t\t\t\t<option value=\"yyyy-MM-dd\">yyyy-MM-dd</option>\r\n\t\t\t\t\t\t\t<option value=\"dd/mm/yyyy\">dd/mm/yyyy</option>\r\n\t\t\t\t\t\t\t<option value=\"mm/dd/yyyy\">mm/dd/yyyy</option>\r\n\t\t\t\t\t\t\t<option value=\"yyyy/mm/dd\">yyyy/mm/dd</option>\r\n\t\t\t\t\t\t\tAdd more options as needed\r\n\t\t\t\t\t\t</select>\r\n\r\n\t\t\t\t\t\t{dateFormatError && <span className=\"error\">{dateFormatError}</span>}\r\n\t\t\t\t\t</div> */}\r\n\r\n\t\t\t\t\t\r\n\t\t\t\t\t<div className=\"qadpt-txtfld\">\r\n          <label htmlFor=\"type\" className=\"qadpt-txtlabel\">Type* </label>\r\n\t\t\t\t\t\t<select\r\n\t\t\t\t\t\t\tid=\"type\"\r\n\t\t\t\t\t\t\tname=\"Type\"\r\n\t\t\t\t\t\t\tvalue={type}\r\n\t\t\t\t\t\t\tonChange={handleChange}\r\n\t\t\t\t\t\t\tclassName={typeError ? \"error-input\" : \"qadpt-txtinp\"}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<option value=\"Client\">Client</option>\r\n\t\t\t\t\t\t\t<option value=\"Testing\">Testing</option>\r\n\t\t\t\t\t\t\t<option value=\"POC\">POC</option>\r\n\t\t\t\t\t\t\t<option value=\"Prospects\">Prospects</option>\r\n\t\t\t\t\t\t</select>\r\n\t\t\t\t\t\t{typeError && <span className=\"error\">{typeError}</span>}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-txtfld\">\r\n          <label htmlFor=\"type\" className=\"qadpt-txtlabel\">Plan </label>\r\n\t\t\t\t\t\t<select\r\n\t\t\t\t\t\t\tid=\"plan\"\r\n\t\t\t\t\t\t\tname=\"Plan Type\"\r\n\t\t\t\t\t\t\tvalue={planType}\r\n\t\t\t\t\t\t\tonChange={handleChange}\r\n\t\t\t\t\t\t\tclassName={planTypeError ? \"error-input\" : \"qadpt-txtinp\"}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<option value=\"Free Trail\">Free Trail</option>\r\n\t\t\t\t\t\t\t<option value=\"Basic\">Basic</option>\r\n\t\t\t\t\t\t\t<option value=\"Advanced\">Advanced</option>\r\n\t\t\t\t\t\t</select>\r\n\t\t\t\t\t\t{planTypeError && <span className=\"error\">{planTypeError}</span>}\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t{/* <div style={{ marginTop: \"14px\", marginBottom: \"9px\" }}>\r\n\t\t\t\t\t\t\t<span style={{ marginRight: \"90px\", marginTop: \"5px\" }}>RTL</span>\r\n\t\t\t\t\t\t\t<label className=\"switch\">\r\n\t\t\t\t\t\t\t<input type=\"checkbox\"  />\r\n\t\t\t\t\t\t\t\t<span className=\"slider round\" style={{height:27,width:53}}></span>\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t</div>  */}\r\n\t\t\t\t\t <div className=\"qadpt-txtfld qadpt-switch\">\r\n\t\t\t\t\t\t<span  className=\"qadpt-txtlabel\">RTL </span>\r\n\t\t\t\t\t\t<FormControlLabel\r\n\t\t\t\t\tcontrol={\r\n\r\n\t\t\t\t\t\t<Switch\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\tid=\"isRTL\"\r\n\t\t\t\t\t\t\t\tname=\"isRTL\"\r\n\t\t\t\t\t\t\t\tchecked={isRTL}\r\n\t\t\t\t\t\t\t\tonChange={handleChange}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t}\r\n\t\t\t\t\tlabel=\"\"\r\n/>\r\n\t\t\t\t\t\r\n\r\n\t\t\t\t\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</form>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t<div className=\"qadpt-button\">\r\n\t\t\t\t\t<button\r\n\t\t\t\t\t\t\tonClick={handleSubmitOrganization}\r\n\t\t\t\t\t\t\tclassName={isFormValid ? \"qadpt-enab\" : \"qadpt-disab\"}\r\n\t\t\t\t\t\t\tdisabled={!isFormValid}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\tSave\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t) : (\r\n\t\t\t\t\"\"\r\n\t\t\t)}\r\n\r\n\t\t\t{showeditPopup ? (\r\n\t\t\t\t<EditOrganization\r\n\t\t\t\t\tmodelsData={modelsData}\r\n\t\t\t\t\tsetModelsData={setModelsData}\r\n\t\t\t\t\tshowEditPopup={showeditPopup}\r\n\t\t\t\t\tsetShowEditPopup={setShowEditPopup}\r\n\t\t\t\t\tOrganizationId={orgIdEdit}\r\n\t\t\t\t\tsortModel={sortModel}\r\n\t\t\t\t\tfilters={filters}\r\n\r\n\t\t\t\t\tgetOrganizations={getOrganizations}\r\n\t\t\t\t\tsetModels={setModels}\r\n\t\t\t\t\tmodels={models}\r\n\t\t\t\t\tsetLoading={setLoading}\r\n\t\t\t\t\thandleClose={handleClose}\r\n\t\t\t\t\tskip={skip}\r\n\t\t\t\t\ttop={top}\r\n\t\t\t\t\tsetTotalcount={setTotalcount}\r\n\t\t\t\t\tupdateOrganizationDetails={updateOrganizationDetails}\r\n\t\t\t\t/>\r\n\t\t\t) : (\r\n\t\t\t\t\"\"\r\n\t\t\t)}\r\n\r\n\t\t\t{showActivatePopup ? (\r\n                 <div className=\"qadpt-modal-overlay\">\r\n\t\t\t\t <div className=\"qadpt-usrconfirm-popup qadpt-success\">\r\n\t\t\t\t\t\t  <div>\r\n\t\t\t   <div className=\"qadpt-icon\">\r\n\t\t\t\t <CorporateFareIcon/>\r\n\t\t\t\t\t\t\t  </div>\r\n\t\t\t\t\t\t\t  </div>\r\n\t\t\t   <div className=\"qadpt-popup-title\">Activate Account</div>\r\n\t\t\t   <div className=\"qadpt-warning\">\r\n\t\t\t\t\t\t\tAre you sure you want to Activate Organization\r\n\t\t\t\t\t\t</div>\r\n\t\t\t   <div className=\"qadpt-buttons\">\r\n\t\t\t\t <button\r\n\t\t\t\t   onClick={() => setShowActivatePopup(false)}\r\n\t\t\t\t   className=\"qadpt-cancel-button\"\r\n\t\t\t\t >\r\n\t\t\t\t   Cancel\r\n\t\t\t\t </button>\r\n\t\t\t\t <button\r\n\t\t\t\t   onClick={handleActivateClick}\r\n\t\t\t\t   className=\"qadpt-conform-button\"\r\n\t\t\t\t   type=\"submit\"\r\n\t\t\t\t >\r\n\t\t\t\t   Activate\r\n\t\t\t\t </button>\r\n\t\t\t   </div>\r\n\t\t\t </div>\r\n\t\t   </div>\r\n\t\t\t) : (\r\n\t\t\t\t\"\"\r\n\t\t\t)}\r\n\r\n\r\n\t\t\t{showDeactivatePopup ? (\r\n           <div className=\"qadpt-modal-overlay\">\r\n\t\t\t\t\t<div className=\"qadpt-usrconfirm-popup qadpt-danger\">\r\n\t\t\t\t\t\t<div>\r\n\t\t\t <div className=\"qadpt-icon\">\r\n\t\t\t   <WarningAmberIcon/>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t <div className=\"qadpt-popup-title\">Deactivate Account</div>\r\n\t\t\t <div className=\"qadpt-warning\">\r\n\t\t\t   Are you sure you want to deactivate this organization? This action cannot be undone.\r\n\t\t\t </div>\r\n\t\t\t <div className=\"qadpt-buttons\">\r\n\t\t\t   <button\r\n\t\t\t\t onClick={() => setShowDeactivatePopup(false)}\r\n\t\t\t\t className=\"qadpt-cancel-button\"\r\n\t\t\t   >\r\n\t\t\t\t Cancel\r\n\t\t\t   </button>\r\n\t\t\t   <button\r\n\t\t\t\t onClick={handleDeactivateClick}\r\n\t\t\t\t className=\"qadpt-conform-button\"\r\n\t\t\t\t type=\"submit\"\r\n\t\t\t   >\r\n\t\t\t\t Deactivate\r\n\t\t\t   </button>\r\n\t\t\t </div>\r\n\t\t   </div>\r\n\t\t </div>\r\n\t\t \r\n\t\t   \r\n\t\t\t) : (\r\n\t\t\t\t\"\"\r\n\t\t\t)}\r\n\r\n\r\n\r\n\t\t\t<Snackbar\r\n\t\t\t\topen={snackbarOpen}\r\n\t\t\t\tautoHideDuration={6000}\r\n\t\t\t\tonClose={handleSnackbarClose}\r\n\t\t\t\tanchorOrigin={{ vertical: \"top\", horizontal: \"center\" }}\r\n\t\t\t\tsx={{ zIndex: 10000, marginTop: 4 }} // Optionally adjust the zIndex if needed\r\n\t\t\t>\r\n\t\t\t\t<Alert\r\n\t\t\t\t\tonClose={handleSnackbarClose}\r\n\t\t\t\t\tseverity={snackbarSeverity}\r\n\t\t\t\t\tsx={{ width: \"100%\" }}\r\n\t\t\t\t>\r\n\t\t\t\t\t{snackbarMessage}\r\n\t\t\t\t</Alert>\r\n\t\t\t</Snackbar>\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t\t\t{showSubscribe ? (\r\n\t\t\t\t<EditSubscription\r\n\t\t\t\t\tcloseSubscribe={closeSubscribe}\r\n\t\t\t\t\tshowSubscribe={showSubscribe}\r\n\t\t\t\t\torganizationId={orgIdEdit}\r\n\t\t\t\t\tsetSnackbarOpen={setSnackbarOpen}\r\n\t\t\t\t\tsetSnackbarSeverity={setSnackbarSeverity}\r\n\t\t\t\t\tsetSnackbarMessage={setSnackbarMessage}\r\n\t\t\t\t\tupdateOrganizationDetails={updateOrganizationDetails}\r\n\t\t\t\t\t\r\n\t\t\t\t/>\r\n\t\t\t) : (\r\n\t\t\t\t\"\"\r\n\t\t\t)}\r\n\t\t</div>\r\n\t)\r\n}\r\n\r\nexport default OrganizationList"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAElD,OAAOC,MAAM,MAAM,sBAAsB;AAGzC,OAAO,iBAAiB;AAExB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,QAAQ,QAAoB,eAAe;AACpD;AACA,SACCC,QAAQ,QAOF,kBAAkB;AACzB,SAAiCC,gBAAgB,EAAEC,UAAU,EAAEC,OAAO,EAA0BC,KAAK,QAAQ,eAAe;AAC5H,OAAOC,QAAQ,MAAM,0BAA0B;AAI/C,OAAOC,MAAM,MAAM,yBAAyB;AAI5C;;AAEA,OAAOC,gBAAgB,MAAM,oBAAoB;AAGjD,SAASC,oBAAoB,EAAEC,kBAAkB,EAAEC,sBAAsB,EAAEC,kBAAkB,EAAEC,gBAAgB,EAAuBC,kBAAkB,QAAQ,oCAAoC;AAKpM,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,OAAOC,4BAA4B,MAAM,gCAAgC;AACzE,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,iBAAiB,MAAM,mCAAmC;AACjE,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD;AACA;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAOA,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAAA,IAAAC,EAAA,GAAAC,YAAA;EACxC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,GAAG,CAAC;EACjD,MAAM,CAACqC,MAAM,EAAEC,SAAS,CAAC,GAAGtC,QAAQ,CAAiB,EAAE,CAAC;EACxD,MAAM,CAACuC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2C,QAAQ,EAAEC,WAAW,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC6C,SAAS,EAAEC,YAAY,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC+C,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAc,IAAI,CAAC;EAC3D,MAAM,CAACiD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACmD,YAAY,EAAEC,eAAe,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqD,QAAQ,EAAEC,WAAW,CAAC,GAAGtD,QAAQ,CAAqB,IAAI,CAAC;EAClE,MAAM,CAACuD,SAAS,EAAEC,YAAY,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyD,MAAM,EAAEC,SAAS,CAAC,GAAG1D,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAC;EACzC,MAAM,CAAC2D,cAAc,EAAEC,iBAAiB,CAAC,GAAG5D,QAAQ,CAAgB,IAAI,CAAC;EACzE,MAAM,CAAC6D,QAAQ,EAAEC,WAAW,CAAC,GAAG9D,QAAQ,CAAiB,IAAI,CAAC;EAC9D,MAAM,CAAC+D,aAAa,EAAEC,gBAAgB,CAAC,GAAGhE,QAAQ,CAAS,EAAE,CAAC;EAC9D,MAAM,CAACiE,KAAK,EAAEC,QAAQ,CAAC,GAAGlE,QAAQ,CAAS,EAAE,CAAC;EAS9C,MAAM,CAACmE,OAAO,EAAEC,UAAU,CAAC,GAAGpE,QAAQ,CAAW,EAAE,CAAC;EACpD,MAAM,CAACqE,aAAa,EAAEC,gBAAgB,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACuE,KAAK,EAAEC,QAAQ,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EACzC,MAAM,CAACyE,UAAU,EAAEC,aAAa,CAAC,GAAG1E,QAAQ,CAAC,YAAY,CAAC;EAC1D,MAAM,CAAC2E,IAAI,EAAEC,OAAO,CAAC,GAAG5E,QAAQ,CAAC,QAAQ,CAAC;EAC1C,MAAM,CAAC6E,IAAI,EAAEC,OAAO,CAAC,GAAG9E,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAAC+E,OAAO,EAAEC,UAAU,CAAC,GAAGhF,QAAQ,CAAgB,IAAI,CAAC;EAE3D,MAAM,CAACiF,aAAa,EAAEC,gBAAgB,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmF,eAAe,EAAEC,kBAAkB,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACqF,WAAW,EAAEC,cAAc,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuF,SAAS,EAAEC,YAAY,CAAC,GAAGxF,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1F,QAAQ,CAAS,cAAc,CAAC,CAAC,CAAC;EAClF,MAAM,CAAC2F,YAAY,EAAEC,eAAe,CAAC,GAAG5F,QAAQ,CAA6B,CAAC,CAAC,CAAC;EAChF,MAAM,CAAC6F,YAAY,EAAEC,eAAe,CAAC,GAAG9F,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC+F,eAAe,EAAEC,kBAAkB,CAAC,GAAGhG,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACiG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlG,QAAQ,CAAsB,SAAS,CAAC;EACxF,MAAM,CAACmG,IAAI,EAAEC,OAAO,CAAC,GAAGpG,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACqG,GAAG,EAAEC,MAAM,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACuG,UAAU,EAAEC,aAAa,CAAC,GAAGxG,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACyG,eAAe,EAAEC,kBAAkB,CAAC,GAAG1G,QAAQ,CAAsB;IAC3E2G,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG9G,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAAC+G,QAAQ,EAAEC,WAAW,CAAC,GAAGhH,QAAQ,CAAC,YAAY,CAAC;EACtD,MAAM,CAACiH,aAAa,EAAEC,gBAAgB,CAAC,GAAGlH,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAACmH,WAAW,EAAEC,cAAc,CAAC,GAAGpH,QAAQ,CAAS,EAAE,CAAC;EAC1D,MAAMqH,eAAe,GAAIC,QAAkB,IAAK;IAC/CZ,kBAAkB,CAAGa,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEZ,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;IACrDG,wBAAwB,CAACQ,QAAQ,CAAC;EACnC,CAAC;EAGD,MAAME,oBAAoB,GAAIC,KAAU,IAAK;IAC5C/B,mBAAmB,CAAC+B,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACxC,CAAC;EACD,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IACjC9B,eAAe,CAAC,KAAK,CAAC;EACvB,CAAC;EACD,MAAM,CAAC+B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9H,QAAQ,CAAiB,IAAI,CAAC;EAClF,MAAM,CAAC+H,UAAU,EAAEC,aAAa,CAAC,GAAGhI,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACiI,QAAQ,EAAEC,WAAW,CAAC,GAAGlI,QAAQ,CAAU,IAAI,CAAC;EACvD,MAAMmI,OAAO,GAAIC,MAAW,IAAK;IAEhC,MAAMC,eAAe,GAAI1D,IAAS,IAAK,CAEvC,CAAC;IACD,MAAM2D,iBAAiB,GAAIC,cAAsB,IAAK;MACrDC,kBAAkB,CAAC,IAAI,CAAC;MACxB5F,WAAW,CAAC2F,cAAc,CAAC;IAC5B,CAAC;IAGD,MAAME,iBAAiB,GAAGA,CAACF,cAAsB,EAAEG,aAAsB,KAAK;MAC7EC,oBAAoB,CAAC,KAAK,CAAC;MAC3BC,sBAAsB,CAAC,KAAK,CAAC;MAC7BhG,WAAW,CAAC2F,cAAc,CAAC;MAC3BT,qBAAqB,CAAC,CAACY,aAAa,CAAC;MAErC,IAAI,CAACA,aAAa,EAAE;QACnBC,oBAAoB,CAAC,IAAI,CAAC;MAC3B,CAAC,MAAM;QACNC,sBAAsB,CAAC,IAAI,CAAC;MAC7B;IACD,CAAC;IACD,MAAMC,eAAe,GAAIN,cAAsB,IAAK;MACnDO,gBAAgB,CAAC,IAAI,CAAC;MACtBhG,YAAY,CAACyF,cAAc,CAAC;IAC7B,CAAC;IACD,MAAMQ,WAAW,GAAGA,CAAA,KAAM;MACzBzF,WAAW,CAAC,IAAI,CAAC;MACjBkF,kBAAkB,CAAC,KAAK,CAAC;IAC1B,CAAC;IACD,oBACC9G,OAAA;MAAAsH,QAAA,gBAeGtH,OAAA,CAAClB,OAAO;QAACyI,KAAK,EAAC,mBAAmB;QAACC,KAAK;QAAAF,QAAA,eACvCtH,OAAA,CAACnB,UAAU;UACJ4I,SAAS,EAAC,eAAe;UAC/B,cAAW,MAAM;UACjBC,OAAO,EAAEA,CAAA,KAAMP,eAAe,CAACT,MAAM,CAACG,cAAc,CAAE;UAAAS,QAAA,eAEhDtH,OAAA,CAAChB,QAAQ;YAAA2I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eACT9H,OAAA,CAAClB,OAAO;QAACyI,KAAK,EAAC,mBAAmB;QAACC,KAAK;QAAAF,QAAA,eACtCtH,OAAA,CAACnB,UAAU;UACJ4I,SAAS,EAAC,eAAe;UAC/B,cAAW,MAAM;UAChBC,OAAO,EAAEA,CAAA,KAAMK,aAAa,CAACrB,MAAM,CAAE;UAAAY,QAAA,eAE/BtH,OAAA,CAACH,eAAe;YAAA8H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAIZ9H,OAAA,CAACnB,UAAU;QAAC4I,SAAS,EAAC,eAAe;QAAAH,QAAA,eACvCtH,OAAA;UAAOyH,SAAS,EAAC,eAAe;UAACO,KAAK,EAAE;YAAEC,MAAM,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAU,CAAE;UAAAZ,QAAA,gBACvEtH,OAAA;YACEiD,IAAI,EAAC,UAAU;YACfkF,OAAO,EAAElE,YAAY,CAACyC,MAAM,CAACG,cAAc,CAAC,IAAI,KAAM;YACtDuB,QAAQ,EAAEA,CAAA,KACRrB,iBAAiB,CAACL,MAAM,CAACG,cAAc,EAAE5C,YAAY,CAACyC,MAAM,CAACG,cAAc,CAAC,CAC7E;YACDwB,IAAI,EAAE,UAAU3B,MAAM,CAACG,cAAc;UAAG;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACF9H,OAAA;YAAMyH,SAAS,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAQL,CAAC;EAER,CAAC;EAED,MAAMQ,gBAAgB,GAAIvC,KAAU,IAAK;IACxC,MAAMwC,SAAS,GAAGxC,KAAK,CAACC,MAAM,CAACC,KAAK;IACpCjD,aAAa,CAACuF,SAAS,CAAC;IACxBC,iBAAiB,CAACD,SAAS,CAAC;EAC7B,CAAC;EACD,MAAME,YAAY,GAAIxC,KAAe,IAAK;IACzC;IACA;IACAvD,UAAU,CAACuD,KAAK,IAAIA,KAAK,CAACyC,MAAM,GAAG,CAAC;MAAEC,SAAS,EAAE,MAAM;MAAEC,WAAW,EAAE,QAAQ;MAAEC,SAAS,EAAE,IAAI;MAAEC,KAAK,EAAE7C,KAAK,CAAC8C,IAAI,CAAC,GAAG,CAAC;MAAEC,aAAa,EAAE;IAAM,CAAC,CAAC,GAAG,EAAE,CAAC;EACvJ,CAAC;EACD,MAAMC,OAAqB,GAAG,CAE5B;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE,mBAAmB;IAAEC,KAAK,EAAE,GAAG;IAACC,IAAI,EAAC,CAAC;IAACC,UAAU,EAAC;EAAM,CAAC;EACtF;EACA;IAAEJ,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE,mBAAmB;IAAEC,KAAK,EAAE,GAAG;IAACC,IAAI,EAAC,CAAC;IAACC,UAAU,EAAC,KAAK;IAAEC,iBAAiB,EAAE;EAAI,CAAC,EAC/G;IACCL,KAAK,EAAE,aAAa;IACpBC,UAAU,EAAE,cAAc;IAC1BC,KAAK,EAAE,GAAG;IACVC,IAAI,EAAE,CAAC;IACPC,UAAU,EAAE,KAAK;IACjBE,QAAQ,EAAE,IAAI;IACdD,iBAAiB,EAAE,IAAI;IACvBE,UAAU,EAAGC,MAAM,IAAK;MACvB,MAAMC,UAAU,GAAGD,MAAM,CAACE,GAAG,CAACC,WAAW;MACzC,MAAMC,SAAS,GAAGJ,MAAM,CAACE,GAAG,CAACG,UAMhB;MACb,OAAOtK,cAAc,CAACkK,UAAU,EAAEG,SAAS,CAAC;IAC7C;EACD,CAAC,EAGA;IAAEZ,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE,MAAM;IAAEC,KAAK,EAAE,GAAG;IAACC,IAAI,EAAC,CAAC;IAACC,UAAU,EAAC;EAAK,CAAC,EACzE;IACCJ,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrBK,QAAQ,EAAE,KAAK;IACfD,iBAAiB,EAAE,IAAI;IACvBH,KAAK,EAAE,GAAG;IACVC,IAAI,EAAE,CAAC;IACPI,UAAU,EAAGC,MAAM,IAAK;MACvB,MAAM7C,cAAc,GAAG6C,MAAM,CAACE,GAAG,CAACI,cAAc,IAAI,KAAK;MACzD9H,iBAAiB,CAAC2E,cAAc,CAAC;MACjC,MAAMoD,IAAI,GAAGP,MAAM,CAACE,GAAG,CAACC,WAAW,IAAI,KAAK;MAC5CnE,cAAc,CAACuE,IAAI,CAAC;MAIpB,oBACCjK,OAAA;QAAAsH,QAAA,eACCtH,OAAA,CAACyG,OAAO;UAEPI,cAAc,EAAEA,cAAe;UAC/BqD,KAAK,EAAER,MAAM,CAACE,GAAG,CAACO;QAAG;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAER;EACD,CAAC,CACD;EAED,MAAM,CAACsC,aAAa,EAAEC,gBAAgB,CAAC,GAAG/L,QAAQ,CAAC,SAAS,CAAC;EAE7D,MAAMgM,iBAAiB,GAAIrE,KAAU,IAAK;IACzCoE,gBAAgB,CAACpE,KAAK,CAAC;EACxB,CAAC;EACD,MAAMsE,YAAY,GAAIC,OAAe,IAAK,CAC1C,CAAC;EACD,MAAMC,gBAAgB,GAAIC,WAAmB,IAAK,CAClD,CAAC;EACD,MAAMC,aAAa,GAAI1H,IAAS,IAAK;IACpCjC,aAAa,CAACiC,IAAI,CAAC;IACnB2H,gBAAgB,CAAC,IAAI,CAAC;EACvB,CAAC;EACD,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxM,QAAQ,CAAiB,EAAE,CAAC;EACtE,MAAM,CAACyM,SAAS,EAAEC,YAAY,CAAC,GAAG1M,QAAQ,CAAgB,CAAC;IAAE4K,KAAK,EAAE,aAAa;IAAE+B,IAAI,EAAE;EAAO,CAAC,CAAC,CAAC;EACnG,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG7M,QAAQ,CAAC;IAAE8M,KAAK,EAAE;EAAG,CAAC,CAAC;EAC7D,MAAMC,mBAAmB,GAAIC,YAAiB,IAAK;IAClD1K,SAAS,CAAC0K,YAAY,CAAC;IACvBC,eAAe,CAACD,YAAY,CAAC;EAC9B,CAAC;EACD/M,SAAS,CAAC,MAAM;IACf,MAAMiN,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC7B,MAAMC,SAAS,GAAG1G,eAAe,CAACG,QAAQ,IAAI,EAAE;MAChD,MAAMwG,UAAU,GAAG3G,eAAe,CAACE,IAAI,GAAGwG,SAAS;MACnD,MAAME,KAAK,GAAGD,UAAU;MACxB,MAAM/G,GAAG,GAAGI,eAAe,CAACG,QAAQ;MACpCR,OAAO,CAACiH,KAAK,CAAC;MACd/G,MAAM,CAACD,GAAG,CAAC;MACX,IAAIlC,OAAO,CAACiG,MAAM,GAAG,CAAC,EAAE;QACvB,MAAMnJ,gBAAgB,CAACqB,SAAS,EAAEE,UAAU,EAAE6K,KAAK,EAAEhH,GAAG,EAAEG,aAAa,EAAEiG,SAAS,EAAEtI,OAAO,CAAC;MAC7F,CAAC,MAAM;QACN,MAAMlD,gBAAgB,CAAC8L,mBAAmB,EAAEvK,UAAU,EAAE6K,KAAK,EAAEhH,GAAG,EAAEG,aAAa,EAAEiG,SAAS,EAAEtI,OAAO,CAAC;MACvG;IACD,CAAC;IACD,IAAI0C,qBAAqB,EAAE;MAC1BC,wBAAwB,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC,MAAM;MACNoG,SAAS,CAAC,CAAC;IACZ;IACA;EACD,CAAC,EAAE,CAACzG,eAAe,EAAEtC,OAAO,EAAEsI,SAAS,CAAC,CAAC;EAEzCxM,SAAS,CAAC,MAAM;IACf,MAAMiN,SAAS,GAAG,MAAAA,CAAA,KAAY;MAE7B,MAAMC,SAAS,GAAG1G,eAAe,CAACG,QAAQ,IAAI,EAAE;MAChD,MAAMwG,UAAU,GAAG3G,eAAe,CAACE,IAAI,GAAGwG,SAAS;MACnD,MAAME,KAAK,GAAGD,UAAU;MACxB,MAAM/G,GAAG,GAAGI,eAAe,CAACG,QAAQ;MACpCR,OAAO,CAACiH,KAAK,CAAC;MACd/G,MAAM,CAACD,GAAG,CAAC;MAEX,IAAIlC,OAAO,CAACiG,MAAM,GAAG,CAAC,EAAE;QACvB,MAAMnJ,gBAAgB,CAACqB,SAAS,EAAEE,UAAU,EAAE6K,KAAK,EAAEhH,GAAG,EAAEG,aAAa,EAAEiG,SAAS,EAAEtI,OAAO,CAAC;MAC7F,CAAC,MAAM;QACN,MAAMlD,gBAAgB,CAAC8L,mBAAmB,EAAEvK,UAAU,EAAE6K,KAAK,EAAEhH,GAAG,EAAEG,aAAa,EAAEiG,SAAS,EAAEtI,OAAO,CAAC;MACvG;IACD,CAAC;IACD+I,SAAS,CAAC,CAAC;EACZ,CAAC,EAAC,CAACT,SAAS,CAAC,CAAC;EAEdxM,SAAS,CAAC,MAAM;IACfoH,eAAe,CAAClD,OAAO,CAACiG,MAAM,GAAC,CAAC,CAAC;EAClC,CAAC,EAAE,CAACjG,OAAO,CAAC,CAAC;EAEb,MAAM,CAACmJ,UAAU,EAAEC,aAAa,CAAC,GAAGvN,QAAQ,CAAiB,EAAE,CAAC;EAEhEC,SAAS,CAAC,MAAM;IACf,MAAMuN,YAAY,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACH,MAAMxM,kBAAkB,CAACuM,aAAa,EAAE/K,UAAU,CAAC;MACpD,CAAC,CAAC,OAAOiL,KAAK,EAAE,CAChB,CAAC,SAAS;QACTjL,UAAU,CAAC,KAAK,CAAC;MAClB;IACD,CAAC;IAEDgL,YAAY,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,qBAAqB,GAAIC,KAAU,IAAK;IAC7CjB,YAAY,CAACiB,KAAK,CAAC;IACnBnL,UAAU,CAAC,KAAK,CAAC;EAClB,CAAC;EACD,MAAMoL,YAAY,GAAGvL,MAAM,CAACwL,MAAM,CAAEvC,GAAQ,IAAK;IAChD,MAAMwC,OAAO,GAAGxC,GAAG,CAACyC,OAAO,IAAI,EAAE;IACjC,MAAMC,aAAa,GAAG1C,GAAG,CAAC2C,aAAa,IAAI,EAAE;IAC7C,MAAMC,QAAQ,GAAG5C,GAAG,CAAC6C,QAAQ,IAAI,EAAE;IAEnC,OACCL,OAAO,CAACM,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpM,UAAU,CAACmM,WAAW,CAAC,CAAC,CAAC,IACxDJ,aAAa,CAACI,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpM,UAAU,CAACmM,WAAW,CAAC,CAAC,CAAC,IAC9DF,QAAQ,CAACE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpM,UAAU,CAACmM,WAAW,CAAC,CAAC,CAAC;EAE3D,CAAC,CAAC;EACFnO,SAAS,CAAC,MAAM;IACf2F,eAAe,CAACvD,MAAM,CAACiM,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;MAC3CD,GAAG,CAACC,GAAG,CAAC9C,cAAc,CAAC,GAAG8C,GAAG,CAACvG,QAAQ;MACtC,OAAOsG,GAAG;IACX,CAAC,EAAE,CAAC,CAA+B,CAAC,CAAC;EACtC,CAAC,EAAE,CAAClM,MAAM,CAAC,CAAC;EACZ,MAAM,CAACoM,SAAS,EAAEC,YAAY,CAAC,GAAG1O,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC2O,aAAa,EAAE7F,gBAAgB,CAAC,GAAG9I,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4O,aAAa,EAAEtC,gBAAgB,CAAC,GAAGtM,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC6O,eAAe,EAAErG,kBAAkB,CAAC,GAAGxI,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC8O,mBAAmB,EAAElG,sBAAsB,CAAC,GAAG5I,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC+O,iBAAiB,EAAEpG,oBAAoB,CAAC,GAAG3I,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACgP,YAAY,EAAE/B,eAAe,CAAC,GAAGjN,QAAQ,CAAiB,EAAE,CAAC;EACpE,MAAMiP,QAAQ,GAAG9O,WAAW,CAAC,CAAC;EAC9B,MAAM+O,SAAS,GAAGA,CAAA,KAAM;IACvBR,YAAY,CAAC,IAAI,CAAC;EACnB,CAAC;EACD,MAAMjF,aAAa,GAAIrB,MAAU,IAAK;IACrCtF,YAAY,CAACsF,MAAM,CAACG,cAAc,CAAC;IACnC+D,gBAAgB,CAAC,IAAI,CAAC;EACvB,CAAC;EACD,MAAM6C,cAAc,GAAGA,CAAA,KAAM;IAC5B7C,gBAAgB,CAAC,KAAK,CAAC;EACxB,CAAC;EACD,MAAM8C,aAA4B,GAAGA,CAAA,KAAM;IAAAvN,EAAA;IAC1C,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAGtD,QAAQ,CAAqB,IAAI,CAAC;IAElE,MAAMqP,qBAAqB,GAAI5H,KAA0C,IAAK;MAC7EnE,WAAW,CAACmE,KAAK,CAAC6H,aAAa,CAAC;IACjC,CAAC;IAED,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;MACnCjM,WAAW,CAAC,IAAI,CAAC;IAClB,CAAC;IAED,MAAMkM,wBAAwB,GAAGA,CAAA,KAAM;MACtCD,qBAAqB,CAAC,CAAC;IACxB,CAAC;IAGD,oBACC7N,OAAA;MAAA2H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OA2BK,CAAC;EAER,CAAC;EAAA3H,EAAA,CA9CKuN,aAA4B;EAiDlC,MAAMK,kBAAkB,GAAGA,CAAClH,cAAmB,EAAEmH,SAAkB,KAAK;IACvE9J,eAAe,CAAC+J,UAAU,KAAK;MAC9B,GAAGA,UAAU;MACb,CAACpH,cAAc,GAAGmH;IACnB,CAAC,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAIjN,QAAQ,KAAK,IAAI,EAAE;MACtB,MAAM9B,oBAAoB,CAAC8B,QAAQ,EAAEoF,UAAU,EAAEY,oBAAoB,EAAErG,SAAS,EAAEE,UAAU,CAAC;MAC7FiN,kBAAkB,CAAC9M,QAAQ,EAAE,IAAI,CAAC;MAClC,MAAM1B,gBAAgB,CAACqB,SAAS,EAAEE,UAAU,EAAE2D,IAAI,EAAEE,GAAG,EAAEG,aAAa,EAAEiG,SAAS,EAAEtI,OAAO,CAAC;MAE3FwE,oBAAoB,CAAC,KAAK,CAAC;MAC3B3C,kBAAkB,CAAC,qCAAqC,CAAC;MACzDE,mBAAmB,CAAC,SAAS,CAAC;MAC9BJ,eAAe,CAAC,IAAI,CAAC;MACrB+J,UAAU,CAAC,MAAM;QAChB/J,eAAe,CAAC,KAAK,CAAC;MACvB,CAAC,EAAE,IAAI,CAAC;IACT;EACD,CAAC;EAED,MAAMgK,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACzC,IAAInN,QAAQ,KAAK,IAAI,EAAE;MACtB,MAAM5B,sBAAsB,CAAC4B,QAAQ,EAAEoF,UAAU,EAAEa,sBAAsB,EAAEtG,SAAS,EAAEE,UAAU,CAAC;MACjGiN,kBAAkB,CAAC9M,QAAQ,EAAE,KAAK,CAAC;MACnC,MAAM1B,gBAAgB,CAACqB,SAAS,EAAEE,UAAU,EAAE2D,IAAI,EAAEE,GAAG,EAAEG,aAAa,EAAEiG,SAAS,EAAEtI,OAAO,CAAC;MAE3FyE,sBAAsB,CAAC,KAAK,CAAC;MAC7B5C,kBAAkB,CAAC,uCAAuC,CAAC;MAC3DE,mBAAmB,CAAC,SAAS,CAAC;MAC9BJ,eAAe,CAAC,IAAI,CAAC;MACrB+J,UAAU,CAAC,MAAM;QAChB/J,eAAe,CAAC,KAAK,CAAC;MACvB,CAAC,EAAE,IAAI,CAAC;IACT;EACD,CAAC;EACD,IAAIiK,YAAY,GAAG,EAAE;EACrB,MAAMC,YAAY,GAAIC,CAAM,IAAK;IAChC,MAAM;MAAElG,IAAI;MAAEpC,KAAK;MAAEkC;IAAQ,CAAC,GAAGoG,CAAC,CAACvI,MAAM;IACzC,QAAQqC,IAAI;MACX,KAAK,MAAM;QACV7G,mBAAmB,CAACyE,KAAK,CAAC;QAC1BuI,wBAAwB,CAACvI,KAAK,CAAC;QAC/B;MACD,KAAK,OAAO;QACXnD,QAAQ,CAACqF,OAAO,CAAC;QACjB;MACD,KAAK,MAAM;QACVjF,OAAO,CAAC+C,KAAK,CAAC;QACd;MACD,KAAK,WAAW;QACfX,WAAW,CAACW,KAAK,CAAC;QAClB;MAED;QACC;IACF;EACD,CAAC;EAGD,MAAMuC,iBAAiB,GAAIiG,MAAW,IAAK;IAC1C,MAAMxE,IAAI,GAAG,IAAIyE,IAAI,CAAC,CAAC;IACvB,IAAIC,aAAa,GAAG,EAAE;IAEtB,QAAQF,MAAM;MACb,KAAK,YAAY;QAChBE,aAAa,GAAG1E,IAAI,CAAC2E,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChD;MACD,KAAK,YAAY;QAChBF,aAAa,GAAG,GAAGG,MAAM,CAAC7E,IAAI,CAAC8E,OAAO,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,MAAM,CAAC7E,IAAI,CAACgF,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI/E,IAAI,CAACiF,WAAW,CAAC,CAAC,EAAE;QAClI;MACD,KAAK,YAAY;QAChBP,aAAa,GAAG,GAAGG,MAAM,CAAC7E,IAAI,CAACgF,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,MAAM,CAAC7E,IAAI,CAAC8E,OAAO,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI/E,IAAI,CAACiF,WAAW,CAAC,CAAC,EAAE;QAClI;MACD,KAAK,YAAY;QAChBP,aAAa,GAAG,GAAGG,MAAM,CAAC7E,IAAI,CAAC8E,OAAO,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,MAAM,CAAC7E,IAAI,CAACgF,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI/E,IAAI,CAACiF,WAAW,CAAC,CAAC,EAAE;QAClI;MACD,KAAK,YAAY;QAChBP,aAAa,GAAG,GAAGG,MAAM,CAAC7E,IAAI,CAACgF,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,MAAM,CAAC7E,IAAI,CAAC8E,OAAO,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI/E,IAAI,CAACiF,WAAW,CAAC,CAAC,EAAE;QAClI;MACD,KAAK,YAAY;QAChBP,aAAa,GAAG,GAAG1E,IAAI,CAACiF,WAAW,CAAC,CAAC,IAAIJ,MAAM,CAAC7E,IAAI,CAACgF,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,MAAM,CAAC7E,IAAI,CAAC8E,OAAO,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;QAClI;MACD;QACCL,aAAa,GAAG1E,IAAI,CAAC2E,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAClD;IAEAjL,cAAc,CAAC+K,aAAa,CAAC;EAC9B,CAAC;EAGDpQ,SAAS,CAAC,MAAM;IACfiK,iBAAiB,CAACzF,UAAU,CAAC;EAC9B,CAAC,EAAE,EAAE,CAAC;EACN,MAAMyL,wBAAwB,GAAInG,IAAY,IAAK;IAClD3G,eAAe,CAAC,EAAE,CAAC;IACnB,MAAMyN,WAAW,GAAG9G,IAAI,CAAC+G,IAAI,CAAC,CAAC;IAC/B,IAAID,WAAW,KAAK,EAAE,EAAE;MACvBzN,eAAe,CAAC,+BAA+B,CAAC;MAChD;IACD;IACA,IAAI,CAACyN,WAAW,CAACzG,MAAM,GAAG,CAAC,IAAIyG,WAAW,CAACzG,MAAM,GAAG,EAAE,KAAM,CAAC,iBAAiB,CAAC2G,IAAI,CAACF,WAAW,CAAE,EAAE;MAClGzN,eAAe,CAAC,0FAA0F,CAAC;MAC3G;IACD;IACA,IAAIyN,WAAW,CAACzG,MAAM,GAAG,CAAC,IAAIyG,WAAW,CAACzG,MAAM,GAAG,EAAE,EAAE;MACtDhH,eAAe,CAAC,uDAAuD,CAAC;MACxE;IACD;IACA,IAAI,CAAC,iBAAiB,CAAC2N,IAAI,CAACF,WAAW,CAAC,EAAE;MACzCzN,eAAe,CAAC,oCAAoC,CAAC;MACrD;IACD;IACA,MAAM4N,eAAe,GAAG1D,UAAU,CAAC2D,IAAI,CAACzC,GAAG,IAAIA,GAAG,CAAC0C,IAAI,KAAKL,WAAW,CAAC;IACxE,IAAIG,eAAe,EAAE;MACpB5N,eAAe,CAAC,kCAAkC,CAAC;MACnD;IACD;IACAA,eAAe,CAAC,EAAE,CAAC;EACpB,CAAC;EAID,MAAM2F,WAAW,GAAGA,CAAA,KAAM;IACzBzF,WAAW,CAAC,IAAI,CAAC;IACjBkF,kBAAkB,CAAC,KAAK,CAAC;IACzBtF,mBAAmB,CAAC,EAAE,CAAC;IACvBF,WAAW,CAAC,IAAI,CAAC;IACjB0L,YAAY,CAAC,KAAK,CAAC;IACnB5F,gBAAgB,CAAC,KAAK,CAAC;IACvBwD,gBAAgB,CAAC,KAAK,CAAC;IACvBlJ,eAAe,CAAC,EAAE,CAAC;IACnBI,YAAY,CAAC,EAAE,CAAC;EACjB,CAAC;EAED,MAAM2N,QAAQ,GAAIC,IAAU,IAAsB;IACjD,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACvC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,aAAa,CAACN,IAAI,CAAC;MAC1BI,MAAM,CAACG,MAAM,GAAG,MAAML,OAAO,CAACE,MAAM,CAACI,MAAgB,CAAC;MACtDJ,MAAM,CAACK,OAAO,GAAGpE,KAAK,IAAI8D,MAAM,CAAC9D,KAAK,CAAC;IACxC,CAAC,CAAC;EACH,CAAC;EACD,MAAMqE,gBAAgB,GAAIrK,KAA0C,IAAK;IACxE,IAAIA,KAAK,CAACC,MAAM,CAACqK,KAAK,IAAItK,KAAK,CAACC,MAAM,CAACqK,KAAK,CAAC,CAAC,CAAC,EAAE;MAChD,MAAMX,IAAI,GAAG3J,KAAK,CAACC,MAAM,CAACqK,KAAK,CAAC,CAAC,CAAC;MAClC/O,WAAW,CAACoO,IAAI,CAAC;MACjBpM,UAAU,CAACgN,GAAG,CAACC,eAAe,CAACb,IAAI,CAAC,CAAC;MACrCrB,YAAY,GAAGqB,IAAI,CAACrH,IAAI;MACxBvG,YAAY,CAAC,EAAE,CAAC;IACjB;EACD,CAAC;EAED,MAAM0O,wBAAwB,GAAG,MAAOzK,KAAU,IAAK;IACtDA,KAAK,CAAC0K,cAAc,CAAC,CAAC;IACtB/O,eAAe,CAAC,EAAE,CAAC;IACnBgC,kBAAkB,CAAC,EAAE,CAAC;IACtBI,YAAY,CAAC,EAAE,CAAC;IAChBhC,YAAY,CAAC,EAAE,CAAC;IAChB,IAAI4O,QAAQ,GAAG,KAAK;IACpB,MAAMC,cAAc,GAAGpP,gBAAgB,CAAC6N,IAAI,CAAC,CAAC;IAC9CZ,wBAAwB,CAACmC,cAAc,CAAC;IAExC,IAAIlP,YAAY,EAAE;MACjBiP,QAAQ,GAAG,IAAI;IAChB;;IAEA;IACA;IACA;IACA;;IAEA,IAAI,CAACzN,IAAI,EAAE;MACVa,YAAY,CAAC,mBAAmB,CAAC;MACjC4M,QAAQ,GAAG,IAAI;IAChB;IAEA,IAAI,CAACrL,QAAQ,EAAE;MACdC,WAAW,CAAC,kBAAkB,CAAC;MAC/BoL,QAAQ,GAAG,IAAI;IAChB;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA,IAAIA,QAAQ,EAAE;MACb;IACD;IACA,MAAME,MAAM,GAAG;MACdpB,IAAI,EAAEjO,gBAAgB;MACtBsP,IAAI,EAAExC,YAAY;MAClByC,QAAQ,EAAE/M,gBAAgB;MAC1BgG,UAAU,EAAEhH,UAAU;MACtBgO,IAAI,EAAE9N,IAAI;MACV+N,GAAG,EAAEnO,KAAK;MACVoO,IAAI,EAAE5L;IACP,CAAC;IACDjG,kBAAkB,CACjB0B,UAAU,EACVkM,YAAY,EACZpM,SAAS,EACTwG,gBAAgB,EAChBwJ,MAAM,EACNtM,kBAAkB,EAClBE,mBAAmB,EACnBJ,eAAe,EACdnC,cAAsB,IAAK;MAC3BP,eAAe,CAAC,EAAE,CAAC;MACnBF,mBAAmB,CAAC,EAAE,CAAC;MACvBF,WAAW,CAAC,IAAI,CAAC;MACjBQ,YAAY,CAAC,EAAE,CAAC;MAChB,MAAMoP,WAAW,GAAG,eAAejP,cAAc,cAAc;MAC/DkM,UAAU,CAAC,MAAM;QAChBZ,QAAQ,CAAC2D,WAAW,CAAC;MACtB,CAAC,EAAE,IAAI,CAAC;IACT,CACD,CAAC;EACF,CAAC;EAED,MAAMC,yBAAyB,GAAG,MAAOC,UAAc,IAAG;IACzD,MAAM5R,kBAAkB,CACvBsB,UAAU,EACVF,SAAS,EACTwG,gBAAgB,EAChBgK,UAAU,EACV9M,kBAAkB,EAClBE,mBAAmB,EACnBJ,eAAe,EACf,MAAM;MACJgD,gBAAgB,CAAC,KAAK,CAAC;MACvB7H,gBAAgB,CAAC8L,mBAAmB,EAAEvK,UAAU,EAAE2D,IAAI,EAAEE,GAAG,EAAEG,aAAa,EAAEiG,SAAS,EAAE,EAAE,CAAC;MAC1FzL,kBAAkB,CAACuM,aAAa,EAAE/K,UAAU,CAAC;IAC/C,CACD,CAAC;IACD8J,gBAAgB,CAAC,KAAK,CAAC;IACvBxD,gBAAgB,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMiK,YAAY,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAAChQ,QAAQ,EAAE;MACdS,YAAY,CAAC,kBAAkB,CAAC;IACjC,CAAC,MAAM;MACNA,YAAY,CAAC,EAAE,CAAC;IACjB;EACD,CAAC;EACD,MAAM,CAACwP,aAAa,EAAEC,gBAAgB,CAAC,GAAGjT,QAAQ,CAAM,IAAI,CAAC,CAAC,CAAC;;EAE/D,MAAMkT,qBAAqB,GAAGA,CAAA,KAAM;IACnC,IAAIF,aAAa,EAAE;MAClBA,aAAa,CAACG,cAAc,CAAC,CAAC,CAAC,CAAC;IACjC;EACD,CAAC;EACD,MAAMC,WAAW,GAAG,CAACjQ,YAAY;EACjC,oBACCzB,OAAA;IAAAsH,QAAA,gBACCtH,OAAA;MAAKyH,SAAS,EAAC,YAAY;MAAAH,QAAA,gBACxBtH,OAAA;QAAKyH,SAAS,EAAC,iBAAiB;QAAAH,QAAA,eAC/BtH,OAAA;UAAKyH,SAAS,EAAC,aAAa;UAAAH,QAAA,EAAC;QAAiB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,eACN9H,OAAA;QAAKyH,SAAS,EAAC,kBAAkB;QAAAH,QAAA,eACnCtH,OAAA;UACC0H,OAAO,EAAE8F,SAAU;UAChB/F,SAAS,EAAC,oBAAoB;UAAAH,QAAA,gBAE9BtH,OAAA;YAAGyH,SAAS,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnC9H,OAAA;YAAAsH,QAAA,EAAM;UAAmB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEC,CAAC,EAEPjH,OAAO,gBACNb,OAAA;MAAKyH,SAAS,EAAC,cAAc;MAAAH,QAAA,eAC7BtH,OAAA;QACA2R,GAAG,EAAE1S,MAAO;QACT2S,GAAG,EAAC,SAAS;QACbnK,SAAS,EAAC;MAAqB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,gBAEN9H,OAAA;MAAAsH,QAAA,eAEDtH,OAAA,CAACrB,QAAQ;QAAC8I,SAAS,EAAC;QACxB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAAA;QACMoK,IAAI,EAAElR,MAAO;QACbsI,OAAO,EAAEA,OAAQ;QACjB6I,QAAQ,EAAGlI,GAAG,IAAKA,GAAG,CAACI,cAAe;QACtCjF,eAAe,EAAEA,eAAgB;QACjCgN,uBAAuB,EAAG9F,KAAK,IAAK;UACnCjH,kBAAkB,CAACiH,KAAK,CAAC;UACzBnL,UAAU,CAAC,KAAK,CAAC;QAClB,CAAE;QACFkR,UAAU;QACVC,cAAc,EAAC,QAAQ;QACvBC,QAAQ,EAAErN,UAAW;QACrBsN,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAE;QACnCC,UAAU,EAAE;UACXC,kBAAkB,EAAE;YACnBC,gBAAgB,EAAE;UACnB;QACD,CAAE;QACFC,qBAAqB;QACrB1R,OAAO,EAAEA,OAAQ;QACjB2R,0BAA0B,EAAE,KAAM;QAClCzH,SAAS,EAAEA,SAAU;QACrB0H,iBAAiB,EAAEzG,qBAAsB;QACzC0G,KAAK,EAAE;UACNC,UAAU,EAAGC,SAAS,IAAK;YAC1B,IAAIA,SAAS,CAACC,MAAM,CAAC3J,KAAK,KAAK,MAAM,IAAI0J,SAAS,CAACC,MAAM,CAAC3J,KAAK,KAAK,MAAM,EAAE;cAC3E,oBACClJ,OAAA,CAACN,4BAA4B;gBAE5BoT,MAAM,EAAEF,SAAS,CAACC,MAAM,CAAC3J,KAAM;gBAC/BtI,SAAS,EAAEA,SAAU;gBACrBE,UAAU,EAAEA,UAAW;gBACvB2D,IAAI,EAAEA,IAAK;gBACXE,GAAG,EAAEA,GAAI;gBACTqF,cAAc,EAAE/H,cAAe;gBAC/B8I,SAAS,EAAEA,SAAU;gBACrBjG,aAAa,EAAEA,aAAc;gBAC7BnC,aAAa,EAAEA,aAAc;gBAC7BF,OAAO,EAAEA,OAAQ;gBACjB9B,MAAM,EAAEA,MAAO;gBACfiL,UAAU,EAAEA,UAAW;gBACvBlJ,UAAU,EAAEA,UAAW;gBAAA,GACnBkQ,SAAS;gBACbtF,YAAY,EAAEA,YAAa;gBAC3ByF,OAAO,EAAEnH,UAAU,CAACoH,GAAG,CAAE/G,KAAU,IAAKA,KAAK,CAAC2G,SAAS,CAACC,MAAM,CAAC3J,KAAK,CAAC,IAAI,EAAE,CAAE;gBAC7E+J,QAAQ,EAAExK,YAAa;gBACvByK,QAAQ,EAAEN,SAAS,CAACM,QAAS;gBAC7BnO,eAAe,EAAEA;cAAgB,GAnB5B6G,UAAU,CAACoH,GAAG,CAACG,CAAC,IAAIA,CAAC,CAACnJ,cAAc,CAAC,CAACjB,IAAI,CAAC,GAAG,CAAC;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoBpD,CAAC;YAEJ;YACA,OAAO,IAAI;UACZ;QACD;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGE,CACL,EAIAiF,SAAS,gBACT/M,OAAA;MAAKyH,SAAS,EAAC,YAAY;MAAAH,QAAA,gBAC1BtH,OAAA;QAAKyH,SAAS,EAAC,cAAc;QAAAH,QAAA,gBAC5BtH,OAAA;UAAAsH,QAAA,EAAM;QAAmB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAChC9H,OAAA;UACC0H,OAAO,EAAEA,CAAA,KAAML,WAAW,CAAC,CAAE;UAC7BI,SAAS,EAAC,YAAY;UACtB2L,KAAK,EAAC,4BAA4B;UAClCC,CAAC,EAAC,KAAK;UACPC,CAAC,EAAC,KAAK;UACPlK,KAAK,EAAC,IAAI;UACVmK,MAAM,EAAC,IAAI;UACXC,OAAO,EAAC,WAAW;UAAAlM,QAAA,eAEnBtH,OAAA;YAAMyT,CAAC,EAAC;UAA+M;YAAA9L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3N,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACN9H,OAAA;QAAKyH,SAAS,EAAC,eAAe;QAAAH,QAAA,eAC7BtH,OAAA;UAAM0T,QAAQ,EAAElD,wBAAyB;UAAAlJ,QAAA,gBACxCtH,OAAA;YAAKyH,SAAS,EAAC,cAAc;YAAAH,QAAA,gBAC/BtH,OAAA;cAAO2T,OAAO,EAAC,kBAAkB;cAAClM,SAAS,EAAC,gBAAgB;cAAAH,QAAA,EAAC;YAAkB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtF9H,OAAA;cACDyH,SAAS,EAAE,gBAAgBhG,YAAY,GAAG,aAAa,GAAG,EAAE,EAAG;cAC/DwB,IAAI,EAAC,MAAM;cACToF,IAAI,EAAC,MAAM;cACXpC,KAAK,EAAE1E,gBAAiB;cACtB6G,QAAQ,EAAEkG;cACb;YAAA;cAAA3G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,EACRrG,YAAY,iBAAIzB,OAAA;cAAMyH,SAAS,EAAC,OAAO;cAAAH,QAAA,EAAE7F;YAAY;cAAAkG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,EAqBLjG,SAAS,iBAAI7B,OAAA;YAAMyH,SAAS,EAAC,OAAO;YAAAH,QAAA,EAAEzF;UAAS;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAmDzD9H,OAAA;YAAKyH,SAAS,EAAC,cAAc;YAAAH,QAAA,gBACxBtH,OAAA;cAAO2T,OAAO,EAAC,MAAM;cAAClM,SAAS,EAAC,gBAAgB;cAAAH,QAAA,EAAC;YAAM;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnE9H,OAAA;cACCmK,EAAE,EAAC,MAAM;cACT9B,IAAI,EAAC,MAAM;cACXpC,KAAK,EAAEhD,IAAK;cACZmF,QAAQ,EAAEkG,YAAa;cACvB7G,SAAS,EAAE5D,SAAS,GAAG,aAAa,GAAG,cAAe;cAAAyD,QAAA,gBAEtDtH,OAAA;gBAAQiG,KAAK,EAAC,QAAQ;gBAAAqB,QAAA,EAAC;cAAM;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtC9H,OAAA;gBAAQiG,KAAK,EAAC,SAAS;gBAAAqB,QAAA,EAAC;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC9H,OAAA;gBAAQiG,KAAK,EAAC,KAAK;gBAAAqB,QAAA,EAAC;cAAG;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChC9H,OAAA;gBAAQiG,KAAK,EAAC,WAAW;gBAAAqB,QAAA,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,EACRjE,SAAS,iBAAI7D,OAAA;cAAMyH,SAAS,EAAC,OAAO;cAAAH,QAAA,EAAEzD;YAAS;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACN9H,OAAA;YAAKyH,SAAS,EAAC,cAAc;YAAAH,QAAA,gBAC1BtH,OAAA;cAAO2T,OAAO,EAAC,MAAM;cAAClM,SAAS,EAAC,gBAAgB;cAAAH,QAAA,EAAC;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClE9H,OAAA;cACCmK,EAAE,EAAC,MAAM;cACT9B,IAAI,EAAC,WAAW;cAChBpC,KAAK,EAAEZ,QAAS;cAChB+C,QAAQ,EAAEkG,YAAa;cACvB7G,SAAS,EAAElC,aAAa,GAAG,aAAa,GAAG,cAAe;cAAA+B,QAAA,gBAE1DtH,OAAA;gBAAQiG,KAAK,EAAC,YAAY;gBAAAqB,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9C9H,OAAA;gBAAQiG,KAAK,EAAC,OAAO;gBAAAqB,QAAA,EAAC;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpC9H,OAAA;gBAAQiG,KAAK,EAAC,UAAU;gBAAAqB,QAAA,EAAC;cAAQ;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,EACRvC,aAAa,iBAAIvF,OAAA;cAAMyH,SAAS,EAAC,OAAO;cAAAH,QAAA,EAAE/B;YAAa;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eASL9H,OAAA;YAAKyH,SAAS,EAAC,2BAA2B;YAAAH,QAAA,gBAC1CtH,OAAA;cAAOyH,SAAS,EAAC,gBAAgB;cAAAH,QAAA,EAAC;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7C9H,OAAA,CAACpB,gBAAgB;cAClBgV,OAAO,eAEN5T,OAAA,CAACxB,MAAM;gBAEL2L,EAAE,EAAC,OAAO;gBACV9B,IAAI,EAAC,OAAO;gBACZF,OAAO,EAAEtF,KAAM;gBACfuF,QAAQ,EAAEkG;cAAa;gBAAA3G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CACF;cACD+L,KAAK,EAAC;YAAE;cAAAlM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIQ,CAAC,eACN9H,OAAA;YAAA2H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACP9H,OAAA;QAAKyH,SAAS,EAAC,cAAc;QAAAH,QAAA,eAC7BtH,OAAA;UACE0H,OAAO,EAAE8I,wBAAyB;UAClC/I,SAAS,EAAEiK,WAAW,GAAG,YAAY,GAAG,aAAc;UACtDoC,QAAQ,EAAE,CAACpC,WAAY;UAAApK,QAAA,EACvB;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,GAEN,EACA,EAEAmF,aAAa,gBACbjN,OAAA,CAACd,gBAAgB;MAChB0M,UAAU,EAAEA,UAAW;MACvBC,aAAa,EAAEA,aAAc;MAC7BkI,aAAa,EAAE9G,aAAc;MAC7B7F,gBAAgB,EAAEA,gBAAiB;MACnC4C,cAAc,EAAE7I,SAAU;MAC1B4J,SAAS,EAAEA,SAAU;MACrBtI,OAAO,EAAEA,OAAQ;MAEjBlD,gBAAgB,EAAEA,gBAAiB;MACnCqB,SAAS,EAAEA,SAAU;MACrBD,MAAM,EAAEA,MAAO;MACfG,UAAU,EAAEA,UAAW;MACvBuG,WAAW,EAAEA,WAAY;MACzB5C,IAAI,EAAEA,IAAK;MACXE,GAAG,EAAEA,GAAI;MACTG,aAAa,EAAEA,aAAc;MAC7BqM,yBAAyB,EAAEA;IAA0B;MAAAxJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrD,CAAC,GAEF,EACA,EAEAuF,iBAAiB,gBACJrN,OAAA;MAAKyH,SAAS,EAAC,qBAAqB;MAAAH,QAAA,eAChDtH,OAAA;QAAKyH,SAAS,EAAC,sCAAsC;QAAAH,QAAA,gBAClDtH,OAAA;UAAAsH,QAAA,eACFtH,OAAA;YAAKyH,SAAS,EAAC,YAAY;YAAAH,QAAA,eAC5BtH,OAAA,CAACJ,iBAAiB;cAAA+H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACT9H,OAAA;UAAKyH,SAAS,EAAC,mBAAmB;UAAAH,QAAA,EAAC;QAAgB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzD9H,OAAA;UAAKyH,SAAS,EAAC,eAAe;UAAAH,QAAA,EAAC;QAE/B;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN9H,OAAA;UAAKyH,SAAS,EAAC,eAAe;UAAAH,QAAA,gBAC/BtH,OAAA;YACE0H,OAAO,EAAEA,CAAA,KAAMT,oBAAoB,CAAC,KAAK,CAAE;YAC3CQ,SAAS,EAAC,qBAAqB;YAAAH,QAAA,EAChC;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9H,OAAA;YACE0H,OAAO,EAAEwG,mBAAoB;YAC7BzG,SAAS,EAAC,sBAAsB;YAChCxE,IAAI,EAAC,QAAQ;YAAAqE,QAAA,EACd;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,GAEP,EACA,EAGAsF,mBAAmB,gBACZpN,OAAA;MAAKyH,SAAS,EAAC,qBAAqB;MAAAH,QAAA,eAC1CtH,OAAA;QAAKyH,SAAS,EAAC,qCAAqC;QAAAH,QAAA,gBACnDtH,OAAA;UAAAsH,QAAA,eACFtH,OAAA;YAAKyH,SAAS,EAAC,YAAY;YAAAH,QAAA,eACzBtH,OAAA,CAACL,gBAAgB;cAAAgI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACT9H,OAAA;UAAKyH,SAAS,EAAC,mBAAmB;UAAAH,QAAA,EAAC;QAAkB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC3D9H,OAAA;UAAKyH,SAAS,EAAC,eAAe;UAAAH,QAAA,EAAC;QAE/B;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN9H,OAAA;UAAKyH,SAAS,EAAC,eAAe;UAAAH,QAAA,gBAC5BtH,OAAA;YACD0H,OAAO,EAAEA,CAAA,KAAMR,sBAAsB,CAAC,KAAK,CAAE;YAC7CO,SAAS,EAAC,qBAAqB;YAAAH,QAAA,EAC7B;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9H,OAAA;YACD0H,OAAO,EAAE0G,qBAAsB;YAC/B3G,SAAS,EAAC,sBAAsB;YAChCxE,IAAI,EAAC,QAAQ;YAAAqE,QAAA,EACX;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,GAIL,EACA,eAID9H,OAAA,CAACtB,QAAQ;MACRsV,IAAI,EAAE7P,YAAa;MACnB8P,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEhO,mBAAoB;MAC7BiO,YAAY,EAAE;QAAEC,QAAQ,EAAE,KAAK;QAAEC,UAAU,EAAE;MAAS,CAAE;MACxDC,EAAE,EAAE;QAAEC,MAAM,EAAE,KAAK;QAAEC,SAAS,EAAE;MAAE,CAAE,CAAC;MAAA;MAAAlN,QAAA,eAErCtH,OAAA,CAACjB,KAAK;QACLmV,OAAO,EAAEhO,mBAAoB;QAC7BuO,QAAQ,EAAElQ,gBAAiB;QAC3B+P,EAAE,EAAE;UAAElL,KAAK,EAAE;QAAO,CAAE;QAAA9B,QAAA,EAErBjD;MAAe;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAaVoF,aAAa,gBACblN,OAAA,CAACF,gBAAgB;MAChB2N,cAAc,EAAEA,cAAe;MAC/BP,aAAa,EAAEA,aAAc;MAC7BjL,cAAc,EAAEd,SAAU;MAC1BiD,eAAe,EAAEA,eAAgB;MACjCI,mBAAmB,EAAEA,mBAAoB;MACzCF,kBAAkB,EAAEA,kBAAmB;MACvC6M,yBAAyB,EAAEA;IAA0B;MAAAxJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAErD,CAAC,GAEF,EACA;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAER,CAAC;AAAA5H,GAAA,CA5kCKD,gBAA0B;EAAA,QAiVdxB,WAAW;AAAA;AAAAiW,EAAA,GAjVvBzU,gBAA0B;AA8kChC,eAAeA,gBAAgB;AAAA,IAAAyU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}