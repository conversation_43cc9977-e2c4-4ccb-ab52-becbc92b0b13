{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptWeb\\\\src\\\\components\\\\common\\\\Home.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { useAuth } from \"../auth/AuthProvider\";\nimport { isSidebarOpen } from \"../adminMenu/sidemenustate\";\nimport jwt_decode from \"jwt-decode\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport { useTranslation } from \"react-i18next\";\nimport { Homepageimg, Wavinghand } from \"../../assets/icons/icons\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nlet OrganizationId;\nlet UserId;\nlet userDetails;\nlet OrgDetails;\nconst Home = () => {\n  _s();\n  const {\n    signOut\n  } = useAuth();\n  const [loginUserDetails, setUserDetails] = useState(null);\n  const [organizationDetails, setOrganizationDetails] = useState(null);\n  const [sidebarOpen, setSidebarOpen] = useState(isSidebarOpen());\n  const [userId, setUserId] = useState(String);\n  const [organizationId] = useState(String);\n  const [loginUserInfo, setLoginUserInfo] = useState(undefined);\n  const location = useLocation();\n  const Navigate = useNavigate();\n  useEffect(() => {\n    // Check if we're in auto-login flow to avoid interfering\n    const isAutoLoginCompleted = sessionStorage.getItem('autoLoginCompleted') === 'true';\n    const urlParams = new URLSearchParams(window.location.search);\n    const hasAutoLoginParams = urlParams.has('token') && urlParams.has('userId') && urlParams.has('orgId');\n    let token = localStorage.getItem(\"access_token\");\n    const userInfo = JSON.parse(localStorage.getItem(\"userInfo\") || '{}');\n    let storedUserDetails = null;\n    if (userInfo['oidc-info'] && userInfo['user'] && userInfo['orgDetails']) {\n      userDetails = JSON.parse(userInfo['user']);\n      OrgDetails = JSON.parse(userInfo['orgDetails']);\n      OrganizationId = OrgDetails.OrganizationId;\n      token = JSON.parse(userInfo['oidc-info']).access_token;\n    } else if (userInfo['user'] && userInfo['orgDetails']) {\n      // Handle auto-login case where we have user data but no oidc-info\n      storedUserDetails = JSON.parse(userInfo['user']);\n      userDetails = storedUserDetails;\n      OrgDetails = JSON.parse(userInfo['orgDetails']);\n      OrganizationId = OrgDetails.OrganizationId;\n    }\n    const lastpath = localStorage.getItem(\"last_path\");\n    if (token) {\n      try {\n        var _userDetails;\n        if (((_userDetails = userDetails) === null || _userDetails === void 0 ? void 0 : _userDetails.UserType) === \"SuperAdmin\") {\n          Navigate(\"/superadmin/organizations\");\n          return;\n        }\n        const loggedinUserInfo = jwt_decode(token);\n        if (lastpath && lastpath != \"\") {\n          localStorage.setItem(\"last_path\", \"\");\n          Navigate(lastpath);\n        }\n      } catch (error) {\n        // If JWT decode fails but we have stored user details (auto-login case), don't sign out\n        if (storedUserDetails || isAutoLoginCompleted) {\n          var _userDetails2;\n          if (((_userDetails2 = userDetails) === null || _userDetails2 === void 0 ? void 0 : _userDetails2.UserType) === \"SuperAdmin\") {\n            Navigate(\"/superadmin/organizations\");\n          } else if (lastpath && lastpath != \"\") {\n            localStorage.setItem(\"last_path\", \"\");\n            Navigate(lastpath);\n          }\n          // For free trial users (Admin) and regular users, stay on home page\n        } else {\n          signOut();\n        }\n      }\n    } else if (storedUserDetails || isAutoLoginCompleted) {\n      var _userDetails3;\n      // Handle auto-login case where we don't have a JWT token but have user details\n      if (((_userDetails3 = userDetails) === null || _userDetails3 === void 0 ? void 0 : _userDetails3.UserType) === \"SuperAdmin\") {\n        Navigate(\"/superadmin/organizations\");\n      } else if (lastpath && lastpath != \"\") {\n        localStorage.setItem(\"last_path\", \"\");\n        Navigate(lastpath);\n      }\n      // For free trial users (Admin) and regular users, stay on home page\n    } else {\n      signOut();\n    }\n  }, [userDetails]);\n  const {\n    t: translate\n  } = useTranslation();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"qadpt-homepg\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-imgsec\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: Homepageimg,\n        alt: \"Homepage Logo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 5\n      }, this), \" \"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 4\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-des\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-msg\",\n        children: translate('Welcome Back,')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 4\n      }, this), userDetails && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-usrnm\",\n        children: [userDetails.FirstName, /*#__PURE__*/_jsxDEV(\"img\", {\n          src: Wavinghand,\n          alt: \"Wavinghand\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-statmsg\",\n        children: [\" \", translate('Ready to engage your users? Start creating easy Interactions with our Services')]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 5\n      }, this), \" \"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 100,\n    columnNumber: 3\n  }, this);\n};\n_s(Home, \"nR2MQslz8iDuosA94ECseVPIosI=\", false, function () {\n  return [useAuth, useLocation, useNavigate, useTranslation];\n});\n_c = Home;\nexport default Home;\nexport { OrganizationId, UserId };\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "isSidebarOpen", "jwt_decode", "useLocation", "useNavigate", "useTranslation", "Homepageimg", "Wavinghand", "jsxDEV", "_jsxDEV", "OrganizationId", "UserId", "userDetails", "OrgDetails", "Home", "_s", "signOut", "loginUserDetails", "setUserDetails", "organizationDetails", "setOrganizationDetails", "sidebarOpen", "setSidebarOpen", "userId", "setUserId", "String", "organizationId", "loginUserInfo", "setLoginUserInfo", "undefined", "location", "Navigate", "isAutoLoginCompleted", "sessionStorage", "getItem", "urlParams", "URLSearchParams", "window", "search", "hasAutoLoginParams", "has", "token", "localStorage", "userInfo", "JSON", "parse", "storedUserDetails", "access_token", "lastpath", "_userDetails", "UserType", "loggedinUserInfo", "setItem", "error", "_userDetails2", "_userDetails3", "t", "translate", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "FirstName", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/components/common/Home.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { useAuth } from \"../auth/AuthProvider\";\r\nimport { isSidebarOpen, subscribe } from \"../adminMenu/sidemenustate\";\r\nimport userManager from \"../auth/UseAuth\";\r\nimport { getOrganizationById } from \"../../services/OrganizationService\";\r\nimport { User } from \"../../models/User\";\r\nimport { Organization } from \"../../models/Organization\";\r\nimport jwt_decode from \"jwt-decode\";\r\nimport { LoginUserInfo } from \"../../models/LoginUserInfo\";\r\nimport { Navigate, useLocation, useNavigate } from \"react-router-dom\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport {\r\n\tHomepageimg,\r\n\tWavinghand,\r\n} from \"../../assets/icons/icons\";\r\nlet OrganizationId: string;\r\nlet UserId: string;\r\nlet userDetails: User;\r\nlet OrgDetails: Organization;\r\n\r\nconst Home: React.FC = () => {\r\n\tconst { signOut} = useAuth();\r\n\tconst [loginUserDetails, setUserDetails] = useState<User | null>(null);\r\n\tconst [organizationDetails, setOrganizationDetails] = useState<Organization | null>(null);\r\n\tconst [sidebarOpen, setSidebarOpen] = useState(isSidebarOpen());\r\n\tconst [userId, setUserId] = useState(String);\r\n\tconst [organizationId] = useState(String);\r\n\tconst [loginUserInfo, setLoginUserInfo] = useState<LoginUserInfo | undefined>(undefined);\r\n\tconst location = useLocation();\r\n\tconst Navigate = useNavigate();\r\n\r\n\tuseEffect(() => {\r\n\t\t// Check if we're in auto-login flow to avoid interfering\r\n\t\tconst isAutoLoginCompleted = sessionStorage.getItem('autoLoginCompleted') === 'true';\r\n\t\tconst urlParams = new URLSearchParams(window.location.search);\r\n\t\tconst hasAutoLoginParams = urlParams.has('token') && urlParams.has('userId') && urlParams.has('orgId');\r\n\r\n\t\tlet token = localStorage.getItem(\"access_token\");\r\n\t\tconst userInfo = JSON.parse(localStorage.getItem(\"userInfo\") || '{}');\r\n\t\tlet storedUserDetails = null;\r\n\r\n\t\tif (userInfo['oidc-info'] && userInfo['user'] && userInfo['orgDetails']) {\r\n\t\t\tuserDetails = JSON.parse(userInfo['user'])\r\n\t\t\tOrgDetails = JSON.parse(userInfo['orgDetails'])\r\n\t\t\tOrganizationId = OrgDetails.OrganizationId;\r\n\t\t\ttoken = JSON.parse(userInfo['oidc-info']).access_token;\r\n\t\t} else if (userInfo['user'] && userInfo['orgDetails']) {\r\n\t\t\t// Handle auto-login case where we have user data but no oidc-info\r\n\t\t\tstoredUserDetails = JSON.parse(userInfo['user']);\r\n\t\t\tuserDetails = storedUserDetails;\r\n\t\t\tOrgDetails = JSON.parse(userInfo['orgDetails']);\r\n\t\t\tOrganizationId = OrgDetails.OrganizationId;\r\n\t\t}\r\n\r\n\t\tconst lastpath = localStorage.getItem(\"last_path\");\r\n\r\n\t\tif (token) {\r\n\t\t\ttry {\r\n\t\t\t\tif (userDetails?.UserType === \"SuperAdmin\") {\r\n\t\t\t\t\tNavigate(\"/superadmin/organizations\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconst loggedinUserInfo = jwt_decode<LoginUserInfo>(token);\r\n\r\n\t\t\t\tif (lastpath && lastpath != \"\") {\r\n\t\t\t\t\tlocalStorage.setItem(\"last_path\",\"\");\r\n\t\t\t\t\tNavigate(lastpath);\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\t// If JWT decode fails but we have stored user details (auto-login case), don't sign out\r\n\t\t\t\tif (storedUserDetails || isAutoLoginCompleted) {\r\n\t\t\t\t\tif (userDetails?.UserType === \"SuperAdmin\") {\r\n\t\t\t\t\t\tNavigate(\"/superadmin/organizations\");\r\n\t\t\t\t\t} else if (lastpath && lastpath != \"\") {\r\n\t\t\t\t\t\tlocalStorage.setItem(\"last_path\",\"\");\r\n\t\t\t\t\t\tNavigate(lastpath);\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// For free trial users (Admin) and regular users, stay on home page\r\n\t\t\t\t} else {\r\n\t\t\t\t\tsignOut();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t} else if (storedUserDetails || isAutoLoginCompleted) {\r\n\t\t\t// Handle auto-login case where we don't have a JWT token but have user details\r\n\t\t\tif (userDetails?.UserType === \"SuperAdmin\") {\r\n\t\t\t\tNavigate(\"/superadmin/organizations\");\r\n\t\t\t} else if (lastpath && lastpath != \"\") {\r\n\t\t\t\tlocalStorage.setItem(\"last_path\",\"\");\r\n\t\t\t\tNavigate(lastpath);\r\n\t\t\t}\r\n\t\t\t// For free trial users (Admin) and regular users, stay on home page\r\n\t\t}\r\n\t\telse {\r\n\t\t\tsignOut();\r\n\t\t}\r\n\t}, [userDetails]);\r\nconst{t:translate}=useTranslation();\r\n\treturn (\r\n\t\t<div\r\n\t\t\tclassName=\"qadpt-homepg\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-imgsec\">\r\n\t\t\t <img \r\n\t\t\t\t\t\t\t\t\t\t\t\tsrc={Homepageimg} \r\n\t\t\t\t\t\t\t\t\t\t\t\talt=\"Homepage Logo\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t/> </div>\r\n\t\t\t<div className=\"qadpt-des\">\r\n\t\t\t<div className=\"qadpt-msg\">{translate('Welcome Back,')}</div>\r\n\t\t\t{userDetails && (\r\n\t\t\t\t<div className=\"qadpt-usrnm\">\r\n\t\t\t\t\t\t{userDetails.FirstName}\r\n\t\t\t\t\t\t<img \r\n\t\t\t\t\t\t\t\t\t\t\t\tsrc={Wavinghand} \r\n\t\t\t\t\t\t\t\t\t\t\t\talt=\"Wavinghand\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t/>\r\n\t\t\t\t</div>\r\n\t\t\t\t)}\r\n\t\t\t\t<div className=\"qadpt-statmsg\"> {translate('Ready to engage your users? Start creating easy Interactions with our Services')}</div> </div>\r\n\t\t\t\r\n\t\t</div>\r\n\t);\r\n};\r\n\r\nexport default Home;\r\nexport { OrganizationId, UserId };\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,aAAa,QAAmB,4BAA4B;AAKrE,OAAOC,UAAU,MAAM,YAAY;AAEnC,SAAmBC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACrE,SAASC,cAAc,QAAQ,eAAe;AAC9C,SACCC,WAAW,EACXC,UAAU,QACJ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAClC,IAAIC,cAAsB;AAC1B,IAAIC,MAAc;AAClB,IAAIC,WAAiB;AACrB,IAAIC,UAAwB;AAE5B,MAAMC,IAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM;IAAEC;EAAO,CAAC,GAAGhB,OAAO,CAAC,CAAC;EAC5B,MAAM,CAACiB,gBAAgB,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAc,IAAI,CAAC;EACtE,MAAM,CAACqB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGtB,QAAQ,CAAsB,IAAI,CAAC;EACzF,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAACG,aAAa,CAAC,CAAC,CAAC;EAC/D,MAAM,CAACsB,MAAM,EAAEC,SAAS,CAAC,GAAG1B,QAAQ,CAAC2B,MAAM,CAAC;EAC5C,MAAM,CAACC,cAAc,CAAC,GAAG5B,QAAQ,CAAC2B,MAAM,CAAC;EACzC,MAAM,CAACE,aAAa,EAAEC,gBAAgB,CAAC,GAAG9B,QAAQ,CAA4B+B,SAAS,CAAC;EACxF,MAAMC,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM4B,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAE9BL,SAAS,CAAC,MAAM;IACf;IACA,MAAMiC,oBAAoB,GAAGC,cAAc,CAACC,OAAO,CAAC,oBAAoB,CAAC,KAAK,MAAM;IACpF,MAAMC,SAAS,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACP,QAAQ,CAACQ,MAAM,CAAC;IAC7D,MAAMC,kBAAkB,GAAGJ,SAAS,CAACK,GAAG,CAAC,OAAO,CAAC,IAAIL,SAAS,CAACK,GAAG,CAAC,QAAQ,CAAC,IAAIL,SAAS,CAACK,GAAG,CAAC,OAAO,CAAC;IAEtG,IAAIC,KAAK,GAAGC,YAAY,CAACR,OAAO,CAAC,cAAc,CAAC;IAChD,MAAMS,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACR,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC;IACrE,IAAIY,iBAAiB,GAAG,IAAI;IAE5B,IAAIH,QAAQ,CAAC,WAAW,CAAC,IAAIA,QAAQ,CAAC,MAAM,CAAC,IAAIA,QAAQ,CAAC,YAAY,CAAC,EAAE;MACxE/B,WAAW,GAAGgC,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,MAAM,CAAC,CAAC;MAC1C9B,UAAU,GAAG+B,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,YAAY,CAAC,CAAC;MAC/CjC,cAAc,GAAGG,UAAU,CAACH,cAAc;MAC1C+B,KAAK,GAAGG,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,WAAW,CAAC,CAAC,CAACI,YAAY;IACvD,CAAC,MAAM,IAAIJ,QAAQ,CAAC,MAAM,CAAC,IAAIA,QAAQ,CAAC,YAAY,CAAC,EAAE;MACtD;MACAG,iBAAiB,GAAGF,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,MAAM,CAAC,CAAC;MAChD/B,WAAW,GAAGkC,iBAAiB;MAC/BjC,UAAU,GAAG+B,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,YAAY,CAAC,CAAC;MAC/CjC,cAAc,GAAGG,UAAU,CAACH,cAAc;IAC3C;IAEA,MAAMsC,QAAQ,GAAGN,YAAY,CAACR,OAAO,CAAC,WAAW,CAAC;IAElD,IAAIO,KAAK,EAAE;MACV,IAAI;QAAA,IAAAQ,YAAA;QACH,IAAI,EAAAA,YAAA,GAAArC,WAAW,cAAAqC,YAAA,uBAAXA,YAAA,CAAaC,QAAQ,MAAK,YAAY,EAAE;UAC3CnB,QAAQ,CAAC,2BAA2B,CAAC;UACrC;QACD;QAEA,MAAMoB,gBAAgB,GAAGjD,UAAU,CAAgBuC,KAAK,CAAC;QAEzD,IAAIO,QAAQ,IAAIA,QAAQ,IAAI,EAAE,EAAE;UAC/BN,YAAY,CAACU,OAAO,CAAC,WAAW,EAAC,EAAE,CAAC;UACpCrB,QAAQ,CAACiB,QAAQ,CAAC;QACnB;MACD,CAAC,CAAC,OAAOK,KAAK,EAAE;QACf;QACA,IAAIP,iBAAiB,IAAId,oBAAoB,EAAE;UAAA,IAAAsB,aAAA;UAC9C,IAAI,EAAAA,aAAA,GAAA1C,WAAW,cAAA0C,aAAA,uBAAXA,aAAA,CAAaJ,QAAQ,MAAK,YAAY,EAAE;YAC3CnB,QAAQ,CAAC,2BAA2B,CAAC;UACtC,CAAC,MAAM,IAAIiB,QAAQ,IAAIA,QAAQ,IAAI,EAAE,EAAE;YACtCN,YAAY,CAACU,OAAO,CAAC,WAAW,EAAC,EAAE,CAAC;YACpCrB,QAAQ,CAACiB,QAAQ,CAAC;UACnB;UACA;QACD,CAAC,MAAM;UACNhC,OAAO,CAAC,CAAC;QACV;MACD;IACD,CAAC,MAAM,IAAI8B,iBAAiB,IAAId,oBAAoB,EAAE;MAAA,IAAAuB,aAAA;MACrD;MACA,IAAI,EAAAA,aAAA,GAAA3C,WAAW,cAAA2C,aAAA,uBAAXA,aAAA,CAAaL,QAAQ,MAAK,YAAY,EAAE;QAC3CnB,QAAQ,CAAC,2BAA2B,CAAC;MACtC,CAAC,MAAM,IAAIiB,QAAQ,IAAIA,QAAQ,IAAI,EAAE,EAAE;QACtCN,YAAY,CAACU,OAAO,CAAC,WAAW,EAAC,EAAE,CAAC;QACpCrB,QAAQ,CAACiB,QAAQ,CAAC;MACnB;MACA;IACD,CAAC,MACI;MACJhC,OAAO,CAAC,CAAC;IACV;EACD,CAAC,EAAE,CAACJ,WAAW,CAAC,CAAC;EAClB,MAAK;IAAC4C,CAAC,EAACC;EAAS,CAAC,GAACpD,cAAc,CAAC,CAAC;EAClC,oBACCI,OAAA;IACCiD,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAExBlD,OAAA;MAAKiD,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC5BlD,OAAA;QACQmD,GAAG,EAAEtD,WAAY;QACjBuD,GAAG,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE3B,CAAC,KAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACTxD,OAAA;MAAKiD,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAC1BlD,OAAA;QAAKiD,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAEF,SAAS,CAAC,eAAe;MAAC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAC5DrD,WAAW,iBACXH,OAAA;QAAKiD,SAAS,EAAC,aAAa;QAAAC,QAAA,GACzB/C,WAAW,CAACsD,SAAS,eACtBzD,OAAA;UACMmD,GAAG,EAAErD,UAAW;UAChBsD,GAAG,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAExB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACJ,eACDxD,OAAA;QAAKiD,SAAS,EAAC,eAAe;QAAAC,QAAA,GAAC,GAAC,EAACF,SAAS,CAAC,gFAAgF,CAAC;MAAA;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,KAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEvI,CAAC;AAER,CAAC;AAAClD,EAAA,CAxGID,IAAc;EAAA,QACAd,OAAO,EAOTG,WAAW,EACXC,WAAW,EAoEVC,cAAc;AAAA;AAAA8D,EAAA,GA7E3BrD,IAAc;AA0GpB,eAAeA,IAAI;AACnB,SAASJ,cAAc,EAAEC,MAAM;AAAG,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}