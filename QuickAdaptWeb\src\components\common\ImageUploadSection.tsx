import React, { useState } from "react";
import { Box, Typography, Popover, Grid, IconButton } from "@mui/material";
import { useTranslation } from "react-i18next";
import {
    uploadfile,
    hyperlink,
    files,
    uploadicon,
    replaceimageicon,
    galleryicon,
} from "../../assets/icons/icons";
import "./ImageUploadSection.css";
import CloseIcon from "@mui/icons-material/Close";
import { useSnackbar } from "../../SnackbarContext";

interface ImageUploadSectionProps {
    handleFileUpload?: (event: React.ChangeEvent<HTMLInputElement>) => void;
    selectedFiles: File[];
    setSelectedFiles: React.Dispatch<React.SetStateAction<File[]>>;
}

const ImageUploadSection: React.FC<ImageUploadSectionProps> = ({
    selectedFiles,
    setSelectedFiles
}) => {
    const { t: translate } = useTranslation();
    const { openSnackbar } = useSnackbar();
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);


const [isDragging, setIsDragging] = useState(false);

// File validation constants
const MAX_FILES = 10;
const MAX_FILE_SIZE = 2 * 1024 * 1024; // 2MB in bytes

// Validation function for file uploads
const validateFiles = (newFiles: File[], currentFiles: File[]) => {
    // Check file count limit
    if (currentFiles.length + newFiles.length > MAX_FILES) {
        openSnackbar(`Cannot upload more than ${MAX_FILES} files. You can upload ${MAX_FILES - currentFiles.length} more file(s).`, "error");
        return false;
    }

    // Check individual file sizes
    const oversizedFiles = newFiles.filter(file => file.size > MAX_FILE_SIZE);
    if (oversizedFiles.length > 0) {
        openSnackbar(`Some files exceed the 2MB limit: ${oversizedFiles.map(f => f.name).join(', ')}`, "error");
        return false;
    }

    return true;
};

const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
};

const handleDragLeave = () => {
    setIsDragging(false);
};

const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    const droppedFiles = Array.from(e.dataTransfer.files).filter(file =>
        file.type.startsWith("image/")
    );

    if (droppedFiles.length === 0) {
        openSnackbar("Please drop only image files", "error");
        return;
    }

    if (validateFiles(droppedFiles, selectedFiles)) {
        setSelectedFiles(prev => [...prev, ...droppedFiles]);
        openSnackbar(`${droppedFiles.length} file(s) added successfully`, "success");
    }
};

// Enhanced file input handler with validation
const handleValidatedFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    const fileArray = Array.from(files).filter(file =>
        file.type.startsWith("image/")
    );

    if (fileArray.length === 0) {
        openSnackbar("Please select only image files", "error");
        return;
    }

    if (validateFiles(fileArray, selectedFiles)) {
        setSelectedFiles(prev => [...prev, ...fileArray]);
        // openSnackbar(`${fileArray.length} file(s) added successfully`, "success");
    }

    // Reset the input value to allow selecting the same files again
    event.target.value = '';
};

    const handleClose = () => {
        setAnchorEl(null);
    };

    const formatFileSize = (sizeInBytes:any) => {
        if (sizeInBytes >= 1024 * 1024) {
          return (sizeInBytes / (1024 * 1024)).toFixed(2) + " MB";
        } else {
          return (sizeInBytes / 1024).toFixed(2) + " KB";
        }
    };

    const handleRemoveFile = (index: number) => {
        const updatedFiles = [...selectedFiles];
        updatedFiles.splice(index, 1);
        setSelectedFiles(updatedFiles);
    };


    const open = Boolean(anchorEl);
    const id = open ? "image-popover" : undefined;

    const imagePreviews = selectedFiles.map((file) => ({
        file,
        preview: URL.createObjectURL(file),
        size: formatFileSize(file.size),
    }));

    
    

    return (
        <>
            <Box className="qadpt-uploadcontainer">
               
                
            <Box
                className={`qadpt-drag-drop-container ${isDragging ? 'dragging' : ''}`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                    onDrop={handleDrop}
                    sx={{ cursor: "pointer" }}
                    onClick={() => document.getElementById("new-file-upload")?.click()}
                     
            >
                    <Box className="qadpt-imageupload">
                            <span
                                // onClick={() => document.getElementById("file-upload")?.click()}
                                dangerouslySetInnerHTML={{ __html: uploadfile }}
                        />
                        
                    </Box>
                    <Typography
						variant="body2"
						align="center"
						color="textSecondary"
                        sx={{ fontSize: "14px", flexDirection: "column" }}
                    	>
						{translate("Drag & Drop to upload file")}
                    </Typography>
                    <Typography
						variant="body2"
						align="center"
						color="textSecondary"
                        sx={{ fontSize: "12px", marginTop: "4px", opacity: 0.7 }}
                    	>
						{translate(`Max ${MAX_FILES} files, 2MB each`)}
                    </Typography>
                    {/* <Typography
						variant="body2"
						align="center"
						color="textSecondary"
                        sx={{ marginTop: "8px", fontSize: "14px" }}
                        >
						{translate("Or")}
					</Typography>  */}
                    <Box className="qadpt-imageupload">
                        {/* <span
                            onClick={() => document.getElementById("new-file-upload")?.click()}
                            dangerouslySetInnerHTML={{ __html: uploadicon }}
                        /> */}
                        <input
                            type="file"
                            id="new-file-upload"
                            accept="image/*"
                            onChange={handleValidatedFileUpload}
                            multiple
                            style={{display:"none"}}
                            />
                    </Box>
                                
                                
                </Box>
                {selectedFiles.length > 0 && (
        <Box className="selected-files">
    <Typography className="qadpt-title">
        {translate("Uploaded Files")}
    </Typography>

    <Grid container spacing={2} className="qadpt-upload-grid">
        {imagePreviews.map((file, index) => (
            <Grid item xs={6} key={index}>
                <Box className="qadpt-upload-card">
                    <div className="qadpt-upload-left">
                        <img
                            src={file.preview}
                            alt={`preview-${index}`}
                            className="qadpt-upload-img"
                        />
                        <div className="qadpt-upload-info">
                            <div className="qadpt-upload-filename">{file.file.name}</div>
                            <span className="qadpt-upload-size">{file.size}</span>
                        </div>
                    </div>
                    <IconButton
                        className="qadpt-upload-close"
                        size="small"
                        onClick={() => handleRemoveFile(index)}
                    >
                        <CloseIcon fontSize="small" />
                    </IconButton>
                </Box>
            </Grid>
        ))}
    </Grid>
</Box>


                )}
                    
            </Box>

            <Popover
                id={id}
                open={open}
                anchorEl={anchorEl}
                onClose={handleClose}
                anchorOrigin={{
                    vertical: "bottom",
                    horizontal: "center",
                }}
                transformOrigin={{
                    vertical: "top",
                    horizontal: "center",
                }}
                className="qadpt-imagepopup"
            >
                <Box className="qadpt-imagepopup-content">
                    <Box className="qadpt-imagepopup-item">
                        <span dangerouslySetInnerHTML={{ __html: replaceimageicon }} />
                        <Typography className="qadpt-imagepopup-text">
                            {translate("Replace Image", { defaultValue: "Replace Image" })}
                        </Typography>
                        <input
                            type="file"
                            id="replace-upload"
                            className="qadpt-imagepopup-upload"
                            accept="image/*"
                            onChange={handleValidatedFileUpload}
                            multiple
                        />
                    </Box>

                </Box>
            </Popover>
        </>
    );
};

export default ImageUploadSection;
