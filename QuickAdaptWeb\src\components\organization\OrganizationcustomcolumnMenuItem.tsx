
import React, { useState, useEffect } from "react";
import TextField from "@mui/material/TextField";
import Checkbox from "@mui/material/Checkbox";
import { MenuItem, ListItemText, Button, Box, Paper } from "@mui/material";
import FilterAltIcon from "@mui/icons-material/FilterAlt";
import Autocomplete from "@mui/material/Autocomplete";
import { getOrganizations, getAllOrganizations,getOrganizationsData } from "../../services/OrganizationService";
import styles from "./OrganizationStyles.module.scss";

const OrganizationCustomColumnMenuItem: React.FC<{
  column: any;
  skip: any;
  top: any;
  models: any[];
  OrganizationId: any;
  setTotalcount: any;
  orderByFields: any;
  setModels: any;
  sortModel: any;
  setLoading: any;
  filters: any;
  setFilters: any;
  options: string[];
  onSearch: (value: string[]) => void;
  open: boolean;
  hideMenu: (event: React.SyntheticEvent<Element, Event>) => void;
  colDef: any;
  modelsData: any[];
  optionsModel: any;
  paginationModel: any;
}> = ({
  column,
  skip,
  top,
  OrganizationId,
  setTotalcount,
  orderByFields,
  setModels,
  sortModel,
  setLoading,
  filters,
  setFilters,
  options,
  onSearch,
  open,
  hideMenu,
  models,
  colDef,
  modelsData,
  optionsModel,
  paginationModel,
  ...other
}) => {
  const [searchText, setSearchText] = useState("");
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  const [filteredOptions, setFilteredOptions] = useState<string[]>(options);

  // Maintain a list of all available names
  const [allNames, setAllNames] = useState<string[]>([]);
  // State for all organization names (for Name column search)
  const [allOrganizationNames, setAllOrganizationNames] = useState<string[]>([]);
  const [allNamesLoaded, setAllNamesLoaded] = useState<boolean>(false);
  
  // State for all organization types (for Type column search)
  const [allOrganizationTypes, setAllOrganizationTypes] = useState<string[]>([]);
  const [allTypesLoaded, setAllTypesLoaded] = useState<boolean>(false);

  useEffect(() => {
    const uniqueOptions = Array.from(new Set(options));
    setFilteredOptions(uniqueOptions);

    // For Name column, check if there's an existing filter
    if (column === "Name") {
      const existingFilter = filters.find((filter: any) => filter.FieldName === column);
      
      if (existingFilter) {
        // If filter exists, show only the currently selected values as selected
        const currentlySelected = existingFilter.Value.split(',').filter((val: string) => val.trim());
        setSelectedOptions(currentlySelected);
        // But keep all available names in filteredOptions for display
        if (allOrganizationNames.length > 0) {
          setFilteredOptions(allOrganizationNames);
        } else {
          const currentPageNames = Array.from(new Set(models.map((model: any) => model.Name)));
          setFilteredOptions(currentPageNames);
        }
      } else {
        // If no filter, select all current page names by default
        const currentPageNames = Array.from(new Set(models.map((model: any) => model.Name)));
        setSelectedOptions(currentPageNames);
        setFilteredOptions(currentPageNames);
      }
    }
  }, [options, models, column, filters]);

  useEffect(() => {
    if (column === "Type") {
      // Check if there's an existing filter for this column
      const existingFilter = filters.find((filter: any) => filter.FieldName === column);
      
      if (existingFilter) {
        // If filter exists, show only the currently selected values as selected
        const currentlySelected = existingFilter.Value.split(',').filter((val: string) => val.trim());
        setSelectedOptions(currentlySelected);
        // But keep all available types in filteredOptions for display
        if (Array.isArray(optionsModel) && optionsModel.length > 0) {
          const typeOptions = Array.from(new Set(optionsModel.map((model: any) => model.Type)));
          setFilteredOptions(typeOptions);
        }
      } else {
        // If no filter, show all available types from optionsModel
        if (Array.isArray(optionsModel) && optionsModel.length > 0) {
          const typeOptions = Array.from(new Set(optionsModel.map((model: any) => model.Type)));
          setSelectedOptions(typeOptions);
          setFilteredOptions(typeOptions);
        }
      }
    }
  }, [column, models, optionsModel, filters]);

  const handleOptionChange = (event: React.ChangeEvent<{}>, value: string[]) => {
    setSelectedOptions(value);
  };

  const handleCancelClick = (event: React.SyntheticEvent<Element, Event>) => {
    hideMenu(event);
  };

  const handleClearFiltersClick = (event: React.SyntheticEvent<Element, Event>) => {
    setSearchText("");
    setFilters([]);
    getAllOrganizations(setModels, setLoading);
    hideMenu(event);
  };

  const handleApplyClick = async (event: React.SyntheticEvent<Element, Event>) => {
    const searchValue = selectedOptions.length ? selectedOptions : [searchText];
    // If all org names/types are selected, treat as no filter
    if (column === "Name" && allOrganizationNames.length > 0 && selectedOptions.length === allOrganizationNames.length) {
      setFilters([]);
      await getAllOrganizations(setModels, setLoading);
      setIsDropdownOpen(false);
      hideMenu(event);
      return;
    } else if (column === "Type" && allOrganizationTypes.length > 0 && selectedOptions.length === allOrganizationTypes.length) {
      setFilters([]);
      await getAllOrganizations(setModels, setLoading);
      setIsDropdownOpen(false);
      hideMenu(event);
      return;
    }
    const combinedValue = searchValue.join(",");
    const newFilter = {
      FieldName: column,
      ElementType: "string",
      Condition: "in",
      Value: combinedValue,
      IsCustomField: false,
    };
    const updatedFilters = [...filters];
    const filterIndex = updatedFilters.findIndex(
      (filter) => filter.FieldName === column
    );
    if (filterIndex !== -1) {
      updatedFilters[filterIndex] = newFilter;
    } else {
      updatedFilters.push(newFilter);
    }
    setFilters(updatedFilters);
    await getOrganizations(setModels, setLoading, 0, paginationModel.pageSize, setTotalcount, sortModel, updatedFilters);
    setSelectedOptions(searchValue);
    setIsDropdownOpen(true); // Do not close dropdown
    hideMenu(event); 
  };

  const handleSelectAll = () => {
      let allToSelect: string[] = filteredOptions;
  if (column === "Name" && allOrganizationNames.length > 0) {
    allToSelect = allOrganizationNames;
  } else if (column === "Type" && allOrganizationTypes.length > 0) {
    allToSelect = allOrganizationTypes;
  }
    // Use all available options if loaded, otherwise fallback to filteredOptions
    const newSelectedOptions =
      selectedOptions.length === allToSelect.length ? [] : allToSelect;
    setSelectedOptions(newSelectedOptions);
  };

  const isClearFiltersDisabled = !searchText && selectedOptions.length === options.length;
  const isSearchButtonDisabled = selectedOptions.length === 0 && !searchText;
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  // Fetch all organization names/types when menu is opened
  const handleDropdownOpen = async () => {
    setIsDropdownOpen(true);
    
    // Check if there's an existing filter for the current column
    const existingFilter = filters.find((filter: any) => filter.FieldName === column);
    
    if (column === "Name" && !allNamesLoaded) {
      try {
        const allOrgs = await getOrganizationsData(setModels, setLoading);
        const allNamesList = Array.from(new Set((allOrgs || []).map((org: any) => org.Name)));
        setAllOrganizationNames(allNamesList);
        setAllNamesLoaded(true);
        
        setFilteredOptions(allNamesList);
        if (!existingFilter) {
          // Only set all selected if no filter is applied
          setSelectedOptions(allNamesList);
        }
      } catch (error) {
        // Optionally handle error
      }
    } else if (column === "Name" && allOrganizationNames.length > 0) {
      setFilteredOptions(allOrganizationNames);
      if (!existingFilter) {
        // Only set all selected if no filter is applied
        setSelectedOptions(allOrganizationNames);
      }
    } else if (column === "Type" && !allTypesLoaded) {
      try {
        const allOrgs = await getOrganizationsData(setModels, setLoading);
        const allTypesList = Array.from(new Set((allOrgs || []).map((org: any) => org.Type)));
        setAllOrganizationTypes(allTypesList);
        setAllTypesLoaded(true);
        
        setFilteredOptions(allTypesList);
        if (!existingFilter) {
          // Only set all selected if no filter is applied
          setSelectedOptions(allTypesList);
        }
      } catch (error) {
        // Optionally handle error
      }
    } else if (column === "Type" && allOrganizationTypes.length > 0) {
      setFilteredOptions(allOrganizationTypes);
      if (!existingFilter) {
        // Only set all selected if no filter is applied
        setSelectedOptions(allOrganizationTypes);
      }
    }
  };

  // Compute options for Autocomplete: always show all available options
  const getAutocompleteOptions = () => {
    if (column === "Name") {
      return allOrganizationNames.length > 0 ? allOrganizationNames : filteredOptions;
    } else if (column === "Type") {
      return allOrganizationTypes.length > 0 ? allOrganizationTypes : filteredOptions;
    }
    return filteredOptions;
  };


  return (

    <div className="qadpt-org-filter" style={{ position: "relative" }}>
      <Autocomplete
        multiple
        disableCloseOnSelect
        options={getAutocompleteOptions()}
        value={selectedOptions}
        open={isDropdownOpen}
        onOpen={handleDropdownOpen}
        onClose={() => setIsDropdownOpen(true)}
        onChange={handleOptionChange}
        onInputChange={async (event, value) => {
          setSearchText(value);
          if (column === "Name" && value && !allNamesLoaded) {
            try {
              const allOrgs = await getOrganizationsData(setModels, setLoading);
              const allNamesList = Array.from(new Set((allOrgs || []).map((org: any) => org.Name)));
              setAllOrganizationNames(allNamesList);
              setAllNamesLoaded(true);
            } catch (error) {
              // Optionally handle error
            }
          } else if (column === "Type" && value && !allTypesLoaded) {
            try {
              const allOrgs = await getOrganizationsData(setModels, setLoading);
              const allTypesList = Array.from(new Set((allOrgs || []).map((org: any) => org.Type)));
              setAllOrganizationTypes(allTypesList);
              setAllTypesLoaded(true);
            } catch (error) {
              // Optionally handle error
            }
          }
        }}
        // onOpen={() => setIsDropdownOpen(true)}
        //onClose={() => setIsDropdownOpen(false)}
        renderOption={(props, option, { selected }) => (
          <div>
            {option === filteredOptions[0] && (
              <MenuItem key="select-all" onClick={handleSelectAll}>
                <Checkbox
                  checked={(() => {
                    if (column === "Name" && allOrganizationNames.length > 0) {
                      return selectedOptions.length === allOrganizationNames.length;
                    } else if (column === "Type" && allOrganizationTypes.length > 0) {
                      return selectedOptions.length === allOrganizationTypes.length;
                    }
                    return selectedOptions.length === filteredOptions.length;
                  })()}
                  indeterminate={(() => {
                    const totalOptions = (() => {
                      if (column === "Name" && allOrganizationNames.length > 0) {
                        return allOrganizationNames.length;
                      } else if (column === "Type" && allOrganizationTypes.length > 0) {
                        return allOrganizationTypes.length;
                      }
                      return filteredOptions.length;
                    })();
                    return selectedOptions.length > 0 && selectedOptions.length < totalOptions;
                  })()}
                />
                <ListItemText primary="Select All" />
              </MenuItem>
            )}
            <MenuItem {...props} key={option}>
              <Checkbox checked={selectedOptions.includes(option)} />
              <ListItemText primary={option} />
            </MenuItem>
          </div>
        )}
        renderInput={(params) => (
          <TextField
            {...params}
            variant="outlined"
            label="Search"
            placeholder="Select Options"
            onKeyDown={(event) => {
              if (["ArrowUp", "ArrowDown", "Enter"].includes(event.key)) {
                event.stopPropagation();
              }
              else
              {
                event.stopPropagation();
              }
            }}
            InputProps={{
              ...params.InputProps,
              endAdornment: (
                <React.Fragment>
                  {params.InputProps.endAdornment}
                </React.Fragment>
              ),
            }}
          />
        )}
        PaperComponent={(props) => (
          <Paper
            {...props}
            style={{
              position: "absolute",
              top: "100%",
              left: 0,
              width: "100%",
              zIndex: 10,
              marginTop: "4px", // Small gap between input and dropdown
            }}
            onWheel={(event) => event.stopPropagation()}
          />
        )}
        ListboxProps={{
          style: { maxHeight: "220px", overflowY: "auto" }, 
        }}
        renderTags={() => null}
      />
    
      <Box
        display="flex"
        justifyContent="flex-end"
        mt={2}
        className="qadpt-btn"
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          padding:"0 10px",
          marginTop: isDropdownOpen ? "250px" : "10px", // Dynamically change marginTop
          transition: "margin-top 0.3s ease-in-out", // Smooth transition
        }}
      >
        <Button variant="outlined" color="primary" onClick={handleApplyClick} disabled={isSearchButtonDisabled}>
          OK
        </Button>
        <Button variant="outlined" color="secondary" onClick={handleCancelClick} style={{ marginLeft: "8px" }}>
          Cancel
        </Button>
        <Button onClick={handleClearFiltersClick} startIcon={<FilterAltIcon />} disabled={isClearFiltersDisabled} style={{ marginLeft: "8px" }}>
          Clear Filters
        </Button>
      </Box>
    </div>
  );
};

export default OrganizationCustomColumnMenuItem;
