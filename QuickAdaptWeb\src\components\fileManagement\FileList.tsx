import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
    GridColDef,
    GridToolbarContainer,
    GridToolbarColumnsButton,
    GridToolbarFilterButton,
    GridToolbarDensitySelector,
} from "@mui/x-data-grid";
import {
    Button,
    Menu,
    MenuItem,
    FormControlLabel,
    IconButton,
    Link,
    Dialog,
    DialogActions,
    DialogTitle,
    DialogContent,
    Box,
    Card,
    Snackbar,
    Alert,
    Tooltip,
    CardContent,
    CardActions,
    CardMedia,
    Typography,
    Modal,
    TextField,
    InputAdornment,
    Grid,
} from "@mui/material";
import {
    Edit as EditIcon,
    Delete as DeleteIcon,
    SaveAlt as SaveAltIcon,
    Opacity,
    FileUpload,
    Search as SearchIcon,
} from "@mui/icons-material";
import SwapHorizIcon from '@mui/icons-material/SwapHoriz';
import loader from "../../assets/loader.gif";
import CustomGrid from "../common/Grid";
import { getAllFiles,GetGuidesUsedByFile } from "../../services/FileManagementService";
import { isSidebarOpen, subscribe } from "../adminMenu/sidemenustate";
import AddIcon from '@mui/icons-material/Add';
import { UploadImage,DeleteFile ,ReplaceFile} from "../../services/FileManagementService";
import { height } from "@mui/system";
import { inherits } from "util";
import LinkIcon from '@mui/icons-material/Link';
import UpgradeIcon from '@mui/icons-material/Upgrade';
import { Trash, PreviewImage, Replace, copy } from "../../assets/icons/icons";
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import CloseIcon from '@mui/icons-material/Close';
import ImageUploadSection from "../common/ImageUploadSection";
import { useSnackbar } from "../../SnackbarContext";
import {
    uploadfile,
    
} from "../../assets/icons/icons";
// import IconButton from '@mui/material/IconButton';



interface FileUpload {
    ImageId: string;
    FileName: string | null;
    Url: string;
}

const FileList: React.FC = () => {
    const { openSnackbar } = useSnackbar();
    const [models, setModels] = useState<FileUpload[]>([]);
    const [loading, setLoading] = useState(false);
    const [emailiddelete, setemailiddelete] = useState("");
    const [useridedit, setUserIdEdit] = useState("");
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const [sidebarOpen, setSidebarOpen] = useState(isSidebarOpen());
    const [showPopup, setShowPopup] = useState(false);
    const [selectedFiles, setSelectedFiles] = useState<File[]>([]);

    const [preview, setPreview] = useState(false);
    const [showEditPopup, setShowEditPopup] = useState(false);
    const [showDeletePopup, setShowDeletePopup] = useState(false);
    const [fileUploads, setFileUploads] = useState<FileUpload[]>([]);
    const [snackbarOpen, setSnackbarOpen] = useState(false);
    const [snackbarMessage, setSnackbarMessage] = useState("");
    const [snackbarSeverity, setSnackbarSeverity] = useState<"success" | "error">("success");
    const [replaceFileId, setReplaceFileId] = useState("");
    const [hoverId, setHoverId] = useState("");
    const [previewMode, setPreviewMode] = useState<FileUpload>();
    const [guideNames, setGuideNames] = useState([]);
    const [skip, setSkip] = useState(0);
    const [limit, setLimit] = useState(12);
    const [filters, setFilters] = useState({});
    const [searchTerm, setSearchTerm] = useState("");
    const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
    const [totalCount, setTotalCount] = useState(0);



    useEffect(() => {
        //getAllFiles();
        const unsubscribe = subscribe(setSidebarOpen);
        return () => unsubscribe();
    }, []);

    // Debounce search term
    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedSearchTerm(searchTerm);
        }, 500);

        return () => clearTimeout(timer);
    }, [searchTerm]);

    const fetchData = async () => {
        setLoading(true);
                        try {
                const response = await getAllFiles(skip,limit,debouncedSearchTerm,"");
                            if (response) {
                                const files = response?.results;
		                        const count = response?._count;
                        const uploads: FileUpload[] = files.map((file:any) => ({
                        ImageId: file.Id,
                        FileName: file.Name || null,
                        Url: file.Url + "?timestamp=" + new Date().getTime()|| '',
                    }));
                                setModels(uploads);
                                setTotalCount(count);
                } else {
                }
            } catch (error) {
            } finally {
                setLoading(false);
            }
    };

    useEffect(() => {

        if (!showPopup) {
            fetchData();
        }
    }, [showPopup,skip,debouncedSearchTerm]);
    
    const openPopup = () => {
		setShowPopup(true);
	};

    const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(event.target.value);
    };

    // Reset pagination when search term changes
    useEffect(() => {
        setSkip(0);
    }, [debouncedSearchTerm]);
    const MatEdit = (params: any) => {
        const handleDeleteClick = (emailId: string) => {
            setShowDeletePopup(true);
            setemailiddelete(emailId);
        };

        const handleEditClick = (userid: string) => {
            setShowEditPopup(true);
            setUserIdEdit(userid);
        };

        return (
            <div>
                <FormControlLabel
                    control={
                        <IconButton
                            color="secondary"
                            aria-label="edit"
                            onClick={() => handleEditClick(params.userid)}
                        >
                            <EditIcon style={{ color: "blue" }} />
                        </IconButton>
                    }
                    label={""}
                />
                 <FormControlLabel
                    control={
                        <IconButton
                            color="secondary"
                            aria-label="delete"
                            onClick={() => handleDeleteClick(params.emailId)}
                        >
                            <SwapHorizIcon style={{ color: "blue" }} />
                        </IconButton>
                    }
                    label={""}
                />
                <FormControlLabel
                    control={
                        <IconButton
                            color="secondary"
                            aria-label="delete"
                            onClick={() => handleDeleteClick(params.emailId)}
                        >
                            <DeleteIcon style={{ color: "blue" }} />
                        </IconButton>
                    }
                    label={""}
                />  
            </div>
        );
    };

  // Define columns
    const columns: GridColDef[] = [
        {
            field: "ImageId",
            headerName: "ImageId",
            width: sidebarOpen ? 150 : 170,
            align: 'center',
            headerAlign: 'center',
        },
        {
            field: "FileName", 
            headerName: "Name", 
            width: sidebarOpen ? 220 : 250,
            align: 'left',
            headerAlign: 'left',
        },
        {
            field: "Url",
            headerName: "Link",
            width: sidebarOpen ? 450 : 600,
            renderCell: (params) => (
                <Link href={params.value} target="_blank" rel="noopener noreferrer" color="inherit">
                    {params.value}
                </Link>
            ),
            align: 'left',
            headerAlign: 'left',
        },
        {
            field: "actions",
            headerName: "Actions",
            sortable: false,
            width: 300,
            renderCell: (params) => (
                <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                   
                        <MatEdit/>
                   
                </div>
            ),
            align: 'center',
            headerAlign: 'center',
        },
    ];

// Handle edit action (replace this with your actual implementation)
const handleEdit = (row:any) => {
    // Implement your edit logic here
};
   

const handleClose = () => {
    setShowPopup(false);
    setSelectedFiles([]);
    setPreview(false);
};

    const CustomToolbar: React.FC = () => {
        const handleExportMenuClick = (event: React.MouseEvent<HTMLButtonElement>) => {
            setAnchorEl(event.currentTarget);
        };

        const handleExportMenuClose = () => {
         setAnchorEl(null);
        };
       
        const handleDownloadExcelClick = () => {
            handleExportMenuClose();
        };

        return (
            <div style={{ display: "flex", alignItems: "center" }}>
                <GridToolbarContainer>
                    <GridToolbarColumnsButton />
                    <GridToolbarFilterButton />
                    <GridToolbarDensitySelector />
                </GridToolbarContainer>
                <Button
                    aria-controls="export-menu"
                    aria-haspopup="true"
                    onClick={handleExportMenuClick}
                    style={{ marginLeft: "10px" }}
                    startIcon={<SaveAltIcon />}
                >
                    Export
                </Button>
                <Menu
                    id="export-menu"
                    anchorEl={anchorEl}
                    keepMounted
                    open={Boolean(anchorEl)}
                    onClose={handleExportMenuClose}
                >
                    <MenuItem onClick={handleDownloadExcelClick}>Download Excel</MenuItem>
                </Menu>
            </div>
        );
    };
const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newFiles = Array.from(event.target.files || []);
    setSelectedFiles(prev => [...prev, ...newFiles]);
};



const handleUpload = async () => {
    if (selectedFiles && selectedFiles.length > 0) {
        try {
            setLoading(true);

            // Convert FileList to File[]
            const filesArray = Array.from(selectedFiles);

await UploadImage(
    selectedFiles,
    setLoading,
    setShowPopup,
    setModels,
    setSelectedFiles
);


             openSnackbar(translate("Files Uploaded Successfully"), "success");

            setSelectedFiles([]);
            setShowPopup(false);
            fetchData();
        } catch (error) {
            let message = translate("Upload failed. Please try again.");
            const err = error as any;

            if (err?.response?.data) {
                message =
                    typeof err.response.data === "string"
                        ? err.response.data
                        : err.response.data.message || message;
            }

            openSnackbar(message, "error");
        } finally {
            setLoading(false);
        }
    }
};



    const copyUrl = async (url: string) => {
        try {
            await navigator.clipboard.writeText(url);
            openSnackbar(translate("URL Copied Successfully"), "success");

            
            
            
        } catch (error) {
            // console.log(error);
            openSnackbar(translate("Failed to copy URL"), "error");
        }
    }



    const deleteFileIconClick = async (file: any) => {
        try {
            const status = await DeleteFile(file.ImageId);
            if (status.Success !== false) {
                openSnackbar(translate("File Deleted Successfully"), "success");
                fetchData();
            } else {
                openSnackbar(status.ErrorMessage || translate("Error in Deleting File"), "error");
            }
        } catch (error) {
            // console.log(error);
            openSnackbar(translate("Error in Deleting File"), "error");
            
        }
    }

    const ReplaceImage = async (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      console.log(replaceFileId, "Is replace file id");
      if (file) {
        try {
          setLoading(true);
          await ReplaceFile(replaceFileId, file, setLoading);
          setModels([]);

            fetchData();
            openSnackbar(translate("Image replaced successfully"), "success");
        } catch (error) {
            let message = translate("Upload failed. Please try again.");
          const err = error as any;

          if (err?.response?.data) {
            message =
              typeof err.response.data === "string"
                ? err.response.data
                : err.response.data.message || message;
          }
          openSnackbar(message, "error");
        } finally {
          setLoading(false);
        }
      }
    };


    const setReplaceFileUrl = (id : any) => {
        console.log(id, "from click");
        setReplaceFileId(id);
    }

    const previewFile = async (model : any) => {
        console.log("Preview clicked");
        
        const names = await GetGuidesUsedByFile(model.ImageId, setLoading);
        setGuideNames(names);
        setPreview(true);
        setPreviewMode(model);
    }
    const { t: translate } = useTranslation();

    const onNextClick = () => {
        setSkip(skip+12);
        //setTop(top + 12);
    }

    const onPreviousClick = () => {
        setSkip(skip - 12 >= 0 ? skip - 12 : 0);
        //setTop(top-12>=12?top-12:12 )
    }

    return (
      
      <div className='qadpt-web'>
            <div className='qadpt-webcontent qadpt-file-mgn'>
                 <div className="qadpt-head">
							<div className="qadpt-title-sec">
                        <div className="qadpt-title"> {translate('File Management')}</div>
                        <div className="qadpt-file-cnt"> {totalCount} </div>
							</div>
                    <div className="qadpt-right-part">
                          <TextField
                            size="small"
                            placeholder={translate('Search...')}
                            value={searchTerm}
                            onChange={handleSearchChange}
                            InputProps={{
                                startAdornment: (
                                    <InputAdornment position="start" sx={{ mr: 0.5 }}>
                                        <SearchIcon sx={{ fontSize: 20 }}/>
                                    </InputAdornment>
                                ),
                            }}
                            
                            sx={{ width: '250px' }}
                        />
								<button
               onClick={() => setShowPopup(true)}
                className="qadpt-memberButton"
								>
									<i className="fal fa-add-plus"></i>
                  <span>{translate('Upload File')}</span>								</button>
							</div>
          </div>
                 <Dialog open={showPopup}
                         onClose={handleClose}
                        className="qadpt-upldpopup"
                    >
                        <DialogTitle className="qadpt-upldpopup-title"
                            
                        >
                            {translate('Upload')}
                            {/* <span
                                onClick={() => document.getElementById("file-upload")?.click()}
                                dangerouslySetInnerHTML={{ __html: uploadfile }}
                                /> */}
                        </DialogTitle>
                        <DialogContent>
                            <ImageUploadSection
    handleFileUpload={handleFileUpload} selectedFiles={selectedFiles} setSelectedFiles={setSelectedFiles}
/>
                        {/* <Box
                                display="flex"
                                justifyContent="center"
                                alignItems="center"
                                minHeight="50px" // Adjust as needed */}
                            {/* > */}
                            {/* <input type="file" onChange={handleFileChange} /> */}

                                {/* <input type="file" onChange={handleFileUpload} accept="image/*" multiple/> */}
                            {/* </Box> */}


                        </DialogContent>
                        <DialogActions >
                            <Button onClick={handleClose}  className="qadpt-cancelbtn">
                            {translate('Cancel')}
                        </Button>
                            <Button className="qadpt-upldbtn"
                                onClick={async () => {
                                    await handleUpload();
                                }}
    disabled={!selectedFiles || selectedFiles.length === 0}
>
    {translate('Upload')}
</Button>

                        </DialogActions>
                    </Dialog>
                {loading ? (
                     <div className="Loaderstyles">
                        <img
      src={loader}
			alt="Spinner"
			className="LoaderSpinnerStyles"
      />
                    </div>
            ) : (
                    <>
<Box className="qadpt-file-grd">
    {models.length === 0 && !loading ? (
        <Typography
            variant="body1"
            align="center"
            sx={{ width: "100%", padding: "20px", color: "#999" }}
        >
            {translate("No Images Found")}
        </Typography>
    ) : (
        models.map((model, index) => (
            <Card key={index} className="qadpt-file-card">
                                    <Box className="qadpt-imgcont"
                                       
                                    onMouseEnter={() => setHoverId(model.ImageId)}
                                    onMouseLeave={() => setHoverId("")}>
                                    <CardMedia
                                        component="img"
                                        image={model.Url||""}
                                            alt={model.FileName || "Image"}
                                            
                                           
                                            onClick={()=>previewFile(model)}
                                             className={`qadpt-cardimage ${hoverId === model.ImageId ? "qadpt-image-blur" : ""}`}
                                        />
                                        
                                        
                                        { hoverId == model.ImageId &&(
                                            <Box sx={{
                                                position: "absolute",
                                                display: "flex",
                                                justifyContent: "center",
                                                alignItems: "center",
                                                
                                            }}
                                            onClick={()=>previewFile(model)}
                                            >
                                                {/* <Preview /> */}
                                                <img src={PreviewImage} alt="Preview Image" />
                                                {/* <Typography>Preview</Typography> */}
                                            </Box>)
                                        }
                                        
                                            
                                            
                                    
                                    <IconButton
                                            onClick={() => deleteFileIconClick(model)}
                                            className="qadpt-deletebtn"
                                    >
                                        <img src={Trash} alt="Delete"/>
                                    </IconButton>
                                    </Box>
                                    <CardActions className="qadpt-cardaction">
                                        <div className="qadpt-filename">{model.FileName}</div>
                                        <div className="qadpt-fileactions">
                                    
                                        <Tooltip title="Replace Image" arrow>
                                        <IconButton 
                                                onClick={(event) => {
                                                    setReplaceFileUrl(model.ImageId);
                                                event?.stopPropagation();
                                                document.getElementById("file-upload")?.click();
                                            }}>
                                                    <img src={Replace} alt="Replace"/>
                                                    
                                                </IconButton>
                                                
                                            </Tooltip>
                                            <IconButton onClick={() => copyUrl(model.Url)}>
                                    <img src={copy} alt="copy"/>
                                        </IconButton>
                                            <input
                                                type="file"
                                                id="file-upload"
                                                style={{ display: "none" }}
                                                accept="image/*"
                                                onChange={ReplaceImage}
                                            />
                                            </div>
                                    </CardActions>
            </Card>
        ))
    )}
</Box>
                            {
                                preview && (
                                    <Modal
                                        open={preview}
                                        onClose={() => {
                                            setPreview(false);
                                            setPreviewMode(undefined);
                                        }
                                            
                                        }
                                        aria-labelledby="modal-modal-title"
                                        aria-describedby="modal-modal-description"
                                        >
                                        <Box className="qadpt-preview-modal">

                                                            <IconButton
                                                aria-label="close"
                                                className="qadpt-closebtn"
                    onClick={() => {
                        setPreview(false);
                        setPreviewMode(undefined);
                    }}
    
>
    <CloseIcon />
                </IconButton>
                                            
                                            <img src={previewMode?.Url || ""} alt="Model Image" className="qadpt-preview-img" />
                                            <div className="qadpt-preview-content">
                                            <div id="modal-modal-description" className="qadpt-preview-title">
                                                {previewMode?.FileName}
                                                </div>
                                                <div className="qadpt-filesusedin">
                                            <div id="modal-modal-description">
                                                {translate('File Used In')}
                                            </div>
                                            <div>
                                                {guideNames?.length<1 ? translate('No Guides Used this file ') : guideNames }
                                                    </div>
                                                    </div>
                                            </div>
                                        </Box>
                                    </Modal>
                                )
                                
                    }

                    {/* <CustomGrid
                        rows={models}
                        columns={columns}
                        pageSize={100}
                        totalRows={models.length}   
                        onPageChange={(newPage) => newPage}
                        onPageSizeChange={(newPageSize) => newPageSize}
                        rowsPerPageOptions={[10, 20, 50, 100]}
                        Toolbar={CustomToolbar}
                        /> */}
                            <Box className="qadpt-pagination">
                                <div >
                                    <Button
                                        onClick={onPreviousClick}
                                        startIcon={<ArrowBackIcon />}
                                        variant="contained"
                                        disabled={skip === 0}
                                        className="qadpt-prevbtn"
                                    >{translate('Previous')}
                                       
                                    </Button>
                                </div>
                                <div>
                                    <Button
                                        onClick={onNextClick}
                                        endIcon={<ArrowForwardIcon />}
                                        variant="contained"
                                        disabled={skip + limit >= totalCount}
                                        className="qadpt-nextbtn"
                                    >{translate('Next')}
                                        
                                    </Button>
                                </div>
                            </Box>
                        </>
            )}
            </div>
            </div>
    );
};

export default FileList;
