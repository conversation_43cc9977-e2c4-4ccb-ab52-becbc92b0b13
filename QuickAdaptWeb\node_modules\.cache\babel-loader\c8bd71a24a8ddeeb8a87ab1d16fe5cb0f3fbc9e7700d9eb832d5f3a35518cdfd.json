{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptWeb\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { Suspense, useEffect, useState, useCallback } from \"react\";\nimport \"./App.scss\";\nimport CircularIndeterminate from \"./components/common/CircularIndeterminate\";\nimport Layout from \"./components/layout/Layout\";\nimport { RtlProvider } from \"./RtlContext\";\nimport { SnackbarProvider } from \"./SnackbarContext\";\nimport Routing from \"./routing/Routings\";\nimport { AuthProvider, useAuth } from \"./components/auth/AuthProvider\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport { ResetPasswordLinkConsumed } from \"./services/ExpirelinkService\";\nimport { setupInterceptors } from \"./services/APIService\";\nimport { AccountProvider } from \"./components/account/AccountContext\";\nimport { ExtensionProvider } from \"./ExtensionContext\";\nimport jwtDecode from \"jwt-decode\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const location = useLocation();\n  //const passwordLogId = useParams();\n  const navigate = useNavigate();\n  setupInterceptors(navigate, (message, severity = 'info') => {});\n  const {\n    signOut,\n    loggedOut\n  } = useAuth();\n  //const history = useHistory();\n  const [isResetLinkValid, setIsResetLinkValid] = useState(false);\n  const [isCheckingLink, setIsCheckingLink] = useState(false);\n  //const noLayoutRoutes = [\"/login\", \"/forgotpassword\", \"/resetpassword/passwordLogId\", \"/admin/adminlogin\"];\n  const [loginUserDetails, setLoginUserDetail] = useState();\n  const noLayoutRoutes = [\"/login\", \"/forgotpassword\", \"/resetpassword/:passwordLogId\", \"/uninstall\", \"/admin/adminlogin\", \"/linkexpired\"];\n  const uuidRegex = \"[0-9a-fA-F-]{36}\";\n  const isNoLayoutRoute = noLayoutRoutes.some(route => {\n    if (route === \"/resetpassword/:passwordLogId\") {\n      const resetPasswordRegex = new RegExp(`^/resetpassword/${uuidRegex}$`);\n      return resetPasswordRegex.test(location.pathname);\n    }\n    return location.pathname === route;\n  });\n  // Check if this is an automatic login flow from free trial\n  const urlParams = new URLSearchParams(location.search);\n  const isAutoLogin = urlParams.get('auto_login') === 'true';\n  const urlToken = urlParams.get('token');\n\n  // Check if this is an auto-login flow first\n  const hasAutoLoginParams = urlParams.has('token') && urlParams.has('user_id') && urlParams.has('org_id');\n  const isAutoLoginCompleted = sessionStorage.getItem('autoLoginCompleted') === 'true';\n  const isOnLoginPage = location.pathname.includes('/login');\n\n  // Check if we have user data from auto-login (indicates completed auto-login)\n  const userInfo = JSON.parse(localStorage.getItem(\"userInfo\") || '{}');\n  const hasAutoLoginUserData = userInfo['user'] && userInfo['oidc-info'];\n\n  // Get token from URL if auto login, otherwise from localStorage\n  // Also check localStorage if auto-login was completed\n  const token = isAutoLogin && urlToken ? urlToken : localStorage.getItem(\"access_token\");\n\n  // Check if we need to redirect to login for auto-login processing\n  const shouldRedirectToLogin = hasAutoLoginParams && !isOnLoginPage && !isAutoLoginCompleted;\n\n  // Skip token validation entirely if we're in auto-login flow, it's completed, or we have auto-login user data\n  if (hasAutoLoginParams || isAutoLoginCompleted || hasAutoLoginUserData) {\n    // Skip token validation for auto-login flow\n  } else if (token) {\n    // Only validate JWT tokens for regular login flow\n    try {\n      const decodedToken = jwtDecode(token);\n      const currentTime = Math.floor(Date.now() / 1000);\n      if (decodedToken.exp < currentTime) {\n        // Double-check we're not in auto-login before clearing\n        if (!hasAutoLoginParams && !isAutoLoginCompleted && !hasAutoLoginUserData) {\n          localStorage.clear();\n          navigate(\"/login\");\n        }\n      }\n    } catch (error) {\n      // Check if this might be an auto-login token that's not a JWT or if we have valid user info\n      const userInfo = localStorage.getItem(\"userInfo\");\n      const hasValidUserInfo = userInfo && userInfo !== '{}' && userInfo !== 'null';\n      if (hasValidUserInfo) {\n        // Try to parse userInfo to see if it has valid oidc-info\n        try {\n          const parsedUserInfo = JSON.parse(userInfo);\n          if (parsedUserInfo['oidc-info'] && parsedUserInfo['user']) {\n            // Don't clear storage if we have valid user info - just skip the clearing logic\n          } else {\n            // Invalid userInfo, proceed with clearing\n            if (!hasAutoLoginParams && !isAutoLoginCompleted && !hasAutoLoginUserData) {\n              localStorage.clear();\n              navigate(\"/login\");\n            }\n          }\n        } catch (parseError) {\n          // If we can't parse userInfo, proceed with clearing\n          if (!hasAutoLoginParams && !isAutoLoginCompleted && !hasAutoLoginUserData) {\n            localStorage.clear();\n            navigate(\"/login\");\n          }\n        }\n      } else {\n        // No valid userInfo found, proceed with clearing\n        if (!hasAutoLoginParams && !isAutoLoginCompleted && !hasAutoLoginUserData) {\n          localStorage.clear();\n          navigate(\"/login\");\n        }\n      }\n    }\n  } else if (!hasAutoLoginParams && !isAutoLoginCompleted && !hasAutoLoginUserData) {\n    // No token and not in auto-login flow - check if we should redirect to login\n    const userInfo = localStorage.getItem(\"userInfo\");\n    const hasValidUserInfo = userInfo && userInfo !== '{}' && userInfo !== 'null';\n    if (!hasValidUserInfo && !isOnLoginPage) {\n      navigate(\"/login\");\n    }\n  }\n  const extractPasswordLogId = () => {\n    const match = location.pathname.match(new RegExp(`/resetpassword/(${uuidRegex})`));\n    return match ? match[1] : null;\n  };\n  const passwordLogId = extractPasswordLogId();\n  const checkResetLinkConsumed = useCallback(async passwordLogId => {\n    try {\n      setIsCheckingLink(true);\n      const response = await ResetPasswordLinkConsumed(passwordLogId);\n      if (response === true) {\n        const isConsumed = await response;\n        setIsResetLinkValid(!isConsumed);\n        if (isConsumed === true) {\n          navigate(\"/linkexpired\");\n        }\n      } else {\n        navigate(`/resetpassword/${passwordLogId}`);\n      }\n    } catch (error) {\n      navigate(\"/login\");\n    } finally {\n      setIsCheckingLink(false);\n    }\n  }, [navigate]);\n\n  // Handle auto-login redirect\n  useEffect(() => {\n    if (shouldRedirectToLogin) {\n      navigate('/login' + location.search, {\n        replace: true\n      });\n    }\n  }, [shouldRedirectToLogin, navigate, location.search]);\n  useEffect(() => {\n    if (location.pathname.includes(\"/resetpassword\") && passwordLogId) {\n      checkResetLinkConsumed(passwordLogId);\n    }\n  }, [passwordLogId, checkResetLinkConsumed, location.pathname]);\n\n  // Load QuickAdopt embedded script dynamically with user details\n  useEffect(() => {\n    const scriptUrl = process.env.REACT_APP_EMBEDDED_SCRIPT_URL;\n    const enableScript = process.env.REACT_APP_ENABLE_EMBEDDED_SCRIPT === 'true';\n    if (!enableScript) {\n      console.log('QuickAdopt embedded script is disabled via REACT_APP_ENABLE_EMBEDDED_SCRIPT environment variable');\n      return;\n    }\n    if (!scriptUrl) {\n      console.warn('REACT_APP_EMBEDDED_SCRIPT_URL environment variable is not set');\n      return;\n    }\n\n    // QuickAdopt script with user details capture\n    (function (g, u, i, d, e, s) {\n      g[e] = g[e] || [];\n      g.AccountId = s;\n      var f = u.getElementsByTagName(i)[0];\n      var k = u.createElement(i);\n      k.async = true;\n      k.src = scriptUrl;\n      f.parentNode.insertBefore(k, f);\n      k.onload = function () {\n        if (g.captureUserDetails) {\n          // Get user details from localStorage or context\n          const userInfo = JSON.parse(localStorage.getItem(\"userInfo\") || '{}');\n          const userDetails = userInfo['user'] ? JSON.parse(userInfo['user']) : {};\n          const orgDetails = userInfo['orgDetails'] ? JSON.parse(userInfo['orgDetails']) : {};\n          g.captureUserDetails({\n            OrganizationId: orgDetails.OrganizationId || '',\n            UserId: userDetails.UserId || '',\n            UserName: `${userDetails.FirstName || ''} ${userDetails.LastName || ''}`.trim(),\n            EmailId: userDetails.EmailId || '',\n            UserType: userDetails.UserType || '',\n            ScreenResolution: `${window.screen.width}x${window.screen.height}`,\n            SessionId: localStorage.getItem(\"access_token\") || '',\n            TimeZone: Intl.DateTimeFormat().resolvedOptions().timeZone || ''\n          });\n        }\n      };\n    })(window, document, 'script', 'guiding', 'layer', '25072025-080558004-6eaec78c-56d9-4013-ab79-aba4c21dbc44');\n    return () => {\n      // Cleanup: remove script when component unmounts\n      const existingScript = document.querySelector('script[src=\"' + scriptUrl + '\"]');\n      if (existingScript && document.body.contains(existingScript)) {\n        document.body.removeChild(existingScript);\n      }\n    };\n  }, []); // Empty dependency array means this runs once on mount\n\n  // Extension detection is now handled by ExtensionContext\n  if (isCheckingLink) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Show loading state when redirecting for auto-login\n  if (shouldRedirectToLogin) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"Redirecting to login...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(SnackbarProvider, {\n      children: /*#__PURE__*/_jsxDEV(ExtensionProvider, {\n        children: isNoLayoutRoute ? /*#__PURE__*/_jsxDEV(Routing, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(AuthProvider, {\n          children: /*#__PURE__*/_jsxDEV(AuthWrapper, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n}\n\n// AuthWrapper to handle loading state\n_s(App, \"wdWh1t4MLV5OMkyl163U4wfQ8LM=\", false, function () {\n  return [useLocation, useNavigate, useAuth];\n});\n_c = App;\nconst AuthWrapper = () => {\n  _s2();\n  const {\n    isLoading\n  } = useAuth();\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularIndeterminate, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(AccountProvider, {\n    children: /*#__PURE__*/_jsxDEV(Suspense, {\n      fallback: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 27\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(RtlProvider, {\n        children: /*#__PURE__*/_jsxDEV(Layout, {\n          children: /*#__PURE__*/_jsxDEV(Routing, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 263,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthWrapper, \"KgeqK6xiYzIUQYmsX3kNDmKeJRo=\", false, function () {\n  return [useAuth];\n});\n_c2 = AuthWrapper;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"App\");\n$RefreshReg$(_c2, \"AuthWrapper\");", "map": {"version": 3, "names": ["React", "Suspense", "useEffect", "useState", "useCallback", "CircularIndeterminate", "Layout", "RtlProvider", "SnackbarProvider", "Routing", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "useLocation", "useNavigate", "ResetPasswordLinkConsumed", "setupInterceptors", "Account<PERSON><PERSON><PERSON>", "ExtensionProvider", "jwtDecode", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "_s", "location", "navigate", "message", "severity", "signOut", "loggedOut", "isResetLinkValid", "setIsResetLinkValid", "isCheckingLink", "setIsCheckingLink", "loginUserDetails", "setLoginUserDetail", "noLayoutRoutes", "uuidRegex", "isNoLayoutRoute", "some", "route", "resetPasswordRegex", "RegExp", "test", "pathname", "urlParams", "URLSearchParams", "search", "isAutoLogin", "get", "urlToken", "hasAutoLoginParams", "has", "isAutoLoginCompleted", "sessionStorage", "getItem", "isOnLoginPage", "includes", "userInfo", "JSON", "parse", "localStorage", "hasAutoLoginUserData", "token", "shouldRedirectToLogin", "decodedToken", "currentTime", "Math", "floor", "Date", "now", "exp", "clear", "error", "hasValidUserInfo", "parsedUserInfo", "parseError", "extractPasswordLogId", "match", "passwordLogId", "checkResetLinkConsumed", "response", "isConsumed", "replace", "scriptUrl", "process", "env", "REACT_APP_EMBEDDED_SCRIPT_URL", "enableScript", "REACT_APP_ENABLE_EMBEDDED_SCRIPT", "console", "log", "warn", "g", "u", "i", "d", "e", "s", "AccountId", "f", "getElementsByTagName", "k", "createElement", "async", "src", "parentNode", "insertBefore", "onload", "captureUserDetails", "userDetails", "orgDetails", "OrganizationId", "UserId", "UserName", "FirstName", "LastName", "trim", "EmailId", "UserType", "ScreenResolution", "window", "screen", "width", "height", "SessionId", "TimeZone", "Intl", "DateTimeFormat", "resolvedOptions", "timeZone", "document", "existingScript", "querySelector", "body", "contains", "<PERSON><PERSON><PERSON><PERSON>", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "display", "justifyContent", "alignItems", "AuthWrapper", "_c", "_s2", "isLoading", "fallback", "_c2", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/App.tsx"], "sourcesContent": ["import React, { Suspense, lazy,useEffect,useState,useCallback } from \"react\";\r\nimport { BrowserRouter as Router } from \"react-router-dom\";\r\nimport \"./App.scss\";\r\nimport CircularIndeterminate from \"./components/common/CircularIndeterminate\";\r\nimport Layout from \"./components/layout/Layout\";\r\nimport { RtlProvider } from \"./RtlContext\";\r\nimport { SnackbarProvider } from \"./SnackbarContext\";\r\nimport Routing from \"./routing/Routings\";\r\nimport { AuthProvider, useAuth } from \"./components/auth/AuthProvider\";\r\nimport { useLocation,useNavigate } from \"react-router-dom\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport { ResetPasswordLinkConsumed } from \"./services/ExpirelinkService\";\r\nimport { setupInterceptors } from \"./services/APIService\";\r\nimport { AccountProvider } from \"./components/account/AccountContext\";\r\nimport { ExtensionProvider } from \"./ExtensionContext\";\r\nimport Cookies from \"js-cookie\";\r\nimport { User } from \"./models/User\";\r\nimport jwtDecode from \"jwt-decode\";\r\n\r\nfunction App() {\r\n\tconst location = useLocation();\r\n\t\t//const passwordLogId = useParams();\r\n  const navigate = useNavigate();\r\n  setupInterceptors(navigate, (message: string, severity = 'info') => {\r\n    \r\n  });\r\n\tconst { signOut, loggedOut } = useAuth();\r\n\t\t//const history = useHistory();\r\n  const [isResetLinkValid, setIsResetLinkValid] = useState(false);\r\n  const [isCheckingLink, setIsCheckingLink] = useState(false);\r\n  //const noLayoutRoutes = [\"/login\", \"/forgotpassword\", \"/resetpassword/passwordLogId\", \"/admin/adminlogin\"];\r\n  const [loginUserDetails, setLoginUserDetail] = useState<User | null>();\r\n  const noLayoutRoutes = [\"/login\", \"/forgotpassword\", \"/resetpassword/:passwordLogId\", \"/uninstall\",\"/admin/adminlogin\", \"/linkexpired\"];\r\n  const uuidRegex = \"[0-9a-fA-F-]{36}\";\r\n  const isNoLayoutRoute = noLayoutRoutes.some(route => {\r\n    if (route === \"/resetpassword/:passwordLogId\") {\r\n      const resetPasswordRegex = new RegExp(`^/resetpassword/${uuidRegex}$`);\r\n      return resetPasswordRegex.test(location.pathname);\r\n    }\r\n\r\n\t\treturn location.pathname === route;\r\n  });\r\n  // Check if this is an automatic login flow from free trial\r\n  const urlParams = new URLSearchParams(location.search);\r\n  const isAutoLogin = urlParams.get('auto_login') === 'true';\r\n  const urlToken = urlParams.get('token');\r\n\r\n  // Check if this is an auto-login flow first\r\n  const hasAutoLoginParams = urlParams.has('token') && urlParams.has('user_id') && urlParams.has('org_id');\r\n  const isAutoLoginCompleted = sessionStorage.getItem('autoLoginCompleted') === 'true';\r\n  const isOnLoginPage = location.pathname.includes('/login');\r\n\r\n  // Check if we have user data from auto-login (indicates completed auto-login)\r\n  const userInfo = JSON.parse(localStorage.getItem(\"userInfo\") || '{}');\r\n  const hasAutoLoginUserData = userInfo['user'] && userInfo['oidc-info'];\r\n\r\n  // Get token from URL if auto login, otherwise from localStorage\r\n  // Also check localStorage if auto-login was completed\r\n  const token = (isAutoLogin && urlToken) ? urlToken : localStorage.getItem(\"access_token\");\r\n\r\n\r\n\r\n  // Check if we need to redirect to login for auto-login processing\r\n  const shouldRedirectToLogin = hasAutoLoginParams && !isOnLoginPage && !isAutoLoginCompleted;\r\n\r\n  // Skip token validation entirely if we're in auto-login flow, it's completed, or we have auto-login user data\r\n  if (hasAutoLoginParams || isAutoLoginCompleted || hasAutoLoginUserData) {\r\n    // Skip token validation for auto-login flow\r\n  } else if (token) {\r\n    // Only validate JWT tokens for regular login flow\r\n    try {\r\n      const decodedToken: any = jwtDecode(token);\r\n      const currentTime = Math.floor(Date.now() / 1000);\r\n      if (decodedToken.exp < currentTime) {\r\n        // Double-check we're not in auto-login before clearing\r\n        if (!hasAutoLoginParams && !isAutoLoginCompleted && !hasAutoLoginUserData) {\r\n          localStorage.clear();\r\n          navigate(\"/login\")\r\n        }\r\n      }\r\n    } catch (error) {\r\n      // Check if this might be an auto-login token that's not a JWT or if we have valid user info\r\n      const userInfo = localStorage.getItem(\"userInfo\");\r\n      const hasValidUserInfo = userInfo && userInfo !== '{}' && userInfo !== 'null';\r\n\r\n      if (hasValidUserInfo) {\r\n        // Try to parse userInfo to see if it has valid oidc-info\r\n        try {\r\n          const parsedUserInfo = JSON.parse(userInfo);\r\n          if (parsedUserInfo['oidc-info'] && parsedUserInfo['user']) {\r\n            // Don't clear storage if we have valid user info - just skip the clearing logic\r\n          } else {\r\n            // Invalid userInfo, proceed with clearing\r\n            if (!hasAutoLoginParams && !isAutoLoginCompleted && !hasAutoLoginUserData) {\r\n              localStorage.clear();\r\n              navigate(\"/login\");\r\n            }\r\n          }\r\n        } catch (parseError) {\r\n          // If we can't parse userInfo, proceed with clearing\r\n          if (!hasAutoLoginParams && !isAutoLoginCompleted && !hasAutoLoginUserData) {\r\n            localStorage.clear();\r\n            navigate(\"/login\");\r\n          }\r\n        }\r\n      } else {\r\n        // No valid userInfo found, proceed with clearing\r\n        if (!hasAutoLoginParams && !isAutoLoginCompleted && !hasAutoLoginUserData) {\r\n          localStorage.clear();\r\n          navigate(\"/login\");\r\n        }\r\n      }\r\n    }\r\n  } else if (!hasAutoLoginParams && !isAutoLoginCompleted && !hasAutoLoginUserData) {\r\n    // No token and not in auto-login flow - check if we should redirect to login\r\n    const userInfo = localStorage.getItem(\"userInfo\");\r\n    const hasValidUserInfo = userInfo && userInfo !== '{}' && userInfo !== 'null';\r\n\r\n    if (!hasValidUserInfo && !isOnLoginPage) {\r\n      navigate(\"/login\");\r\n    }\r\n  }\r\n  const extractPasswordLogId = () => {\r\n    const match = location.pathname.match(new RegExp(`/resetpassword/(${uuidRegex})`));\r\n    return match ? match[1] : null;\r\n  };\r\n\r\n  const passwordLogId = extractPasswordLogId();\r\n  const checkResetLinkConsumed = useCallback(async (passwordLogId: any) => {\r\n    try {\r\n      setIsCheckingLink(true);\r\n\t\tconst response = await ResetPasswordLinkConsumed(passwordLogId);\r\n\r\n      if (response === true) {\r\n        const isConsumed = await response\r\n        setIsResetLinkValid(!isConsumed);\r\n        if (isConsumed === true) {\r\n          navigate(\"/linkexpired\");\r\n        }\r\n      } else {\r\n        navigate(`/resetpassword/${passwordLogId}`)\r\n      }\r\n\t} catch (error) {\r\n\r\n      \tnavigate(\"/login\")\r\n    } finally {\r\n      setIsCheckingLink(false);\r\n    }\r\n\t}, [navigate]);\r\n\r\n\r\n\r\n  // Handle auto-login redirect\r\n  useEffect(() => {\r\n    if (shouldRedirectToLogin) {\r\n      navigate('/login' + location.search, { replace: true });\r\n    }\r\n  }, [shouldRedirectToLogin, navigate, location.search]);\r\n\r\n  useEffect(() => {\r\n\tif (location.pathname.includes(\"/resetpassword\") && passwordLogId) {\r\n\t\tcheckResetLinkConsumed(passwordLogId);\r\n\t}\r\n  }, [passwordLogId, checkResetLinkConsumed, location.pathname]);\r\n\r\n  // Load QuickAdopt embedded script dynamically with user details\r\n  useEffect(() => {\r\n    const scriptUrl = process.env.REACT_APP_EMBEDDED_SCRIPT_URL;\r\n    const enableScript = process.env.REACT_APP_ENABLE_EMBEDDED_SCRIPT === 'true';\r\n\r\n    if (!enableScript) {\r\n      console.log('QuickAdopt embedded script is disabled via REACT_APP_ENABLE_EMBEDDED_SCRIPT environment variable');\r\n      return;\r\n    }\r\n\r\n    if (!scriptUrl) {\r\n      console.warn('REACT_APP_EMBEDDED_SCRIPT_URL environment variable is not set');\r\n      return;\r\n    }\r\n\r\n    // QuickAdopt script with user details capture\r\n    (function (g: any, u: any, i: any, d: any, e: any, s: any) {\r\n      g[e] = g[e] || []\r\n      g.AccountId = s;\r\n      var f = u.getElementsByTagName(i)[0]\r\n      var k = u.createElement(i) as HTMLScriptElement;\r\n      k.async = true;\r\n      k.src = scriptUrl;\r\n      f.parentNode.insertBefore(k, f);\r\n      k.onload = function() {\r\n        if (g.captureUserDetails) {\r\n          // Get user details from localStorage or context\r\n          const userInfo = JSON.parse(localStorage.getItem(\"userInfo\") || '{}');\r\n          const userDetails = userInfo['user'] ? JSON.parse(userInfo['user']) : {};\r\n          const orgDetails = userInfo['orgDetails'] ? JSON.parse(userInfo['orgDetails']) : {};\r\n\r\n          g.captureUserDetails({\r\n            OrganizationId: orgDetails.OrganizationId || '',\r\n            UserId: userDetails.UserId || '',\r\n            UserName: `${userDetails.FirstName || ''} ${userDetails.LastName || ''}`.trim(),\r\n            EmailId: userDetails.EmailId || '',\r\n            UserType: userDetails.UserType || '',\r\n            ScreenResolution: `${window.screen.width}x${window.screen.height}`,\r\n            SessionId: localStorage.getItem(\"access_token\") || '',\r\n            TimeZone: Intl.DateTimeFormat().resolvedOptions().timeZone || ''\r\n          });\r\n        }\r\n      }\r\n    })(window, document, 'script', 'guiding', 'layer', '25072025-080558004-6eaec78c-56d9-4013-ab79-aba4c21dbc44');\r\n\r\n    return () => {\r\n      // Cleanup: remove script when component unmounts\r\n      const existingScript = document.querySelector('script[src=\"' + scriptUrl + '\"]');\r\n      if (existingScript && document.body.contains(existingScript)) {\r\n        document.body.removeChild(existingScript);\r\n      }\r\n    };\r\n  }, []); // Empty dependency array means this runs once on mount\r\n\r\n  // Extension detection is now handled by ExtensionContext\r\n  if (isCheckingLink) {\r\n    return <div>Loading...</div>;\r\n  }\r\n\r\n  // Show loading state when redirecting for auto-login\r\n  if (shouldRedirectToLogin) {\r\n    return (\r\n      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>\r\n        <div>Redirecting to login...</div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n\t  <>\r\n      <SnackbarProvider>\r\n        <ExtensionProvider>\r\n          {isNoLayoutRoute ? (\r\n            <Routing />\r\n          ) : (\r\n            <AuthProvider>\r\n              <AuthWrapper />\r\n            </AuthProvider>\r\n          )}\r\n        </ExtensionProvider>\r\n      </SnackbarProvider>\r\n    </>\r\n  );\r\n\r\n}\r\n\r\n// AuthWrapper to handle loading state\r\nconst AuthWrapper: React.FC = () => {\r\n  const { isLoading } = useAuth();\r\n\r\n  if (isLoading) {\r\n    return <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>\r\n      <CircularIndeterminate />\r\n    </div>;\r\n  }\r\n\r\n  return (\r\n    <AccountProvider>\r\n      <Suspense fallback={<div>Loading...</div>}>\r\n        <RtlProvider>\r\n          <Layout>\r\n            <Routing />\r\n          </Layout>\r\n        </RtlProvider>\r\n      </Suspense>\r\n    </AccountProvider>\r\n  );\r\n};\r\n\r\nexport default App;\r\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAOC,SAAS,EAACC,QAAQ,EAACC,WAAW,QAAQ,OAAO;AAE5E,OAAO,YAAY;AACnB,OAAOC,qBAAqB,MAAM,2CAA2C;AAC7E,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,SAASC,WAAW,QAAQ,cAAc;AAC1C,SAASC,gBAAgB,QAAQ,mBAAmB;AACpD,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,YAAY,EAAEC,OAAO,QAAQ,gCAAgC;AACtE,SAASC,WAAW,EAACC,WAAW,QAAQ,kBAAkB;AAE1D,SAASC,yBAAyB,QAAQ,8BAA8B;AACxE,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,eAAe,QAAQ,qCAAqC;AACrE,SAASC,iBAAiB,QAAQ,oBAAoB;AAGtD,OAAOC,SAAS,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnC,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACd,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC7B;EACA,MAAMc,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9BE,iBAAiB,CAACW,QAAQ,EAAE,CAACC,OAAe,EAAEC,QAAQ,GAAG,MAAM,KAAK,CAEpE,CAAC,CAAC;EACH,MAAM;IAAEC,OAAO;IAAEC;EAAU,CAAC,GAAGnB,OAAO,CAAC,CAAC;EACvC;EACA,MAAM,CAACoB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC8B,cAAc,EAAEC,iBAAiB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC3D;EACA,MAAM,CAACgC,gBAAgB,EAAEC,kBAAkB,CAAC,GAAGjC,QAAQ,CAAc,CAAC;EACtE,MAAMkC,cAAc,GAAG,CAAC,QAAQ,EAAE,iBAAiB,EAAE,+BAA+B,EAAE,YAAY,EAAC,mBAAmB,EAAE,cAAc,CAAC;EACvI,MAAMC,SAAS,GAAG,kBAAkB;EACpC,MAAMC,eAAe,GAAGF,cAAc,CAACG,IAAI,CAACC,KAAK,IAAI;IACnD,IAAIA,KAAK,KAAK,+BAA+B,EAAE;MAC7C,MAAMC,kBAAkB,GAAG,IAAIC,MAAM,CAAC,mBAAmBL,SAAS,GAAG,CAAC;MACtE,OAAOI,kBAAkB,CAACE,IAAI,CAACnB,QAAQ,CAACoB,QAAQ,CAAC;IACnD;IAEF,OAAOpB,QAAQ,CAACoB,QAAQ,KAAKJ,KAAK;EAClC,CAAC,CAAC;EACF;EACA,MAAMK,SAAS,GAAG,IAAIC,eAAe,CAACtB,QAAQ,CAACuB,MAAM,CAAC;EACtD,MAAMC,WAAW,GAAGH,SAAS,CAACI,GAAG,CAAC,YAAY,CAAC,KAAK,MAAM;EAC1D,MAAMC,QAAQ,GAAGL,SAAS,CAACI,GAAG,CAAC,OAAO,CAAC;;EAEvC;EACA,MAAME,kBAAkB,GAAGN,SAAS,CAACO,GAAG,CAAC,OAAO,CAAC,IAAIP,SAAS,CAACO,GAAG,CAAC,SAAS,CAAC,IAAIP,SAAS,CAACO,GAAG,CAAC,QAAQ,CAAC;EACxG,MAAMC,oBAAoB,GAAGC,cAAc,CAACC,OAAO,CAAC,oBAAoB,CAAC,KAAK,MAAM;EACpF,MAAMC,aAAa,GAAGhC,QAAQ,CAACoB,QAAQ,CAACa,QAAQ,CAAC,QAAQ,CAAC;;EAE1D;EACA,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACN,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC;EACrE,MAAMO,oBAAoB,GAAGJ,QAAQ,CAAC,MAAM,CAAC,IAAIA,QAAQ,CAAC,WAAW,CAAC;;EAEtE;EACA;EACA,MAAMK,KAAK,GAAIf,WAAW,IAAIE,QAAQ,GAAIA,QAAQ,GAAGW,YAAY,CAACN,OAAO,CAAC,cAAc,CAAC;;EAIzF;EACA,MAAMS,qBAAqB,GAAGb,kBAAkB,IAAI,CAACK,aAAa,IAAI,CAACH,oBAAoB;;EAE3F;EACA,IAAIF,kBAAkB,IAAIE,oBAAoB,IAAIS,oBAAoB,EAAE;IACtE;EAAA,CACD,MAAM,IAAIC,KAAK,EAAE;IAChB;IACA,IAAI;MACF,MAAME,YAAiB,GAAGhD,SAAS,CAAC8C,KAAK,CAAC;MAC1C,MAAMG,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;MACjD,IAAIL,YAAY,CAACM,GAAG,GAAGL,WAAW,EAAE;QAClC;QACA,IAAI,CAACf,kBAAkB,IAAI,CAACE,oBAAoB,IAAI,CAACS,oBAAoB,EAAE;UACzED,YAAY,CAACW,KAAK,CAAC,CAAC;UACpB/C,QAAQ,CAAC,QAAQ,CAAC;QACpB;MACF;IACF,CAAC,CAAC,OAAOgD,KAAK,EAAE;MACd;MACA,MAAMf,QAAQ,GAAGG,YAAY,CAACN,OAAO,CAAC,UAAU,CAAC;MACjD,MAAMmB,gBAAgB,GAAGhB,QAAQ,IAAIA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,MAAM;MAE7E,IAAIgB,gBAAgB,EAAE;QACpB;QACA,IAAI;UACF,MAAMC,cAAc,GAAGhB,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC;UAC3C,IAAIiB,cAAc,CAAC,WAAW,CAAC,IAAIA,cAAc,CAAC,MAAM,CAAC,EAAE;YACzD;UAAA,CACD,MAAM;YACL;YACA,IAAI,CAACxB,kBAAkB,IAAI,CAACE,oBAAoB,IAAI,CAACS,oBAAoB,EAAE;cACzED,YAAY,CAACW,KAAK,CAAC,CAAC;cACpB/C,QAAQ,CAAC,QAAQ,CAAC;YACpB;UACF;QACF,CAAC,CAAC,OAAOmD,UAAU,EAAE;UACnB;UACA,IAAI,CAACzB,kBAAkB,IAAI,CAACE,oBAAoB,IAAI,CAACS,oBAAoB,EAAE;YACzED,YAAY,CAACW,KAAK,CAAC,CAAC;YACpB/C,QAAQ,CAAC,QAAQ,CAAC;UACpB;QACF;MACF,CAAC,MAAM;QACL;QACA,IAAI,CAAC0B,kBAAkB,IAAI,CAACE,oBAAoB,IAAI,CAACS,oBAAoB,EAAE;UACzED,YAAY,CAACW,KAAK,CAAC,CAAC;UACpB/C,QAAQ,CAAC,QAAQ,CAAC;QACpB;MACF;IACF;EACF,CAAC,MAAM,IAAI,CAAC0B,kBAAkB,IAAI,CAACE,oBAAoB,IAAI,CAACS,oBAAoB,EAAE;IAChF;IACA,MAAMJ,QAAQ,GAAGG,YAAY,CAACN,OAAO,CAAC,UAAU,CAAC;IACjD,MAAMmB,gBAAgB,GAAGhB,QAAQ,IAAIA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,MAAM;IAE7E,IAAI,CAACgB,gBAAgB,IAAI,CAAClB,aAAa,EAAE;MACvC/B,QAAQ,CAAC,QAAQ,CAAC;IACpB;EACF;EACA,MAAMoD,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,KAAK,GAAGtD,QAAQ,CAACoB,QAAQ,CAACkC,KAAK,CAAC,IAAIpC,MAAM,CAAC,mBAAmBL,SAAS,GAAG,CAAC,CAAC;IAClF,OAAOyC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI;EAChC,CAAC;EAED,MAAMC,aAAa,GAAGF,oBAAoB,CAAC,CAAC;EAC5C,MAAMG,sBAAsB,GAAG7E,WAAW,CAAC,MAAO4E,aAAkB,IAAK;IACvE,IAAI;MACF9C,iBAAiB,CAAC,IAAI,CAAC;MAC3B,MAAMgD,QAAQ,GAAG,MAAMpE,yBAAyB,CAACkE,aAAa,CAAC;MAE3D,IAAIE,QAAQ,KAAK,IAAI,EAAE;QACrB,MAAMC,UAAU,GAAG,MAAMD,QAAQ;QACjClD,mBAAmB,CAAC,CAACmD,UAAU,CAAC;QAChC,IAAIA,UAAU,KAAK,IAAI,EAAE;UACvBzD,QAAQ,CAAC,cAAc,CAAC;QAC1B;MACF,CAAC,MAAM;QACLA,QAAQ,CAAC,kBAAkBsD,aAAa,EAAE,CAAC;MAC7C;IACL,CAAC,CAAC,OAAON,KAAK,EAAE;MAEVhD,QAAQ,CAAC,QAAQ,CAAC;IACrB,CAAC,SAAS;MACRQ,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACH,CAAC,EAAE,CAACR,QAAQ,CAAC,CAAC;;EAIb;EACAxB,SAAS,CAAC,MAAM;IACd,IAAI+D,qBAAqB,EAAE;MACzBvC,QAAQ,CAAC,QAAQ,GAAGD,QAAQ,CAACuB,MAAM,EAAE;QAAEoC,OAAO,EAAE;MAAK,CAAC,CAAC;IACzD;EACF,CAAC,EAAE,CAACnB,qBAAqB,EAAEvC,QAAQ,EAAED,QAAQ,CAACuB,MAAM,CAAC,CAAC;EAEtD9C,SAAS,CAAC,MAAM;IACjB,IAAIuB,QAAQ,CAACoB,QAAQ,CAACa,QAAQ,CAAC,gBAAgB,CAAC,IAAIsB,aAAa,EAAE;MAClEC,sBAAsB,CAACD,aAAa,CAAC;IACtC;EACC,CAAC,EAAE,CAACA,aAAa,EAAEC,sBAAsB,EAAExD,QAAQ,CAACoB,QAAQ,CAAC,CAAC;;EAE9D;EACA3C,SAAS,CAAC,MAAM;IACd,MAAMmF,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,6BAA6B;IAC3D,MAAMC,YAAY,GAAGH,OAAO,CAACC,GAAG,CAACG,gCAAgC,KAAK,MAAM;IAE5E,IAAI,CAACD,YAAY,EAAE;MACjBE,OAAO,CAACC,GAAG,CAAC,kGAAkG,CAAC;MAC/G;IACF;IAEA,IAAI,CAACP,SAAS,EAAE;MACdM,OAAO,CAACE,IAAI,CAAC,+DAA+D,CAAC;MAC7E;IACF;;IAEA;IACA,CAAC,UAAUC,CAAM,EAAEC,CAAM,EAAEC,CAAM,EAAEC,CAAM,EAAEC,CAAM,EAAEC,CAAM,EAAE;MACzDL,CAAC,CAACI,CAAC,CAAC,GAAGJ,CAAC,CAACI,CAAC,CAAC,IAAI,EAAE;MACjBJ,CAAC,CAACM,SAAS,GAAGD,CAAC;MACf,IAAIE,CAAC,GAAGN,CAAC,CAACO,oBAAoB,CAACN,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,IAAIO,CAAC,GAAGR,CAAC,CAACS,aAAa,CAACR,CAAC,CAAsB;MAC/CO,CAAC,CAACE,KAAK,GAAG,IAAI;MACdF,CAAC,CAACG,GAAG,GAAGrB,SAAS;MACjBgB,CAAC,CAACM,UAAU,CAACC,YAAY,CAACL,CAAC,EAAEF,CAAC,CAAC;MAC/BE,CAAC,CAACM,MAAM,GAAG,YAAW;QACpB,IAAIf,CAAC,CAACgB,kBAAkB,EAAE;UACxB;UACA,MAAMnD,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACN,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC;UACrE,MAAMuD,WAAW,GAAGpD,QAAQ,CAAC,MAAM,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;UACxE,MAAMqD,UAAU,GAAGrD,QAAQ,CAAC,YAAY,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC;UAEnFmC,CAAC,CAACgB,kBAAkB,CAAC;YACnBG,cAAc,EAAED,UAAU,CAACC,cAAc,IAAI,EAAE;YAC/CC,MAAM,EAAEH,WAAW,CAACG,MAAM,IAAI,EAAE;YAChCC,QAAQ,EAAE,GAAGJ,WAAW,CAACK,SAAS,IAAI,EAAE,IAAIL,WAAW,CAACM,QAAQ,IAAI,EAAE,EAAE,CAACC,IAAI,CAAC,CAAC;YAC/EC,OAAO,EAAER,WAAW,CAACQ,OAAO,IAAI,EAAE;YAClCC,QAAQ,EAAET,WAAW,CAACS,QAAQ,IAAI,EAAE;YACpCC,gBAAgB,EAAE,GAAGC,MAAM,CAACC,MAAM,CAACC,KAAK,IAAIF,MAAM,CAACC,MAAM,CAACE,MAAM,EAAE;YAClEC,SAAS,EAAEhE,YAAY,CAACN,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE;YACrDuE,QAAQ,EAAEC,IAAI,CAACC,cAAc,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC,CAACC,QAAQ,IAAI;UAChE,CAAC,CAAC;QACJ;MACF,CAAC;IACH,CAAC,EAAET,MAAM,EAAEU,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,yDAAyD,CAAC;IAE7G,OAAO,MAAM;MACX;MACA,MAAMC,cAAc,GAAGD,QAAQ,CAACE,aAAa,CAAC,cAAc,GAAGjD,SAAS,GAAG,IAAI,CAAC;MAChF,IAAIgD,cAAc,IAAID,QAAQ,CAACG,IAAI,CAACC,QAAQ,CAACH,cAAc,CAAC,EAAE;QAC5DD,QAAQ,CAACG,IAAI,CAACE,WAAW,CAACJ,cAAc,CAAC;MAC3C;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACA,IAAIpG,cAAc,EAAE;IAClB,oBAAOb,OAAA;MAAAsH,QAAA,EAAK;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC9B;;EAEA;EACA,IAAI7E,qBAAqB,EAAE;IACzB,oBACE7C,OAAA;MAAK2H,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,UAAU,EAAE,QAAQ;QAAErB,MAAM,EAAE;MAAQ,CAAE;MAAAa,QAAA,eAC/FtH,OAAA;QAAAsH,QAAA,EAAK;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC;EAEV;EAEA,oBACC1H,OAAA,CAAAE,SAAA;IAAAoH,QAAA,eACGtH,OAAA,CAACZ,gBAAgB;MAAAkI,QAAA,eACftH,OAAA,CAACH,iBAAiB;QAAAyH,QAAA,EACfnG,eAAe,gBACdnB,OAAA,CAACX,OAAO;UAAAkI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEX1H,OAAA,CAACV,YAAY;UAAAgI,QAAA,eACXtH,OAAA,CAAC+H,WAAW;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACf;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACgB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC,gBACnB,CAAC;AAGP;;AAEA;AAAAtH,EAAA,CAxOSD,GAAG;EAAA,QACMX,WAAW,EAEVC,WAAW,EAIEF,OAAO;AAAA;AAAAyI,EAAA,GAP9B7H,GAAG;AAyOZ,MAAM4H,WAAqB,GAAGA,CAAA,KAAM;EAAAE,GAAA;EAClC,MAAM;IAAEC;EAAU,CAAC,GAAG3I,OAAO,CAAC,CAAC;EAE/B,IAAI2I,SAAS,EAAE;IACb,oBAAOlI,OAAA;MAAK2H,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,UAAU,EAAE,QAAQ;QAAErB,MAAM,EAAE;MAAQ,CAAE;MAAAa,QAAA,eACtGtH,OAAA,CAACf,qBAAqB;QAAAsI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EACR;EAEA,oBACE1H,OAAA,CAACJ,eAAe;IAAA0H,QAAA,eACdtH,OAAA,CAACnB,QAAQ;MAACsJ,QAAQ,eAAEnI,OAAA;QAAAsH,QAAA,EAAK;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAE;MAAAJ,QAAA,eACxCtH,OAAA,CAACb,WAAW;QAAAmI,QAAA,eACVtH,OAAA,CAACd,MAAM;UAAAoI,QAAA,eACLtH,OAAA,CAACX,OAAO;YAAAkI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEtB,CAAC;AAACO,GAAA,CApBIF,WAAqB;EAAA,QACHxI,OAAO;AAAA;AAAA6I,GAAA,GADzBL,WAAqB;AAsB3B,eAAe5H,GAAG;AAAC,IAAA6H,EAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAL,EAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}