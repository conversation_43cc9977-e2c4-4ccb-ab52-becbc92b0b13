{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptWeb\\\\src\\\\components\\\\auth\\\\AuthProvider.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport userManager from './UseAuth';\nimport jwt_decode from \"jwt-decode\";\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { startTransition } from 'react';\nimport { getRolesByUser } from '../../services/UserRoleService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nlet userLocalData = {};\nconst AuthContext = /*#__PURE__*/createContext(undefined);\nlet initialsData;\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  var _location$state;\n  const location = useLocation();\n  const [user, setUser] = useState(null);\n  const noLayoutRoutes = [\"/login\", \"/forgotpassword\", \"/resetpassword/:passwordLogId\", \"/uninstall\", \"/admin/adminlogin\", \"/linkexpired\"];\n  const uuidRegex = \"[0-9a-fA-F-]{36}\";\n  const [userDetails, setUserDetails] = useState(((_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.userDetail) || null);\n  const [loggedOut, setLoggedOut] = useState(false);\n  const [userRoles, setUserRoles] = useState({});\n  const [isLoading, setIsLoading] = useState(true);\n  const navigate = useNavigate();\n  const calculateInitials = (firstName, lastName) => {\n    const firstInitial = firstName ? firstName[0].toUpperCase() : '';\n    const lastInitial = lastName ? lastName[0].toUpperCase() : '';\n    return firstInitial + lastInitial;\n  };\n  // setUserDetails(location.state?.userDetails);\n\n  useEffect(() => {\n    const initializeUser = async () => {\n      if (loggedOut) return; // Skip reinitialization if user has logged out\n\n      try {\n        const userInfo = JSON.parse(localStorage.getItem(\"userInfo\") || '{}');\n        if (userInfo['oidc-info'] && userInfo['user']) {\n          const user = JSON.parse(userInfo['user']);\n          const oidcinfo = JSON.parse(userInfo['oidc-info']);\n          setUser(user);\n          if (oidcinfo.access_token) {\n            try {\n              // Try to decode as JWT for regular login tokens\n              const decodedToken = jwt_decode(oidcinfo.access_token);\n              localStorage.setItem(\"userType\", user.UserType);\n              setUserDetails(user);\n              GetUserRoles();\n              const finalData = calculateInitials(user !== null && user !== void 0 && user.FirstName ? user === null || user === void 0 ? void 0 : user.FirstName.substring(0, 1).toUpperCase() : '', user !== null && user !== void 0 && user.LastName ? user === null || user === void 0 ? void 0 : user.LastName.substring(0, 1).toUpperCase() : '');\n              initialsData = finalData;\n            } catch (error) {\n              // If JWT decode fails, still set user details for auto-login flow\n              localStorage.setItem(\"userType\", user.UserType);\n              setUserDetails(user);\n              GetUserRoles();\n              const finalData = calculateInitials(user !== null && user !== void 0 && user.FirstName ? user === null || user === void 0 ? void 0 : user.FirstName.substring(0, 1).toUpperCase() : '', user !== null && user !== void 0 && user.LastName ? user === null || user === void 0 ? void 0 : user.LastName.substring(0, 1).toUpperCase() : '');\n              initialsData = finalData;\n            }\n          }\n        } else {\n          // Check if we have user data from auto-login (might not have oidc-info)\n          const hasUserData = userInfo['user'];\n          const isAutoLoginCompleted = sessionStorage.getItem('autoLoginCompleted') === 'true';\n          const isNoLayoutRoute = noLayoutRoutes.some(route => {\n            if (route === \"/resetpassword/:passwordLogId\") {\n              const resetPasswordRegex = new RegExp(`^/resetpassword/${uuidRegex}$`);\n              return resetPasswordRegex.test(location.pathname);\n            }\n            return location.pathname === route;\n          });\n\n          // Don't sign out if we have user data (auto-login case) or if we're on a no-layout route\n          if (!isNoLayoutRoute && !hasUserData && !isAutoLoginCompleted) {\n            signOut();\n          } else if (hasUserData) {\n            // Handle auto-login case where we have user data but no oidc-info\n            const user = JSON.parse(userInfo['user']);\n            setUserDetails(user);\n            localStorage.setItem(\"userType\", user.UserType);\n            const finalData = calculateInitials(user !== null && user !== void 0 && user.FirstName ? user === null || user === void 0 ? void 0 : user.FirstName.substring(0, 1).toUpperCase() : '', user !== null && user !== void 0 && user.LastName ? user === null || user === void 0 ? void 0 : user.LastName.substring(0, 1).toUpperCase() : '');\n            initialsData = finalData;\n          }\n        }\n      } catch (error) {\n        console.error('Failed to fetch user details:', error);\n        userManager.signoutRedirect();\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    startTransition(() => {\n      initializeUser();\n    });\n  }, [loggedOut]);\n  const GetUserRoles = async () => {\n    try {\n      const rolesData = await getRolesByUser();\n      console.log(rolesData);\n      const dist = rolesData.reduce((acc, curr) => {\n        if (!acc[curr.AccountId]) {\n          acc[curr.AccountId] = [];\n        }\n        if (!acc[curr.AccountId].includes(curr.RoleName)) {\n          acc[curr.AccountId].push(curr.RoleName);\n        }\n        return acc;\n      }, {});\n      setUserRoles(dist);\n    } catch (e) {}\n  };\n  const signOut = () => {\n    const logeduserType = localStorage.getItem('userType');\n    setLoggedOut(true);\n    startTransition(() => {\n      setUser(null);\n      setUserDetails(null);\n      localStorage.clear();\n      document.cookie.split(\";\").forEach(cookie => {\n        const [name] = cookie.split(\"=\");\n        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;\n      });\n      localStorage.setItem('logout-event', Date.now().toString());\n      if ((logeduserType === null || logeduserType === void 0 ? void 0 : logeduserType.toLowerCase()) !== \"superadmin\") {\n        navigate(\"/login\");\n      } else {\n        navigate(\"/admin/adminlogin\");\n      }\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: {\n      user,\n      userDetails,\n      signOut,\n      loggedOut,\n      userRoles,\n      isLoading\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 174,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthProvider, \"RnGAdj3cK9vJTpiXjv17/KksqKo=\", false, function () {\n  return [useLocation, useNavigate];\n});\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  var _context;\n  let context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  if ((_context = context) !== null && _context !== void 0 && _context.user) {\n    const userInfo = JSON.parse(localStorage.getItem(\"userInfo\") || '{}');\n    userLocalData[\"oidc-info\"] = JSON.stringify(context.user);\n    if (userInfo['user']) {\n      userLocalData[\"user\"] = JSON.stringify(userInfo['user']);\n    }\n  } else {\n    const userInfo = JSON.parse(localStorage.getItem(\"userInfo\") || '{}');\n    if (userInfo['oidc-info'] && userInfo['user']) {\n      context = {\n        ...context,\n        user: JSON.parse(userInfo['oidc-info'])\n      };\n      context.userDetails = JSON.parse(userInfo['user']);\n      context.loggedOut = false;\n    }\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport { initialsData };\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "userManager", "jwt_decode", "useNavigate", "useLocation", "startTransition", "getRolesByUser", "jsxDEV", "_jsxDEV", "userLocalData", "AuthContext", "undefined", "initialsData", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "_location$state", "location", "user", "setUser", "noLayoutRoutes", "uuidRegex", "userDetails", "setUserDetails", "state", "userDetail", "loggedOut", "setLoggedOut", "userRoles", "setUserRoles", "isLoading", "setIsLoading", "navigate", "calculateInitials", "firstName", "lastName", "firstInitial", "toUpperCase", "lastInitial", "initializeUser", "userInfo", "JSON", "parse", "localStorage", "getItem", "oidcinfo", "access_token", "decodedToken", "setItem", "UserType", "GetUserRoles", "finalData", "FirstName", "substring", "LastName", "error", "hasUserData", "isAutoLoginCompleted", "sessionStorage", "isNoLayoutRoute", "some", "route", "resetPasswordRegex", "RegExp", "test", "pathname", "signOut", "console", "signoutRedirect", "rolesData", "log", "dist", "reduce", "acc", "curr", "AccountId", "includes", "RoleName", "push", "e", "logeduserType", "clear", "document", "cookie", "split", "for<PERSON>ach", "name", "Date", "now", "toString", "toLowerCase", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "_context", "context", "Error", "stringify", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/components/auth/AuthProvider.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';\r\nimport { User, UserManager } from 'oidc-client-ts';\r\nimport userManager from './UseAuth';\r\nimport { LoginUserInfo } from '../../models/LoginUserInfo';\r\nimport jwt_decode from \"jwt-decode\";\r\nimport { User as LoginUser } from '../../models/User';\r\nimport { useNavigate, useLocation } from 'react-router-dom';\r\nimport { Content } from 'antd/es/layout/layout';\r\nimport { startTransition } from 'react';\r\nimport axios from 'axios';\r\nimport { getRolesByUser, getUserRoles } from '../../services/UserRoleService';\r\ninterface AuthContextType {\r\n  user: User | null;\r\n  userDetails: LoginUser | null;\r\n  signOut: () => void;\r\n  loggedOut: boolean;\r\n  userRoles: any;\r\n  isLoading: boolean;\r\n}\r\nlet userLocalData: { [key: string]: any } = {}\r\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\r\n\r\ninterface AuthProviderProps {\r\n  children: ReactNode;\r\n}\r\nlet initialsData: string;\r\n\r\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\r\n  const location = useLocation();\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const noLayoutRoutes = [\"/login\", \"/forgotpassword\", \"/resetpassword/:passwordLogId\",\"/uninstall\", \"/admin/adminlogin\", \"/linkexpired\"];\r\n\tconst uuidRegex = \"[0-9a-fA-F-]{36}\";\r\n  const [userDetails, setUserDetails] = useState<LoginUser | null>(location.state?.userDetail || null);\r\n  const [loggedOut, setLoggedOut] = useState<boolean>(false);\r\n  const [userRoles, setUserRoles] = useState({});\r\n  const [isLoading, setIsLoading] = useState<boolean>(true);\r\n\r\n  const navigate = useNavigate();\r\n\r\n  const calculateInitials = (firstName?: string, lastName?: string) => {\r\n    const firstInitial = firstName ? firstName[0].toUpperCase() : '';\r\n    const lastInitial = lastName ? lastName[0].toUpperCase() : '';\r\n    return firstInitial + lastInitial;\r\n  };\r\n  // setUserDetails(location.state?.userDetails);\r\n  \r\n  useEffect(() => {\r\n    const initializeUser = async () => {\r\n      if (loggedOut) return;  // Skip reinitialization if user has logged out\r\n\r\n      try {\r\n        const userInfo = JSON.parse(localStorage.getItem(\"userInfo\") || '{}');\r\n        if (userInfo['oidc-info'] && userInfo['user']) {\r\n          const user = JSON.parse(userInfo['user'])\r\n          const oidcinfo = JSON.parse(userInfo['oidc-info'])\r\n          setUser(user);\r\n          if (oidcinfo.access_token) {\r\n            try {\r\n              // Try to decode as JWT for regular login tokens\r\n              const decodedToken = jwt_decode<LoginUserInfo>(oidcinfo.access_token);\r\n              localStorage.setItem(\"userType\", user.UserType);\r\n              setUserDetails(user);\r\n              GetUserRoles();\r\n              const finalData = calculateInitials(\r\n                user?.FirstName ? user?.FirstName.substring(0, 1).toUpperCase() : '',\r\n                user?.LastName ? user?.LastName.substring(0, 1).toUpperCase() : ''\r\n              );\r\n              initialsData = finalData;\r\n            } catch (error) {\r\n              // If JWT decode fails, still set user details for auto-login flow\r\n              localStorage.setItem(\"userType\", user.UserType);\r\n              setUserDetails(user);\r\n              GetUserRoles();\r\n              const finalData = calculateInitials(\r\n                user?.FirstName ? user?.FirstName.substring(0, 1).toUpperCase() : '',\r\n                user?.LastName ? user?.LastName.substring(0, 1).toUpperCase() : ''\r\n              );\r\n              initialsData = finalData;\r\n            }\r\n          }\r\n        }\r\n        else {\r\n          // Check if we have user data from auto-login (might not have oidc-info)\r\n          const hasUserData = userInfo['user'];\r\n          const isAutoLoginCompleted = sessionStorage.getItem('autoLoginCompleted') === 'true';\r\n\r\n          const isNoLayoutRoute = noLayoutRoutes.some(route => {\r\n            if (route === \"/resetpassword/:passwordLogId\") {\r\n              const resetPasswordRegex = new RegExp(`^/resetpassword/${uuidRegex}$`);\r\n              return resetPasswordRegex.test(location.pathname);\r\n            }\r\n            return location.pathname === route;\r\n          });\r\n\r\n          // Don't sign out if we have user data (auto-login case) or if we're on a no-layout route\r\n          if (!isNoLayoutRoute && !hasUserData && !isAutoLoginCompleted) {\r\n            signOut();\r\n          } else if (hasUserData) {\r\n            // Handle auto-login case where we have user data but no oidc-info\r\n            const user = JSON.parse(userInfo['user']);\r\n            setUserDetails(user);\r\n            localStorage.setItem(\"userType\", user.UserType);\r\n            const finalData = calculateInitials(\r\n              user?.FirstName ? user?.FirstName.substring(0, 1).toUpperCase() : '',\r\n              user?.LastName ? user?.LastName.substring(0, 1).toUpperCase() : ''\r\n            );\r\n            initialsData = finalData;\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Failed to fetch user details:', error);\r\n        userManager.signoutRedirect();\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n  \r\n    startTransition(() => {\r\n      initializeUser();\r\n    });\r\n  }, [loggedOut]);\r\n  \r\n  const GetUserRoles = async () => {\r\n    try {\r\n      const rolesData = await getRolesByUser();\r\n      console.log(rolesData);\r\n      const dist = rolesData.reduce((acc: { [x: string]: any[]; }, curr: { AccountId: string | number; RoleName: any; }) => {\r\n        if (!acc[curr.AccountId]) {\r\n          acc[curr.AccountId] = [];\r\n        }\r\n        if (!acc[curr.AccountId].includes(curr.RoleName)) {\r\n          acc[curr.AccountId].push(curr.RoleName);\r\n        }\r\n        return acc;\r\n      }, {});\r\n\r\n      setUserRoles(dist);\r\n              \r\n      \r\n    } catch (e) {\r\n      \r\n    }\r\n  }\r\n  \r\n  \r\n\r\n\r\n\r\n\r\n  const signOut = () => {\r\n   \r\n    const logeduserType = localStorage.getItem('userType');\r\n   setLoggedOut(true);\r\n   startTransition(() => {\r\n     setUser(null);\r\n     setUserDetails(null);\r\n     localStorage.clear();\r\n     document.cookie.split(\";\").forEach(cookie => {\r\n       const [name] = cookie.split(\"=\");\r\n       document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;\r\n     });\r\n     localStorage.setItem('logout-event', Date.now().toString());\r\n     if (logeduserType?.toLowerCase() !== \"superadmin\") {\r\n       navigate(\"/login\");\r\n     } else {\r\n       navigate(\"/admin/adminlogin\");\r\n     }\r\n   });\r\n};\r\n\r\n  \r\n\r\n  return (\r\n    <AuthContext.Provider value={{ user, userDetails, signOut,loggedOut,userRoles,isLoading }}>\r\n      {children}\r\n    </AuthContext.Provider>\r\n    );\r\n    \r\n};\r\n\r\nexport const useAuth = (): AuthContextType => {\r\n  let context = useContext(AuthContext);\r\n\r\n  if (context === undefined) {\r\n    throw new Error('useAuth must be used within an AuthProvider');\r\n  }\r\n  if (context?.user) {\r\n    const userInfo = JSON.parse(localStorage.getItem(\"userInfo\") || '{}');\r\n    userLocalData[\"oidc-info\"] = JSON.stringify(context.user);    \r\n\r\n    if (userInfo['user']) {\r\n      userLocalData[\"user\"] =  JSON.stringify(userInfo['user'])\r\n    }   \r\n  } \r\n  else {\r\n    const userInfo = JSON.parse(localStorage.getItem(\"userInfo\") || '{}');\r\n    if (userInfo['oidc-info'] && userInfo['user']) {\r\n      context = { ...context, user: JSON.parse(userInfo['oidc-info']) };\r\n      context.userDetails =  JSON.parse(userInfo['user'])\r\n      context.loggedOut = false;\r\n    }\r\n  }\r\n\r\n  return context;\r\n};\r\n\r\nexport {initialsData}"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAmB,OAAO;AAExF,OAAOC,WAAW,MAAM,WAAW;AAEnC,OAAOC,UAAU,MAAM,YAAY;AAEnC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAE3D,SAASC,eAAe,QAAQ,OAAO;AAEvC,SAASC,cAAc,QAAsB,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAS9E,IAAIC,aAAqC,GAAG,CAAC,CAAC;AAC9C,MAAMC,WAAW,gBAAGb,aAAa,CAA8Bc,SAAS,CAAC;AAKzE,IAAIC,YAAoB;AAExB,OAAO,MAAMC,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,eAAA;EACzE,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACc,IAAI,EAAEC,OAAO,CAAC,GAAGnB,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAMoB,cAAc,GAAG,CAAC,QAAQ,EAAE,iBAAiB,EAAE,+BAA+B,EAAC,YAAY,EAAE,mBAAmB,EAAE,cAAc,CAAC;EACxI,MAAMC,SAAS,GAAG,kBAAkB;EACnC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAmB,EAAAgB,eAAA,GAAAC,QAAQ,CAACO,KAAK,cAAAR,eAAA,uBAAdA,eAAA,CAAgBS,UAAU,KAAI,IAAI,CAAC;EACpG,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAU,KAAK,CAAC;EAC1D,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9C,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAU,IAAI,CAAC;EAEzD,MAAMgC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAE9B,MAAM8B,iBAAiB,GAAGA,CAACC,SAAkB,EAAEC,QAAiB,KAAK;IACnE,MAAMC,YAAY,GAAGF,SAAS,GAAGA,SAAS,CAAC,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC,GAAG,EAAE;IAChE,MAAMC,WAAW,GAAGH,QAAQ,GAAGA,QAAQ,CAAC,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC,GAAG,EAAE;IAC7D,OAAOD,YAAY,GAAGE,WAAW;EACnC,CAAC;EACD;;EAEAvC,SAAS,CAAC,MAAM;IACd,MAAMwC,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAIb,SAAS,EAAE,OAAO,CAAE;;MAExB,IAAI;QACF,MAAMc,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC;QACrE,IAAIJ,QAAQ,CAAC,WAAW,CAAC,IAAIA,QAAQ,CAAC,MAAM,CAAC,EAAE;UAC7C,MAAMtB,IAAI,GAAGuB,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,MAAM,CAAC,CAAC;UACzC,MAAMK,QAAQ,GAAGJ,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,WAAW,CAAC,CAAC;UAClDrB,OAAO,CAACD,IAAI,CAAC;UACb,IAAI2B,QAAQ,CAACC,YAAY,EAAE;YACzB,IAAI;cACF;cACA,MAAMC,YAAY,GAAG7C,UAAU,CAAgB2C,QAAQ,CAACC,YAAY,CAAC;cACrEH,YAAY,CAACK,OAAO,CAAC,UAAU,EAAE9B,IAAI,CAAC+B,QAAQ,CAAC;cAC/C1B,cAAc,CAACL,IAAI,CAAC;cACpBgC,YAAY,CAAC,CAAC;cACd,MAAMC,SAAS,GAAGlB,iBAAiB,CACjCf,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkC,SAAS,GAAGlC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkC,SAAS,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAChB,WAAW,CAAC,CAAC,GAAG,EAAE,EACpEnB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEoC,QAAQ,GAAGpC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,QAAQ,CAACD,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAChB,WAAW,CAAC,CAAC,GAAG,EAClE,CAAC;cACDzB,YAAY,GAAGuC,SAAS;YAC1B,CAAC,CAAC,OAAOI,KAAK,EAAE;cACd;cACAZ,YAAY,CAACK,OAAO,CAAC,UAAU,EAAE9B,IAAI,CAAC+B,QAAQ,CAAC;cAC/C1B,cAAc,CAACL,IAAI,CAAC;cACpBgC,YAAY,CAAC,CAAC;cACd,MAAMC,SAAS,GAAGlB,iBAAiB,CACjCf,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkC,SAAS,GAAGlC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkC,SAAS,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAChB,WAAW,CAAC,CAAC,GAAG,EAAE,EACpEnB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEoC,QAAQ,GAAGpC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,QAAQ,CAACD,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAChB,WAAW,CAAC,CAAC,GAAG,EAClE,CAAC;cACDzB,YAAY,GAAGuC,SAAS;YAC1B;UACF;QACF,CAAC,MACI;UACH;UACA,MAAMK,WAAW,GAAGhB,QAAQ,CAAC,MAAM,CAAC;UACpC,MAAMiB,oBAAoB,GAAGC,cAAc,CAACd,OAAO,CAAC,oBAAoB,CAAC,KAAK,MAAM;UAEpF,MAAMe,eAAe,GAAGvC,cAAc,CAACwC,IAAI,CAACC,KAAK,IAAI;YACnD,IAAIA,KAAK,KAAK,+BAA+B,EAAE;cAC7C,MAAMC,kBAAkB,GAAG,IAAIC,MAAM,CAAC,mBAAmB1C,SAAS,GAAG,CAAC;cACtE,OAAOyC,kBAAkB,CAACE,IAAI,CAAC/C,QAAQ,CAACgD,QAAQ,CAAC;YACnD;YACA,OAAOhD,QAAQ,CAACgD,QAAQ,KAAKJ,KAAK;UACpC,CAAC,CAAC;;UAEF;UACA,IAAI,CAACF,eAAe,IAAI,CAACH,WAAW,IAAI,CAACC,oBAAoB,EAAE;YAC7DS,OAAO,CAAC,CAAC;UACX,CAAC,MAAM,IAAIV,WAAW,EAAE;YACtB;YACA,MAAMtC,IAAI,GAAGuB,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,MAAM,CAAC,CAAC;YACzCjB,cAAc,CAACL,IAAI,CAAC;YACpByB,YAAY,CAACK,OAAO,CAAC,UAAU,EAAE9B,IAAI,CAAC+B,QAAQ,CAAC;YAC/C,MAAME,SAAS,GAAGlB,iBAAiB,CACjCf,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkC,SAAS,GAAGlC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkC,SAAS,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAChB,WAAW,CAAC,CAAC,GAAG,EAAE,EACpEnB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEoC,QAAQ,GAAGpC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,QAAQ,CAACD,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAChB,WAAW,CAAC,CAAC,GAAG,EAClE,CAAC;YACDzB,YAAY,GAAGuC,SAAS;UAC1B;QACF;MACF,CAAC,CAAC,OAAOI,KAAK,EAAE;QACdY,OAAO,CAACZ,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrDtD,WAAW,CAACmE,eAAe,CAAC,CAAC;MAC/B,CAAC,SAAS;QACRrC,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAED1B,eAAe,CAAC,MAAM;MACpBkC,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACb,SAAS,CAAC,CAAC;EAEf,MAAMwB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMmB,SAAS,GAAG,MAAM/D,cAAc,CAAC,CAAC;MACxC6D,OAAO,CAACG,GAAG,CAACD,SAAS,CAAC;MACtB,MAAME,IAAI,GAAGF,SAAS,CAACG,MAAM,CAAC,CAACC,GAA4B,EAAEC,IAAoD,KAAK;QACpH,IAAI,CAACD,GAAG,CAACC,IAAI,CAACC,SAAS,CAAC,EAAE;UACxBF,GAAG,CAACC,IAAI,CAACC,SAAS,CAAC,GAAG,EAAE;QAC1B;QACA,IAAI,CAACF,GAAG,CAACC,IAAI,CAACC,SAAS,CAAC,CAACC,QAAQ,CAACF,IAAI,CAACG,QAAQ,CAAC,EAAE;UAChDJ,GAAG,CAACC,IAAI,CAACC,SAAS,CAAC,CAACG,IAAI,CAACJ,IAAI,CAACG,QAAQ,CAAC;QACzC;QACA,OAAOJ,GAAG;MACZ,CAAC,EAAE,CAAC,CAAC,CAAC;MAEN5C,YAAY,CAAC0C,IAAI,CAAC;IAGpB,CAAC,CAAC,OAAOQ,CAAC,EAAE,CAEZ;EACF,CAAC;EAOD,MAAMb,OAAO,GAAGA,CAAA,KAAM;IAEpB,MAAMc,aAAa,GAAGrC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IACvDjB,YAAY,CAAC,IAAI,CAAC;IAClBtB,eAAe,CAAC,MAAM;MACpBc,OAAO,CAAC,IAAI,CAAC;MACbI,cAAc,CAAC,IAAI,CAAC;MACpBoB,YAAY,CAACsC,KAAK,CAAC,CAAC;MACpBC,QAAQ,CAACC,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAACF,MAAM,IAAI;QAC3C,MAAM,CAACG,IAAI,CAAC,GAAGH,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC;QAChCF,QAAQ,CAACC,MAAM,GAAG,GAAGG,IAAI,mDAAmD;MAC9E,CAAC,CAAC;MACF3C,YAAY,CAACK,OAAO,CAAC,cAAc,EAAEuC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC;MAC3D,IAAI,CAAAT,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEU,WAAW,CAAC,CAAC,MAAK,YAAY,EAAE;QACjD1D,QAAQ,CAAC,QAAQ,CAAC;MACpB,CAAC,MAAM;QACLA,QAAQ,CAAC,mBAAmB,CAAC;MAC/B;IACF,CAAC,CAAC;EACL,CAAC;EAIC,oBACExB,OAAA,CAACE,WAAW,CAACiF,QAAQ;IAACC,KAAK,EAAE;MAAE1E,IAAI;MAAEI,WAAW;MAAE4C,OAAO;MAACxC,SAAS;MAACE,SAAS;MAACE;IAAU,CAAE;IAAAhB,QAAA,EACvFA;EAAQ;IAAA+E,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAG3B,CAAC;AAACjF,EAAA,CAvJWF,YAAyC;EAAA,QACnCT,WAAW,EASXD,WAAW;AAAA;AAAA8F,EAAA,GAVjBpF,YAAyC;AAyJtD,OAAO,MAAMqF,OAAO,GAAGA,CAAA,KAAuB;EAAAC,GAAA;EAAA,IAAAC,QAAA;EAC5C,IAAIC,OAAO,GAAGvG,UAAU,CAACY,WAAW,CAAC;EAErC,IAAI2F,OAAO,KAAK1F,SAAS,EAAE;IACzB,MAAM,IAAI2F,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,KAAAF,QAAA,GAAIC,OAAO,cAAAD,QAAA,eAAPA,QAAA,CAASlF,IAAI,EAAE;IACjB,MAAMsB,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC;IACrEnC,aAAa,CAAC,WAAW,CAAC,GAAGgC,IAAI,CAAC8D,SAAS,CAACF,OAAO,CAACnF,IAAI,CAAC;IAEzD,IAAIsB,QAAQ,CAAC,MAAM,CAAC,EAAE;MACpB/B,aAAa,CAAC,MAAM,CAAC,GAAIgC,IAAI,CAAC8D,SAAS,CAAC/D,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC3D;EACF,CAAC,MACI;IACH,MAAMA,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC;IACrE,IAAIJ,QAAQ,CAAC,WAAW,CAAC,IAAIA,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC7C6D,OAAO,GAAG;QAAE,GAAGA,OAAO;QAAEnF,IAAI,EAAEuB,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,WAAW,CAAC;MAAE,CAAC;MACjE6D,OAAO,CAAC/E,WAAW,GAAImB,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,MAAM,CAAC,CAAC;MACnD6D,OAAO,CAAC3E,SAAS,GAAG,KAAK;IAC3B;EACF;EAEA,OAAO2E,OAAO;AAChB,CAAC;AAACF,GAAA,CAxBWD,OAAO;AA0BpB,SAAQtF,YAAY;AAAC,IAAAqF,EAAA;AAAAO,YAAA,CAAAP,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}