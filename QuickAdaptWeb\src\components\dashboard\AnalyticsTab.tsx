import React, { useState, useEffect } from 'react';
import { Box, Typography } from '@mui/material';
import Card from '../common/Card';
import { 
  getGuideAnalyticsWithStoredAccount,
  GuideAnalyticsResponse 
} from '../../services/DashboardService';

interface AnalyticsTabProps {
  timeFilter?: string;
}

const AnalyticsTab: React.FC<AnalyticsTabProps> = ({ timeFilter = '30d' }) => {
  const [selectedGuideType, setSelectedGuideType] = useState<string | null>(null);
  const [selectedGuideFromList, setSelectedGuideFromList] = useState<string | null>(null);
  const [guideData, setGuideData] = useState<GuideAnalyticsResponse | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Convert timeFilter to days
  const getDaysFromFilter = (filter: string): number => {
    switch (filter) {
      case '7d': return 7;
      case '30d': return 30;
      case '90d': return 90;
      case '1y': return 365;
      default: return 30;
    }
  };

  // Fetch guide analytics data
  useEffect(() => {
    const fetchGuideData = async () => {
      setLoading(true);
      setError(null);
      try {
        const days = getDaysFromFilter(timeFilter);
        const data = await getGuideAnalyticsWithStoredAccount(days);
        setGuideData(data);
      } catch (err) {
        console.error('Error fetching guide analytics:', err);
        setError('Failed to fetch guide analytics data');
      } finally {
        setLoading(false);
      }
    };

    fetchGuideData();
  }, [timeFilter]);

  // Create guide types array from API data
  const guideTypes = React.useMemo(() => {
    if (!guideData?.GuideTypes) {
      // Fallback static data if API data is not available
      return [
        {
          id: 'tours',
          name: 'Tours',
          adoptionRate: 78.5,
          avgTime: '4m 52s',
          interactions: 16478,
          trend: 12.3,
          color: '#3b82f6',
          metrics: { active: 45, inactive: 8, draft: 2 }
        },
        {
          id: 'banners',
          name: 'Banners',
          adoptionRate: 65.2,
          avgTime: '1m 45s',
          interactions: 12348,
          trend: 8.7,
          color: '#ef4444',
          metrics: { active: 23, inactive: 6, draft: 1 }
        },
        {
          id: 'announcements',
          name: 'Announcements',
          adoptionRate: 72.1,
          avgTime: '2m 18s',
          interactions: 8874,
          trend: 15.4,
          color: '#10b981',
          metrics: { active: 18, inactive: 3, draft: 1 }
        },
        {
          id: 'checklists',
          name: 'Checklists',
          adoptionRate: 85.3,
          avgTime: '6m 20s',
          interactions: 2854,
          trend: 5.2,
          color: '#06b6d4',
          metrics: { active: 12, inactive: 2, draft: 0 }
        },
        {
          id: 'tooltips',
          name: 'Tooltips',
          adoptionRate: 92.4,
          avgTime: '3m 15s',
          interactions: 9623,
          trend: 18.9,
          color: '#f59e0b',
          metrics: { active: 34, inactive: 1, draft: 2 }
        },
        {
          id: 'hotspots',
          name: 'Hotspots',
          adoptionRate: 68.9,
          avgTime: '2m 45s',
          interactions: 5234,
          trend: -2.1,
          color: '#8b5cf6',
          metrics: { active: 19, inactive: 4, draft: 1 }
        }
      ];
    }

    // Create guide types from real API data
    const apiGuideTypes = guideData.GuideTypes;
    const stats = guideData.GuideStatistics || {};
    
    return [
      {
        id: 'tours',
        name: 'Tours',
        adoptionRate: stats.Tours || 0,
        avgTime: '4m 52s', // Static for now - not in API
        interactions: apiGuideTypes.Tours.Count * 10, // Estimated interactions
        trend: apiGuideTypes.Tours.Trend,
        color: '#3b82f6',
        metrics: { 
          active: apiGuideTypes.Tours.Active, 
          inactive: apiGuideTypes.Tours.InActive, 
          draft: apiGuideTypes.Tours.Draft 
        }
      },
      {
        id: 'banners',
        name: 'Banners',
        adoptionRate: stats.Banners || 0,
        avgTime: '1m 45s',
        interactions: apiGuideTypes.Banners.Count * 10,
        trend: apiGuideTypes.Banners.Trend,
        color: '#ef4444',
        metrics: { 
          active: apiGuideTypes.Banners.Active, 
          inactive: apiGuideTypes.Banners.InActive, 
          draft: apiGuideTypes.Banners.Draft 
        }
      },
      {
        id: 'announcements',
        name: 'Announcements',
        adoptionRate: stats.Announcements || 0,
        avgTime: '2m 18s',
        interactions: apiGuideTypes.Announcements.Count * 10,
        trend: apiGuideTypes.Announcements.Trend,
        color: '#10b981',
        metrics: { 
          active: apiGuideTypes.Announcements.Active, 
          inactive: apiGuideTypes.Announcements.InActive, 
          draft: apiGuideTypes.Announcements.Draft 
        }
      },
      {
        id: 'checklists',
        name: 'Checklists',
        adoptionRate: stats.Checklists || 0,
        avgTime: '6m 20s',
        interactions: apiGuideTypes.Checklists.Count * 10,
        trend: apiGuideTypes.Checklists.Trend,
        color: '#06b6d4',
        metrics: { 
          active: apiGuideTypes.Checklists.Active, 
          inactive: apiGuideTypes.Checklists.InActive, 
          draft: apiGuideTypes.Checklists.Draft 
        }
      },
      {
        id: 'tooltips',
        name: 'Tooltips',
        adoptionRate: stats.Tooltips || 0,
        avgTime: '3m 15s',
        interactions: apiGuideTypes.Tooltips.Count * 10,
        trend: apiGuideTypes.Tooltips.Trend,
        color: '#f59e0b',
        metrics: { 
          active: apiGuideTypes.Tooltips.Active, 
          inactive: apiGuideTypes.Tooltips.InActive, 
          draft: apiGuideTypes.Tooltips.Draft 
        }
      },
      {
        id: 'hotspots',
        name: 'Hotspots',
        adoptionRate: stats.Hotspots || 0,
        avgTime: '2m 45s',
        interactions: apiGuideTypes.Hotspots.Count * 10,
        trend: apiGuideTypes.Hotspots.Trend,
        color: '#8b5cf6',
        metrics: { 
          active: apiGuideTypes.Hotspots.Active, 
          inactive: apiGuideTypes.Hotspots.InActive, 
          draft: apiGuideTypes.Hotspots.Draft 
        }
      }
    ];
  }, [guideData]);

  const guidesByType: Record<string, any[]> = {
    tours: [
      { id: 'tour-1', name: 'Product Onboarding', views: 1400, completed: 1247, dropOff: 11, status: 'excellent', lastUpdated: '2 days ago' },
      { id: 'tour-2', name: 'Feature Discovery', views: 1174, completed: 892, dropOff: 24, status: 'good', lastUpdated: '1 day ago' },
      { id: 'tour-3', name: 'Advanced Settings', views: 962, completed: 634, dropOff: 32, status: 'needs attention', lastUpdated: '5 days ago' }
    ],
    banners: [
      { id: 'banner-1', name: 'Welcome Banner', views: 2100, completed: 1932, dropOff: 8, status: 'excellent', lastUpdated: '1 day ago' },
      { id: 'banner-2', name: 'Feature Announcement', views: 1850, completed: 1665, dropOff: 10, status: 'excellent', lastUpdated: '3 days ago' }
    ],
    announcements: [
      { id: 'announcement-1', name: 'New Feature Release', views: 980, completed: 764, dropOff: 22, status: 'good', lastUpdated: '1 week ago' },
      { id: 'announcement-2', name: 'Maintenance Notice', views: 1200, completed: 936, dropOff: 22, status: 'good', lastUpdated: '2 days ago' }
    ],
    checklists: [
      { id: 'checklist-1', name: 'Setup Checklist', views: 1800, completed: 1534, dropOff: 15, status: 'excellent', lastUpdated: '1 day ago' },
      { id: 'checklist-2', name: 'Onboarding Tasks', views: 1450, completed: 1218, dropOff: 16, status: 'excellent', lastUpdated: '3 days ago' }
    ],
    tooltips: [
      { id: 'tooltip-1', name: 'Button Helper', views: 3200, completed: 2816, dropOff: 12, status: 'excellent', lastUpdated: '1 day ago' },
      { id: 'tooltip-2', name: 'Form Guidance', views: 2800, completed: 2464, dropOff: 12, status: 'excellent', lastUpdated: '2 days ago' }
    ],
    hotspots: [
      { id: 'hotspot-1', name: 'Navigation Helper', views: 1100, completed: 891, dropOff: 19, status: 'good', lastUpdated: '3 days ago' },
      { id: 'hotspot-2', name: 'Feature Spotlight', views: 950, completed: 654, dropOff: 31, status: 'needs attention', lastUpdated: '5 days ago' }
    ]
  };

  // Set default selections on component mount
  useEffect(() => {
    if (guideTypes.length > 0 && !selectedGuideType) {
      const firstGuideType = guideTypes[0];
      setSelectedGuideType(firstGuideType.id);

      // Set the first guide from the first guide type as selected
      const firstGuideTypeGuides = guidesByType[firstGuideType.id];
      if (firstGuideTypeGuides && firstGuideTypeGuides.length > 0) {
        setSelectedGuideFromList(firstGuideTypeGuides[0].id);
      }
    }
  }, []);

  return (
    <>
      {/* Level 1 - Guide Type Cards */}
      <Card title="Analytics Overview" subtitle="Select a guide type to view detailed analytics" padding="lg">
        <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: 3, mb: 4 }}>
          {guideTypes.map((guideType) => (
            <Box
              key={guideType.id}
              onClick={() => {
                setSelectedGuideType(guideType.id);
                // Automatically select the first guide from the newly selected type
                const guidesForType = guidesByType[guideType.id];
                if (guidesForType && guidesForType.length > 0) {
                  setSelectedGuideFromList(guidesForType[0].id);
                } else {
                  setSelectedGuideFromList(null);
                }
              }}
              sx={{
                p: 3,
                border: selectedGuideType === guideType.id ? `2px solid ${guideType.color}` : '1px solid #e2e8f0',
                borderRadius: '12px',
                backgroundColor: 'white',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                boxShadow: selectedGuideType === guideType.id ? '0 4px 12px rgba(0, 0, 0, 0.1)' : '0 1px 3px rgba(0, 0, 0, 0.05)',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                }
              }}
            >
              {/* Header with icon */}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <Box sx={{
                  width: 20,
                  height: 20,
                  borderRadius: '4px',
                  backgroundColor: guideType.color,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <Typography sx={{ fontSize: '10px', color: 'white' }}>
                    {guideType.name === 'Tours' ? '🎯' :
                     guideType.name === 'Banners' ? '📢' :
                     guideType.name === 'Announcements' ? '📣' :
                     guideType.name === 'Checklists' ? '✅' :
                     guideType.name === 'Tooltips' ? '💡' : '📍'}
                  </Typography>
                </Box>
                <Typography variant="h6" fontWeight="bold" sx={{ color: '#1e293b', fontSize: '14px' }}>
                  {guideType.name}
                </Typography>
              </Box>

              {/* Adoption Rate */}
              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2" sx={{ color: '#64748b', fontSize: '12px' }}>
                    Adoption Rate
                  </Typography>
                  <Typography variant="h5" fontWeight="bold" sx={{ color: '#1e293b', fontSize: '18px' }}>
                    {guideType.adoptionRate}%
                  </Typography>
                </Box>
                <Box sx={{
                  width: '100%',
                  height: 4,
                  backgroundColor: '#f1f5f9',
                  borderRadius: '2px',
                  overflow: 'hidden'
                }}>
                  <Box sx={{
                    width: `${guideType.adoptionRate}%`,
                    height: '100%',
                    backgroundColor: guideType.color,
                    borderRadius: '2px'
                  }} />
                </Box>
              </Box>

              {/* Metrics Row */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                <Box>
                  <Typography variant="caption" sx={{ color: '#64748b', fontSize: '10px', display: 'block' }}>
                    Avg Time
                  </Typography>
                  <Typography variant="body2" fontWeight="medium" sx={{ color: '#1e293b', fontSize: '11px' }}>
                    {guideType.avgTime}
                  </Typography>
                </Box>
                <Box sx={{ textAlign: 'right' }}>
                  <Typography variant="caption" sx={{ color: '#64748b', fontSize: '10px', display: 'block' }}>
                    Interactions
                  </Typography>
                  <Typography variant="body2" fontWeight="medium" sx={{ color: '#1e293b', fontSize: '11px' }}>
                    {guideType.interactions.toLocaleString()}
                  </Typography>
                </Box>
              </Box>

              {/* Status Indicators */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', gap: 1, mb: 2 }}>
                <Box sx={{
                  textAlign: 'center',
                  flex: 1,
                  p: 1,
                  backgroundColor: '#f0fdf4',
                  borderRadius: '6px',
                  border: '1px solid #bbf7d0'
                }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5, mb: 0.5 }}>
                    <Box sx={{ width: 6, height: 6, borderRadius: '50%', backgroundColor: '#22c55e' }} />
                    <Typography variant="caption" sx={{ fontSize: '9px', color: '#16a34a', fontWeight: 'medium' }}>
                      Active
                    </Typography>
                  </Box>
                  <Typography variant="body2" sx={{ fontSize: '12px', color: '#16a34a', fontWeight: 'bold' }}>
                    {guideType.metrics.active}
                  </Typography>
                </Box>
                <Box sx={{
                  textAlign: 'center',
                  flex: 1,
                  p: 1,
                  backgroundColor: '#fffbeb',
                  borderRadius: '6px',
                  border: '1px solid #fed7aa'
                }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5, mb: 0.5 }}>
                    <Box sx={{ width: 6, height: 6, borderRadius: '50%', backgroundColor: '#f59e0b' }} />
                    <Typography variant="caption" sx={{ fontSize: '9px', color: '#d97706', fontWeight: 'medium' }}>
                      Draft
                    </Typography>
                  </Box>
                  <Typography variant="body2" sx={{ fontSize: '12px', color: '#d97706', fontWeight: 'bold' }}>
                    {guideType.metrics.draft}
                  </Typography>
                </Box>
                <Box sx={{
                  textAlign: 'center',
                  flex: 1,
                  p: 1,
                  backgroundColor: '#f8fafc',
                  borderRadius: '6px',
                  border: '1px solid #e2e8f0'
                }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5, mb: 0.5 }}>
                    <Box sx={{ width: 6, height: 6, borderRadius: '50%', backgroundColor: '#94a3b8' }} />
                    <Typography variant="caption" sx={{ fontSize: '9px', color: '#64748b', fontWeight: 'medium' }}>
                      Inactive
                    </Typography>
                  </Box>
                  <Typography variant="body2" sx={{ fontSize: '12px', color: '#64748b', fontWeight: 'bold' }}>
                    {guideType.metrics.inactive}
                  </Typography>
                </Box>
              </Box>

              {/* Trend */}
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Typography variant="caption" sx={{ color: '#64748b', fontSize: '10px' }}>
                  Trend
                </Typography>
                <Typography variant="caption" sx={{
                  color: '#22c55e',
                  fontSize: '10px',
                  fontWeight: 'medium',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 0.5
                }}>
                  ↗ {guideType.trend}%
                </Typography>
              </Box>
            </Box>
          ))}
        </Box>
      </Card>

      {/* Level 2 & 3 - Top Guides Performance and Interactive Guide Funnel */}
      {selectedGuideType && (
        <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 'var(--spacing-6)' }}>
          {/* Left Column - Top Guides Performance */}
          <Card title="📊 Top Guides Performance" subtitle="Comprehensive guide analytics with completion and engagement metrics" padding="lg">
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>
              {guidesByType[selectedGuideType]?.map((guide, index) => {
                const completionRate = Math.round((guide.completed / guide.views) * 100);
                const guideColor = guideTypes.find(gt => gt.id === selectedGuideType)?.color || '#8b5cf6';

                return (
                  <Box
                    key={guide.id}
                    onClick={() => setSelectedGuideFromList(guide.id)}
                    sx={{
                      p: 3,
                      border: selectedGuideFromList === guide.id ? `2px solid ${guideColor}` : '1px solid var(--color-gray-200)',
                      borderRadius: 'var(--radius-lg)',
                      backgroundColor: selectedGuideFromList === guide.id ? `${guideColor}08` : 'white',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        backgroundColor: selectedGuideFromList === guide.id ? `${guideColor}12` : 'var(--color-gray-50)',
                        transform: 'translateY(-2px)',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                      }
                    }}
                  >
                    {/* Guide Header */}
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Box sx={{
                          width: 8,
                          height: 8,
                          borderRadius: '50%',
                          backgroundColor: guideColor
                        }} />
                        <Typography variant="h6" fontWeight="semibold">
                          {guide.name}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="h5" fontWeight="bold" sx={{ color: guideColor }}>
                          {completionRate}%
                        </Typography>
                        <Box sx={{
                          px: 1.5,
                          py: 0.5,
                          backgroundColor: guide.status === 'excellent' ? '#e8f5e9' : guide.status === 'good' ? '#e3f2fd' : '#fff3e0',
                          color: guide.status === 'excellent' ? '#2e7d32' : guide.status === 'good' ? '#1976d2' : '#f57c00',
                          borderRadius: 'var(--radius-sm)',
                          fontSize: '11px',
                          fontWeight: 'medium'
                        }}>
                          {guide.status}
                        </Box>
                      </Box>
                    </Box>

                    {/* Progress Bar */}
                    <Box sx={{ mb: 2 }}>
                      <Box sx={{
                        width: '100%',
                        height: 8,
                        backgroundColor: 'var(--color-gray-200)',
                        borderRadius: 'var(--radius-full)',
                        overflow: 'hidden'
                      }}>
                        <Box sx={{
                          width: `${completionRate}%`,
                          height: '100%',
                          backgroundColor: guideColor,
                          borderRadius: 'var(--radius-full)'
                        }} />
                      </Box>
                    </Box>

                    {/* Metrics Row */}
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h6" fontWeight="bold" sx={{ color: '#3b82f6' }}>
                          {guide.views.toLocaleString()}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          👁 Views
                        </Typography>
                      </Box>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h6" fontWeight="bold" sx={{ color: '#10b981' }}>
                          {guide.completed.toLocaleString()}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          ✅ Completed
                        </Typography>
                      </Box>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h6" fontWeight="bold" sx={{
                          color: guide.dropOff < 15 ? '#10b981' : guide.dropOff < 25 ? '#f59e0b' : '#ef4444'
                        }}>
                          {guide.dropOff}%
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          📉 Drop-off
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                );
              })}
            </Box>
          </Card>

          {/* Right Column - Interactive Guide Funnel */}
          <Card title="🔄 Interactive Guide Funnel" subtitle="Step-by-step user journey analysis with conversion metrics" padding="lg">
            {selectedGuideFromList ? (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>
                {/* Selected Guide Info */}
                {(() => {
                  const selectedGuide = guidesByType[selectedGuideType]?.find(g => g.id === selectedGuideFromList);
                  const guideColor = guideTypes.find(gt => gt.id === selectedGuideType)?.color || '#8b5cf6';

                  if (!selectedGuide) return null;

                  return (
                    <>
                      <Box sx={{
                        p: 3,
                        backgroundColor: `${guideColor}08`,
                        borderRadius: 'var(--radius-lg)',
                        border: `1px solid ${guideColor}30`
                      }}>
                        <Typography variant="h6" fontWeight="bold" sx={{ color: guideColor, mb: 1 }}>
                          {selectedGuide.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Last updated: {selectedGuide.lastUpdated}
                        </Typography>
                      </Box>

                      {/* Funnel Steps */}
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, alignItems: 'center' }}>
                        {/* Guide Started */}
                        <Box sx={{
                          width: '100%',
                          p: 2.5,
                          backgroundColor: '#f0fdf4',
                          borderRadius: '12px',
                          border: '1px solid #bbf7d0',
                          position: 'relative'
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                            <Box sx={{ width: 8, height: 8, borderRadius: '50%', backgroundColor: '#22c55e' }} />
                            <Typography variant="body2" fontWeight="bold" sx={{ color: '#16a34a', fontSize: '12px' }}>
                              Guide Started
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                            <Typography variant="h5" fontWeight="bold" sx={{ color: '#16a34a' }}>
                              {selectedGuide.views.toLocaleString()}
                            </Typography>
                            <Typography variant="h6" fontWeight="bold" sx={{ color: '#16a34a' }}>
                              100%
                            </Typography>
                          </Box>
                          <Typography variant="caption" sx={{ color: '#16a34a', fontSize: '10px' }}>
                            Users
                          </Typography>
                          <Box sx={{
                            position: 'absolute',
                            bottom: -8,
                            left: '50%',
                            transform: 'translateX(-50%)',
                            backgroundColor: '#fecaca',
                            px: 1,
                            py: 0.5,
                            borderRadius: '4px',
                            fontSize: '9px',
                            color: '#dc2626'
                          }}>
                            -{Math.round((1 - 0.85) * 100)}% drop-off (15%)
                          </Box>
                        </Box>

                        {/* Step 1: Welcome */}
                        <Box sx={{
                          width: '85%',
                          p: 2.5,
                          backgroundColor: '#eff6ff',
                          borderRadius: '12px',
                          border: '1px solid #bfdbfe',
                          position: 'relative',
                          mt: 2
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                            <Box sx={{ width: 8, height: 8, borderRadius: '50%', backgroundColor: '#3b82f6' }} />
                            <Typography variant="body2" fontWeight="bold" sx={{ color: '#1d4ed8', fontSize: '12px' }}>
                              Step 1: Welcome
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                            <Typography variant="h5" fontWeight="bold" sx={{ color: '#1d4ed8' }}>
                              {Math.round(selectedGuide.views * 0.85).toLocaleString()}
                            </Typography>
                            <Typography variant="h6" fontWeight="bold" sx={{ color: '#1d4ed8' }}>
                              85%
                            </Typography>
                          </Box>
                          <Typography variant="caption" sx={{ color: '#1d4ed8', fontSize: '10px' }}>
                            Users
                          </Typography>
                          <Box sx={{
                            position: 'absolute',
                            bottom: -8,
                            left: '50%',
                            transform: 'translateX(-50%)',
                            backgroundColor: '#fecaca',
                            px: 1,
                            py: 0.5,
                            borderRadius: '4px',
                            fontSize: '9px',
                            color: '#dc2626'
                          }}>
                            -{Math.round((0.85 - 0.72) * 100)}% drop-off (13%)
                          </Box>
                        </Box>

                        {/* Step 2: Setup */}
                        <Box sx={{
                          width: '72%',
                          p: 2.5,
                          backgroundColor: '#fffbeb',
                          borderRadius: '12px',
                          border: '1px solid #fed7aa',
                          position: 'relative',
                          mt: 2
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                            <Box sx={{ width: 8, height: 8, borderRadius: '50%', backgroundColor: '#f59e0b' }} />
                            <Typography variant="body2" fontWeight="bold" sx={{ color: '#d97706', fontSize: '12px' }}>
                              Step 2: Setup
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                            <Typography variant="h5" fontWeight="bold" sx={{ color: '#d97706' }}>
                              {Math.round(selectedGuide.views * 0.72).toLocaleString()}
                            </Typography>
                            <Typography variant="h6" fontWeight="bold" sx={{ color: '#d97706' }}>
                              72%
                            </Typography>
                          </Box>
                          <Typography variant="caption" sx={{ color: '#d97706', fontSize: '10px' }}>
                            Users
                          </Typography>
                          <Box sx={{
                            position: 'absolute',
                            bottom: -8,
                            left: '50%',
                            transform: 'translateX(-50%)',
                            backgroundColor: '#fecaca',
                            px: 1,
                            py: 0.5,
                            borderRadius: '4px',
                            fontSize: '9px',
                            color: '#dc2626'
                          }}>
                            -{Math.round((0.72 - 0.65) * 100)}% drop-off (7%)
                          </Box>
                        </Box>

                        {/* Step 3: Configuration */}
                        <Box sx={{
                          width: '65%',
                          p: 2.5,
                          backgroundColor: '#faf5ff',
                          borderRadius: '12px',
                          border: '1px solid #e9d5ff',
                          position: 'relative',
                          mt: 2
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                            <Box sx={{ width: 8, height: 8, borderRadius: '50%', backgroundColor: '#a855f7' }} />
                            <Typography variant="body2" fontWeight="bold" sx={{ color: '#7c3aed', fontSize: '12px' }}>
                              Step 3: Configuration
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                            <Typography variant="h5" fontWeight="bold" sx={{ color: '#7c3aed' }}>
                              {Math.round(selectedGuide.views * 0.65).toLocaleString()}
                            </Typography>
                            <Typography variant="h6" fontWeight="bold" sx={{ color: '#7c3aed' }}>
                              65%
                            </Typography>
                          </Box>
                          <Typography variant="caption" sx={{ color: '#7c3aed', fontSize: '10px' }}>
                            Users
                          </Typography>
                          <Box sx={{
                            position: 'absolute',
                            bottom: -8,
                            left: '50%',
                            transform: 'translateX(-50%)',
                            backgroundColor: '#fecaca',
                            px: 1,
                            py: 0.5,
                            borderRadius: '4px',
                            fontSize: '9px',
                            color: '#dc2626'
                          }}>
                            -{Math.round((0.65 - (selectedGuide.completed / selectedGuide.views)) * 100)}% drop-off
                          </Box>
                        </Box>

                        {/* Guide Completed */}
                        <Box sx={{
                          width: '55%',
                          p: 2.5,
                          backgroundColor: '#fef2f2',
                          borderRadius: '12px',
                          border: '1px solid #fecaca',
                          mt: 2
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                            <Box sx={{ width: 8, height: 8, borderRadius: '50%', backgroundColor: '#ef4444' }} />
                            <Typography variant="body2" fontWeight="bold" sx={{ color: '#dc2626', fontSize: '12px' }}>
                              Guide Completed
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                            <Typography variant="h5" fontWeight="bold" sx={{ color: '#dc2626' }}>
                              {selectedGuide.completed.toLocaleString()}
                            </Typography>
                            <Typography variant="h6" fontWeight="bold" sx={{ color: '#dc2626' }}>
                              {Math.round((selectedGuide.completed / selectedGuide.views) * 100)}%
                            </Typography>
                          </Box>
                          <Typography variant="caption" sx={{ color: '#dc2626', fontSize: '10px' }}>
                            Users
                          </Typography>
                        </Box>
                      </Box>

                      {/* Conversion Metrics */}
                      {/* <Box sx={{
                        p: 3,
                        backgroundColor: '#f0fdf4',
                        borderRadius: 'var(--radius-lg)',
                        border: '1px solid #bbf7d0'
                      }}>
                        <Typography variant="h6" fontWeight="bold" sx={{ color: '#16a34a', mb: 2 }}>
                          Conversion Metrics
                        </Typography>
                        <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: 2 }}>
                          <Box sx={{ textAlign: 'center' }}>
                            <Typography variant="h5" fontWeight="bold" sx={{ color: '#16a34a' }}>
                              {Math.round((selectedGuide.completed / selectedGuide.views) * 100)}%
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              Completion Rate
                            </Typography>
                          </Box>
                          <Box sx={{ textAlign: 'center' }}>
                            <Typography variant="h5" fontWeight="bold" sx={{ color: '#dc2626' }}>
                              {selectedGuide.dropOff}%
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              Drop-off Rate
                            </Typography>
                          </Box>
                        </Box>
                      </Box> */}
                    </>
                  );
                })()}
              </Box>
            ) : (
              <Box sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                height: '300px',
                color: 'text.secondary'
              }}>
                <Typography variant="h6" sx={{ mb: 1 }}>
                  No guides available
                </Typography>
                <Typography variant="body2">
                  There are no guides in the selected category to analyze
                </Typography>
              </Box>
            )}
          </Card>
        </Box>
      )}
    </>
  );
};

export default AnalyticsTab;
