import { Search } from "@mui/icons-material";
import CloseIcon from '@mui/icons-material/Close';
import DeleteIcon from '@mui/icons-material/Delete';
import DoneIcon from '@mui/icons-material/Done';
import EditIcon from '@mui/icons-material/Edit';
import LanguageIcon from '@mui/icons-material/Language';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import FirstPageIcon from '@mui/icons-material/FirstPage';
import LastPageIcon from '@mui/icons-material/LastPage';
import { Button, Checkbox, CircularProgress, Dialog, DialogActions, DialogContent, DialogTitle, IconButton, InputAdornment, ListItemText, OutlinedInput, TextField, Typography } from "@mui/material";
import Box from "@mui/material/Box";
import FormControl from "@mui/material/FormControl";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { deleteLanguage, getPlatformLabels, saveLanguage, getLabels, getLanguages, updateLanguageLabels, addLanguageLabel } from "../../services/MultilingualService";
import NavigationPrompt from "../../routing/NavigationPromptMultilingual";
import { useSnackbar } from "../../SnackbarContext";




interface language {
  LanguageId: string;
  Language: string;
  LanguageCode: string;
  FlagIcon: string;
}
export const availableLanguages: language[] = [
  { LanguageId: "1", Language: "Telugu", LanguageCode: "te", FlagIcon: "India" },
  { LanguageId: "2", Language: "Hindi", LanguageCode: "hi", FlagIcon: "India" },
  { LanguageId: "3", Language: "Arabic", LanguageCode: "ar", FlagIcon: "UAE" },
  { LanguageId: "4", Language: "English", LanguageCode: "en", FlagIcon: "US" },
  { LanguageId: "5", Language: "Mandarin Chinese", LanguageCode: "zh", FlagIcon: "China" },
  { LanguageId: "6", Language: "Spanish", LanguageCode: "es", FlagIcon: "Spain" },
  { LanguageId: "7", Language: "Portuguese", LanguageCode: "pt", FlagIcon: "Portugal" },
  { LanguageId: "8", Language: "Russian", LanguageCode: "ru", FlagIcon: "Russia" },
  { LanguageId: "9", Language: "Korean", LanguageCode: "ko", FlagIcon: "South Korea" },
  { LanguageId: "10", Language: "Bengali", LanguageCode: "bn", FlagIcon: "Bangladesh" },
  { LanguageId: "11", Language: "French", LanguageCode: "fr", FlagIcon: "France" },
  { LanguageId: "12", Language: "German", LanguageCode: "de", FlagIcon: "Germany" },
  { LanguageId: "13", Language: "Japanese", LanguageCode: "ja", FlagIcon: "Japan" },
  { LanguageId: "14", Language: "Italian", LanguageCode: "it", FlagIcon: "Italy" },
  { LanguageId: "15", Language: "Turkish", LanguageCode: "tr", FlagIcon: "Turkey" },
  { LanguageId: "16", Language: "Dutch", LanguageCode: "nl", FlagIcon: "Netherlands" },
  { LanguageId: "17", Language: "Indonesian", LanguageCode: "id", FlagIcon: "Indonesia" },
  { LanguageId: "18", Language: "Thai", LanguageCode: "th", FlagIcon: "Thailand" },
  { LanguageId: "19", Language: "Vietnamese", LanguageCode: "vi", FlagIcon: "Vietnam" },
  { LanguageId: "20", Language: "Urdu", LanguageCode: "ur", FlagIcon: "Pakistan" },
  { LanguageId: "21", Language: "Swahili", LanguageCode: "sw", FlagIcon: "Kenya" },
  { LanguageId: "22", Language: "Hebrew", LanguageCode: "he", FlagIcon: "Israel" },
  { LanguageId: "23", Language: "Malay", LanguageCode: "ms", FlagIcon: "Malaysia" },
  { LanguageId: "24", Language: "Polish", LanguageCode: "pl", FlagIcon: "Poland" },
  { LanguageId: "25", Language: "Ukrainian", LanguageCode: "uk", FlagIcon: "Ukraine" },
  { LanguageId: "26", Language: "Farsi (Persian)", LanguageCode: "fa", FlagIcon: "Iran" },
  { LanguageId: "27", Language: "Marathi", LanguageCode: "mr", FlagIcon: "India" }



];




const Translater = () => {
  const [selectedLanguages, setSelectedLanguages] = useState<language[]>([]);
  const { openSnackbar } = useSnackbar();
  const [labels, setLabels] = useState<any>({});
  const [labelData, setLabelData] = useState<any[]>([]); // Store original API data with IDs
  const [dropdownLanguage] = useState(30);
  const [open, setOpen] = useState(false);
  const [toLanguage, setToLanguage] = useState<string>('');
  const [editLabelKey, setEditLabelKey] = useState<string | null>(null);
  const [editLabelValue, setEditLabelValue] = useState<string>('');

  // Pagination state
  const [paginationModel, setPaginationModel] = useState({
    page: 0,
    pageSize: 15
  });

  const selectRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const fetchLabels = async () => {
      // Don't fetch if toLanguage is empty or if it's English (primary language)
      if (!toLanguage || toLanguage.toLowerCase() === 'english') {
        setLabels({});
        setLabelData([]);
        return;
      }

      try {
        console.log('Fetching labels for language:', toLanguage);
        const result = await getPlatformLabels(toLanguage);

        // Store original data with IDs
        setLabelData(Array.isArray(result) ? result : []);

        // Transform array response to the expected format for UI
        if (Array.isArray(result)) {
          const languageObj = selectedLanguages.find(lang => lang.Language === toLanguage);
          const codeOfLanguage = languageObj ? languageObj.LanguageCode : toLanguage;

          const transformedLabels: any = {};
          transformedLabels[codeOfLanguage] = {};

          result.forEach((item: any) => {
            if (item.LabelName && item[toLanguage] !== undefined) {
              transformedLabels[codeOfLanguage][item.LabelName] = item[toLanguage];
            }
          });

          setLabels(transformedLabels);
        } else {
          // Fallback for old format if still supported
          setLabels(result);
        }
      } catch (error) {
        console.error('Error fetching labels for language:', toLanguage, error);
        setLabels({});
        setLabelData([]);
      }
    };
    fetchLabels();
  }, [toLanguage, selectedLanguages]);

  const languageObj = selectedLanguages.find(lang => lang.Language === toLanguage);
  const codeOfLanguage = languageObj ? languageObj.LanguageCode : toLanguage;

  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const handleClickOpen = () => {
    // Prevent opening manage languages dialog if user is currently editing a label
    if (editLabelKey) {
      openSnackbar("Please save or cancel your current edit before managing languages", "error");
      return;
    }

    // Prevent opening manage languages dialog if there are unsaved changes
    if (isLabelUpdated) {
      openSnackbar("Please save your changes before managing languages", "error");
      return;
    }

    setOpen(true);
    setNewLanguagesAdded(false);
  };

  const handleClose = () => {
    setIsDropdownVisible(false);
    setOpen(false);
    setNewSelectedLanguages([]);
  };

  const [newSelectedLanguages, setNewSelectedLanguages] = useState<string[]>([]);

  const [triggerAdd, setTriggerAdd] = useState(false);

  const handleAddLanguage = useCallback((selectedLanguageCodes: language[]) => {
  // Handle adding new languages logic here if needed
    setIsDropdownVisible(false);
  }, []);

  const [newLanguagesAdded, setNewLanguagesAdded] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [languageToDelete, setLanguageToDelete] = useState<string | null>(null);

  const handleDeleteLanguage = async (languageId: string) => {
    // Prevent deletion if user is currently editing
    if (editLabelKey) {
      openSnackbar("Please save or cancel your current edit before deleting a language", "error");
      return;
    }

    // Prevent deletion if there are unsaved changes
    if (isLabelUpdated) {
      openSnackbar("Please save your changes before deleting a language", "error");
      return;
    }

    setLanguageToDelete(languageId);
    setDeleteConfirmOpen(true);
  };

  const confirmDeleteLanguage = async () => {
    if (!languageToDelete) return;

    try {
      const response = await deleteLanguage(languageToDelete);
      if (response.data.Success) {
        // Remove the language from selectedLanguages
        setSelectedLanguages((prevLanguages) =>
          prevLanguages.filter((lang) => lang.LanguageId !== languageToDelete)
        );

        // If the deleted language was the current toLanguage, reset it
        const deletedLanguage = selectedLanguages.find(lang => lang.LanguageId === languageToDelete);
        if (deletedLanguage?.Language === toLanguage) {
          const remainingLanguages = selectedLanguages.filter(lang =>
            lang.LanguageId !== languageToDelete && lang.Language.toLowerCase() !== 'english'
          );
          if (remainingLanguages.length > 0) {
            setToLanguage(remainingLanguages[0].Language);
          } else {
            setToLanguage('');
          }
        }

        openSnackbar("Language deleted successfully", "success");
      } else {
        openSnackbar("Failed to delete language", "error");
      }
    } catch (error) {
      console.error("Error deleting language:", error);
      openSnackbar("Error deleting language", "error");
    } finally {
      setDeleteConfirmOpen(false);
      setLanguageToDelete(null);
    }
  };



  // Handle changes in the language dropdown
  const handleLanguageChange = (event: any) => {
    const { target: { value } } = event;
    const selectedValues = typeof value === 'string' ? value.split(',') : value;

    // Filter to only include valid language codes that exist in availableLanguages
    // and are not already selected/added
    const validSelectedValues = selectedValues.filter((code: string) =>
      availableLanguages.some(lang => lang.LanguageCode === code) &&
      !selectedLanguages.some((lang: language) => lang.LanguageCode === code)
    );

    setNewSelectedLanguages(validSelectedValues);
    setNewLanguagesAdded(validSelectedValues.length > 0);

    // Show count of selected languages
    console.log(`Selected ${validSelectedValues.length} languages for addition`);
  };

  useEffect(() => {
    if (triggerAdd) {
      handleAddLanguage(selectedLanguages);
      setTriggerAdd(false);
    }
  }, [triggerAdd, selectedLanguages, handleAddLanguage]);

  const handleToLanguageChange = async (e: any) => {
    // Prevent language change if user is currently editing a label
    if (editLabelKey) {
      openSnackbar("Please save or cancel your current edit before changing language", "error");
      return;
    }

    // Prevent language change if there are unsaved changes
    if (isLabelUpdated) {
      openSnackbar("Please save your changes before switching to another language", "error");
      return;
    }

    const newToLanguage = e.target.value as string;
    setToLanguage(newToLanguage);
  };

  const handleShowDropdown = () => {
    setIsDropdownVisible(true);
  };

  const handleEditLabel = (key: any, value: any) => {
    // Prevent editing if another label is already being edited
    if (editLabelKey && editLabelKey !== key) {
      openSnackbar("Please save or cancel your current edit before editing another label", "error");
      return;
    }

    // Prevent editing if user is adding a new label
    if (showAddRow) {
      openSnackbar("Please save or cancel the new label before editing existing labels", "error");
      return;
    }

    setEditLabelKey(key);
    setEditLabelValue(value);
  };
  const [updatedLabels, setUpdatedLabels] = useState<any[]>([]);

  const handleCancelEdit = () => {
    setEditLabelKey(null);
    setEditLabelValue("");
  };

  const [searchTerm, setSearchTerm] = useState<string>('');
  const [languageSearchTerm, setLanguageSearchTerm] = useState<string>('');
  const handleSearchChange = (e: any) => {
    setSearchTerm(e.target.value);
  };

  const [isLabelUpdated, setIsLabelUpdated] = useState(false);
  const handleSaveLabel = (key: string) => {
    // Validation: Check if the edited value is not just whitespace
    if (editLabelValue.trim() === "") {
      openSnackbar("Label value cannot be empty", "error");
      return;
    }

    // Mark that a label was updated
    setIsLabelUpdated(true);

    // Find the original label data to get the ID and Module
    const originalLabelData = labelData.find(item => item.LabelName === key);

    if (!originalLabelData) {
      console.error('Original label data not found for:', key);
      openSnackbar("Error: Original label data not found", "error");
      return;
    }

    // Update or insert into updatedLabels array with the new format
    setUpdatedLabels((prev) => {
      const index = prev.findIndex((label) => label.LabelName === key);

      if (index !== -1) {
        // Update existing label
        const updated = [...prev];
        updated[index] = {
          ...updated[index],
          SecondaryLanguageLabel: editLabelValue.trim(),
        };
        return updated;
      }

      // Add new label with required fields
      return [
        ...prev,
        {
          Id: originalLabelData.Id,
          LabelName: key,
          Module: originalLabelData.Module,
          SecondaryLanguageLabel: editLabelValue.trim(),
        },
      ];
    });

    // Update local labels state for UI
    setLabels((prev: any) => ({
      ...prev,
      [codeOfLanguage]: {
        ...prev[codeOfLanguage],
        [key]: editLabelValue.trim(),
      },
    }));

    // Reset editing state
    setEditLabelKey(null);
    setEditLabelValue('');
  };



  const getFilteredLabelEntries = (
    labelsObj: { [key: string]: string } = {},
    searchTerm: string
  ): [string, string][] => {
    return Object.entries(labelsObj).filter(
      ([key, value]) =>
        key.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (value && value.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  };

  const renderLabelsKeys = (
    filteredEntries: [string, string][]
  ) => {
    if (filteredEntries.length === 0) {
      return (
        <Box display="flex" justifyContent="center" alignItems="center">
          <Typography variant="body1" color="textSecondary">
            No Labels Found
          </Typography>
        </Box>
      );
    }

    return (
      <ul>
        {filteredEntries.map(([key], index) => (
          <MenuItem key={index} style={{ minHeight: '48px', padding: '8px 16px' }}>
            <Typography
              variant="body2"
              sx={{
                wordBreak: 'break-word',
                overflowWrap: 'break-word',
                whiteSpace: 'normal',
                lineHeight: 1.4,
                fontSize: '0.875rem'
              }}
            >
              {key}
            </Typography>
          </MenuItem>
        ))}
      </ul>
    );
  };

  const renderLabels = (filteredEntries: [string, string][]) => {
    if (filteredEntries.length === 0) {
      return (
        <Box display="flex" justifyContent="center" alignItems="center">
          <Typography variant="body1" color="textSecondary">
            No Labels Found
          </Typography>
        </Box>
      );
    }

    return (
      <ul>
        {filteredEntries.map(([key, value], index) => (
          <MenuItem key={index} style={{ minHeight: '48px', padding: '8px 16px' }}>
            {editLabelKey === key ? (
              <Box display="flex" alignItems="center" width="100%">
                <TextField
                  fullWidth
                  value={editLabelValue}
                  onChange={(e) => setEditLabelValue(e.target.value)}
                  size="small"
                  sx={{ mr: 1 }}
                  multiline
                  maxRows={3}
                  inputRef={input => {
                    if (input) {
                      // Move cursor to end
                      const length = input.value.length;
                      input.setSelectionRange(length, length);
                      input.focus();
                    }
                  }}
                />
                <IconButton onClick={() => handleSaveLabel(key)}>
                  <DoneIcon fontSize="small" />
                </IconButton>
                <IconButton onClick={handleCancelEdit}>
                  <CloseIcon fontSize="small" />
                </IconButton>
              </Box>
            ) : (
                <Box display="flex" justifyContent="space-between" alignItems="flex-start" width="100%">
                  <Typography
                    sx={{
                      wordBreak: 'break-word',
                      overflowWrap: 'break-word',
                      whiteSpace: 'normal',
                      lineHeight: 1.4,
                      fontSize: '0.875rem',
                      flex: 1,
                      pr: 1
                    }}
                  >
                    {value}
                  </Typography>
                  <IconButton 
                    onClick={() => handleEditLabel(key, value)}
                    size="small"
                    sx={{ flexShrink: 0, ml: 1 }}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
              </Box>
            )}
          </MenuItem>
        ))}
      </ul>
    );
  };

  useEffect(() => {
    const fetchLanguages = async () => {
      try {
        const response = await getLanguages();
        // Filter out English since it's the primary language
        const filteredLanguages = response.filter((lang: language) =>
          lang.Language.toLowerCase() !== 'english'
        );
        setSelectedLanguages(filteredLanguages);
        if (filteredLanguages.length > 0) {
          setToLanguage(filteredLanguages[0].Language);
        }
      } catch (err) {
        console.error("Error fetching languages:", err);
      }
    };

    fetchLanguages();
  }, []);




  const handleSaveLanguages = useCallback(async () => {
    if (updatedLabels.length === 0) {
      return;
    }

    try {
      const response = await updateLanguageLabels(updatedLabels, toLanguage);
      if (response.Success === true) {
        openSnackbar(response.SuccessMessage, "success");
        setIsLabelUpdated(false);
        setUpdatedLabels([]);

        // Refresh the labels to get the updated data
        const refreshedResult = await getPlatformLabels(toLanguage);
        setLabelData(Array.isArray(refreshedResult) ? refreshedResult : []);

        // Update transformed labels
        if (Array.isArray(refreshedResult)) {
          const languageObj = selectedLanguages.find(lang => lang.Language === toLanguage);
          const codeOfLanguage = languageObj ? languageObj.LanguageCode : toLanguage;

          const transformedLabels: any = {};
          transformedLabels[codeOfLanguage] = {};

          refreshedResult.forEach((item: any) => {
            if (item.LabelName && item[toLanguage] !== undefined) {
              transformedLabels[codeOfLanguage][item.LabelName] = item[toLanguage];
            }
          });

          setLabels(transformedLabels);
        }
      } else {
        openSnackbar(response.statusText, "error");
      }
    } catch (error) {
      openSnackbar("Failed to save language updates", "error");
    }
  }, [updatedLabels, toLanguage, selectedLanguages, openSnackbar]);

  const handleSave = async () => {
    try {
      // Map the new selected language codes to the proper language objects
      const languagesToSave = newSelectedLanguages.map((code: string) => {
        const language = availableLanguages.find((lang) => lang.LanguageCode === code);
        return language ? {
          LanguageId: language.LanguageId,
          Language: language.Language,
          LanguageCode: language.LanguageCode,
          FlagIcon: language.FlagIcon
        } : null;
      }).filter((lang): lang is language => lang !== null);

      if (languagesToSave.length > 0) {
        // Save all selected languages in a single API call
        await saveLanguage(languagesToSave);

        // Refresh the languages list
        const response = await getLanguages();
        // Filter out English since it's the primary language
        const filteredLanguages = response.filter((lang: language) =>
          lang.Language.toLowerCase() !== 'english'
        );
        setSelectedLanguages(filteredLanguages);

        if (filteredLanguages.length > 0 && !toLanguage) {
          setToLanguage(filteredLanguages[0].Language);
        }

        handleClose();

        // Show success message with count
        const languageNames = languagesToSave.map(lang => lang.Language).join(', ');
        if (languagesToSave.length === 1) {
          openSnackbar(`Language "${languageNames}" saved successfully`, "success");
        } else {
          openSnackbar(`${languagesToSave.length} languages (${languageNames}) saved successfully`, "success");
        }
      } else {
        openSnackbar("No new languages to save", "error");
      }
    } catch (error) {
      console.error("Error saving languages:", error);
      openSnackbar("Error saving languages", "error");
    }
    handleClose();
  };







  const actualLabels = labels?.[codeOfLanguage] ?? {};
  const filteredEntries = getFilteredLabelEntries(actualLabels, searchTerm);
  
  // Pagination logic
  const totalRows = filteredEntries.length;
  const startIndex = paginationModel.page * paginationModel.pageSize;
  const endIndex = startIndex + paginationModel.pageSize;
  const paginatedEntries = filteredEntries.slice(startIndex, endIndex);

  // Reset to first page when search changes
  useEffect(() => {
    setPaginationModel(prev => ({ ...prev, page: 0 }));
  }, [searchTerm]);

  const [showAddRow, setShowAddRow] = useState(false);
  const [newLabelKey, setNewLabelKey] = useState("");
  const [isAddingLabel, setIsAddingLabel] = useState(false);

  // Add keyboard shortcuts for save action
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl+S to save changes
      if (event.ctrlKey && event.key === 's') {
        event.preventDefault();
        if (isLabelUpdated) {
          handleSaveLanguages();
        }
      }
      // Escape key to cancel editing
      if (event.key === 'Escape') {
        if (editLabelKey) {
          handleCancelEdit();
        }
        if (showAddRow) {
          handleCancelAddRow();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isLabelUpdated, editLabelKey, showAddRow, handleSaveLanguages]);

  // Prevent navigation away from page when there are unsaved changes
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (isLabelUpdated || editLabelKey || showAddRow) {
        event.preventDefault();
        event.returnValue = ''; // Chrome requires returnValue to be set
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [isLabelUpdated, editLabelKey, showAddRow]);

  // Handler to show add row
  const handleAddRowClick = () => {
    // Prevent adding new label if user is currently editing a label
    if (editLabelKey) {
      openSnackbar("Please save or cancel your current edit before adding a new label", "error");
      return;
    }
    
    setShowAddRow(true);
    setNewLabelKey("");
  };
  // Handler to save new label
  const handleSaveNewLabel = async () => {
    // Validation: Check if key field is filled
    if (!newLabelKey.trim()) {
      openSnackbar("Please enter a Label Key", "error");
      return;
    }

    // Validation: Check for special characters or invalid format
    const keyRegex = /^[a-zA-Z0-9_.\s]+$/;
    if (!keyRegex.test(newLabelKey.trim())) {
      openSnackbar("Label Key can only contain letters, numbers, spaces, dots , underscore ", "error");
      return;
    }

    // Check if label key already exists
    const existingLabels = labels?.[codeOfLanguage] || {};
    if (existingLabels[newLabelKey.trim()]) {
      openSnackbar("Label Key already exists. Please use a different key.", "error");
      return;
    }

    // Check if we have a valid language selected
    if (!toLanguage || !codeOfLanguage) {
      openSnackbar("Please select a language before adding labels", "error");
      return;
    }

    setIsAddingLabel(true);

    try {
      // Call the new API to add the label
      const labelPayload = {
        LabelName: newLabelKey.trim(),
      };

      const response = await addLanguageLabel(labelPayload);

      if (response.Success === true) {
        // Update local state only after successful API call
        setLabels((prev: any) => ({
          ...prev,
          [codeOfLanguage]: {
            ...prev[codeOfLanguage],
            [newLabelKey.trim()]: "",
          },
        }));

        setShowAddRow(false);
        setNewLabelKey("");

        // Success message
        openSnackbar(response.SuccessMessage || "Label added successfully", "success");
      } else {
        openSnackbar(response.ErrorMessage || "Failed to add label", "error");
      }
    } catch (error) {
      console.error("Error adding label:", error);
      openSnackbar("Error adding label", "error");
    } finally {
      setIsAddingLabel(false);
    }
  };
  // Handler to cancel add row
  const handleCancelAddRow = () => {
    setShowAddRow(false);
    setNewLabelKey("");
  };

  return (
    <>
    <div className="qadpt-midpart setng-box">
      <div className="qadpt-content-block qadpt-multilingual">
        <div className="qadpt-head">
          <div className="qadpt-title-sec">
            <div className="qadpt-title">
                Language Translation
              {(isLabelUpdated || editLabelKey || showAddRow) && (
                <span style={{ color: 'orange', fontSize: '0.8em', marginLeft: '8px' }}>
                  (Unsaved changes)
                </span>
              )}
            </div>
              <div className="qadpt-description">select the secondary language to modify</div>
          </div>
          <div className="qadpt-right-part">
            <button
              onClick={handleClickOpen}
              className="qadpt-memberButton"
              disabled={editLabelKey !== null || isLabelUpdated}
              style={{
                opacity: (editLabelKey !== null || isLabelUpdated) ? 0.5 : 1,
                cursor: (editLabelKey !== null || isLabelUpdated) ? 'not-allowed' : 'pointer'
              }}
            >
              <i className="fal fa-add-plus"></i>
                <span>Manage Languages</span>
            </button>
            <button
              className={`qadpt-memberButton ${isLabelUpdated ? 'qadpt-mem-updated' : 'qadpt-disabled'}`} onClick={handleSaveLanguages}
              disabled={!isLabelUpdated} >
                Save
            </button>
          </div>
        </div>
        <div className="qadpt-filters">


          <div className="qadpt-filter-right">
            <Box sx={{ padding: 1 }}>
              <TextField
                variant="outlined"
                  placeholder="Search labels..."
                fullWidth
                value={searchTerm}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>
          </div>
          <div><Button
            variant="outlined"
            size="small"
            onClick={handleAddRowClick}
            disabled={editLabelKey !== null || showAddRow}
            className="qadpt-adalbl"
          >
            + Add Label
          </Button></div>
        </div>





        <div className="qadpt-language-selection">
          <div className="qadpt-headers">
            <FormControl variant="standard" className="qadpt-lang-left" fullWidth>
              <InputLabel id="demo-simple-select-readonly-label"></InputLabel>
              <Select
                ref={selectRef}
                labelId="demo-simple-select-readonly-label"
                id="demo-simple-select-readonly"
                value={"English"}
                inputProps={{ readOnly: true }}
                startAdornment={
                  <InputAdornment position="start">
                    <IconButton>
                      <LanguageIcon />
                    </IconButton>
                  </InputAdornment>
                }
              >
                <MenuItem value="English">
                  English
                </MenuItem>
              </Select>
            </FormControl>
            <FormControl className="qadpt-lang-rgt" fullWidth>
              <InputLabel id="demo-simple-select-label">Select Language to Modify</InputLabel>
              <Select
                ref={selectRef}
                labelId="demo-simple-select-label"
                id="demo-simple-selects"
                defaultValue={toLanguage}
                value={toLanguage}
                label="select language to modify"
                onChange={handleToLanguageChange}
                disabled={editLabelKey !== null || isLabelUpdated}
                MenuProps={{
                  anchorOrigin: {
                    vertical: 'bottom',
                    horizontal: 'left',
                  },
                  transformOrigin: {
                    vertical: 'top',
                    horizontal: 'left',
                  },
                  PaperProps: {
                    style: {
                      maxHeight: 250,
                      marginTop: 4,
                    },
                  },
                }}
                startAdornment={
                  <InputAdornment position="start">
                    <IconButton>
                      <LanguageIcon sx={{ color: (editLabelKey !== null || isLabelUpdated) ? 'grey' : 'black' }} />
                    </IconButton>
                  </InputAdornment>
                }
              >
                <Box sx={{ position: 'sticky', top: 0, backgroundColor: 'white', zIndex: 1, p: 1, borderBottom: '1px solid #e0e0e0' }}>
                  <TextField
                    size="small"
                    placeholder="Search languages..."
                    fullWidth
                    value={languageSearchTerm}
                    onChange={(e) => setLanguageSearchTerm(e.target.value)}
                    onClick={(e) => e.stopPropagation()}
                    onKeyDown={(e) => e.stopPropagation()}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Search fontSize="small" />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Box>
                {selectedLanguages
                  .filter((lang: language) =>
                    lang.Language.toLowerCase().includes(languageSearchTerm.toLowerCase()) ||
                    lang.LanguageCode.toLowerCase().includes(languageSearchTerm.toLowerCase())
                  )
                  .map((lang: language) => (
                    <MenuItem key={lang.LanguageId} value={lang.Language}>
                      {lang.Language}
                    </MenuItem>
                  ))}
                {selectedLanguages
                  .filter((lang: language) =>
                    lang.Language.toLowerCase().includes(languageSearchTerm.toLowerCase()) ||
                    lang.LanguageCode.toLowerCase().includes(languageSearchTerm.toLowerCase())
                  ).length === 0 && languageSearchTerm && (
                    <MenuItem disabled>
                      <Typography variant="body2" color="textSecondary">
                        No languages found
                      </Typography>
                    </MenuItem>
                  )}
              </Select>
            </FormControl>

          </div>
          <div className="qadpt-language-section">
            <Box display="flex" flexDirection="column">
              {showAddRow && (
                <Box display="flex" alignItems="center" mb={1} width="100%" className = "qadpt-addlabel">
                  {/* Left column - Buttons and Label Key */}
                  <Box flex={1} display="flex" alignItems="center" gap={1}>
                    <TextField
                      fullWidth
                      size="small"
                      placeholder="Label Key"
                      value={newLabelKey}
                      onChange={(e) => setNewLabelKey(e.target.value)}
                      disabled={isAddingLabel}
                    />
                   
                    <Button
                      onClick={handleCancelAddRow}
                     
                      variant="outlined"
                      size="small"
                      disabled={isAddingLabel}
                      className = "qadpt-secd-btn"
                    >
                      Cancel
                    </Button>
                     <Button
                      onClick={handleSaveNewLabel}
                      className = "qadpt-prim-btn"
                      variant="contained"
                      size="small"
                      disabled={isAddingLabel}
                      startIcon={isAddingLabel ? <CircularProgress size={16} color="inherit" /> : null}
                    >
                      {isAddingLabel ? 'Saving...' : 'Save'}
                    </Button>
                   
                  </Box>

                  {/* Right column - Empty space to match layout */}
                  <Box flex={1} pl={1}>
                    {/* Empty space */}
                  </Box>
                </Box>
              )}

              <Box display="flex" gap={2} className="qadpt-convlabels">
                {/* Left Column - Keys */}
                <div className="qadpt-sec-left" style={{ flex: 1, minWidth: 0 }}>
                  {renderLabelsKeys(paginatedEntries)}
                </div>

                {/* Right Column - Values */}
                <div className="qadpt-sec-right" style={{ flex: 1, minWidth: 0 }}>
                  {renderLabels(paginatedEntries)}
                </div>
              </Box>
            </Box>
          </div>

        </div>

        {/* Pagination Controls - Always visible at bottom right */}
        {filteredEntries.length > 0 && (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'flex-end',
              alignItems: 'center',
              borderTop: '1px solid #e0e0e0',
              backgroundColor: '#fafafa',
              gap: 2
            }}
          >
            {/* Records per page */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="body2" sx={{ fontSize: '0.875rem', color: '#666' }}>
                Rows per page:
              </Typography>
              <Select
                size="small"
                value={paginationModel.pageSize}
                onChange={(e) => setPaginationModel(prev => ({
                  page: 0,
                  pageSize: Number(e.target.value)
                }))}
                sx={{
                  minWidth: 70,
                  '& .MuiSelect-select': {
                    padding: '4px 8px',
                    fontSize: '0.875rem'
                  }
                }}
              >
                <MenuItem value={15}>15</MenuItem>
                <MenuItem value={30}>30</MenuItem>
                <MenuItem value={45}>45</MenuItem>
                <MenuItem value={60}>60</MenuItem>
              </Select>
            </Box>

            {/* Range display */}
            <Typography variant="body2" sx={{ fontSize: '0.875rem', color: '#666' }}>
              {totalRows === 0 ? '0-0 of 0' :
                `${startIndex + 1}-${Math.min(endIndex, totalRows)} of ${totalRows}`}
            </Typography>

            {/* Navigation buttons */}
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <IconButton
                size="small"
                onClick={() => setPaginationModel(prev => ({ ...prev, page: 0 }))}
                disabled={paginationModel.page === 0}
                sx={{ padding: '4px' }}
              >
                <FirstPageIcon fontSize="small" />
              </IconButton>
              <IconButton
                size="small"
                onClick={() => setPaginationModel(prev => ({ ...prev, page: prev.page - 1 }))}
                disabled={paginationModel.page === 0}
                sx={{ padding: '4px' }}
              >
                <ChevronLeftIcon fontSize="small" />
              </IconButton>
              <IconButton
                size="small"
                onClick={() => setPaginationModel(prev => ({ ...prev, page: prev.page + 1 }))}
                disabled={startIndex + paginationModel.pageSize >= totalRows}
                sx={{ padding: '4px' }}
              >
                <ChevronRightIcon fontSize="small" />
              </IconButton>
              <IconButton
                size="small"
                onClick={() => setPaginationModel(prev => ({
                  ...prev,
                  page: Math.ceil(totalRows / paginationModel.pageSize) - 1
                }))}
                disabled={startIndex + paginationModel.pageSize >= totalRows}
                sx={{ padding: '4px' }}
              >
                <LastPageIcon fontSize="small" />
              </IconButton>
            </Box>
          </Box>
        )}

        <div>
          <Dialog open={open} onClose={handleClose} className="qadpt-mngpwd-popup">
            <DialogTitle>Manage Languages</DialogTitle>
            <DialogContent>
              <div className="qadpt-primary-language-row">
                <TextField
                  className="qadpt-primary-lang"
                  variant="standard"
                  fullWidth
                  label="Primary Language"
                  value="English"
                  InputProps={{
                    readOnly: true,
                  }}
                />
                <Button
                  className="qadpt-add-button"
                  variant="contained"
                  onClick={handleShowDropdown}
                  disabled={availableLanguages.filter((lang) =>
                    !selectedLanguages.some((selectedLang) => selectedLang.LanguageCode === lang.LanguageCode)
                  ).length === 0}
                >
                  + Add
                </Button>
              </div>
              {isDropdownVisible ? (
                <Box display="flex" alignItems="center" mt={2}>
                  <div className="qadpt-lang-dropdown">
                    <div >
                      {availableLanguages.filter((lang) =>
                        !selectedLanguages.some((selectedLang) => selectedLang.LanguageCode === lang.LanguageCode)
                      ).length === 0 ? (
                        <Typography variant="body2" color="textSecondary" sx={{ p: 2 }}>
                          All available languages have been added.
                        </Typography>
                      ) : (
                          <FormControl>
                            <InputLabel id="select-language-label">Select Language</InputLabel>
                            <Select
                              labelId="select-language-label"
                              id="select-language"
                              multiple
                              value={newSelectedLanguages}
                              onChange={handleLanguageChange}
                              input={<OutlinedInput label="Select Language" />}
                              renderValue={(selected) => {
                                const selectedList = selected as string[];

                                if (selectedList.length === 0) {
                                  return "";
                                }

                                return selectedList
                                  .map((code) => {
                                    const language = availableLanguages.find((lang) => lang.LanguageCode === code);
                                    return language ? language.Language : code;
                                  })
                                  .join(", ");
                              }}
                            >
                              {availableLanguages
                                .filter((lang) =>
                                  !selectedLanguages.some((selectedLang) => selectedLang.LanguageCode === lang.LanguageCode) && lang.Language.toLowerCase() !== 'english'
                                )
                                .map((lang) => (
                                  <MenuItem key={lang.LanguageCode} value={lang.LanguageCode}>
                                    <Checkbox checked={newSelectedLanguages.indexOf(lang.LanguageCode) > -1} />
                                    <ListItemText primary={lang.Language} />
                                  </MenuItem>
                                ))}
                            </Select>
                          </FormControl>
                      )}

                    </div>
                  </div>


                </Box>
              ) : (
                null
              )}

              {/* {selectedLanguages.length > 0 && ( */}
              <Box mt={2}>
                <div className="qadpt-lang-subhead">Secondary Language(s)</div> {/* Heading */}
                {selectedLanguages.map((language: language, index: number) => (
                  <Box key={language.LanguageId} display="flex" alignItems="center" sx={{ mb: 2 }}>
                    <TextField
                      className="qadpt-second-lang"
                      variant="standard"
                      fullWidth
                      label={`Secondary Language ${index + 1}`}
                      value={language.Language}
                      InputProps={{
                        readOnly: true,
                      }}
                      sx={{ mr: 1 }}
                    />
                    <IconButton onClick={() => handleDeleteLanguage(language.LanguageId)}>
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                ))}
              </Box>

              {/* )} */}
            </DialogContent>
            <DialogActions>
              <Button onClick={handleClose} className="qadpt-langclose">
                Cancel
              </Button>
              <Button onClick={handleSave} disabled={!newLanguagesAdded}
                className={`qadpt-langsave ${newLanguagesAdded ? 'qadpt-langsave-active' : 'qadpt-langsave-inactive'}`} >
                {newSelectedLanguages.length > 1 ? `Save ${newSelectedLanguages.length} Languages` : 'Save'}
              </Button>
            </DialogActions>

          </Dialog>

          {/* Delete Confirmation Dialog */}
          <Dialog
            open={deleteConfirmOpen}
            onClose={() => setDeleteConfirmOpen(false)}
            aria-labelledby="delete-dialog-title"
            aria-describedby="delete-dialog-description"
            PaperProps={{
              sx: {
                minWidth: 320,
                maxWidth: 360,
                padding: 1.5,
                borderRadius: 2,
              }
            }}
          >
            <DialogTitle id="delete-dialog-title" sx={{ fontSize: '1rem', fontWeight: 600, paddingBottom: 0.5 }}>
              Confirm Language Deletion
            </DialogTitle>
            <DialogContent sx={{ paddingTop: 0.5, paddingBottom: 0.5 }}>
              <Typography id="delete-dialog-description" sx={{ fontSize: '0.95rem', color: '#555', lineHeight: 1.4 }}>
                Are you sure you want to delete this language?<br />

              </Typography>
            </DialogContent>
            <DialogActions sx={{ justifyContent: 'flex-end', padding: '8px 16px' }}>
              <Button onClick={() => setDeleteConfirmOpen(false)} color="primary" sx={{ fontSize: '0.9rem', minWidth: 64 }}>
                Cancel
              </Button>
              <Button onClick={confirmDeleteLanguage} color="error" autoFocus sx={{ fontSize: '0.9rem', minWidth: 64 }}>
                Delete
              </Button>
            </DialogActions>
          </Dialog>
        </div>

      </div>
    </div>

      {/* Navigation guard to prevent leaving page with unsaved changes */}
      <NavigationPrompt when={isLabelUpdated || editLabelKey !== null || showAddRow} />
    </>
  );
}

export default Translater;
