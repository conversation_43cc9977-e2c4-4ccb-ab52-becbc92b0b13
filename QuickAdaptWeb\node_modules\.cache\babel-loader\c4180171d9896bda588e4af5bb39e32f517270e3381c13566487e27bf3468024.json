{"ast": null, "code": "// -----------------------Account APIs -----------------------\n\nimport { adminApiService } from \"./APIService\";\nimport i18n from \"i18next\";\nlet AccountDeleteDetails = {\n  AccountId: \"\",\n  AccountName: \"\",\n  AccountType: \"\",\n  CreatedBy: \"\",\n  CreatedDate: \"\",\n  OrganizationId: \"\",\n  UpdatedBy: \"\",\n  UpdatedDate: \"\",\n  Active: Boolean(true)\n};\nconst adminUrl = process.env.REACT_APP_ADMIN_API;\nexport const GetAllAccounts = async (setModels, setLoading) => {\n  try {\n    const response = await adminApiService.get(\"/Account/GetAllAccounts\");\n    let apiData = response.data;\n    if (Array.isArray(apiData)) {\n      apiData = apiData.map(account => ({\n        ...account,\n        CreatedDate: account.CreatedDate.split(\"T\")[0],\n        UpdatedDate: account.UpdatedDate ? account.UpdatedDate.split(\"T\")[0] : null\n      }));\n      setModels(apiData);\n    } else {\n      console.error(\"Invalid data format from API:\", apiData);\n    }\n  } catch (error) {\n    console.error(\"Error fetching Accounts:\", error);\n  } finally {\n    //setLoading(false);\n  }\n};\n_c = GetAllAccounts;\nexport const GetAccountsList = async (setModels, setLoading, OrganizationId, skip, top, setTotalcount, orderByField, filters) => {\n  try {\n    setLoading(true);\n    const requestBody = {\n      skip,\n      top,\n      filters: filters ? filters : \"\",\n      // Assuming an empty array for now, adjust as necessary\n      orderByFields: orderByField // Assuming an empty string for now, adjust as necessary\n    };\n    const response = await adminApiService.post(`/Account/GetAccountsByOrgId`, requestBody);\n    let apiData = response.data.results;\n    setTotalcount(response.data._count);\n    if (Array.isArray(apiData)) {\n      apiData = apiData.map(account => ({\n        ...account,\n        CreatedDate: account.CreatedDate.split(\"T\")[0],\n        UpdatedDate: account.UpdatedDate ? account.UpdatedDate.split(\"T\")[0] : null\n      }));\n      setModels(apiData);\n    } else {\n      console.error(\"Invalid data format from API:\", apiData);\n    }\n  } catch (error) {\n    console.error(\"Error fetching Accounts:\", error);\n  } finally {\n    setLoading(false);\n  }\n};\n_c2 = GetAccountsList;\nexport const SubmitCreateAccount = async (setLoading, setShowPopup, setModels, inputs, OrganizationId, skip, top, setTotalcount, openSnackbar, orderByField, filters) => {\n  try {\n    setLoading(true);\n    const response = await adminApiService.post(`/Account/CreateAccount`, inputs);\n    const responseData = response.data;\n    if (responseData.Success) {\n      openSnackbar(i18n.t(responseData.SuccessMessage), \"success\");\n      setShowPopup(false);\n      GetAccountsList(setModels, setLoading, OrganizationId, skip, top, setTotalcount, orderByField, filters);\n    } else {\n      openSnackbar(i18n.t(responseData.ErrorMessage), \"error\");\n    }\n  } catch (error) {\n    console.error(\"Error creating account:\", error);\n    openSnackbar(`${i18n.t(\"Error creating account\")} : ${error.message}`, \"error\");\n  } finally {\n    setLoading(false);\n  }\n};\n_c3 = SubmitCreateAccount;\nexport const SubmitAccountDetails = async (setLoading, setModels, setShowEditPopup, AccountDetails, organizationId, skip, top, setTotalcount, openSnackbar, orderByField, filters) => {\n  try {\n    const response = await adminApiService.put(`/Account/Update`, AccountDetails);\n    const responseData = response.data;\n    if (responseData.Success) {\n      openSnackbar(i18n.t(responseData.SuccessMessage), \"success\");\n      GetAccountsList(setModels, setLoading, organizationId, skip, top, setTotalcount, orderByField, filters);\n      setShowEditPopup(false);\n    } else {\n      openSnackbar(i18n.t(responseData.ErrorMessage), \"error\");\n    }\n  } catch (error) {\n    throw error;\n  } finally {}\n};\n_c4 = SubmitAccountDetails;\nexport const fetchDeleteAccountDetails = async (accountidedit, setLoading, setModels, setShowDeletePopup, OrganizationId, skip, top, setTotalcount, setSnackbarMessage, setSnackbarSeverity, setSnackbarOpen, openSnackbar, orderByField, filters) => {\n  try {\n    const response = await adminApiService.get(`/Account/GetAccountById?accountId=${accountidedit}`);\n    const responseData = response.data;\n    if (!responseData) {\n      throw new Error(\"Network response was not ok\");\n    }\n    AccountDeleteDetails = {\n      AccountId: responseData.AccountId,\n      AccountName: responseData.AccountName,\n      AccountType: responseData.AccountType,\n      CreatedBy: responseData.CreatedBy,\n      CreatedDate: responseData.CreatedDate,\n      OrganizationId: responseData.OrganizationId,\n      UpdatedBy: responseData.UpdatedBy,\n      UpdatedDate: responseData.UpdatedDate,\n      Active: false\n    };\n    DeleteAccountDetails(setLoading, setModels, setShowDeletePopup, AccountDeleteDetails, OrganizationId, skip, top, setTotalcount, setSnackbarMessage, setSnackbarSeverity, setSnackbarOpen, openSnackbar, orderByField, filters);\n  } catch (error) {\n    console.error(\"Failed to fetch user details:\", error);\n  }\n};\nexport const DeleteAccountDetails = async (setLoading, setModels, setShowDeletePopup, AccountDeleteDetails, OrganizationId, skip, top, setTotalcount, setSnackbarMessage, setSnackbarSeverity, setSnackbarOpen, openSnackbar, orderByField, filters) => {\n  try {\n    const response = await adminApiService.put(`/Account/Delete`, AccountDeleteDetails);\n    const responseData = response.data;\n    if (responseData) {\n      if (responseData.Success) {\n        openSnackbar(i18n.t(responseData.SuccessMessage), \"success\");\n      } else {\n        openSnackbar(i18n.t(responseData.ErrorMessage), \"error\");\n      }\n      GetAccountsList(setModels, setLoading, OrganizationId, skip, top, setTotalcount, orderByField, filters);\n      setShowDeletePopup(false);\n    }\n  } catch (error) {\n    setSnackbarMessage(`Error Deleting user: ${error.message}`);\n    setSnackbarSeverity(\"error\");\n    setSnackbarOpen(true);\n  }\n};\n\n// ---------------------Guide APIs -------------------------\n_c5 = DeleteAccountDetails;\nexport const saveGuide = async guideData => {\n  const formattedGuideData = {\n    GuideId: guideData.GuideId,\n    Title: guideData.Title,\n    Content: guideData.Content,\n    OrganizationId: guideData.OrganizationId,\n    CreatedDate: guideData.CreatedDate,\n    UpdatedDate: guideData.UpdatedDate,\n    CreatedBy: guideData.CreatedBy,\n    UpdatedBy: guideData.UpdatedBy,\n    GuideStep: guideData.GuideStep.map(step => ({\n      StepTitle: step.StepTitle,\n      Text: step.Text,\n      Element: step.Element,\n      On: step.On,\n      Image: step.Image,\n      BackgroundColor: step.BackgroundColor,\n      Id: step.Id,\n      Arrow: step.Arrow,\n      Classes: step.Classes,\n      IsClickable: step.IsClickable\n    }))\n  };\n  try {\n    const response = await adminApiService.post(\"/Guide/Saveguide\", formattedGuideData);\n    return response.data;\n  } catch (error) {\n    console.error(\"Error saving guide:\", error);\n    throw error;\n  }\n};\nexport const getAllGuides = async () => {\n  try {\n    const response = await adminApiService.get(\"/Guide/GetAllguides\");\n    return response.data;\n  } catch (error) {\n    console.error(\"Error fetching guides:\", error);\n    throw error;\n  }\n};\nexport const deleteGuide = async guideId => {\n  try {\n    const response = await adminApiService.post(`/Guide/Deleteguide?guideId=${guideId}`);\n    if (response.status === 204) {} else {\n      console.error('Failed to delete guide');\n    }\n  } catch (error) {\n    throw error;\n  }\n};\nexport const GetAllAccountsList = async (skip, top, orderByFields, filters, organization) => {\n  try {\n    const response = await adminApiService.post(\"/Account/GetAllAccounts\", {\n      skip,\n      top,\n      orderByFields,\n      filters,\n      organization\n    });\n    const apiData = response.data.results;\n    if (Array.isArray(apiData)) {\n      return apiData;\n    } else {\n      console.error(\"Invalid data format from API:\", apiData);\n      return [];\n    }\n  } catch (error) {\n    console.error(\"Error fetching Accounts:\", error);\n    return [];\n  }\n};\n_c6 = GetAllAccountsList;\nexport const fetchAccountsById = async id => {\n  try {\n    const response = await adminApiService.get(`/Account/GetAccountById?accountId=${id}`);\n    const responseData = response.data;\n    if (!responseData) {\n      throw new Error(\"Network response was not ok\");\n    }\n    return responseData;\n  } catch (error) {\n    console.error(\"Failed to fetch user details:\", error);\n  }\n};\nexport const GetAccountsListById = async (skip, top, orderByFields, filters, organization) => {\n  try {\n    const response = await adminApiService.post(\"/Account/GetAccountsByOrgId\", {\n      skip,\n      top,\n      orderByFields,\n      filters,\n      organization\n    });\n    const apiData = response.data.results;\n    if (Array.isArray(apiData)) {\n      return apiData;\n    } else {\n      console.error(\"Invalid data format from API:\", apiData);\n      return [];\n    }\n  } catch (error) {\n    console.error(\"Error fetching Accounts:\", error);\n    return [];\n  }\n};\n_c7 = GetAccountsListById;\nexport const UpdateAccountOpenAiKey = async (setLoading, AccountDetails, openSnackbar, OpenAIKeyUpdatedSuccesFully, setError, setOpenAiKeyValid, FetchAccountById) => {\n  try {\n    setLoading(true);\n    const response = await adminApiService.put(`/Account/UpdateOpenAIKey?accountId=${AccountDetails.AccountId}&openAIKey=${AccountDetails.OpenAIKey}`);\n    const responseData = response.data;\n    if (responseData.Success) {\n      openSnackbar(i18n.t(responseData.SuccessMessage), \"success\");\n      OpenAIKeyUpdatedSuccesFully();\n      setLoading(false);\n      FetchAccountById(true);\n    } else {\n      setLoading(false);\n      openSnackbar(i18n.t(responseData.ErrorMessage), \"error\");\n      setError(responseData.ErrorMessage);\n      setOpenAiKeyValid(false);\n    }\n  } catch (error) {\n    setLoading(false);\n    openSnackbar(i18n.t(error.message), \"error\");\n    setError(error.message);\n    setOpenAiKeyValid(old => !old);\n  } finally {}\n};\n_c8 = UpdateAccountOpenAiKey;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"GetAllAccounts\");\n$RefreshReg$(_c2, \"GetAccountsList\");\n$RefreshReg$(_c3, \"SubmitCreateAccount\");\n$RefreshReg$(_c4, \"SubmitAccountDetails\");\n$RefreshReg$(_c5, \"DeleteAccountDetails\");\n$RefreshReg$(_c6, \"GetAllAccountsList\");\n$RefreshReg$(_c7, \"GetAccountsListById\");\n$RefreshReg$(_c8, \"UpdateAccountOpenAiKey\");", "map": {"version": 3, "names": ["adminApiService", "i18n", "AccountDeleteDetails", "AccountId", "Account<PERSON><PERSON>", "AccountType", "CreatedBy", "CreatedDate", "OrganizationId", "UpdatedBy", "UpdatedDate", "Active", "Boolean", "adminUrl", "process", "env", "REACT_APP_ADMIN_API", "GetAllAccounts", "setModels", "setLoading", "response", "get", "apiData", "data", "Array", "isArray", "map", "account", "split", "console", "error", "_c", "GetAccountsList", "skip", "top", "setTotalcount", "orderByField", "filters", "requestBody", "order<PERSON><PERSON><PERSON><PERSON>s", "post", "results", "_count", "_c2", "SubmitCreateAccount", "setShowPopup", "inputs", "openSnackbar", "responseData", "Success", "t", "SuccessMessage", "ErrorMessage", "message", "_c3", "SubmitAccountDetails", "setShowEditPopup", "AccountDetails", "organizationId", "put", "_c4", "fetchDeleteAccountDetails", "accountidedit", "setShowDeletePopup", "setSnackbarMessage", "setSnackbarSeverity", "setSnackbarOpen", "Error", "DeleteAccountDetails", "_c5", "saveGuide", "guideData", "formattedGuideData", "GuideId", "Title", "Content", "GuideStep", "step", "<PERSON><PERSON><PERSON><PERSON>", "Text", "Element", "On", "Image", "BackgroundColor", "Id", "Arrow", "Classes", "IsClickable", "getAllGuides", "deleteGuide", "guideId", "status", "GetAllAccountsList", "organization", "_c6", "fetchAccountsById", "id", "GetAccountsListById", "_c7", "UpdateAccountOpenAiKey", "OpenAIKeyUpdatedSuccesFully", "setError", "setOpenAiKeyValid", "FetchAccountById", "OpenAIKey", "old", "_c8", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/services/AccountService.tsx"], "sourcesContent": ["\r\n// -----------------------Account APIs -----------------------\r\nimport { Status } from \"../models/Status\";\r\nimport { adminApiService } from \"./APIService\";\r\nimport i18n from \"i18next\";\r\n\r\nlet AccountDeleteDetails = {\r\n\tAccountId: \"\",\r\n\tAccountName: \"\",\r\n\tAccountType: \"\",\r\n\tCreatedBy: \"\",\r\n\tCreatedDate: \"\",\r\n\tOrganizationId: \"\",\r\n\tUpdatedBy: \"\",\r\n\tUpdatedDate: \"\",\r\n\tActive: Boolean(true),\r\n};\r\n\r\nconst adminUrl = process.env.REACT_APP_ADMIN_API\r\n\r\nexport const GetAllAccounts = async (setModels: any, setLoading: any) => {\r\n\ttry {\r\n\t\tconst response = await adminApiService.get(\"/Account/GetAllAccounts\");\r\n\t\tlet apiData = response.data;\r\n\r\n\t\tif (Array.isArray(apiData)) {\r\n\t\t\tapiData = apiData.map((account) => ({\r\n\t\t\t\t...account,\r\n\t\t\t\tCreatedDate: account.CreatedDate.split(\"T\")[0],\r\n\t\t\t\tUpdatedDate: account.UpdatedDate ? account.UpdatedDate.split(\"T\")[0] : null,\r\n\t\t\t}));\r\n\t\t\tsetModels(apiData);\r\n\t\t} else {\r\n\t\t\tconsole.error(\"Invalid data format from API:\", apiData);\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error(\"Error fetching Accounts:\", error);\r\n\t} finally {\r\n\t\t//setLoading(false);\r\n\t}\r\n};\r\n\r\nexport const GetAccountsList = async (\r\n\tsetModels: any,\r\n\tsetLoading: any,\r\n\tOrganizationId: any,\r\n\tskip: any,\r\n\ttop: any,\r\n\tsetTotalcount: any,\r\n\torderByField: any,\r\n\tfilters: any\r\n) => {\r\n\ttry {\r\n\t\tsetLoading(true);\r\n\t\tconst requestBody = {\r\n\t\t\tskip,\r\n\t\t\ttop,\r\n\t\t\tfilters: filters ? filters : \"\", // Assuming an empty array for now, adjust as necessary\r\n\t\t\torderByFields: orderByField, // Assuming an empty string for now, adjust as necessary\r\n\t\t};\r\n\t\tconst response = await adminApiService.post(`/Account/GetAccountsByOrgId`, requestBody);\r\n\r\n\t\tlet apiData = response.data.results;\r\n\t\tsetTotalcount(response.data._count);\r\n\r\n\t\tif (Array.isArray(apiData)) {\r\n\t\t\tapiData = apiData.map((account) => ({\r\n\t\t\t\t...account,\r\n\t\t\t\tCreatedDate: account.CreatedDate.split(\"T\")[0],\r\n\t\t\t\tUpdatedDate: account.UpdatedDate ? account.UpdatedDate.split(\"T\")[0] : null,\r\n\t\t\t}));\r\n\t\t\tsetModels(apiData);\r\n\t\t} else {\r\n\t\t\tconsole.error(\"Invalid data format from API:\", apiData);\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error(\"Error fetching Accounts:\", error);\r\n\t} finally {\r\n\t\tsetLoading(false);\r\n\t}\r\n};\r\n\r\n\r\nexport const SubmitCreateAccount = async (\r\n\tsetLoading: any,\r\n\tsetShowPopup: any,\r\n\tsetModels: any,\r\n\tinputs: any,\r\n\tOrganizationId: any,\r\n\tskip: any,\r\n\ttop: any,\r\n\tsetTotalcount: any,\r\n\topenSnackbar: any,\r\n\torderByField: any,\r\n\tfilters: any\r\n) => {\r\n\ttry {\r\n\t\tsetLoading(true);\r\n\t\tconst response = await adminApiService.post(`/Account/CreateAccount`,inputs);\r\n\t\tconst responseData = response.data;\r\n\t\tif (responseData.Success) {\r\n\t\t\t\r\n\t\t\topenSnackbar(i18n.t(responseData.SuccessMessage), \"success\");\r\n\t\t\t\r\n\t\t\tsetShowPopup(false);\r\n\t\t\tGetAccountsList(setModels, setLoading, OrganizationId, skip, top, setTotalcount, orderByField, filters);\r\n\t\t} else {\r\n\t\t\t\r\n\t\t\topenSnackbar(i18n.t(responseData.ErrorMessage), \"error\");\r\n\t\t\t\r\n\t\t}\r\n\t} catch (error: any) {\r\n\t\tconsole.error(\"Error creating account:\", error);\r\n\t\topenSnackbar(`${i18n.t(\"Error creating account\")} : ${error.message}`, \"error\");\r\n\r\n\t} finally {\r\n\t\tsetLoading(false);\r\n\t}\r\n};\r\n\r\nexport const SubmitAccountDetails = async (\r\n\tsetLoading: any,\r\n\tsetModels: any,\r\n\tsetShowEditPopup: any,\r\n\tAccountDetails: any,\r\n\torganizationId: any,\r\n\tskip: any,\r\n\ttop: any,\r\n\tsetTotalcount: any,\r\n\topenSnackbar: any,\r\n\torderByField: any,\r\n\tfilters: any\r\n) => {\r\n\ttry {\r\n\t\tconst response = await adminApiService.put(`/Account/Update`, AccountDetails);\r\n\r\n\t\tconst responseData = response.data;\r\n\t\tif (responseData.Success) {\r\n\t\t\topenSnackbar(i18n.t(responseData.SuccessMessage), \"success\");\r\n\t\t\t\r\n\t\t\tGetAccountsList(setModels, setLoading, organizationId, skip, top, setTotalcount, orderByField, filters);\r\n\t\t\tsetShowEditPopup(false);\r\n\t\t} else {\r\n\t\t\topenSnackbar(i18n.t(responseData.ErrorMessage), \"error\");\r\n\t\t}\r\n\t} catch (error: any) {\r\n\t\tthrow error;\r\n\t} finally {\r\n\r\n\t}\r\n};\r\n\r\nexport const fetchDeleteAccountDetails = async (\r\n\taccountidedit: any,\r\n\tsetLoading: any,\r\n\tsetModels: any,\r\n\tsetShowDeletePopup: any,\r\n\tOrganizationId: any,\r\n\tskip: any,\r\n\ttop: any,\r\n\tsetTotalcount: any,\r\n\tsetSnackbarMessage: any,\r\n\tsetSnackbarSeverity: any,\r\n\tsetSnackbarOpen: any,\r\n\topenSnackbar: any,\r\n\torderByField: any,\r\n\tfilters: any\r\n) => {\r\n\ttry {\r\n\t\tconst response = await adminApiService.get(`/Account/GetAccountById?accountId=${accountidedit}`);\r\n\t\tconst responseData = response.data;\r\n\t\tif (!responseData) {\r\n\t\t\tthrow new Error(\"Network response was not ok\");\r\n\t\t}\r\n\r\n\t\tAccountDeleteDetails = {\r\n\t\t\tAccountId: responseData.AccountId,\r\n\t\t\tAccountName: responseData.AccountName,\r\n\t\t\tAccountType: responseData.AccountType,\r\n\t\t\tCreatedBy: responseData.CreatedBy,\r\n\t\t\tCreatedDate: responseData.CreatedDate,\r\n\t\t\tOrganizationId: responseData.OrganizationId,\r\n\t\t\tUpdatedBy: responseData.UpdatedBy,\r\n\t\t\tUpdatedDate: responseData.UpdatedDate,\r\n\t\t\tActive: false,\r\n\t\t};\r\n\t\tDeleteAccountDetails(\r\n\t\t\tsetLoading,\r\n\t\t\tsetModels,\r\n\t\t\tsetShowDeletePopup,\r\n\t\t\tAccountDeleteDetails,\r\n\t\t\tOrganizationId,\r\n\t\t\tskip,\r\n\t\t\ttop,\r\n\t\t\tsetTotalcount,\r\n\t\t\tsetSnackbarMessage,\r\n\t\t\tsetSnackbarSeverity,\r\n\t\t\tsetSnackbarOpen,\r\n\t\t\topenSnackbar,\r\n\t\t\torderByField,\r\n\t\t\tfilters\r\n\t\t);\r\n\t} catch (error) {\r\n\t\tconsole.error(\"Failed to fetch user details:\", error);\r\n\t}\r\n};\r\n\r\nexport const DeleteAccountDetails = async (\r\n\tsetLoading: any,\r\n\tsetModels: any,\r\n\tsetShowDeletePopup: any,\r\n\tAccountDeleteDetails: any,\r\n\tOrganizationId: any,\r\n\tskip: any,\r\n\ttop: any,\r\n\tsetTotalcount: any,\r\n\tsetSnackbarMessage: any,\r\n\tsetSnackbarSeverity: any,\r\n\tsetSnackbarOpen: any,\r\n\topenSnackbar: any,\r\n\torderByField: any,\r\n\tfilters: any\r\n) => {\r\n\ttry {\r\n\t\tconst response = await adminApiService.put(`/Account/Delete`, AccountDeleteDetails);\r\n\t\tconst responseData = response.data;\r\n\r\n\t\tif (responseData) {\r\n\t\t\tif (responseData.Success) {\r\n\t\t\t\t\r\n\t\t\t\topenSnackbar(i18n.t(responseData.SuccessMessage), \"success\");\r\n\t\t\t} else {\r\n\r\n\t\t\t\topenSnackbar(i18n.t(responseData.ErrorMessage), \"error\");\r\n\t\t\t}\r\n\t\t\tGetAccountsList(setModels, setLoading, OrganizationId, skip, top, setTotalcount, orderByField, filters);\r\n\t\t\tsetShowDeletePopup(false);\r\n\t\t}\r\n\t} catch (error: any) {\r\n\t\tsetSnackbarMessage(`Error Deleting user: ${error.message}`);\r\n\t\tsetSnackbarSeverity(\"error\");\r\n\t\tsetSnackbarOpen(true);\r\n\t}\r\n};\r\n\r\n// ---------------------Guide APIs -------------------------\r\n\r\nexport const saveGuide = async (guideData : any) => {\r\n    const formattedGuideData = {\r\n        GuideId: guideData.GuideId,\r\n        Title: guideData.Title,\r\n        Content: guideData.Content,\r\n        OrganizationId: guideData.OrganizationId,\r\n        CreatedDate: guideData.CreatedDate,\r\n        UpdatedDate: guideData.UpdatedDate,\r\n        CreatedBy: guideData.CreatedBy,\r\n        UpdatedBy: guideData.UpdatedBy,\r\n        GuideStep: guideData.GuideStep.map((step : any) => ({\r\n            StepTitle: step.StepTitle,\r\n            Text: step.Text,\r\n            Element: step.Element,\r\n            On: step.On,\r\n            Image: step.Image,\r\n            BackgroundColor: step.BackgroundColor,\r\n            Id: step.Id,\r\n            Arrow: step.Arrow,\r\n            Classes: step.Classes,\r\n            IsClickable: step.IsClickable,\r\n        }))\r\n    };\r\n\r\n    try {\r\n        const response = await adminApiService.post<Status>(\"/Guide/Saveguide\", formattedGuideData);\r\n        return response.data;\r\n    } catch (error) {\r\n        console.error(\"Error saving guide:\", error);\r\n        throw error;\r\n    }\r\n};\r\n\r\n\r\nexport const getAllGuides = async (): Promise<any[]> => {\r\n\ttry {\r\n\t  const response = await adminApiService.get(\"/Guide/GetAllguides\");\r\n\t  return response.data;\r\n\t} catch (error) {\r\n\t  console.error(\"Error fetching guides:\", error);\r\n\t  throw error;\r\n\t}\r\n  };\r\n  export const deleteGuide = async (guideId: string): Promise<void> => {\r\n    try {\r\n        const response = await adminApiService.post(`/Guide/Deleteguide?guideId=${guideId}`);\r\n        if (response.status === 204) {\r\n        } else {\r\n            console.error('Failed to delete guide');\r\n        }\r\n    } catch (error) {\r\n        throw error;\r\n    }\r\n};\r\n\r\n\r\nexport const GetAllAccountsList = async (\r\n\tskip: number,\r\n\ttop: number,\r\n\torderByFields: string,\r\n\tfilters: any[],\r\n\torganization: string,\r\n\t//setTotalcount: (count: number) => void\r\n  ): Promise<any[]> => {\r\n\ttry {\r\n\t  const response = await adminApiService.post(\"/Account/GetAllAccounts\", {\r\n\t\tskip,\r\n\t\ttop,\r\n\t\torderByFields,\r\n\t\tfilters, \r\n\t\torganization\r\n\t  });\r\n\t  const apiData = response.data.results;\r\n\t  if (Array.isArray(apiData)) {\r\n\t\treturn apiData;\r\n\t  } else {\r\n\t\tconsole.error(\"Invalid data format from API:\", apiData);\r\n\t\treturn [];\r\n\t  }\r\n\t} catch (error) {\r\n\t  console.error(\"Error fetching Accounts:\", error);\r\n\t  return [];\r\n\t}\r\n  };\r\n  \r\n  export const fetchAccountsById = async (\r\n\tid:any\r\n) => {\r\n\ttry {\r\n\t\tconst response = await adminApiService.get(`/Account/GetAccountById?accountId=${id}`);\r\n\t\tconst responseData = response.data;\r\n\t\tif (!responseData) {\r\n\t\t\tthrow new Error(\"Network response was not ok\");\r\n\t\t}\r\n\t\treturn responseData;\r\n\t\t\r\n\t} catch (error) {\r\n\t\tconsole.error(\"Failed to fetch user details:\", error);\r\n\t}\r\n};\r\n\r\n\r\n\r\n\r\n\r\nexport const GetAccountsListById= async (\r\n\tskip: number,\r\n\ttop: number,\r\n\torderByFields: string,\r\n\tfilters: any[],\r\n\torganization: string,\r\n\t//setTotalcount: (count: number) => void\r\n  ): Promise<any[]> => {\r\n\ttry {\r\n\t  const response = await adminApiService.post(\"/Account/GetAccountsByOrgId\", {\r\n\t\tskip,\r\n\t\ttop,\r\n\t\torderByFields,\r\n\t\tfilters, \r\n\t\torganization\r\n\t  });\r\n\t  const apiData = response.data.results;\r\n\t  if (Array.isArray(apiData)) {\r\n\t\treturn apiData;\r\n\t  } else {\r\n\t\tconsole.error(\"Invalid data format from API:\", apiData);\r\n\t\treturn [];\r\n\t  }\r\n\t} catch (error) {\r\n\t  console.error(\"Error fetching Accounts:\", error);\r\n\t  return [];\r\n\t}\r\n};\r\n  \r\nexport const UpdateAccountOpenAiKey = async (\r\n\tsetLoading: any,\r\n\tAccountDetails: any,\r\n\topenSnackbar: any,\r\n\tOpenAIKeyUpdatedSuccesFully: any,\r\n\tsetError: any,\r\n\tsetOpenAiKeyValid: any,\r\n\tFetchAccountById:any\r\n\r\n) => {\r\n\ttry {\r\n\t\tsetLoading(true);\r\n\t\tconst response = await adminApiService.put(`/Account/UpdateOpenAIKey?accountId=${AccountDetails.AccountId}&openAIKey=${AccountDetails.OpenAIKey}`);\r\n\r\n\r\n\t\tconst responseData = response.data;\r\n\r\n\t\tif (responseData.Success) {\r\n\t\t\topenSnackbar(i18n.t(responseData.SuccessMessage), \"success\");\r\n\t\t\tOpenAIKeyUpdatedSuccesFully();\r\n\t\t\tsetLoading(false);\r\n\t\t\tFetchAccountById(true);\r\n\t\t\t\r\n\t\t} else {\r\n\t\t\tsetLoading(false);\r\n\t\t\topenSnackbar(i18n.t(responseData.ErrorMessage), \"error\");\r\n\t\t\tsetError(responseData.ErrorMessage);\r\n\t\t\tsetOpenAiKeyValid(false);\r\n\t\t\t\r\n\t\t}\r\n\t} catch (error: any) {\r\n\t\tsetLoading(false);\r\n\t\topenSnackbar(i18n.t(error.message), \"error\");\r\n\t\tsetError(error.message);\r\n\t\tsetOpenAiKeyValid((old:any)=> !old);\r\n\t} finally {\r\n\r\n\t}\r\n};"], "mappings": "AACA;;AAEA,SAASA,eAAe,QAAQ,cAAc;AAC9C,OAAOC,IAAI,MAAM,SAAS;AAE1B,IAAIC,oBAAoB,GAAG;EAC1BC,SAAS,EAAE,EAAE;EACbC,WAAW,EAAE,EAAE;EACfC,WAAW,EAAE,EAAE;EACfC,SAAS,EAAE,EAAE;EACbC,WAAW,EAAE,EAAE;EACfC,cAAc,EAAE,EAAE;EAClBC,SAAS,EAAE,EAAE;EACbC,WAAW,EAAE,EAAE;EACfC,MAAM,EAAEC,OAAO,CAAC,IAAI;AACrB,CAAC;AAED,MAAMC,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,mBAAmB;AAEhD,OAAO,MAAMC,cAAc,GAAG,MAAAA,CAAOC,SAAc,EAAEC,UAAe,KAAK;EACxE,IAAI;IACH,MAAMC,QAAQ,GAAG,MAAMpB,eAAe,CAACqB,GAAG,CAAC,yBAAyB,CAAC;IACrE,IAAIC,OAAO,GAAGF,QAAQ,CAACG,IAAI;IAE3B,IAAIC,KAAK,CAACC,OAAO,CAACH,OAAO,CAAC,EAAE;MAC3BA,OAAO,GAAGA,OAAO,CAACI,GAAG,CAAEC,OAAO,KAAM;QACnC,GAAGA,OAAO;QACVpB,WAAW,EAAEoB,OAAO,CAACpB,WAAW,CAACqB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC9ClB,WAAW,EAAEiB,OAAO,CAACjB,WAAW,GAAGiB,OAAO,CAACjB,WAAW,CAACkB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG;MACxE,CAAC,CAAC,CAAC;MACHV,SAAS,CAACI,OAAO,CAAC;IACnB,CAAC,MAAM;MACNO,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAER,OAAO,CAAC;IACxD;EACD,CAAC,CAAC,OAAOQ,KAAK,EAAE;IACfD,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;EACjD,CAAC,SAAS;IACT;EAAA;AAEF,CAAC;AAACC,EAAA,GApBWd,cAAc;AAsB3B,OAAO,MAAMe,eAAe,GAAG,MAAAA,CAC9Bd,SAAc,EACdC,UAAe,EACfX,cAAmB,EACnByB,IAAS,EACTC,GAAQ,EACRC,aAAkB,EAClBC,YAAiB,EACjBC,OAAY,KACR;EACJ,IAAI;IACHlB,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMmB,WAAW,GAAG;MACnBL,IAAI;MACJC,GAAG;MACHG,OAAO,EAAEA,OAAO,GAAGA,OAAO,GAAG,EAAE;MAAE;MACjCE,aAAa,EAAEH,YAAY,CAAE;IAC9B,CAAC;IACD,MAAMhB,QAAQ,GAAG,MAAMpB,eAAe,CAACwC,IAAI,CAAC,6BAA6B,EAAEF,WAAW,CAAC;IAEvF,IAAIhB,OAAO,GAAGF,QAAQ,CAACG,IAAI,CAACkB,OAAO;IACnCN,aAAa,CAACf,QAAQ,CAACG,IAAI,CAACmB,MAAM,CAAC;IAEnC,IAAIlB,KAAK,CAACC,OAAO,CAACH,OAAO,CAAC,EAAE;MAC3BA,OAAO,GAAGA,OAAO,CAACI,GAAG,CAAEC,OAAO,KAAM;QACnC,GAAGA,OAAO;QACVpB,WAAW,EAAEoB,OAAO,CAACpB,WAAW,CAACqB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC9ClB,WAAW,EAAEiB,OAAO,CAACjB,WAAW,GAAGiB,OAAO,CAACjB,WAAW,CAACkB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG;MACxE,CAAC,CAAC,CAAC;MACHV,SAAS,CAACI,OAAO,CAAC;IACnB,CAAC,MAAM;MACNO,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAER,OAAO,CAAC;IACxD;EACD,CAAC,CAAC,OAAOQ,KAAK,EAAE;IACfD,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;EACjD,CAAC,SAAS;IACTX,UAAU,CAAC,KAAK,CAAC;EAClB;AACD,CAAC;AAACwB,GAAA,GAtCWX,eAAe;AAyC5B,OAAO,MAAMY,mBAAmB,GAAG,MAAAA,CAClCzB,UAAe,EACf0B,YAAiB,EACjB3B,SAAc,EACd4B,MAAW,EACXtC,cAAmB,EACnByB,IAAS,EACTC,GAAQ,EACRC,aAAkB,EAClBY,YAAiB,EACjBX,YAAiB,EACjBC,OAAY,KACR;EACJ,IAAI;IACHlB,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMC,QAAQ,GAAG,MAAMpB,eAAe,CAACwC,IAAI,CAAC,wBAAwB,EAACM,MAAM,CAAC;IAC5E,MAAME,YAAY,GAAG5B,QAAQ,CAACG,IAAI;IAClC,IAAIyB,YAAY,CAACC,OAAO,EAAE;MAEzBF,YAAY,CAAC9C,IAAI,CAACiD,CAAC,CAACF,YAAY,CAACG,cAAc,CAAC,EAAE,SAAS,CAAC;MAE5DN,YAAY,CAAC,KAAK,CAAC;MACnBb,eAAe,CAACd,SAAS,EAAEC,UAAU,EAAEX,cAAc,EAAEyB,IAAI,EAAEC,GAAG,EAAEC,aAAa,EAAEC,YAAY,EAAEC,OAAO,CAAC;IACxG,CAAC,MAAM;MAENU,YAAY,CAAC9C,IAAI,CAACiD,CAAC,CAACF,YAAY,CAACI,YAAY,CAAC,EAAE,OAAO,CAAC;IAEzD;EACD,CAAC,CAAC,OAAOtB,KAAU,EAAE;IACpBD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/CiB,YAAY,CAAC,GAAG9C,IAAI,CAACiD,CAAC,CAAC,wBAAwB,CAAC,MAAMpB,KAAK,CAACuB,OAAO,EAAE,EAAE,OAAO,CAAC;EAEhF,CAAC,SAAS;IACTlC,UAAU,CAAC,KAAK,CAAC;EAClB;AACD,CAAC;AAACmC,GAAA,GAnCWV,mBAAmB;AAqChC,OAAO,MAAMW,oBAAoB,GAAG,MAAAA,CACnCpC,UAAe,EACfD,SAAc,EACdsC,gBAAqB,EACrBC,cAAmB,EACnBC,cAAmB,EACnBzB,IAAS,EACTC,GAAQ,EACRC,aAAkB,EAClBY,YAAiB,EACjBX,YAAiB,EACjBC,OAAY,KACR;EACJ,IAAI;IACH,MAAMjB,QAAQ,GAAG,MAAMpB,eAAe,CAAC2D,GAAG,CAAC,iBAAiB,EAAEF,cAAc,CAAC;IAE7E,MAAMT,YAAY,GAAG5B,QAAQ,CAACG,IAAI;IAClC,IAAIyB,YAAY,CAACC,OAAO,EAAE;MACzBF,YAAY,CAAC9C,IAAI,CAACiD,CAAC,CAACF,YAAY,CAACG,cAAc,CAAC,EAAE,SAAS,CAAC;MAE5DnB,eAAe,CAACd,SAAS,EAAEC,UAAU,EAAEuC,cAAc,EAAEzB,IAAI,EAAEC,GAAG,EAAEC,aAAa,EAAEC,YAAY,EAAEC,OAAO,CAAC;MACvGmB,gBAAgB,CAAC,KAAK,CAAC;IACxB,CAAC,MAAM;MACNT,YAAY,CAAC9C,IAAI,CAACiD,CAAC,CAACF,YAAY,CAACI,YAAY,CAAC,EAAE,OAAO,CAAC;IACzD;EACD,CAAC,CAAC,OAAOtB,KAAU,EAAE;IACpB,MAAMA,KAAK;EACZ,CAAC,SAAS,CAEV;AACD,CAAC;AAAC8B,GAAA,GA9BWL,oBAAoB;AAgCjC,OAAO,MAAMM,yBAAyB,GAAG,MAAAA,CACxCC,aAAkB,EAClB3C,UAAe,EACfD,SAAc,EACd6C,kBAAuB,EACvBvD,cAAmB,EACnByB,IAAS,EACTC,GAAQ,EACRC,aAAkB,EAClB6B,kBAAuB,EACvBC,mBAAwB,EACxBC,eAAoB,EACpBnB,YAAiB,EACjBX,YAAiB,EACjBC,OAAY,KACR;EACJ,IAAI;IACH,MAAMjB,QAAQ,GAAG,MAAMpB,eAAe,CAACqB,GAAG,CAAC,qCAAqCyC,aAAa,EAAE,CAAC;IAChG,MAAMd,YAAY,GAAG5B,QAAQ,CAACG,IAAI;IAClC,IAAI,CAACyB,YAAY,EAAE;MAClB,MAAM,IAAImB,KAAK,CAAC,6BAA6B,CAAC;IAC/C;IAEAjE,oBAAoB,GAAG;MACtBC,SAAS,EAAE6C,YAAY,CAAC7C,SAAS;MACjCC,WAAW,EAAE4C,YAAY,CAAC5C,WAAW;MACrCC,WAAW,EAAE2C,YAAY,CAAC3C,WAAW;MACrCC,SAAS,EAAE0C,YAAY,CAAC1C,SAAS;MACjCC,WAAW,EAAEyC,YAAY,CAACzC,WAAW;MACrCC,cAAc,EAAEwC,YAAY,CAACxC,cAAc;MAC3CC,SAAS,EAAEuC,YAAY,CAACvC,SAAS;MACjCC,WAAW,EAAEsC,YAAY,CAACtC,WAAW;MACrCC,MAAM,EAAE;IACT,CAAC;IACDyD,oBAAoB,CACnBjD,UAAU,EACVD,SAAS,EACT6C,kBAAkB,EAClB7D,oBAAoB,EACpBM,cAAc,EACdyB,IAAI,EACJC,GAAG,EACHC,aAAa,EACb6B,kBAAkB,EAClBC,mBAAmB,EACnBC,eAAe,EACfnB,YAAY,EACZX,YAAY,EACZC,OACD,CAAC;EACF,CAAC,CAAC,OAAOP,KAAK,EAAE;IACfD,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;EACtD;AACD,CAAC;AAED,OAAO,MAAMsC,oBAAoB,GAAG,MAAAA,CACnCjD,UAAe,EACfD,SAAc,EACd6C,kBAAuB,EACvB7D,oBAAyB,EACzBM,cAAmB,EACnByB,IAAS,EACTC,GAAQ,EACRC,aAAkB,EAClB6B,kBAAuB,EACvBC,mBAAwB,EACxBC,eAAoB,EACpBnB,YAAiB,EACjBX,YAAiB,EACjBC,OAAY,KACR;EACJ,IAAI;IACH,MAAMjB,QAAQ,GAAG,MAAMpB,eAAe,CAAC2D,GAAG,CAAC,iBAAiB,EAAEzD,oBAAoB,CAAC;IACnF,MAAM8C,YAAY,GAAG5B,QAAQ,CAACG,IAAI;IAElC,IAAIyB,YAAY,EAAE;MACjB,IAAIA,YAAY,CAACC,OAAO,EAAE;QAEzBF,YAAY,CAAC9C,IAAI,CAACiD,CAAC,CAACF,YAAY,CAACG,cAAc,CAAC,EAAE,SAAS,CAAC;MAC7D,CAAC,MAAM;QAENJ,YAAY,CAAC9C,IAAI,CAACiD,CAAC,CAACF,YAAY,CAACI,YAAY,CAAC,EAAE,OAAO,CAAC;MACzD;MACApB,eAAe,CAACd,SAAS,EAAEC,UAAU,EAAEX,cAAc,EAAEyB,IAAI,EAAEC,GAAG,EAAEC,aAAa,EAAEC,YAAY,EAAEC,OAAO,CAAC;MACvG0B,kBAAkB,CAAC,KAAK,CAAC;IAC1B;EACD,CAAC,CAAC,OAAOjC,KAAU,EAAE;IACpBkC,kBAAkB,CAAC,wBAAwBlC,KAAK,CAACuB,OAAO,EAAE,CAAC;IAC3DY,mBAAmB,CAAC,OAAO,CAAC;IAC5BC,eAAe,CAAC,IAAI,CAAC;EACtB;AACD,CAAC;;AAED;AAAAG,GAAA,GAtCaD,oBAAoB;AAwCjC,OAAO,MAAME,SAAS,GAAG,MAAOC,SAAe,IAAK;EAChD,MAAMC,kBAAkB,GAAG;IACvBC,OAAO,EAAEF,SAAS,CAACE,OAAO;IAC1BC,KAAK,EAAEH,SAAS,CAACG,KAAK;IACtBC,OAAO,EAAEJ,SAAS,CAACI,OAAO;IAC1BnE,cAAc,EAAE+D,SAAS,CAAC/D,cAAc;IACxCD,WAAW,EAAEgE,SAAS,CAAChE,WAAW;IAClCG,WAAW,EAAE6D,SAAS,CAAC7D,WAAW;IAClCJ,SAAS,EAAEiE,SAAS,CAACjE,SAAS;IAC9BG,SAAS,EAAE8D,SAAS,CAAC9D,SAAS;IAC9BmE,SAAS,EAAEL,SAAS,CAACK,SAAS,CAAClD,GAAG,CAAEmD,IAAU,KAAM;MAChDC,SAAS,EAAED,IAAI,CAACC,SAAS;MACzBC,IAAI,EAAEF,IAAI,CAACE,IAAI;MACfC,OAAO,EAAEH,IAAI,CAACG,OAAO;MACrBC,EAAE,EAAEJ,IAAI,CAACI,EAAE;MACXC,KAAK,EAAEL,IAAI,CAACK,KAAK;MACjBC,eAAe,EAAEN,IAAI,CAACM,eAAe;MACrCC,EAAE,EAAEP,IAAI,CAACO,EAAE;MACXC,KAAK,EAAER,IAAI,CAACQ,KAAK;MACjBC,OAAO,EAAET,IAAI,CAACS,OAAO;MACrBC,WAAW,EAAEV,IAAI,CAACU;IACtB,CAAC,CAAC;EACN,CAAC;EAED,IAAI;IACA,MAAMnE,QAAQ,GAAG,MAAMpB,eAAe,CAACwC,IAAI,CAAS,kBAAkB,EAAEgC,kBAAkB,CAAC;IAC3F,OAAOpD,QAAQ,CAACG,IAAI;EACxB,CAAC,CAAC,OAAOO,KAAK,EAAE;IACZD,OAAO,CAACC,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAC3C,MAAMA,KAAK;EACf;AACJ,CAAC;AAGD,OAAO,MAAM0D,YAAY,GAAG,MAAAA,CAAA,KAA4B;EACvD,IAAI;IACF,MAAMpE,QAAQ,GAAG,MAAMpB,eAAe,CAACqB,GAAG,CAAC,qBAAqB,CAAC;IACjE,OAAOD,QAAQ,CAACG,IAAI;EACtB,CAAC,CAAC,OAAOO,KAAK,EAAE;IACdD,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAC9C,MAAMA,KAAK;EACb;AACC,CAAC;AACD,OAAO,MAAM2D,WAAW,GAAG,MAAOC,OAAe,IAAoB;EACnE,IAAI;IACA,MAAMtE,QAAQ,GAAG,MAAMpB,eAAe,CAACwC,IAAI,CAAC,8BAA8BkD,OAAO,EAAE,CAAC;IACpF,IAAItE,QAAQ,CAACuE,MAAM,KAAK,GAAG,EAAE,CAC7B,CAAC,MAAM;MACH9D,OAAO,CAACC,KAAK,CAAC,wBAAwB,CAAC;IAC3C;EACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;IACZ,MAAMA,KAAK;EACf;AACJ,CAAC;AAGD,OAAO,MAAM8D,kBAAkB,GAAG,MAAAA,CACjC3D,IAAY,EACZC,GAAW,EACXK,aAAqB,EACrBF,OAAc,EACdwD,YAAoB,KAEE;EACtB,IAAI;IACF,MAAMzE,QAAQ,GAAG,MAAMpB,eAAe,CAACwC,IAAI,CAAC,yBAAyB,EAAE;MACxEP,IAAI;MACJC,GAAG;MACHK,aAAa;MACbF,OAAO;MACPwD;IACC,CAAC,CAAC;IACF,MAAMvE,OAAO,GAAGF,QAAQ,CAACG,IAAI,CAACkB,OAAO;IACrC,IAAIjB,KAAK,CAACC,OAAO,CAACH,OAAO,CAAC,EAAE;MAC7B,OAAOA,OAAO;IACb,CAAC,MAAM;MACRO,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAER,OAAO,CAAC;MACvD,OAAO,EAAE;IACR;EACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;IACdD,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,OAAO,EAAE;EACX;AACC,CAAC;AAACgE,GAAA,GA3BSF,kBAAkB;AA6B7B,OAAO,MAAMG,iBAAiB,GAAG,MAClCC,EAAM,IACF;EACJ,IAAI;IACH,MAAM5E,QAAQ,GAAG,MAAMpB,eAAe,CAACqB,GAAG,CAAC,qCAAqC2E,EAAE,EAAE,CAAC;IACrF,MAAMhD,YAAY,GAAG5B,QAAQ,CAACG,IAAI;IAClC,IAAI,CAACyB,YAAY,EAAE;MAClB,MAAM,IAAImB,KAAK,CAAC,6BAA6B,CAAC;IAC/C;IACA,OAAOnB,YAAY;EAEpB,CAAC,CAAC,OAAOlB,KAAK,EAAE;IACfD,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;EACtD;AACD,CAAC;AAMD,OAAO,MAAMmE,mBAAmB,GAAE,MAAAA,CACjChE,IAAY,EACZC,GAAW,EACXK,aAAqB,EACrBF,OAAc,EACdwD,YAAoB,KAEE;EACtB,IAAI;IACF,MAAMzE,QAAQ,GAAG,MAAMpB,eAAe,CAACwC,IAAI,CAAC,6BAA6B,EAAE;MAC5EP,IAAI;MACJC,GAAG;MACHK,aAAa;MACbF,OAAO;MACPwD;IACC,CAAC,CAAC;IACF,MAAMvE,OAAO,GAAGF,QAAQ,CAACG,IAAI,CAACkB,OAAO;IACrC,IAAIjB,KAAK,CAACC,OAAO,CAACH,OAAO,CAAC,EAAE;MAC7B,OAAOA,OAAO;IACb,CAAC,MAAM;MACRO,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAER,OAAO,CAAC;MACvD,OAAO,EAAE;IACR;EACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;IACdD,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,OAAO,EAAE;EACX;AACD,CAAC;AAACoE,GAAA,GA3BWD,mBAAmB;AA6BhC,OAAO,MAAME,sBAAsB,GAAG,MAAAA,CACrChF,UAAe,EACfsC,cAAmB,EACnBV,YAAiB,EACjBqD,2BAAgC,EAChCC,QAAa,EACbC,iBAAsB,EACtBC,gBAAoB,KAEhB;EACJ,IAAI;IACHpF,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMC,QAAQ,GAAG,MAAMpB,eAAe,CAAC2D,GAAG,CAAC,sCAAsCF,cAAc,CAACtD,SAAS,cAAcsD,cAAc,CAAC+C,SAAS,EAAE,CAAC;IAGlJ,MAAMxD,YAAY,GAAG5B,QAAQ,CAACG,IAAI;IAElC,IAAIyB,YAAY,CAACC,OAAO,EAAE;MACzBF,YAAY,CAAC9C,IAAI,CAACiD,CAAC,CAACF,YAAY,CAACG,cAAc,CAAC,EAAE,SAAS,CAAC;MAC5DiD,2BAA2B,CAAC,CAAC;MAC7BjF,UAAU,CAAC,KAAK,CAAC;MACjBoF,gBAAgB,CAAC,IAAI,CAAC;IAEvB,CAAC,MAAM;MACNpF,UAAU,CAAC,KAAK,CAAC;MACjB4B,YAAY,CAAC9C,IAAI,CAACiD,CAAC,CAACF,YAAY,CAACI,YAAY,CAAC,EAAE,OAAO,CAAC;MACxDiD,QAAQ,CAACrD,YAAY,CAACI,YAAY,CAAC;MACnCkD,iBAAiB,CAAC,KAAK,CAAC;IAEzB;EACD,CAAC,CAAC,OAAOxE,KAAU,EAAE;IACpBX,UAAU,CAAC,KAAK,CAAC;IACjB4B,YAAY,CAAC9C,IAAI,CAACiD,CAAC,CAACpB,KAAK,CAACuB,OAAO,CAAC,EAAE,OAAO,CAAC;IAC5CgD,QAAQ,CAACvE,KAAK,CAACuB,OAAO,CAAC;IACvBiD,iBAAiB,CAAEG,GAAO,IAAI,CAACA,GAAG,CAAC;EACpC,CAAC,SAAS,CAEV;AACD,CAAC;AAACC,GAAA,GAtCWP,sBAAsB;AAAA,IAAApE,EAAA,EAAAY,GAAA,EAAAW,GAAA,EAAAM,GAAA,EAAAS,GAAA,EAAAyB,GAAA,EAAAI,GAAA,EAAAQ,GAAA;AAAAC,YAAA,CAAA5E,EAAA;AAAA4E,YAAA,CAAAhE,GAAA;AAAAgE,YAAA,CAAArD,GAAA;AAAAqD,YAAA,CAAA/C,GAAA;AAAA+C,YAAA,CAAAtC,GAAA;AAAAsC,YAAA,CAAAb,GAAA;AAAAa,YAAA,CAAAT,GAAA;AAAAS,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}