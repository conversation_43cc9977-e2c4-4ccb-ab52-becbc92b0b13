{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptWeb\\\\src\\\\components\\\\announcements\\\\Announcements.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useContext } from 'react';\nimport { Container, Tabs, Tab, TextField, Typography, InputAdornment } from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport { IconButton, Tooltip } from '@mui/material';\nimport { getAllGuides, DeleteGuideByGuideId, GetGudeDetailsByGuideId } from '../../services/GuideService';\nimport { useSnackbar } from '../../SnackbarContext';\nimport CloneInteractionDialog from '../common/CloneGuidePopup';\nimport CreateNewPopup from '../common/CreateNewGuidePopup';\nimport ExtensionRequiredPopup from '../common/ExtensionRequiredPopup';\nimport SearchIcon from '@mui/icons-material/Search';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../auth/AuthProvider';\nimport { AccountContext } from '../account/AccountContext';\nimport { useTranslation } from 'react-i18next';\nimport Delete from \"../../assets/icons/delete.svg\";\nimport NoDataw from \"../../assets/icons/NoDataw.svg\";\nimport { formatDateTime } from '../common/TimeZoneConversion';\nimport ClearIcon from \"@mui/icons-material/Clear\";\nimport { Fileedit, clone } from \"../../assets/icons/icons\";\nimport SettingsOutlinedIcon from '@mui/icons-material/SettingsOutlined';\nimport { useExtension } from '../../ExtensionContext';\nimport { openGuideInBuilder } from '../../utils/openGuideInBuilder';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Announcements = () => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const [activeTab, setActiveTab] = useState(0);\n  const [announcements, setAnnouncements] = useState([]);\n  const [paginationModel, setPaginationModel] = useState({\n    page: 0,\n    pageSize: 15\n  });\n  const [totalCount, setTotalCount] = useState(0);\n  const {\n    openSnackbar\n  } = useSnackbar();\n  const [openDialog, setOpenDialog] = useState(false);\n  const [guideIdToDelete, setGuideIdToDelete] = useState(null);\n  const [GuidenametoDelete, setGuideNametoDelete] = useState('');\n  const [isCloneDialogOpen, setIsCloneDialogOpen] = useState(false);\n  const [cloneAnnouncementName, setCloneAnnouncementName] = useState(null);\n  const [showPopup, setShowPopup] = useState(false);\n  const [showExtensionRequiredPopup, setShowExtensionRequiredPopup] = useState(false);\n  const {\n    isExtensionInstalled,\n    checkExtensionStatus\n  } = useExtension();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [name, setName] = useState(\"Announcement\");\n  const userInfo = localStorage.getItem('userInfo');\n  // const parsedInfo = JSON.parse(userInfo ? userInfo : '');\n  // const userData = JSON.parse(parsedInfo.user);\n  const {\n    signOut,\n    userDetails\n  } = useAuth();\n  const organizationId = (userDetails === null || userDetails === void 0 ? void 0 : userDetails.OrganizationId) || \"\";\n  const handleSearch = () => {\n    fetchAnnouncements();\n  };\n  const navigate = useNavigate();\n  const {\n    accountId,\n    roles\n  } = useContext(AccountContext);\n  useEffect(() => {\n    if (searchQuery.trim() === '') {\n      fetchAnnouncements();\n    }\n  }, [searchQuery]);\n  const fetchAnnouncements = async () => {\n    const {\n      page,\n      pageSize\n    } = paginationModel;\n    const offset = page * pageSize;\n    const statusFilter = activeTab === 0 ? 'Active' : activeTab === 1 ? 'InActive' : 'Draft';\n    setAnnouncements([]);\n    const filters = [{\n      FieldName: 'GuideType',\n      ElementType: 'string',\n      Condition: 'equals',\n      Value: 'Announcement',\n      IsCustomField: false\n    }, {\n      FieldName: 'GuideStatus',\n      ElementType: 'string',\n      Condition: 'equals',\n      Value: statusFilter,\n      IsCustomField: false\n    }, {\n      FieldName: 'Name',\n      ElementType: 'string',\n      Condition: 'contains',\n      Value: searchQuery,\n      IsCustomField: false\n    }, {\n      FieldName: 'AccountId',\n      ElementType: 'string',\n      Condition: 'contains',\n      Value: accountId,\n      IsCustomField: false\n    }];\n    const orderByFields = '';\n    const data = await getAllGuides(offset, pageSize, filters, orderByFields);\n    if (data._count > 0) {\n      const rowsWithIds = data.results.map(item => ({\n        ...item,\n        id: item.GuideId\n      }));\n      setAnnouncements(rowsWithIds);\n      setTotalCount(data._count);\n    } else {\n      setAnnouncements([]);\n      setTotalCount(0);\n    }\n  };\n  useEffect(() => {\n    if (accountId) {\n      fetchAnnouncements();\n    }\n  }, [paginationModel, activeTab, accountId]);\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setPaginationModel(prev => ({\n      ...prev,\n      page: 0\n    }));\n  };\n  const handleDelete = async () => {\n    if (guideIdToDelete) {\n      try {\n        const response = await DeleteGuideByGuideId(guideIdToDelete);\n        if (response.Success) {\n          openSnackbar(`${GuidenametoDelete} ${translate(name)} ${translate(\"deleted Successfully\")}`, \"success\");\n          await fetchAnnouncements();\n        } else {\n          openSnackbar(translate(response.ErrorMessage), \"error\");\n        }\n      } catch (error) {}\n    }\n    setOpenDialog(false);\n    setGuideIdToDelete(null);\n    setGuideNametoDelete(\"\");\n  };\n  const handleDeleteConfirmation = guideId => {\n    setGuideIdToDelete(guideId);\n    setOpenDialog(true);\n  };\n  const handleCopyClick = announcement => {\n    setCloneAnnouncementName(announcement);\n    setIsCloneDialogOpen(true);\n  };\n  const handleCloneSuccess = async () => {\n    await fetchAnnouncements();\n  };\n  const openPopup = () => {\n    // Check extension status before showing popup\n    checkExtensionStatus();\n\n    // Now check if extension is installed after the status check\n    setTimeout(() => {\n      if (isExtensionInstalled) {\n        setShowPopup(true);\n      } else {\n        setShowExtensionRequiredPopup(true);\n      }\n    }, 100); // Small delay to ensure state is updated\n  };\n  const handleSettingsClick = async params => {\n    const guideId = params.GuideId;\n    const response = await GetGudeDetailsByGuideId(guideId);\n    if (response) {\n      navigate(`${guideId}/settings`, {\n        state: {\n          response\n        }\n      });\n    } else {\n      openSnackbar(translate(\"GuideId Was empty\"), \"error\");\n    }\n  };\n  const columns = [{\n    field: 'Name',\n    headerName: translate('Name'),\n    // width: 450,\n    hideable: false,\n    resizable: false\n  }, {\n    field: 'UpdatedDate',\n    headerName: translate('Last Edited'),\n    // width: 250,\n    hideable: false,\n    renderCell: params => /*#__PURE__*/_jsxDEV(\"span\", {\n      children: `${formatDateTime(params.row.UpdatedDate, 'dd-MM-yyyy')}`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 9\n    }, this),\n    resizable: false\n  }, {\n    field: 'actions',\n    headerName: translate('Actions'),\n    // width: 250,\n    hideable: false,\n    sortable: false,\n    renderCell: params => /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [\" \", [\"Account Admin\", \"Editor\"].some(role => roles.includes(role)) && /*#__PURE__*/_jsxDEV(Tooltip, {\n        arrow: true,\n        title: translate(\"Edit\"),\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => handleEditClick(params.row),\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: Fileedit,\n            alt: \"Edit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 11\n      }, this), [\"Account Admin\", \"Editor\"].some(role => roles.includes(role)) && /*#__PURE__*/_jsxDEV(Tooltip, {\n        arrow: true,\n        title: translate(\"Clone\"),\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => handleCopyClick(params.row),\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: clone,\n            alt: \"Clone\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        arrow: true,\n        title: translate(\"Settings\"),\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => handleSettingsClick(params.row),\n          children: /*#__PURE__*/_jsxDEV(SettingsOutlinedIcon, {\n            sx: {\n              color: \"#000\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 11\n      }, this), [\"Account Admin\", \"Editor\"].some(role => roles.includes(role)) && /*#__PURE__*/_jsxDEV(Tooltip, {\n        arrow: true,\n        title: translate(\"Delete\"),\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => {\n            handleDeleteConfirmation(params.row.GuideId);\n            setGuideNametoDelete(params.row.Name);\n          },\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: Delete,\n            alt: \"Delete\",\n            style: {\n              filter: \"brightness(0.5)\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 15\n      }, this)]\n    }, void 0, true),\n    resizable: false\n  }];\n  const handleClearSearch = () => {\n    setSearchQuery('');\n    fetchAnnouncements();\n  };\n  const getRowSpacing = React.useCallback(params => {\n    return {\n      top: params.isFirstVisible ? 0 : 5,\n      bottom: params.isLastVisible ? 0 : 5\n    };\n  }, []);\n  const changeTabToDraft = () => {\n    setActiveTab(3);\n  };\n  const handleEditClick = guide => {\n    checkExtensionStatus();\n    setTimeout(() => {\n      if (isExtensionInstalled) {\n        openGuideInBuilder(guide === null || guide === void 0 ? void 0 : guide.TargetUrl, guide === null || guide === void 0 ? void 0 : guide.GuideId, accountId);\n      } else {\n        setShowExtensionRequiredPopup(true);\n      }\n    }, 100);\n  };\n  const getNoRowsLabel = () => {\n    const tabLabels = [\"Active\", \"Inactive\", \"Draft\"];\n    const currentTabLabel = tabLabels[activeTab] || name;\n    return `${translate('No')} ${translate(currentTabLabel)} ${translate(name)}`;\n  };\n  const NoRowsOverlay = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      alignItems: 'center',\n      flexDirection: \"column\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n      src: NoDataw,\n      alt: \"nodataw\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 10\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      sx: {\n        fontWeight: \"600\"\n      },\n      children: getNoRowsLabel()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 293,\n    columnNumber: 9\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"xl\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-web\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-webcontent\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-head\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-title-sec\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-title\",\n              children: [\" \", translate('Announcements')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-right-part\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: openPopup,\n              className: \"qadpt-memberButton\",\n              disabled: ![\"Account Admin\", \"Editor\"].some(role => roles.includes(role)),\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fal fa-add-plus\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: translate('New Announcement')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 19\n              }, this), \"        \"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 8\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-tabs-container\",\n          children: [/*#__PURE__*/_jsxDEV(Tabs, {\n            value: activeTab,\n            onChange: handleTabChange,\n            children: [/*#__PURE__*/_jsxDEV(Tab, {\n              label: translate('Active')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 3\n            }, this), /*#__PURE__*/_jsxDEV(Tab, {\n              label: translate('InActive')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(Tab, {\n              label: translate('Draft')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 3\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 2\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-websearch-container\",\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              variant: \"outlined\",\n              placeholder: translate('Search Announcement'),\n              value: searchQuery,\n              onKeyDown: e => {\n                if (e.key === 'Enter') {\n                  handleSearch();\n                }\n              },\n              onChange: e => {\n                const newValue = e.target.value;\n                setSearchQuery(newValue);\n                if (newValue === \"\") {\n                  handleClearSearch();\n                }\n              },\n              className: \"qadpt-websearch\",\n              InputProps: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    \"aria-label\": \"search\",\n                    onClick: () => handleSearch(),\n                    onMouseDown: event => event.preventDefault(),\n                    children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 352,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 17\n                }, this),\n                endAdornment: searchQuery && /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"end\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    \"aria-label\": \"clear\",\n                    onClick: () => {\n                      setSearchQuery(\"\");\n                      handleClearSearch();\n                    },\n                    children: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 365,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 17\n                }, this)\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 5\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          // style={{ height: 400, width: '83%' }}\n          className: \"qadpt-webgrd qadpt-anngrd\",\n          children: /*#__PURE__*/_jsxDEV(DataGrid, {\n            rows: announcements,\n            columns: columns,\n            getRowId: row => row.GuideId,\n            getRowSpacing: getRowSpacing,\n            pagination: true,\n            paginationModel: paginationModel,\n            paginationMode: \"server\",\n            onPaginationModelChange: setPaginationModel,\n            rowCount: totalCount,\n            pageSizeOptions: [15, 25, 50, 100],\n            localeText: {\n              MuiTablePagination: {\n                labelRowsPerPage: translate('Records Per Page')\n              },\n              noRowsLabel: translate(getNoRowsLabel())\n            },\n            disableColumnMenu: true,\n            disableRowSelectionOnClick: true,\n            slots: {\n              noRowsOverlay: NoRowsOverlay // Using the 'slots' prop for NoRowsOverlay\n            },\n            rowHeight: 38\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 11\n        }, this), openDialog && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-modal-overlay\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-usrconfirm-popup qadpt-danger\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-icon\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  className: \"qadpt-svg\",\n                  children: /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fal fa-trash-alt\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 9\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 9\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 7\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 5\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-popup-title\",\n              children: translate('Delete Announcement')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-warning\",\n              children: [translate('The'), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"qadpt-delete-popup-bold\",\n                children: GuidenametoDelete\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 22\n              }, this), \" \", translate('cannot be restored once it is deleted.')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 1\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-buttons\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setOpenDialog(false),\n                className: \"qadpt-cancel-button\",\n                children: translate('Cancel')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 7\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleDelete,\n                className: \"qadpt-conform-button\",\n                children: translate('Delete')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 7\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 5\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 3\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 6\n        }, this), isCloneDialogOpen && cloneAnnouncementName && /*#__PURE__*/_jsxDEV(CloneInteractionDialog, {\n          open: isCloneDialogOpen,\n          handleClose: () => setIsCloneDialogOpen(false),\n          initialName: cloneAnnouncementName,\n          onCloneSuccess: handleCloneSuccess,\n          name: name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 9\n        }, this), showPopup ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: \"fixed\",\n              top: 0,\n              left: 0,\n              width: \"100vw\",\n              height: \"100vh\",\n              backgroundColor: \"rgba(0, 0, 0, 0.5)\",\n              zIndex: 999\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: \"fixed\",\n              top: \"50%\",\n              left: \"50%\",\n              transform: \"translate(-50%, -50%)\",\n              zIndex: 1000,\n              backgroundColor: \"white\",\n              // padding: \"20px\",\n              borderRadius: \"8px\",\n              boxShadow: \"0px 4px 12px rgba(0, 0, 0, 0.1)\"\n            },\n            children: /*#__PURE__*/_jsxDEV(CreateNewPopup, {\n              setTourslist: setAnnouncements,\n              setShowPopup: setShowPopup,\n              showPopup: showPopup,\n              setTotalcount: setTotalCount,\n              setActiveTab: setActiveTab,\n              activeTab: activeTab,\n              changeTab: changeTabToDraft,\n              name: name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true) : \"\", showExtensionRequiredPopup && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'fixed',\n              top: 0,\n              left: 0,\n              width: '100vw',\n              height: '100vh',\n              backgroundColor: 'rgba(0,0,0,0.5)',\n              zIndex: 999\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'fixed',\n              top: '50%',\n              left: '50%',\n              transform: 'translate(-50%, -50%)',\n              zIndex: 1000,\n              backgroundColor: 'white',\n              borderRadius: '8px',\n              boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)'\n            },\n            children: /*#__PURE__*/_jsxDEV(ExtensionRequiredPopup, {\n              setShowPopup: setShowExtensionRequiredPopup,\n              showPopup: showExtensionRequiredPopup,\n              name: name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 303,\n    columnNumber: 5\n  }, this);\n};\n_s(Announcements, \"GiJxsAzkhtU1YOc9/70aWx6tYAM=\", false, function () {\n  return [useTranslation, useSnackbar, useExtension, useAuth, useNavigate];\n});\n_c = Announcements;\nexport default Announcements;\nvar _c;\n$RefreshReg$(_c, \"Announcements\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useContext", "Container", "Tabs", "Tab", "TextField", "Typography", "InputAdornment", "DataGrid", "IconButton", "<PERSON><PERSON><PERSON>", "getAllGuides", "DeleteGuideByGuideId", "GetGudeDetailsByGuideId", "useSnackbar", "CloneInteractionDialog", "CreateNewPopup", "ExtensionRequiredPopup", "SearchIcon", "useNavigate", "useAuth", "AccountContext", "useTranslation", "Delete", "NoDataw", "formatDateTime", "ClearIcon", "Fileedit", "clone", "SettingsOutlinedIcon", "useExtension", "openGuideInBuilder", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Announcements", "_s", "t", "translate", "activeTab", "setActiveTab", "announcements", "setAnnouncements", "paginationModel", "setPaginationModel", "page", "pageSize", "totalCount", "setTotalCount", "openSnackbar", "openDialog", "setOpenDialog", "guideIdToDelete", "setGuideIdToDelete", "GuidenametoDelete", "setGuideNametoDelete", "isCloneDialogOpen", "setIsCloneDialogOpen", "cloneAnnouncementName", "setCloneAnnouncementName", "showPopup", "setShowPopup", "showExtensionRequiredPopup", "setShowExtensionRequiredPopup", "isExtensionInstalled", "checkExtensionStatus", "searchQuery", "setSearch<PERSON>uery", "name", "setName", "userInfo", "localStorage", "getItem", "signOut", "userDetails", "organizationId", "OrganizationId", "handleSearch", "fetchAnnouncements", "navigate", "accountId", "roles", "trim", "offset", "statusFilter", "filters", "FieldName", "ElementType", "Condition", "Value", "IsCustomField", "order<PERSON><PERSON><PERSON><PERSON>s", "data", "_count", "rowsWithIds", "results", "map", "item", "id", "GuideId", "handleTabChange", "event", "newValue", "prev", "handleDelete", "response", "Success", "ErrorMessage", "error", "handleDeleteConfirmation", "guideId", "handleCopyClick", "announcement", "handleCloneSuccess", "openPopup", "setTimeout", "handleSettingsClick", "params", "state", "columns", "field", "headerName", "hideable", "resizable", "renderCell", "children", "row", "UpdatedDate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sortable", "some", "role", "includes", "arrow", "title", "onClick", "handleEditClick", "src", "alt", "sx", "color", "Name", "style", "filter", "handleClearSearch", "getRowSpacing", "useCallback", "top", "isFirstVisible", "bottom", "isLastVisible", "changeTabToDraft", "guide", "TargetUrl", "getNoRowsLabel", "tabLabels", "currentTabLabel", "NoRowsOverlay", "display", "alignItems", "flexDirection", "fontWeight", "max<PERSON><PERSON><PERSON>", "className", "disabled", "value", "onChange", "label", "variant", "placeholder", "onKeyDown", "e", "key", "target", "InputProps", "startAdornment", "position", "onMouseDown", "preventDefault", "endAdornment", "rows", "getRowId", "pagination", "paginationMode", "onPaginationModelChange", "rowCount", "pageSizeOptions", "localeText", "MuiTablePagination", "labelRowsPerPage", "noRowsLabel", "disableColumnMenu", "disableRowSelectionOnClick", "slots", "noRowsOverlay", "rowHeight", "open", "handleClose", "initialName", "onCloneSuccess", "left", "width", "height", "backgroundColor", "zIndex", "transform", "borderRadius", "boxShadow", "setTourslist", "setTotalcount", "changeTab", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/components/announcements/Announcements.tsx"], "sourcesContent": ["import React, { useEffect, useState ,useContext} from 'react';\r\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>b, Button, Dialog, DialogActions, DialogContent, DialogTitle, TextField, Typography, DialogContentText, InputAdornment } from '@mui/material';\r\nimport { DataGrid, GridColDef, GridRenderCellParams, GridRowSpacingParams } from '@mui/x-data-grid';\r\nimport { IconButton, Tooltip } from '@mui/material';\r\nimport EditOutlinedIcon from '@mui/icons-material/EditOutlined';\r\nimport CopyAllOutlinedIcon from '@mui/icons-material/CopyAllOutlined';\r\nimport DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined';\r\nimport { getAllGuides,DeleteGuideByGuideId,GetGudeDetailsByGuideId } from '../../services/GuideService';\r\nimport settingsiconAnnouncements from \"../../assets/icons/SettingsiconAnnouncements.svg\";\r\nimport { useSnackbar } from '../../SnackbarContext';\r\nimport CloneInteractionDialog from '../common/CloneGuidePopup';\r\nimport CreateNewPopup from '../common/CreateNewGuidePopup';\r\nimport ExtensionRequiredPopup from '../common/ExtensionRequiredPopup';\r\nimport SearchIcon from '@mui/icons-material/Search';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { useAuth } from '../auth/AuthProvider';\r\nimport { AccountContext } from '../account/AccountContext';\r\nimport { useTranslation } from 'react-i18next';\r\nimport Delete from \"../../assets/icons/delete.svg\";\r\nimport NoDataw from \"../../assets/icons/NoDataw.svg\";\r\nimport { formatDateTime } from '../common/TimeZoneConversion';\r\nimport ClearIcon from \"@mui/icons-material/Clear\";\r\nimport { Settings,Fileedit,clone } from \"../../assets/icons/icons\";\r\nimport SettingsOutlinedIcon from '@mui/icons-material/SettingsOutlined';\r\nimport { useExtension } from '../../ExtensionContext';\r\nimport { openGuideInBuilder } from '../../utils/openGuideInBuilder';\r\n\r\ninterface Announcement {\r\n  AccountId: string;\r\n  Content: string;\r\n  CreatedBy: string;\r\n  CreatedDate: string;\r\n  Frequency: string;\r\n  GuideId: string;\r\n  GuideStatus: string;\r\n  GuideType: string;\r\n  Name: string;\r\n  OrganizationId: string;\r\n  Segment: string;\r\n  TargetUrl: string;\r\n  TemplateId: string;\r\n  UpdatedBy: string;\r\n  UpdatedDate: string;\r\n}\r\n\r\nconst Announcements = () => {\r\n  const { t: translate } = useTranslation();\r\n  const [activeTab, setActiveTab] = useState(0);\r\n  const [announcements, setAnnouncements] = useState<Announcement[]>([]);\r\n  const [paginationModel, setPaginationModel] = useState({\r\n    page: 0,\r\n    pageSize: 15,\r\n  });\r\n  const [totalCount, setTotalCount] = useState(0);\r\n  const { openSnackbar } = useSnackbar();\r\n  const [openDialog, setOpenDialog] = useState(false);\r\n  const [guideIdToDelete, setGuideIdToDelete] = useState<string | null>(null);\r\n  const [GuidenametoDelete, setGuideNametoDelete] = useState('');\r\n  const [isCloneDialogOpen, setIsCloneDialogOpen] = useState(false);\r\n  const [cloneAnnouncementName, setCloneAnnouncementName] = useState<Announcement | null>(null);\r\n  const [showPopup, setShowPopup] = useState(false);\r\n  const [showExtensionRequiredPopup, setShowExtensionRequiredPopup] = useState(false);\r\n  const { isExtensionInstalled, checkExtensionStatus } = useExtension();\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [name, setName] = useState(\"Announcement\");\r\n  const userInfo = localStorage.getItem('userInfo');\r\n  // const parsedInfo = JSON.parse(userInfo ? userInfo : '');\r\n  // const userData = JSON.parse(parsedInfo.user);\r\n  const { signOut, userDetails } = useAuth();\r\n  const organizationId = userDetails?.OrganizationId ||\"\";\r\n  const handleSearch = () => {\r\n    fetchAnnouncements();\r\n  };\r\n  const navigate = useNavigate();\r\n  const { accountId,roles } = useContext(AccountContext);\r\n\r\nuseEffect(() => {\r\n  if (searchQuery.trim() === '') {\r\n    fetchAnnouncements();\r\n  }\r\n}, [searchQuery]);\r\n  const fetchAnnouncements = async () => {\r\n    const { page, pageSize } = paginationModel;\r\n    const offset = page * pageSize;\r\n    const statusFilter = activeTab === 0 ? 'Active' : activeTab === 1 ? 'InActive' : 'Draft';\r\n\r\n    setAnnouncements([]);\r\n\r\n    const filters = [\r\n      {\r\n        FieldName: 'GuideType',\r\n        ElementType: 'string',\r\n        Condition: 'equals',\r\n        Value: 'Announcement',\r\n        IsCustomField: false,\r\n      },\r\n      {\r\n        FieldName: 'GuideStatus',\r\n        ElementType: 'string',\r\n        Condition: 'equals',\r\n        Value: statusFilter,\r\n        IsCustomField: false,\r\n      },\r\n      {\r\n        FieldName: 'Name',\r\n        ElementType: 'string',\r\n        Condition: 'contains',\r\n        Value: searchQuery,\r\n        IsCustomField: false,\r\n      },\r\n      {\r\n        FieldName: 'AccountId',\r\n        ElementType: 'string',\r\n        Condition: 'contains',\r\n        Value: accountId,\r\n        IsCustomField: false,\r\n      },\r\n    ];\r\n\r\n    const orderByFields = '';\r\n    const data = await getAllGuides(offset, pageSize, filters, orderByFields);\r\n\r\n    if (data._count > 0) {\r\n      const rowsWithIds = data.results.map((item: any) => ({\r\n        ...item,\r\n        id: item.GuideId\r\n      }));\r\n\r\n      setAnnouncements(rowsWithIds);\r\n      setTotalCount(data._count);\r\n    } else {\r\n      setAnnouncements([]);\r\n      setTotalCount(0);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (accountId) {\r\n      fetchAnnouncements();\r\n    }\r\n  }, [paginationModel, activeTab,accountId]);\r\n\r\n  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {\r\n    setActiveTab(newValue);\r\n    setPaginationModel((prev) => ({ ...prev, page: 0 }));\r\n  };\r\n\r\n  const handleDelete = async () => {\r\n    if (guideIdToDelete) {\r\n      try {\r\n        const response = await DeleteGuideByGuideId(guideIdToDelete);\r\n        if (response.Success) {\r\n          openSnackbar(`${GuidenametoDelete} ${translate(name)} ${translate(\"deleted Successfully\")}`, \"success\");\r\n          await fetchAnnouncements();\r\n        } else {\r\n          openSnackbar(translate(response.ErrorMessage), \"error\");\r\n        }\r\n      } catch (error) {\r\n      }\r\n    }\r\n    setOpenDialog(false);\r\n    setGuideIdToDelete(null);\r\n    setGuideNametoDelete(\"\");\r\n  };\r\n  const handleDeleteConfirmation = (guideId: string) => {\r\n    setGuideIdToDelete(guideId);\r\n    setOpenDialog(true);\r\n  };\r\n  const handleCopyClick = (announcement: Announcement) => {\r\n    setCloneAnnouncementName(announcement);\r\n    setIsCloneDialogOpen(true);\r\n  };\r\n  const handleCloneSuccess = async () => {\r\n    await fetchAnnouncements();\r\n  };\r\n  const openPopup = () => {\r\n    // Check extension status before showing popup\r\n    checkExtensionStatus();\r\n\r\n    // Now check if extension is installed after the status check\r\n    setTimeout(() => {\r\n      if (isExtensionInstalled) {\r\n        setShowPopup(true);\r\n      } else {\r\n        setShowExtensionRequiredPopup(true);\r\n      }\r\n    }, 100); // Small delay to ensure state is updated\r\n  };\r\n  const handleSettingsClick = async (params: any) => {\r\n    const guideId = params.GuideId;\r\n    const response = await GetGudeDetailsByGuideId(guideId);\r\n    if (response) {\r\n      navigate(`${guideId}/settings`, { state: { response } });\r\n    } else {\r\n      openSnackbar(translate(\"GuideId Was empty\"), \"error\");\r\n    }\r\n  };\r\n  const columns: GridColDef[] = [\r\n    {\r\n      field: 'Name',\r\n      headerName: translate('Name'),\r\n      // width: 450,\r\n      hideable: false,\r\n      resizable:false\r\n    },\r\n    {\r\n      field: 'UpdatedDate',\r\n      headerName: translate('Last Edited'),\r\n      // width: 250,\r\n      hideable: false,\r\n      renderCell: (params) => (\r\n        <span>\r\n        {`${formatDateTime(params.row.UpdatedDate, 'dd-MM-yyyy')}`}\r\n        </span>\r\n      ),\r\n     resizable:false\r\n    },\r\n    {\r\n      field: 'actions',\r\n      headerName: translate('Actions'),\r\n      // width: 250,\r\n      hideable: false,\r\n      sortable: false,\r\n      renderCell: (params: GridRenderCellParams) => (\r\n        <> {[\"Account Admin\",\"Editor\"].some(role => roles.includes(role)) && \r\n          <Tooltip arrow title={translate(\"Edit\")}> \r\n          <IconButton onClick={() => handleEditClick(params.row)}>\r\n          <img src={Fileedit} alt=\"Edit\"/>\r\n        </IconButton>\r\n          </Tooltip>}\r\n          {\r\n            [\"Account Admin\",\"Editor\"].some(role => roles.includes(role)) &&\r\n            <Tooltip arrow title={translate(\"Clone\")}>\r\n              <IconButton onClick={() => handleCopyClick(params.row)}>\r\n                <img src={clone} alt=\"Clone\" />\r\n              </IconButton>\r\n            </Tooltip>\r\n          }\r\n          <Tooltip arrow title={translate(\"Settings\")}> \r\n            <IconButton onClick={() => handleSettingsClick(params.row)}>\r\n            <SettingsOutlinedIcon sx={{color:\"#000\"}} />\r\n            </IconButton>\r\n          </Tooltip>\r\n          {\r\n            [\"Account Admin\",\"Editor\"].some(role => roles.includes(role)) &&\r\n              <Tooltip arrow title={translate(\"Delete\")}>\r\n                <IconButton onClick={() => {\r\n                  handleDeleteConfirmation(params.row.GuideId);\r\n                  setGuideNametoDelete(params.row.Name);\r\n                }}>\r\n                  <img src={Delete} alt=\"Delete\" style={{ filter: \"brightness(0.5)\" }} />\r\n\r\n                </IconButton>\r\n              </Tooltip>\r\n          }\r\n        </>\r\n      ),\r\n      resizable:false\r\n    },\r\n  ];\r\n  const handleClearSearch = () => {\r\n    setSearchQuery('');\r\n    fetchAnnouncements();\r\n  };\r\n\r\n\r\n  const getRowSpacing = React.useCallback((params: GridRowSpacingParams) => {\r\n    return {\r\n      top: params.isFirstVisible ? 0 : 5,\r\n      bottom: params.isLastVisible ? 0 : 5,\r\n    };\r\n  }, []);\r\n\r\n  const changeTabToDraft = () => {\r\n    setActiveTab(3);\r\n  };\r\n  const handleEditClick = (guide: Announcement) => {\r\n    checkExtensionStatus();\r\n    setTimeout(() => {\r\n      if (isExtensionInstalled) {\r\n        openGuideInBuilder(guide?.TargetUrl, guide?.GuideId, accountId);\r\n      } else {\r\n        setShowExtensionRequiredPopup(true);\r\n      }\r\n    }, 100);\r\n  };\r\n  const getNoRowsLabel = () => {\r\n\t\tconst tabLabels = [\"Active\", \"Inactive\", \"Draft\"];\r\n\t\tconst currentTabLabel = tabLabels[activeTab] || name;\r\n    return `${translate('No')} ${translate(currentTabLabel)} ${translate(name)}`;\r\n\t};\r\n\tconst NoRowsOverlay = () => (\r\n        <div style={{ display: 'flex', alignItems: 'center', flexDirection:\"column\" }}>\r\n         <img\r\n\t\t\t\t\t\t\t\t\t\t\tsrc={NoDataw}\r\n\t\t\t\t\t\t\t\t\t\t\talt=\"nodataw\"\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t<Typography sx={{fontWeight:\"600\"}}>{getNoRowsLabel()}</Typography>\r\n\r\n        </div>\r\n      );\r\n  return (\r\n    <Container maxWidth=\"xl\">\r\n      <div className='qadpt-web'>\r\n        <div className='qadpt-webcontent'>\r\n      <div className=\"qadpt-head\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-title-sec\">\r\n              <div className=\"qadpt-title\"> {translate('Announcements')}</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-right-part\">\r\n\t\t\t\t\t\t\t\t<button\r\n                onClick={openPopup}\r\n                className=\"qadpt-memberButton\"\r\n                disabled={ ![\"Account Admin\",\"Editor\"].some(role => roles.includes(role)) }\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<i className=\"fal fa-add-plus\"></i>\r\n                  <span>{translate('New Announcement')}</span>\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t</div>\r\n          </div>\r\n          <div className=\"qadpt-tabs-container\">\r\n\t<Tabs value={activeTab} onChange={handleTabChange}>\r\n  <Tab label={translate('Active')} />\r\n        <Tab label={translate('InActive')} />\r\n\t\t<Tab label={translate('Draft')} />\r\n            </Tabs>\r\n            <div className=\"qadpt-websearch-container\">\r\n    <TextField\r\n        variant=\"outlined\"\r\n        placeholder={translate('Search Announcement')}\r\n              value={searchQuery}\r\n              onKeyDown={(e) => {\r\n                if (e.key === 'Enter') {\r\n                  handleSearch();\r\n                }\r\n                }}\r\n        onChange={(e) => {\r\n            const newValue = e.target.value;\r\n            setSearchQuery(newValue);\r\n            if (newValue === \"\") {\r\n                handleClearSearch();\r\n            }\r\n        }}\r\n        className=\"qadpt-websearch\"\r\n        InputProps={{\r\n            startAdornment: (\r\n                <InputAdornment position=\"start\">\r\n                    <IconButton\r\n                        aria-label=\"search\"\r\n                        onClick={() => handleSearch()}\r\n                        onMouseDown={(event) => event.preventDefault()}\r\n                    >\r\n                        <SearchIcon />\r\n                    </IconButton>\r\n                </InputAdornment>\r\n            ),\r\n            endAdornment: searchQuery && (\r\n                <InputAdornment position=\"end\">\r\n                    <IconButton\r\n                        aria-label=\"clear\"\r\n                        onClick={() => {\r\n                            setSearchQuery(\"\");\r\n                            handleClearSearch();\r\n                        }}\r\n                    >\r\n                        <ClearIcon />\r\n                    </IconButton>\r\n                </InputAdornment>\r\n            ),\r\n        }}\r\n    />\r\n</div>\r\n          </div>\r\n\r\n\r\n\r\n\r\n          <div\r\n            // style={{ height: 400, width: '83%' }}\r\n            className='qadpt-webgrd qadpt-anngrd'\r\n          >\r\n        <DataGrid\r\n          rows={announcements}\r\n          columns={columns}\r\n          getRowId={(row) => row.GuideId}\r\n          getRowSpacing={getRowSpacing}\r\n          pagination\r\n          paginationModel={paginationModel}\r\n          paginationMode=\"server\"\r\n          onPaginationModelChange={setPaginationModel}\r\n          rowCount={totalCount}\r\n          pageSizeOptions={[15, 25, 50, 100]}\r\n          localeText={{\r\n            MuiTablePagination: {\r\n              labelRowsPerPage: translate('Records Per Page'),\r\n            },\r\n            noRowsLabel: translate(getNoRowsLabel()),\r\n          }}\r\n          disableColumnMenu\r\n              disableRowSelectionOnClick\r\n              slots={{\r\n                noRowsOverlay: NoRowsOverlay, // Using the 'slots' prop for NoRowsOverlay\r\n              }}\r\n              rowHeight ={38}\r\n        />\r\n          </div>\r\n          {openDialog && (\r\n     <div className=\"qadpt-modal-overlay\">\r\n  <div className=\"qadpt-usrconfirm-popup qadpt-danger\">\r\n    <div>\r\n      <div className=\"qadpt-icon\">\r\n        <IconButton\r\n\t\t\t\t\t\t\tclassName=\"qadpt-svg\">\r\n\t\t\t\t\t\t\t\t<i className='fal fa-trash-alt'></i>\r\n\t\t\t\t\t\t\t\t</IconButton>\r\n      </div>\r\n                </div>\r\n                <div className=\"qadpt-popup-title\">\r\n  {translate('Delete Announcement')}\r\n</div>\r\n<div className=\"qadpt-warning\">\r\n  {translate('The')} <span className=\"qadpt-delete-popup-bold\">{GuidenametoDelete}</span> {translate('cannot be restored once it is deleted.')}\r\n</div>\r\n\r\n    <div className=\"qadpt-buttons\">\r\n      <button\r\n        onClick={() => setOpenDialog(false)}\r\n        className=\"qadpt-cancel-button\"\r\n      >\r\n        {translate('Cancel')}\r\n      </button>\r\n      <button\r\n        onClick={handleDelete}\r\n        className=\"qadpt-conform-button\"\r\n      >\r\n        {translate('Delete')}\r\n      </button>\r\n    </div>\r\n              </div>\r\n              </div>\r\n)}\r\n\r\n\r\n      {isCloneDialogOpen && cloneAnnouncementName && (\r\n        <CloneInteractionDialog\r\n          open={isCloneDialogOpen}\r\n          handleClose={() => setIsCloneDialogOpen(false)}\r\n          initialName={cloneAnnouncementName}\r\n              onCloneSuccess={handleCloneSuccess}\r\n              name={name}\r\n        />\r\n      )}\r\n          {showPopup ? (\r\n            <>\r\n            <div\r\n            style={{\r\n              position: \"fixed\",\r\n              top: 0,\r\n              left: 0,\r\n              width: \"100vw\",\r\n              height: \"100vh\",\r\n              backgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n              zIndex: 999,\r\n            }}\r\n          />\r\n\r\n          <div\r\n            style={{\r\n              position: \"fixed\",\r\n              top: \"50%\",\r\n              left: \"50%\",\r\n              transform: \"translate(-50%, -50%)\",\r\n              zIndex: 1000,\r\n              backgroundColor: \"white\",\r\n              // padding: \"20px\",\r\n              borderRadius: \"8px\",\r\n              boxShadow: \"0px 4px 12px rgba(0, 0, 0, 0.1)\",\r\n            }}\r\n          >\r\n        <CreateNewPopup\r\n          setTourslist={setAnnouncements}\r\n          setShowPopup={setShowPopup}\r\n          showPopup={showPopup}\r\n          setTotalcount={setTotalCount}\r\n          setActiveTab={setActiveTab}\r\n          activeTab={activeTab}\r\n          changeTab={changeTabToDraft}\r\n          name={name}\r\n        />\r\n                </div>\r\n            </>\r\n      ) : (\r\n        \"\"\r\n      )}\r\n\r\n      {showExtensionRequiredPopup && (\r\n        <>\r\n          <div style={{ position: 'fixed', top: 0, left: 0, width: '100vw', height: '100vh', backgroundColor: 'rgba(0,0,0,0.5)', zIndex: 999 }} />\r\n          <div style={{ position: 'fixed', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', zIndex: 1000, backgroundColor: 'white', borderRadius: '8px', boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)' }}>\r\n            <ExtensionRequiredPopup\r\n              setShowPopup={setShowExtensionRequiredPopup}\r\n              showPopup={showExtensionRequiredPopup}\r\n              name={name}\r\n            />\r\n          </div>\r\n        </>\r\n      )}\r\n        </div>\r\n        </div>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default Announcements;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,QAAO,OAAO;AAC7D,SAASC,SAAS,EAAEC,IAAI,EAAEC,GAAG,EAA6DC,SAAS,EAAEC,UAAU,EAAqBC,cAAc,QAAQ,eAAe;AACzK,SAASC,QAAQ,QAAgE,kBAAkB;AACnG,SAASC,UAAU,EAAEC,OAAO,QAAQ,eAAe;AAInD,SAASC,YAAY,EAACC,oBAAoB,EAACC,uBAAuB,QAAQ,6BAA6B;AAEvG,SAASC,WAAW,QAAQ,uBAAuB;AACnD,OAAOC,sBAAsB,MAAM,2BAA2B;AAC9D,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,sBAAsB,MAAM,kCAAkC;AACrE,OAAOC,UAAU,MAAM,4BAA4B;AACnD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,MAAM,MAAM,+BAA+B;AAClD,OAAOC,OAAO,MAAM,gCAAgC;AACpD,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,OAAOC,SAAS,MAAM,2BAA2B;AACjD,SAAkBC,QAAQ,EAACC,KAAK,QAAQ,0BAA0B;AAClE,OAAOC,oBAAoB,MAAM,sCAAsC;AACvE,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,kBAAkB,QAAQ,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAoBpE,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGjB,cAAc,CAAC,CAAC;EACzC,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGzC,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAiB,EAAE,CAAC;EACtE,MAAM,CAAC4C,eAAe,EAAEC,kBAAkB,CAAC,GAAG7C,QAAQ,CAAC;IACrD8C,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM;IAAEkD;EAAa,CAAC,GAAGpC,WAAW,CAAC,CAAC;EACtC,MAAM,CAACqC,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACqD,eAAe,EAAEC,kBAAkB,CAAC,GAAGtD,QAAQ,CAAgB,IAAI,CAAC;EAC3E,MAAM,CAACuD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACyD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC2D,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG5D,QAAQ,CAAsB,IAAI,CAAC;EAC7F,MAAM,CAAC6D,SAAS,EAAEC,YAAY,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+D,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM;IAAEiE,oBAAoB;IAAEC;EAAqB,CAAC,GAAGpC,YAAY,CAAC,CAAC;EACrE,MAAM,CAACqC,WAAW,EAAEC,cAAc,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqE,IAAI,EAAEC,OAAO,CAAC,GAAGtE,QAAQ,CAAC,cAAc,CAAC;EAChD,MAAMuE,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;EACjD;EACA;EACA,MAAM;IAAEC,OAAO;IAAEC;EAAY,CAAC,GAAGvD,OAAO,CAAC,CAAC;EAC1C,MAAMwD,cAAc,GAAG,CAAAD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEE,cAAc,KAAG,EAAE;EACvD,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBC,kBAAkB,CAAC,CAAC;EACtB,CAAC;EACD,MAAMC,QAAQ,GAAG7D,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE8D,SAAS;IAACC;EAAM,CAAC,GAAGjF,UAAU,CAACoB,cAAc,CAAC;EAExDtB,SAAS,CAAC,MAAM;IACd,IAAIoE,WAAW,CAACgB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC7BJ,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAACZ,WAAW,CAAC,CAAC;EACf,MAAMY,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,MAAM;MAAEjC,IAAI;MAAEC;IAAS,CAAC,GAAGH,eAAe;IAC1C,MAAMwC,MAAM,GAAGtC,IAAI,GAAGC,QAAQ;IAC9B,MAAMsC,YAAY,GAAG7C,SAAS,KAAK,CAAC,GAAG,QAAQ,GAAGA,SAAS,KAAK,CAAC,GAAG,UAAU,GAAG,OAAO;IAExFG,gBAAgB,CAAC,EAAE,CAAC;IAEpB,MAAM2C,OAAO,GAAG,CACd;MACEC,SAAS,EAAE,WAAW;MACtBC,WAAW,EAAE,QAAQ;MACrBC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,cAAc;MACrBC,aAAa,EAAE;IACjB,CAAC,EACD;MACEJ,SAAS,EAAE,aAAa;MACxBC,WAAW,EAAE,QAAQ;MACrBC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAEL,YAAY;MACnBM,aAAa,EAAE;IACjB,CAAC,EACD;MACEJ,SAAS,EAAE,MAAM;MACjBC,WAAW,EAAE,QAAQ;MACrBC,SAAS,EAAE,UAAU;MACrBC,KAAK,EAAEvB,WAAW;MAClBwB,aAAa,EAAE;IACjB,CAAC,EACD;MACEJ,SAAS,EAAE,WAAW;MACtBC,WAAW,EAAE,QAAQ;MACrBC,SAAS,EAAE,UAAU;MACrBC,KAAK,EAAET,SAAS;MAChBU,aAAa,EAAE;IACjB,CAAC,CACF;IAED,MAAMC,aAAa,GAAG,EAAE;IACxB,MAAMC,IAAI,GAAG,MAAMlF,YAAY,CAACyE,MAAM,EAAErC,QAAQ,EAAEuC,OAAO,EAAEM,aAAa,CAAC;IAEzE,IAAIC,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;MACnB,MAAMC,WAAW,GAAGF,IAAI,CAACG,OAAO,CAACC,GAAG,CAAEC,IAAS,KAAM;QACnD,GAAGA,IAAI;QACPC,EAAE,EAAED,IAAI,CAACE;MACX,CAAC,CAAC,CAAC;MAEHzD,gBAAgB,CAACoD,WAAW,CAAC;MAC7B9C,aAAa,CAAC4C,IAAI,CAACC,MAAM,CAAC;IAC5B,CAAC,MAAM;MACLnD,gBAAgB,CAAC,EAAE,CAAC;MACpBM,aAAa,CAAC,CAAC,CAAC;IAClB;EACF,CAAC;EAEDlD,SAAS,CAAC,MAAM;IACd,IAAIkF,SAAS,EAAE;MACbF,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAACnC,eAAe,EAAEJ,SAAS,EAACyC,SAAS,CAAC,CAAC;EAE1C,MAAMoB,eAAe,GAAGA,CAACC,KAA2B,EAAEC,QAAgB,KAAK;IACzE9D,YAAY,CAAC8D,QAAQ,CAAC;IACtB1D,kBAAkB,CAAE2D,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAE1D,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;EACtD,CAAC;EAED,MAAM2D,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAIpD,eAAe,EAAE;MACnB,IAAI;QACF,MAAMqD,QAAQ,GAAG,MAAM9F,oBAAoB,CAACyC,eAAe,CAAC;QAC5D,IAAIqD,QAAQ,CAACC,OAAO,EAAE;UACpBzD,YAAY,CAAC,GAAGK,iBAAiB,IAAIhB,SAAS,CAAC8B,IAAI,CAAC,IAAI9B,SAAS,CAAC,sBAAsB,CAAC,EAAE,EAAE,SAAS,CAAC;UACvG,MAAMwC,kBAAkB,CAAC,CAAC;QAC5B,CAAC,MAAM;UACL7B,YAAY,CAACX,SAAS,CAACmE,QAAQ,CAACE,YAAY,CAAC,EAAE,OAAO,CAAC;QACzD;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE,CAChB;IACF;IACAzD,aAAa,CAAC,KAAK,CAAC;IACpBE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,oBAAoB,CAAC,EAAE,CAAC;EAC1B,CAAC;EACD,MAAMsD,wBAAwB,GAAIC,OAAe,IAAK;IACpDzD,kBAAkB,CAACyD,OAAO,CAAC;IAC3B3D,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EACD,MAAM4D,eAAe,GAAIC,YAA0B,IAAK;IACtDrD,wBAAwB,CAACqD,YAAY,CAAC;IACtCvD,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EACD,MAAMwD,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,MAAMnC,kBAAkB,CAAC,CAAC;EAC5B,CAAC;EACD,MAAMoC,SAAS,GAAGA,CAAA,KAAM;IACtB;IACAjD,oBAAoB,CAAC,CAAC;;IAEtB;IACAkD,UAAU,CAAC,MAAM;MACf,IAAInD,oBAAoB,EAAE;QACxBH,YAAY,CAAC,IAAI,CAAC;MACpB,CAAC,MAAM;QACLE,6BAA6B,CAAC,IAAI,CAAC;MACrC;IACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACX,CAAC;EACD,MAAMqD,mBAAmB,GAAG,MAAOC,MAAW,IAAK;IACjD,MAAMP,OAAO,GAAGO,MAAM,CAAClB,OAAO;IAC9B,MAAMM,QAAQ,GAAG,MAAM7F,uBAAuB,CAACkG,OAAO,CAAC;IACvD,IAAIL,QAAQ,EAAE;MACZ1B,QAAQ,CAAC,GAAG+B,OAAO,WAAW,EAAE;QAAEQ,KAAK,EAAE;UAAEb;QAAS;MAAE,CAAC,CAAC;IAC1D,CAAC,MAAM;MACLxD,YAAY,CAACX,SAAS,CAAC,mBAAmB,CAAC,EAAE,OAAO,CAAC;IACvD;EACF,CAAC;EACD,MAAMiF,OAAqB,GAAG,CAC5B;IACEC,KAAK,EAAE,MAAM;IACbC,UAAU,EAAEnF,SAAS,CAAC,MAAM,CAAC;IAC7B;IACAoF,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAC;EACZ,CAAC,EACD;IACEH,KAAK,EAAE,aAAa;IACpBC,UAAU,EAAEnF,SAAS,CAAC,aAAa,CAAC;IACpC;IACAoF,QAAQ,EAAE,KAAK;IACfE,UAAU,EAAGP,MAAM,iBACjBrF,OAAA;MAAA6F,QAAA,EACC,GAAGrG,cAAc,CAAC6F,MAAM,CAACS,GAAG,CAACC,WAAW,EAAE,YAAY,CAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD,CACP;IACFR,SAAS,EAAC;EACX,CAAC,EACD;IACEH,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAEnF,SAAS,CAAC,SAAS,CAAC;IAChC;IACAoF,QAAQ,EAAE,KAAK;IACfU,QAAQ,EAAE,KAAK;IACfR,UAAU,EAAGP,MAA4B,iBACvCrF,OAAA,CAAAE,SAAA;MAAA2F,QAAA,GAAE,GAAC,EAAC,CAAC,eAAe,EAAC,QAAQ,CAAC,CAACQ,IAAI,CAACC,IAAI,IAAIrD,KAAK,CAACsD,QAAQ,CAACD,IAAI,CAAC,CAAC,iBAC/DtG,OAAA,CAACvB,OAAO;QAAC+H,KAAK;QAACC,KAAK,EAAEnG,SAAS,CAAC,MAAM,CAAE;QAAAuF,QAAA,eACxC7F,OAAA,CAACxB,UAAU;UAACkI,OAAO,EAAEA,CAAA,KAAMC,eAAe,CAACtB,MAAM,CAACS,GAAG,CAAE;UAAAD,QAAA,eACvD7F,OAAA;YAAK4G,GAAG,EAAElH,QAAS;YAACmH,GAAG,EAAC;UAAM;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAER,CAAC,eAAe,EAAC,QAAQ,CAAC,CAACE,IAAI,CAACC,IAAI,IAAIrD,KAAK,CAACsD,QAAQ,CAACD,IAAI,CAAC,CAAC,iBAC7DtG,OAAA,CAACvB,OAAO;QAAC+H,KAAK;QAACC,KAAK,EAAEnG,SAAS,CAAC,OAAO,CAAE;QAAAuF,QAAA,eACvC7F,OAAA,CAACxB,UAAU;UAACkI,OAAO,EAAEA,CAAA,KAAM3B,eAAe,CAACM,MAAM,CAACS,GAAG,CAAE;UAAAD,QAAA,eACrD7F,OAAA;YAAK4G,GAAG,EAAEjH,KAAM;YAACkH,GAAG,EAAC;UAAO;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEZnG,OAAA,CAACvB,OAAO;QAAC+H,KAAK;QAACC,KAAK,EAAEnG,SAAS,CAAC,UAAU,CAAE;QAAAuF,QAAA,eAC1C7F,OAAA,CAACxB,UAAU;UAACkI,OAAO,EAAEA,CAAA,KAAMtB,mBAAmB,CAACC,MAAM,CAACS,GAAG,CAAE;UAAAD,QAAA,eAC3D7F,OAAA,CAACJ,oBAAoB;YAACkH,EAAE,EAAE;cAACC,KAAK,EAAC;YAAM;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAER,CAAC,eAAe,EAAC,QAAQ,CAAC,CAACE,IAAI,CAACC,IAAI,IAAIrD,KAAK,CAACsD,QAAQ,CAACD,IAAI,CAAC,CAAC,iBAC3DtG,OAAA,CAACvB,OAAO;QAAC+H,KAAK;QAACC,KAAK,EAAEnG,SAAS,CAAC,QAAQ,CAAE;QAAAuF,QAAA,eACxC7F,OAAA,CAACxB,UAAU;UAACkI,OAAO,EAAEA,CAAA,KAAM;YACzB7B,wBAAwB,CAACQ,MAAM,CAACS,GAAG,CAAC3B,OAAO,CAAC;YAC5C5C,oBAAoB,CAAC8D,MAAM,CAACS,GAAG,CAACkB,IAAI,CAAC;UACvC,CAAE;UAAAnB,QAAA,eACA7F,OAAA;YAAK4G,GAAG,EAAEtH,MAAO;YAACuH,GAAG,EAAC,QAAQ;YAACI,KAAK,EAAE;cAAEC,MAAM,EAAE;YAAkB;UAAE;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE7D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA,eAEd,CACH;IACDR,SAAS,EAAC;EACZ,CAAC,CACF;EACD,MAAMwB,iBAAiB,GAAGA,CAAA,KAAM;IAC9BhF,cAAc,CAAC,EAAE,CAAC;IAClBW,kBAAkB,CAAC,CAAC;EACtB,CAAC;EAGD,MAAMsE,aAAa,GAAGvJ,KAAK,CAACwJ,WAAW,CAAEhC,MAA4B,IAAK;IACxE,OAAO;MACLiC,GAAG,EAAEjC,MAAM,CAACkC,cAAc,GAAG,CAAC,GAAG,CAAC;MAClCC,MAAM,EAAEnC,MAAM,CAACoC,aAAa,GAAG,CAAC,GAAG;IACrC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BlH,YAAY,CAAC,CAAC,CAAC;EACjB,CAAC;EACD,MAAMmG,eAAe,GAAIgB,KAAmB,IAAK;IAC/C1F,oBAAoB,CAAC,CAAC;IACtBkD,UAAU,CAAC,MAAM;MACf,IAAInD,oBAAoB,EAAE;QACxBlC,kBAAkB,CAAC6H,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,SAAS,EAAED,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAExD,OAAO,EAAEnB,SAAS,CAAC;MACjE,CAAC,MAAM;QACLjB,6BAA6B,CAAC,IAAI,CAAC;MACrC;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EACD,MAAM8F,cAAc,GAAGA,CAAA,KAAM;IAC7B,MAAMC,SAAS,GAAG,CAAC,QAAQ,EAAE,UAAU,EAAE,OAAO,CAAC;IACjD,MAAMC,eAAe,GAAGD,SAAS,CAACvH,SAAS,CAAC,IAAI6B,IAAI;IAClD,OAAO,GAAG9B,SAAS,CAAC,IAAI,CAAC,IAAIA,SAAS,CAACyH,eAAe,CAAC,IAAIzH,SAAS,CAAC8B,IAAI,CAAC,EAAE;EAC/E,CAAC;EACD,MAAM4F,aAAa,GAAGA,CAAA,kBACfhI,OAAA;IAAKiH,KAAK,EAAE;MAAEgB,OAAO,EAAE,MAAM;MAAEC,UAAU,EAAE,QAAQ;MAAEC,aAAa,EAAC;IAAS,CAAE;IAAAtC,QAAA,gBAC7E7F,OAAA;MACE4G,GAAG,EAAErH,OAAQ;MACbsH,GAAG,EAAC;IAAS;MAAAb,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC,eACTnG,OAAA,CAAC3B,UAAU;MAACyI,EAAE,EAAE;QAACsB,UAAU,EAAC;MAAK,CAAE;MAAAvC,QAAA,EAAEgC,cAAc,CAAC;IAAC;MAAA7B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAa,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEzD,CACN;EACL,oBACEnG,OAAA,CAAC/B,SAAS;IAACoK,QAAQ,EAAC,IAAI;IAAAxC,QAAA,eACtB7F,OAAA;MAAKsI,SAAS,EAAC,WAAW;MAAAzC,QAAA,eACxB7F,OAAA;QAAKsI,SAAS,EAAC,kBAAkB;QAAAzC,QAAA,gBACnC7F,OAAA;UAAKsI,SAAS,EAAC,YAAY;UAAAzC,QAAA,gBAC1B7F,OAAA;YAAKsI,SAAS,EAAC,iBAAiB;YAAAzC,QAAA,eACzB7F,OAAA;cAAKsI,SAAS,EAAC,aAAa;cAAAzC,QAAA,GAAC,GAAC,EAACvF,SAAS,CAAC,eAAe,CAAC;YAAA;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC,eACNnG,OAAA;YAAKsI,SAAS,EAAC,kBAAkB;YAAAzC,QAAA,eAChC7F,OAAA;cACQ0G,OAAO,EAAExB,SAAU;cACnBoD,SAAS,EAAC,oBAAoB;cAC9BC,QAAQ,EAAG,CAAC,CAAC,eAAe,EAAC,QAAQ,CAAC,CAAClC,IAAI,CAACC,IAAI,IAAIrD,KAAK,CAACsD,QAAQ,CAACD,IAAI,CAAC,CAAG;cAAAT,QAAA,gBAElF7F,OAAA;gBAAGsI,SAAS,EAAC;cAAiB;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1BnG,OAAA;gBAAA6F,QAAA,EAAOvF,SAAS,CAAC,kBAAkB;cAAC;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,YAAQ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNnG,OAAA;UAAKsI,SAAS,EAAC,sBAAsB;UAAAzC,QAAA,gBAC9C7F,OAAA,CAAC9B,IAAI;YAACsK,KAAK,EAAEjI,SAAU;YAACkI,QAAQ,EAAErE,eAAgB;YAAAyB,QAAA,gBACjD7F,OAAA,CAAC7B,GAAG;cAACuK,KAAK,EAAEpI,SAAS,CAAC,QAAQ;YAAE;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7BnG,OAAA,CAAC7B,GAAG;cAACuK,KAAK,EAAEpI,SAAS,CAAC,UAAU;YAAE;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3CnG,OAAA,CAAC7B,GAAG;cAACuK,KAAK,EAAEpI,SAAS,CAAC,OAAO;YAAE;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACPnG,OAAA;YAAKsI,SAAS,EAAC,2BAA2B;YAAAzC,QAAA,eAClD7F,OAAA,CAAC5B,SAAS;cACNuK,OAAO,EAAC,UAAU;cAClBC,WAAW,EAAEtI,SAAS,CAAC,qBAAqB,CAAE;cACxCkI,KAAK,EAAEtG,WAAY;cACnB2G,SAAS,EAAGC,CAAC,IAAK;gBAChB,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;kBACrBlG,YAAY,CAAC,CAAC;gBAChB;cACA,CAAE;cACV4F,QAAQ,EAAGK,CAAC,IAAK;gBACb,MAAMxE,QAAQ,GAAGwE,CAAC,CAACE,MAAM,CAACR,KAAK;gBAC/BrG,cAAc,CAACmC,QAAQ,CAAC;gBACxB,IAAIA,QAAQ,KAAK,EAAE,EAAE;kBACjB6C,iBAAiB,CAAC,CAAC;gBACvB;cACJ,CAAE;cACFmB,SAAS,EAAC,iBAAiB;cAC3BW,UAAU,EAAE;gBACRC,cAAc,eACVlJ,OAAA,CAAC1B,cAAc;kBAAC6K,QAAQ,EAAC,OAAO;kBAAAtD,QAAA,eAC5B7F,OAAA,CAACxB,UAAU;oBACP,cAAW,QAAQ;oBACnBkI,OAAO,EAAEA,CAAA,KAAM7D,YAAY,CAAC,CAAE;oBAC9BuG,WAAW,EAAG/E,KAAK,IAAKA,KAAK,CAACgF,cAAc,CAAC,CAAE;oBAAAxD,QAAA,eAE/C7F,OAAA,CAACf,UAAU;sBAAA+G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CACnB;gBACDmD,YAAY,EAAEpH,WAAW,iBACrBlC,OAAA,CAAC1B,cAAc;kBAAC6K,QAAQ,EAAC,KAAK;kBAAAtD,QAAA,eAC1B7F,OAAA,CAACxB,UAAU;oBACP,cAAW,OAAO;oBAClBkI,OAAO,EAAEA,CAAA,KAAM;sBACXvE,cAAc,CAAC,EAAE,CAAC;sBAClBgF,iBAAiB,CAAC,CAAC;oBACvB,CAAE;oBAAAtB,QAAA,eAEF7F,OAAA,CAACP,SAAS;sBAAAuG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAExB;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,eAKNnG,OAAA;UACE;UACAsI,SAAS,EAAC,2BAA2B;UAAAzC,QAAA,eAEzC7F,OAAA,CAACzB,QAAQ;YACPgL,IAAI,EAAE9I,aAAc;YACpB8E,OAAO,EAAEA,OAAQ;YACjBiE,QAAQ,EAAG1D,GAAG,IAAKA,GAAG,CAAC3B,OAAQ;YAC/BiD,aAAa,EAAEA,aAAc;YAC7BqC,UAAU;YACV9I,eAAe,EAAEA,eAAgB;YACjC+I,cAAc,EAAC,QAAQ;YACvBC,uBAAuB,EAAE/I,kBAAmB;YAC5CgJ,QAAQ,EAAE7I,UAAW;YACrB8I,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAE;YACnCC,UAAU,EAAE;cACVC,kBAAkB,EAAE;gBAClBC,gBAAgB,EAAE1J,SAAS,CAAC,kBAAkB;cAChD,CAAC;cACD2J,WAAW,EAAE3J,SAAS,CAACuH,cAAc,CAAC,CAAC;YACzC,CAAE;YACFqC,iBAAiB;YACbC,0BAA0B;YAC1BC,KAAK,EAAE;cACLC,aAAa,EAAErC,aAAa,CAAE;YAChC,CAAE;YACFsC,SAAS,EAAG;UAAG;YAAAtE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,EACLjF,UAAU,iBAChBlB,OAAA;UAAKsI,SAAS,EAAC,qBAAqB;UAAAzC,QAAA,eACvC7F,OAAA;YAAKsI,SAAS,EAAC,qCAAqC;YAAAzC,QAAA,gBAClD7F,OAAA;cAAA6F,QAAA,eACE7F,OAAA;gBAAKsI,SAAS,EAAC,YAAY;gBAAAzC,QAAA,eACzB7F,OAAA,CAACxB,UAAU;kBACZ8J,SAAS,EAAC,WAAW;kBAAAzC,QAAA,eACpB7F,OAAA;oBAAGsI,SAAS,EAAC;kBAAkB;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC,eACNnG,OAAA;cAAKsI,SAAS,EAAC,mBAAmB;cAAAzC,QAAA,EAC/CvF,SAAS,CAAC,qBAAqB;YAAC;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACNnG,OAAA;cAAKsI,SAAS,EAAC,eAAe;cAAAzC,QAAA,GAC3BvF,SAAS,CAAC,KAAK,CAAC,EAAC,GAAC,eAAAN,OAAA;gBAAMsI,SAAS,EAAC,yBAAyB;gBAAAzC,QAAA,EAAEvE;cAAiB;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,KAAC,EAAC7F,SAAS,CAAC,wCAAwC,CAAC;YAAA;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzI,CAAC,eAEFnG,OAAA;cAAKsI,SAAS,EAAC,eAAe;cAAAzC,QAAA,gBAC5B7F,OAAA;gBACE0G,OAAO,EAAEA,CAAA,KAAMvF,aAAa,CAAC,KAAK,CAAE;gBACpCmH,SAAS,EAAC,qBAAqB;gBAAAzC,QAAA,EAE9BvF,SAAS,CAAC,QAAQ;cAAC;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eACTnG,OAAA;gBACE0G,OAAO,EAAElC,YAAa;gBACtB8D,SAAS,EAAC,sBAAsB;gBAAAzC,QAAA,EAE/BvF,SAAS,CAAC,QAAQ;cAAC;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAClB,EAGM3E,iBAAiB,IAAIE,qBAAqB,iBACzC1B,OAAA,CAAClB,sBAAsB;UACrByL,IAAI,EAAE/I,iBAAkB;UACxBgJ,WAAW,EAAEA,CAAA,KAAM/I,oBAAoB,CAAC,KAAK,CAAE;UAC/CgJ,WAAW,EAAE/I,qBAAsB;UAC/BgJ,cAAc,EAAEzF,kBAAmB;UACnC7C,IAAI,EAAEA;QAAK;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACF,EACIvE,SAAS,gBACR5B,OAAA,CAAAE,SAAA;UAAA2F,QAAA,gBACA7F,OAAA;YACAiH,KAAK,EAAE;cACLkC,QAAQ,EAAE,OAAO;cACjB7B,GAAG,EAAE,CAAC;cACNqD,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,OAAO;cACdC,MAAM,EAAE,OAAO;cACfC,eAAe,EAAE,oBAAoB;cACrCC,MAAM,EAAE;YACV;UAAE;YAAA/E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFnG,OAAA;YACEiH,KAAK,EAAE;cACLkC,QAAQ,EAAE,OAAO;cACjB7B,GAAG,EAAE,KAAK;cACVqD,IAAI,EAAE,KAAK;cACXK,SAAS,EAAE,uBAAuB;cAClCD,MAAM,EAAE,IAAI;cACZD,eAAe,EAAE,OAAO;cACxB;cACAG,YAAY,EAAE,KAAK;cACnBC,SAAS,EAAE;YACb,CAAE;YAAArF,QAAA,eAEN7F,OAAA,CAACjB,cAAc;cACboM,YAAY,EAAEzK,gBAAiB;cAC/BmB,YAAY,EAAEA,YAAa;cAC3BD,SAAS,EAAEA,SAAU;cACrBwJ,aAAa,EAAEpK,aAAc;cAC7BR,YAAY,EAAEA,YAAa;cAC3BD,SAAS,EAAEA,SAAU;cACrB8K,SAAS,EAAE3D,gBAAiB;cAC5BtF,IAAI,EAAEA;YAAK;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACW,CAAC;QAAA,eACR,CAAC,GAEP,EACD,EAEArE,0BAA0B,iBACzB9B,OAAA,CAAAE,SAAA;UAAA2F,QAAA,gBACE7F,OAAA;YAAKiH,KAAK,EAAE;cAAEkC,QAAQ,EAAE,OAAO;cAAE7B,GAAG,EAAE,CAAC;cAAEqD,IAAI,EAAE,CAAC;cAAEC,KAAK,EAAE,OAAO;cAAEC,MAAM,EAAE,OAAO;cAAEC,eAAe,EAAE,iBAAiB;cAAEC,MAAM,EAAE;YAAI;UAAE;YAAA/E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxInG,OAAA;YAAKiH,KAAK,EAAE;cAAEkC,QAAQ,EAAE,OAAO;cAAE7B,GAAG,EAAE,KAAK;cAAEqD,IAAI,EAAE,KAAK;cAAEK,SAAS,EAAE,uBAAuB;cAAED,MAAM,EAAE,IAAI;cAAED,eAAe,EAAE,OAAO;cAAEG,YAAY,EAAE,KAAK;cAAEC,SAAS,EAAE;YAAkC,CAAE;YAAArF,QAAA,eACxM7F,OAAA,CAAChB,sBAAsB;cACrB6C,YAAY,EAAEE,6BAA8B;cAC5CH,SAAS,EAAEE,0BAA2B;cACtCM,IAAI,EAAEA;YAAK;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA,eACN,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAAC/F,EAAA,CAjdID,aAAa;EAAA,QACQd,cAAc,EAQdR,WAAW,EAQmBgB,YAAY,EAMlCV,OAAO,EAKvBD,WAAW;AAAA;AAAAoM,EAAA,GA5BxBnL,aAAa;AAmdnB,eAAeA,aAAa;AAAC,IAAAmL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}