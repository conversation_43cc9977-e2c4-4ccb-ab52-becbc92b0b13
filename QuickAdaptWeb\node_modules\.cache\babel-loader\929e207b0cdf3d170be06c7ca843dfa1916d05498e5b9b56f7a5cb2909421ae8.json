{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptWeb\\\\src\\\\components\\\\login\\\\Superadminloginpage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, Box, Typography, TextField, Button, Link, IconButton, InputAdornment } from '@mui/material';\nimport { useAuth } from '../auth/AuthProvider';\nimport { GetUserDetails } from '../../services/UserService';\nimport { JSEncrypt } from 'jsencrypt';\nimport { useNavigate } from \"react-router-dom\";\nimport { FormHelperText } from '@mui/material';\nimport { LoginService } from \"../../services/LoginService\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nlet SAinitialsData;\nlet userLocalData = {};\nlet userDetails;\nexport default function LoginPage() {\n  _s();\n  const {\n    user,\n    signOut,\n    loggedOut\n  } = useAuth();\n  let UserId;\n  let OrganizationId;\n  const [showPassword, setShowPassword] = useState(false);\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [users, setUser] = useState(null);\n  const [error, setError] = useState(null);\n  const [loginUserInfo, setLoginUserInfo] = useState(undefined);\n  const [response, setresponse] = useState('');\n  const [userIds, setuserId] = useState(\"\");\n  const [organizationDetails, setOrganizationDetails] = useState(null);\n  const [loginUserDetails, setUserDetails] = useState(null);\n  const handleClickShowPassword = () => {\n    setShowPassword(!showPassword);\n  };\n  const handleEmailChange = event => {\n    setEmail(event.target.value);\n  };\n  const handlePasswordChange = event => {\n    setPassword(event.target.value);\n  };\n  const navigate = useNavigate();\n  const handleSubmit = async () => {\n    try {\n      if (password === '' || password == null) {\n        setError('password should not be empty');\n      } else if (email === '' || email == null) {\n        setError('email should not be empty');\n      } else {\n        const isEncryptionEnabled = process.env.REACT_APP_ENABLE_ENCRYPTION === 'true';\n        const publicKey = process.env.REACT_APP_PUBLIC_ENCRYPT_KEY || '';\n        const encryptor = new JSEncrypt();\n        encryptor.setPublicKey(publicKey);\n        const now = new Date().toISOString();\n        const encryptedPassword = encryptor.encrypt(password + '|' + now.trim()).toString();\n        if (!encryptedPassword) {\n          setError('Enter correct password');\n        }\n        const organizationId = \"1\";\n        const rememberLogin = true;\n        const returnUrl = \"\";\n        const response = await LoginService(email, isEncryptionEnabled ? encryptedPassword : password, organizationId, rememberLogin, returnUrl, \"super\", \"admin\");\n        if (response.access_token) {\n          var _userResponse$data$Us, _userResponse$data;\n          userLocalData[\"oidc-info\"] = JSON.stringify(response);\n          localStorage.setItem(\"access_token\", response.access_token);\n          const userResponse = await GetUserDetails();\n          setUserDetails(userResponse ? userResponse.data : null);\n          const firstNameInitials = userResponse !== null && userResponse !== void 0 && userResponse.data.FirstName && userResponse !== null && userResponse !== void 0 && userResponse.data.FirstName ? userResponse === null || userResponse === void 0 ? void 0 : userResponse.data.FirstName.substring(0, 1).toUpperCase() : '';\n          const lastNameinitials = userResponse !== null && userResponse !== void 0 && userResponse.data && userResponse !== null && userResponse !== void 0 && userResponse.data.LastName ? userResponse === null || userResponse === void 0 ? void 0 : userResponse.data.LastName.substring(0, 1).toUpperCase() : '';\n          const finalData = firstNameInitials + lastNameinitials;\n          SAinitialsData = finalData;\n          localStorage.setItem(\"userType\", (_userResponse$data$Us = userResponse === null || userResponse === void 0 ? void 0 : (_userResponse$data = userResponse.data) === null || _userResponse$data === void 0 ? void 0 : _userResponse$data.UserType) !== null && _userResponse$data$Us !== void 0 ? _userResponse$data$Us : \"\");\n          userLocalData[\"user\"] = JSON.stringify(userResponse === null || userResponse === void 0 ? void 0 : userResponse.data);\n          localStorage.setItem(\"userInfo\", JSON.stringify(userLocalData));\n          navigate(\"/superadmin/organizations\", {\n            state: {\n              userDetail: userResponse === null || userResponse === void 0 ? void 0 : userResponse.data,\n              organizationDetails: userResponse === null || userResponse === void 0 ? void 0 : userResponse.data.OrganizationId\n            }\n          });\n        } else {\n          setError(response.error_description);\n        }\n      }\n    } catch (error) {\n      console.error('Login failed:');\n      setError('An unexpected error occurred.'); // Handle unexpected errors\n    }\n  };\n\n  // async function GetLoginUserInfo(userResponse : User) {\n  //     try {\n  //         const firstNameInitials =  userResponse?.FirstName &&  userResponse?.FirstName ? userResponse?.FirstName.substring(0, 1).toUpperCase() : '';\n  //         const lastNameinitials =  userResponse &&  userResponse?.LastName ? userResponse?.LastName.substring(0, 1).toUpperCase() : '';\n  //         const finalData = firstNameInitials + lastNameinitials;\n  //         SAinitialsData = finalData;\n  //         localStorage.setItem(\"userType\", userResponse?.UserType ?? \"\");\n  //     } catch (error) {\n  //         console.error('Error fetching user or organization details', error);\n  //     }\n  // }\n  // useEffect(() => {\n  //     let token = localStorage.getItem(\"access_token\");\n  // \tconst userInfo = JSON.parse(localStorage.getItem(\"userInfo\") || '{}');\n  // \tif (userInfo['oidc-info'] && userInfo['user']) {\n  // \t\tuserDetails = JSON.parse(userInfo['user'])\n  // \t\ttoken = userInfo['oidc-info'].access_token;\n  // \t}\n  //     if (token) {\n  //         try {\n  //             const loggedinUserInfo = jwt_decode<LoginUserInfo>(token);\n  //             setLoginUserInfo(loggedinUserInfo);\n  //             GetLoginUserInfo(userDetails);\n  //             UserId = loggedinUserInfo.UserId;                \n  //         } catch (error) {\n  //             signOut();\n  //         }\n  //     }\n  //     else {\n  //         signOut();\n  //     }\n\n  // }, [user]);\n\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"sm\",\n    className: \"qadpt-superadminlogin\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 4,\n      className: \"qadpt-brand-logo\",\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h3\",\n        className: \"qadpt-brand-logo-text\",\n        children: \"QUICKADOPT\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      className: \"qadpt-welcome-message\",\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        className: \"qadpt-welcome-message-text\",\n        children: \"Welcome back\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      className: \"qadpt-login-form\",\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        className: \"qadpt-form-label\",\n        children: \"Email\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TextField, {\n        required: true,\n        fullWidth: true,\n        type: \"email\",\n        id: \"email\",\n        name: \"Email\",\n        autoComplete: \"Email\",\n        autoFocus: true,\n        value: email,\n        onChange: handleEmailChange,\n        placeholder: \"eg, <EMAIL>\",\n        className: \"qadpt-custom-input\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        className: \"qadpt-form-label\",\n        children: \"Password\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TextField, {\n        required: true,\n        fullWidth: true,\n        type: showPassword ? \"text\" : \"password\",\n        id: \"password\",\n        name: \"password\",\n        autoComplete: \"password\",\n        autoFocus: true,\n        value: password,\n        onChange: handlePasswordChange,\n        placeholder: \"Enter your password\",\n        className: \"qadpt-custom-input\",\n        InputProps: {\n          endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n            position: \"end\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              \"aria-label\": \"toggle password visibility\",\n              onClick: handleClickShowPassword,\n              edge: \"end\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: `fal ${showPassword ? \"fa-eye-slash\" : \"fa-eye\"}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 29\n          }, this)\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 17\n      }, this), error && /*#__PURE__*/_jsxDEV(FormHelperText, {\n        error: true,\n        className: \"qadpt-text-danger\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"button\",\n        fullWidth: true,\n        variant: \"contained\",\n        className: \"qadpt-btn-default\",\n        onClick: handleSubmit,\n        children: \"Continue\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      mt: 12,\n      className: \"qadpt-login-footer\",\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        className: \"qadpt-footer-text\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          sx: {\n            cursor: \"pointer\"\n          },\n          className: \"qadpt-footer-link\",\n          children: \"Terms of use\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 21\n        }, this), \" |\", /*#__PURE__*/_jsxDEV(Link, {\n          sx: {\n            cursor: \"pointer\"\n          },\n          className: \"qadpt-footer-link\",\n          children: \"Privacy Policy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 136,\n    columnNumber: 9\n  }, this);\n}\n_s(LoginPage, \"tvnBwxXerkd6MDYq4cLxvg2ZviU=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = LoginPage;\nexport { SAinitialsData };\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Box", "Typography", "TextField", "<PERSON><PERSON>", "Link", "IconButton", "InputAdornment", "useAuth", "GetUserDetails", "JSEncrypt", "useNavigate", "FormHelperText", "LoginService", "jsxDEV", "_jsxDEV", "SAinitialsData", "userLocalData", "userDetails", "LoginPage", "_s", "user", "signOut", "loggedOut", "UserId", "OrganizationId", "showPassword", "setShowPassword", "email", "setEmail", "password", "setPassword", "users", "setUser", "error", "setError", "loginUserInfo", "setLoginUserInfo", "undefined", "response", "setresponse", "userIds", "setuserId", "organizationDetails", "setOrganizationDetails", "loginUserDetails", "setUserDetails", "handleClickShowPassword", "handleEmailChange", "event", "target", "value", "handlePasswordChange", "navigate", "handleSubmit", "isEncryptionEnabled", "process", "env", "REACT_APP_ENABLE_ENCRYPTION", "public<PERSON>ey", "REACT_APP_PUBLIC_ENCRYPT_KEY", "encryptor", "setPublicKey", "now", "Date", "toISOString", "encryptedPassword", "encrypt", "trim", "toString", "organizationId", "<PERSON><PERSON><PERSON><PERSON>", "returnUrl", "access_token", "_userResponse$data$Us", "_userResponse$data", "JSON", "stringify", "localStorage", "setItem", "userResponse", "data", "firstNameInitials", "FirstName", "substring", "toUpperCase", "lastNameinitials", "LastName", "finalData", "UserType", "state", "userDetail", "error_description", "console", "max<PERSON><PERSON><PERSON>", "className", "children", "mb", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "required", "fullWidth", "type", "id", "name", "autoComplete", "autoFocus", "onChange", "placeholder", "InputProps", "endAdornment", "position", "onClick", "edge", "mt", "sx", "cursor", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/components/login/Superadminloginpage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Con<PERSON>er, Box, Typography, TextField, Button, Link, IconButton, InputAdornment } from '@mui/material';\r\nimport Visibility from '@mui/icons-material/Visibility';\r\nimport { useAuth } from '../auth/AuthProvider';\r\nimport VisibilityOff from '@mui/icons-material/VisibilityOff';\r\nimport { superAdminLogin } from \"../../services/SuperAdminLoginService\"\r\nimport { GetUserDetails, encryptPassword } from '../../services/UserService';\r\nimport { JSEncrypt } from 'jsencrypt';\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { GetUserDetailsById } from '../../services/UserService';\r\nimport userManager from '../auth/UseAuth';\r\nimport { LoginUserInfo } from '../../models/LoginUserInfo';\r\nimport jwt_decode from \"jwt-decode\";\r\nimport { getAllUsers } from '../../services/UserService';\r\nimport { getOrganizationById } from '../../services/OrganizationService';\r\nimport { Organization } from \"../../models/Organization\";\r\nimport { User } from \"../../models/User\";\r\nimport { User as Users, UserManager } from 'oidc-client-ts';\r\nimport { FormHelperText } from '@mui/material';\r\nimport { LoginService } from \"../../services/LoginService\";\r\nlet SAinitialsData: string;\r\nlet userLocalData: { [key: string]: any } = {}\r\nlet userDetails: User;\r\nexport default function LoginPage() {\r\n    const { user,signOut ,loggedOut} = useAuth();\r\n    let UserId: string;\r\n    let OrganizationId: string;\r\n    const [showPassword, setShowPassword] = useState(false);\r\n    const [email, setEmail] = useState('');\r\n    const [password, setPassword] = useState('');\r\n    const [users, setUser] = useState<Users | null>(null);\r\n    const [error, setError] = useState<string | null>(null);\r\n    const [loginUserInfo, setLoginUserInfo] = useState<LoginUserInfo | undefined>(undefined);\r\n    const [response, setresponse] = useState('');\r\n    const [userIds, setuserId] = useState(\"\");\r\n    const [organizationDetails, setOrganizationDetails] = useState<Organization | null>(null);\r\n    const [loginUserDetails, setUserDetails] = useState<User | null>(null);\r\n    const handleClickShowPassword = () => {\r\n        setShowPassword(!showPassword);\r\n    };\r\n\r\n    const handleEmailChange = (event: any) => {\r\n        setEmail(event.target.value);\r\n    };\r\n\r\n    const handlePasswordChange = (event: any) => {\r\n        setPassword(event.target.value);\r\n    };\r\n    const navigate = useNavigate();\r\n    const handleSubmit = async () => {\r\n        try {\r\n            \r\n            if (password === '' || password == null)\r\n            {\r\n                setError('password should not be empty');\r\n            }\r\n            else if (email === '' || email == null)\r\n            {\r\n                setError('email should not be empty');\r\n            }\r\n            else {\r\n                const isEncryptionEnabled = process.env.REACT_APP_ENABLE_ENCRYPTION === 'true';\r\n                const publicKey = process.env.REACT_APP_PUBLIC_ENCRYPT_KEY || '';\r\n                const encryptor = new JSEncrypt();\r\n                encryptor.setPublicKey(publicKey);\r\n                const now = new Date().toISOString();\r\n                const encryptedPassword = encryptor.encrypt(password + '|' + now.trim()).toString();\r\n            if (!encryptedPassword) {\r\n                setError('Enter correct password');\r\n            }\r\n            const organizationId = \"1\";\r\n            const rememberLogin = true;\r\n            const returnUrl = \"\"\r\n                const response = await LoginService(email, isEncryptionEnabled ? encryptedPassword : password, organizationId, rememberLogin, returnUrl,\"super\",\"admin\");\r\n                if (response.access_token) {                    \r\n                userLocalData[\"oidc-info\"] = JSON.stringify(response)\r\n                localStorage.setItem(\"access_token\",response.access_token)          \r\n                const userResponse = await GetUserDetails();\r\n                setUserDetails(userResponse ? userResponse.data : null);\r\n                const firstNameInitials =  userResponse?.data.FirstName &&  userResponse?.data.FirstName ? userResponse?.data.FirstName.substring(0, 1).toUpperCase() : '';\r\n                const lastNameinitials =  userResponse?.data &&  userResponse?.data.LastName ? userResponse?.data.LastName.substring(0, 1).toUpperCase() : '';\r\n                const finalData = firstNameInitials + lastNameinitials;\r\n                SAinitialsData = finalData;\r\n                localStorage.setItem(\"userType\", userResponse?.data?.UserType ?? \"\");            \r\n                userLocalData[\"user\"] = JSON.stringify(userResponse?.data);\r\n                localStorage.setItem(\"userInfo\",JSON.stringify(userLocalData)) \r\n                    navigate(\"/superadmin/organizations\", {\r\n                        state: { userDetail: userResponse?.data, organizationDetails: userResponse?.data.OrganizationId }\r\n                    });\r\n                } else {\r\n                    setError(response.error_description);\r\n                }                \r\n            }\r\n        }\r\n        catch (error) {\r\n            console.error('Login failed:');\r\n            setError('An unexpected error occurred.'); // Handle unexpected errors\r\n        }\r\n    };\r\n   \r\n    // async function GetLoginUserInfo(userResponse : User) {\r\n    //     try {\r\n    //         const firstNameInitials =  userResponse?.FirstName &&  userResponse?.FirstName ? userResponse?.FirstName.substring(0, 1).toUpperCase() : '';\r\n    //         const lastNameinitials =  userResponse &&  userResponse?.LastName ? userResponse?.LastName.substring(0, 1).toUpperCase() : '';\r\n    //         const finalData = firstNameInitials + lastNameinitials;\r\n    //         SAinitialsData = finalData;\r\n    //         localStorage.setItem(\"userType\", userResponse?.UserType ?? \"\");\r\n    //     } catch (error) {\r\n    //         console.error('Error fetching user or organization details', error);\r\n    //     }\r\n    // }\r\n    // useEffect(() => {\r\n    //     let token = localStorage.getItem(\"access_token\");\r\n\t// \tconst userInfo = JSON.parse(localStorage.getItem(\"userInfo\") || '{}');\r\n\t// \tif (userInfo['oidc-info'] && userInfo['user']) {\r\n\t// \t\tuserDetails = JSON.parse(userInfo['user'])\r\n\t// \t\ttoken = userInfo['oidc-info'].access_token;\r\n\t// \t}\r\n    //     if (token) {\r\n    //         try {\r\n    //             const loggedinUserInfo = jwt_decode<LoginUserInfo>(token);\r\n    //             setLoginUserInfo(loggedinUserInfo);\r\n    //             GetLoginUserInfo(userDetails);\r\n    //             UserId = loggedinUserInfo.UserId;                \r\n    //         } catch (error) {\r\n    //             signOut();\r\n    //         }\r\n    //     }\r\n    //     else {\r\n    //         signOut();\r\n    //     }\r\n\r\n    // }, [user]);\r\n\r\n    return (\r\n        <Container maxWidth=\"sm\" className=\"qadpt-superadminlogin\">\r\n            <Box mb={4} className=\"qadpt-brand-logo\">\r\n                <Typography variant=\"h3\" className=\"qadpt-brand-logo-text\">\r\n                    QUICKADOPT\r\n                </Typography>\r\n            </Box>\r\n\r\n            <Box className=\"qadpt-welcome-message\">\r\n                <Typography variant=\"h4\" className=\"qadpt-welcome-message-text\">\r\n                    Welcome back\r\n                </Typography>\r\n            </Box>\r\n\r\n            <Box className=\"qadpt-login-form\">\r\n                <Typography className=\"qadpt-form-label\">\r\n                    Email\r\n                </Typography>\r\n                <TextField\r\n                    required\r\n                    fullWidth\r\n                    type=\"email\"\r\n                    id=\"email\"\r\n                    name=\"Email\"\r\n                    autoComplete=\"Email\"\r\n                    autoFocus\r\n                    value={email}\r\n                    onChange={handleEmailChange}\r\n                    placeholder=\"eg, <EMAIL>\"\r\n                    className=\"qadpt-custom-input\"\r\n                />\r\n\r\n                <Typography className=\"qadpt-form-label\">\r\n                    Password\r\n                </Typography>\r\n                <TextField\r\n                    required\r\n                    fullWidth\r\n                    type={showPassword ? \"text\" : \"password\"}\r\n                    id=\"password\"\r\n                    name=\"password\"\r\n                    autoComplete=\"password\"\r\n                    autoFocus\r\n                    value={password}\r\n                    onChange={handlePasswordChange}\r\n                    placeholder=\"Enter your password\"\r\n                    className=\"qadpt-custom-input\"\r\n                    InputProps={{\r\n                        endAdornment: (\r\n                            <InputAdornment position=\"end\">\r\n                                <IconButton\r\n                                    aria-label=\"toggle password visibility\"\r\n                                    onClick={handleClickShowPassword}\r\n                                    edge=\"end\"\r\n                                >\r\n                                    {/* {showPassword ? <VisibilityOff /> : <Visibility />} */}\r\n                                    <i className={`fal ${showPassword ? \"fa-eye-slash\" : \"fa-eye\"}`}></i>\r\n\r\n                                </IconButton>\r\n                            </InputAdornment>\r\n                        ),\r\n                    }}\r\n                   \r\n                />\r\n                {error && (\r\n                    <FormHelperText error className=\"qadpt-text-danger\">\r\n                        {error}\r\n                    </FormHelperText>\r\n                )}\r\n\r\n                <Button\r\n                    type=\"button\"\r\n                    fullWidth\r\n                    variant=\"contained\"\r\n                    className=\"qadpt-btn-default\"\r\n                    onClick={handleSubmit}\r\n                > \r\n                        Continue\r\n                </Button>\r\n            </Box>\r\n\r\n            <Box mt={12} className=\"qadpt-login-footer\">\r\n                <Typography variant=\"body2\" className=\"qadpt-footer-text\">\r\n                    <Link sx={{ cursor: \"pointer\" }} className=\"qadpt-footer-link\">Terms of use</Link> |\r\n                    <Link sx={{ cursor: \"pointer\" }} className=\"qadpt-footer-link\">Privacy Policy</Link>\r\n                </Typography>\r\n            </Box>\r\n        </Container>\r\n    );\r\n}\r\nexport {SAinitialsData}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAmB,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAEC,IAAI,EAAEC,UAAU,EAAEC,cAAc,QAAQ,eAAe;AAE/G,SAASC,OAAO,QAAQ,sBAAsB;AAG9C,SAASC,cAAc,QAAyB,4BAA4B;AAC5E,SAASC,SAAS,QAAQ,WAAW;AACrC,SAASC,WAAW,QAAQ,kBAAkB;AAU9C,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,YAAY,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAC3D,IAAIC,cAAsB;AAC1B,IAAIC,aAAqC,GAAG,CAAC,CAAC;AAC9C,IAAIC,WAAiB;AACrB,eAAe,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EAChC,MAAM;IAAEC,IAAI;IAACC,OAAO;IAAEC;EAAS,CAAC,GAAGf,OAAO,CAAC,CAAC;EAC5C,IAAIgB,MAAc;EAClB,IAAIC,cAAsB;EAC1B,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiC,KAAK,EAAEC,OAAO,CAAC,GAAGlC,QAAQ,CAAe,IAAI,CAAC;EACrD,MAAM,CAACmC,KAAK,EAAEC,QAAQ,CAAC,GAAGpC,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACqC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtC,QAAQ,CAA4BuC,SAAS,CAAC;EACxF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC0C,OAAO,EAAEC,SAAS,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACzC,MAAM,CAAC4C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7C,QAAQ,CAAsB,IAAI,CAAC;EACzF,MAAM,CAAC8C,gBAAgB,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAc,IAAI,CAAC;EACtE,MAAMgD,uBAAuB,GAAGA,CAAA,KAAM;IAClCpB,eAAe,CAAC,CAACD,YAAY,CAAC;EAClC,CAAC;EAED,MAAMsB,iBAAiB,GAAIC,KAAU,IAAK;IACtCpB,QAAQ,CAACoB,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EAChC,CAAC;EAED,MAAMC,oBAAoB,GAAIH,KAAU,IAAK;IACzClB,WAAW,CAACkB,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACnC,CAAC;EACD,MAAME,QAAQ,GAAG1C,WAAW,CAAC,CAAC;EAC9B,MAAM2C,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MAEA,IAAIxB,QAAQ,KAAK,EAAE,IAAIA,QAAQ,IAAI,IAAI,EACvC;QACIK,QAAQ,CAAC,8BAA8B,CAAC;MAC5C,CAAC,MACI,IAAIP,KAAK,KAAK,EAAE,IAAIA,KAAK,IAAI,IAAI,EACtC;QACIO,QAAQ,CAAC,2BAA2B,CAAC;MACzC,CAAC,MACI;QACD,MAAMoB,mBAAmB,GAAGC,OAAO,CAACC,GAAG,CAACC,2BAA2B,KAAK,MAAM;QAC9E,MAAMC,SAAS,GAAGH,OAAO,CAACC,GAAG,CAACG,4BAA4B,IAAI,EAAE;QAChE,MAAMC,SAAS,GAAG,IAAInD,SAAS,CAAC,CAAC;QACjCmD,SAAS,CAACC,YAAY,CAACH,SAAS,CAAC;QACjC,MAAMI,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACpC,MAAMC,iBAAiB,GAAGL,SAAS,CAACM,OAAO,CAACrC,QAAQ,GAAG,GAAG,GAAGiC,GAAG,CAACK,IAAI,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACvF,IAAI,CAACH,iBAAiB,EAAE;UACpB/B,QAAQ,CAAC,wBAAwB,CAAC;QACtC;QACA,MAAMmC,cAAc,GAAG,GAAG;QAC1B,MAAMC,aAAa,GAAG,IAAI;QAC1B,MAAMC,SAAS,GAAG,EAAE;QAChB,MAAMjC,QAAQ,GAAG,MAAM1B,YAAY,CAACe,KAAK,EAAE2B,mBAAmB,GAAGW,iBAAiB,GAAGpC,QAAQ,EAAEwC,cAAc,EAAEC,aAAa,EAAEC,SAAS,EAAC,OAAO,EAAC,OAAO,CAAC;QACxJ,IAAIjC,QAAQ,CAACkC,YAAY,EAAE;UAAA,IAAAC,qBAAA,EAAAC,kBAAA;UAC3B1D,aAAa,CAAC,WAAW,CAAC,GAAG2D,IAAI,CAACC,SAAS,CAACtC,QAAQ,CAAC;UACrDuC,YAAY,CAACC,OAAO,CAAC,cAAc,EAACxC,QAAQ,CAACkC,YAAY,CAAC;UAC1D,MAAMO,YAAY,GAAG,MAAMvE,cAAc,CAAC,CAAC;UAC3CqC,cAAc,CAACkC,YAAY,GAAGA,YAAY,CAACC,IAAI,GAAG,IAAI,CAAC;UACvD,MAAMC,iBAAiB,GAAIF,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEC,IAAI,CAACE,SAAS,IAAKH,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEC,IAAI,CAACE,SAAS,GAAGH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEC,IAAI,CAACE,SAAS,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG,EAAE;UAC1J,MAAMC,gBAAgB,GAAIN,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEC,IAAI,IAAKD,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEC,IAAI,CAACM,QAAQ,GAAGP,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEC,IAAI,CAACM,QAAQ,CAACH,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG,EAAE;UAC7I,MAAMG,SAAS,GAAGN,iBAAiB,GAAGI,gBAAgB;UACtDtE,cAAc,GAAGwE,SAAS;UAC1BV,YAAY,CAACC,OAAO,CAAC,UAAU,GAAAL,qBAAA,GAAEM,YAAY,aAAZA,YAAY,wBAAAL,kBAAA,GAAZK,YAAY,CAAEC,IAAI,cAAAN,kBAAA,uBAAlBA,kBAAA,CAAoBc,QAAQ,cAAAf,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;UACpEzD,aAAa,CAAC,MAAM,CAAC,GAAG2D,IAAI,CAACC,SAAS,CAACG,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEC,IAAI,CAAC;UAC1DH,YAAY,CAACC,OAAO,CAAC,UAAU,EAACH,IAAI,CAACC,SAAS,CAAC5D,aAAa,CAAC,CAAC;UAC1DoC,QAAQ,CAAC,2BAA2B,EAAE;YAClCqC,KAAK,EAAE;cAAEC,UAAU,EAAEX,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEC,IAAI;cAAEtC,mBAAmB,EAAEqC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEC,IAAI,CAACxD;YAAe;UACpG,CAAC,CAAC;QACN,CAAC,MAAM;UACHU,QAAQ,CAACI,QAAQ,CAACqD,iBAAiB,CAAC;QACxC;MACJ;IACJ,CAAC,CACD,OAAO1D,KAAK,EAAE;MACV2D,OAAO,CAAC3D,KAAK,CAAC,eAAe,CAAC;MAC9BC,QAAQ,CAAC,+BAA+B,CAAC,CAAC,CAAC;IAC/C;EACJ,CAAC;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACH;EACA;EACA;EACA;EACA;EACG;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;;EAEA,oBACIpB,OAAA,CAACf,SAAS;IAAC8F,QAAQ,EAAC,IAAI;IAACC,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBACtDjF,OAAA,CAACd,GAAG;MAACgG,EAAE,EAAE,CAAE;MAACF,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eACpCjF,OAAA,CAACb,UAAU;QAACgG,OAAO,EAAC,IAAI;QAACH,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EAAC;MAE3D;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC,eAENvF,OAAA,CAACd,GAAG;MAAC8F,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eAClCjF,OAAA,CAACb,UAAU;QAACgG,OAAO,EAAC,IAAI;QAACH,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAEhE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC,eAENvF,OAAA,CAACd,GAAG;MAAC8F,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC7BjF,OAAA,CAACb,UAAU;QAAC6F,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAEzC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbvF,OAAA,CAACZ,SAAS;QACNoG,QAAQ;QACRC,SAAS;QACTC,IAAI,EAAC,OAAO;QACZC,EAAE,EAAC,OAAO;QACVC,IAAI,EAAC,OAAO;QACZC,YAAY,EAAC,OAAO;QACpBC,SAAS;QACT1D,KAAK,EAAEvB,KAAM;QACbkF,QAAQ,EAAE9D,iBAAkB;QAC5B+D,WAAW,EAAC,sBAAsB;QAClChB,SAAS,EAAC;MAAoB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eAEFvF,OAAA,CAACb,UAAU;QAAC6F,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAEzC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbvF,OAAA,CAACZ,SAAS;QACNoG,QAAQ;QACRC,SAAS;QACTC,IAAI,EAAE/E,YAAY,GAAG,MAAM,GAAG,UAAW;QACzCgF,EAAE,EAAC,UAAU;QACbC,IAAI,EAAC,UAAU;QACfC,YAAY,EAAC,UAAU;QACvBC,SAAS;QACT1D,KAAK,EAAErB,QAAS;QAChBgF,QAAQ,EAAE1D,oBAAqB;QAC/B2D,WAAW,EAAC,qBAAqB;QACjChB,SAAS,EAAC,oBAAoB;QAC9BiB,UAAU,EAAE;UACRC,YAAY,eACRlG,OAAA,CAACR,cAAc;YAAC2G,QAAQ,EAAC,KAAK;YAAAlB,QAAA,eAC1BjF,OAAA,CAACT,UAAU;cACP,cAAW,4BAA4B;cACvC6G,OAAO,EAAEpE,uBAAwB;cACjCqE,IAAI,EAAC,KAAK;cAAApB,QAAA,eAGVjF,OAAA;gBAAGgF,SAAS,EAAE,OAAOrE,YAAY,GAAG,cAAc,GAAG,QAAQ;cAAG;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAE7D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAExB;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEL,CAAC,EACDpE,KAAK,iBACFnB,OAAA,CAACH,cAAc;QAACsB,KAAK;QAAC6D,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAC9C9D;MAAK;QAAAiE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CACnB,eAEDvF,OAAA,CAACX,MAAM;QACHqG,IAAI,EAAC,QAAQ;QACbD,SAAS;QACTN,OAAO,EAAC,WAAW;QACnBH,SAAS,EAAC,mBAAmB;QAC7BoB,OAAO,EAAE7D,YAAa;QAAA0C,QAAA,EACzB;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAENvF,OAAA,CAACd,GAAG;MAACoH,EAAE,EAAE,EAAG;MAACtB,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eACvCjF,OAAA,CAACb,UAAU;QAACgG,OAAO,EAAC,OAAO;QAACH,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBACrDjF,OAAA,CAACV,IAAI;UAACiH,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAU,CAAE;UAACxB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,MAClF,eAAAvF,OAAA,CAACV,IAAI;UAACiH,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAU,CAAE;UAACxB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEpB;AAAClF,EAAA,CAxMuBD,SAAS;EAAA,QACMX,OAAO,EAwBzBG,WAAW;AAAA;AAAA6G,EAAA,GAzBRrG,SAAS;AAyMjC,SAAQH,cAAc;AAAC,IAAAwG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}