import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User, UserManager } from 'oidc-client-ts';
import userManager from './UseAuth';
import { LoginUserInfo } from '../../models/LoginUserInfo';
import jwt_decode from "jwt-decode";
import { User as LoginUser } from '../../models/User';
import { useNavigate, useLocation } from 'react-router-dom';
import { Content } from 'antd/es/layout/layout';
import { startTransition } from 'react';
import axios from 'axios';
import { getRolesByUser, getUserRoles } from '../../services/UserRoleService';
interface AuthContextType {
  user: User | null;
  userDetails: LoginUser | null;
  signOut: () => void;
  loggedOut: boolean;
  userRoles: any;
  isLoading: boolean;
}
let userLocalData: { [key: string]: any } = {}
const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}
let initialsData: string;

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const location = useLocation();
  const [user, setUser] = useState<User | null>(null);
  const noLayoutRoutes = ["/login", "/forgotpassword", "/resetpassword/:passwordLogId","/uninstall", "/admin/adminlogin", "/linkexpired"];
	const uuidRegex = "[0-9a-fA-F-]{36}";
  const [userDetails, setUserDetails] = useState<LoginUser | null>(location.state?.userDetail || null);
  const [loggedOut, setLoggedOut] = useState<boolean>(false);
  const [userRoles, setUserRoles] = useState({});
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const navigate = useNavigate();

  const calculateInitials = (firstName?: string, lastName?: string) => {
    const firstInitial = firstName ? firstName[0].toUpperCase() : '';
    const lastInitial = lastName ? lastName[0].toUpperCase() : '';
    return firstInitial + lastInitial;
  };
  // setUserDetails(location.state?.userDetails);
  
  useEffect(() => {
    const initializeUser = async () => {
      if (loggedOut) return;  // Skip reinitialization if user has logged out

      try {
        const userInfo = JSON.parse(localStorage.getItem("userInfo") || '{}');
        if (userInfo['oidc-info'] && userInfo['user']) {
          const user = JSON.parse(userInfo['user'])
          const oidcinfo = JSON.parse(userInfo['oidc-info'])
          setUser(user);
          if (oidcinfo.access_token) {
            try {
              // Try to decode as JWT for regular login tokens
              const decodedToken = jwt_decode<LoginUserInfo>(oidcinfo.access_token);
              localStorage.setItem("userType", user.UserType);
              setUserDetails(user);
              GetUserRoles();
              const finalData = calculateInitials(
                user?.FirstName ? user?.FirstName.substring(0, 1).toUpperCase() : '',
                user?.LastName ? user?.LastName.substring(0, 1).toUpperCase() : ''
              );
              initialsData = finalData;
            } catch (error) {
              // If JWT decode fails, still set user details for auto-login flow
              localStorage.setItem("userType", user.UserType);
              setUserDetails(user);
              GetUserRoles();
              const finalData = calculateInitials(
                user?.FirstName ? user?.FirstName.substring(0, 1).toUpperCase() : '',
                user?.LastName ? user?.LastName.substring(0, 1).toUpperCase() : ''
              );
              initialsData = finalData;
            }
          }
        }
        else {
          // Check if we have user data from auto-login (might not have oidc-info)
          const hasUserData = userInfo['user'];
          const isAutoLoginCompleted = sessionStorage.getItem('autoLoginCompleted') === 'true';

          const isNoLayoutRoute = noLayoutRoutes.some(route => {
            if (route === "/resetpassword/:passwordLogId") {
              const resetPasswordRegex = new RegExp(`^/resetpassword/${uuidRegex}$`);
              return resetPasswordRegex.test(location.pathname);
            }
            return location.pathname === route;
          });

          // Don't sign out if we have user data (auto-login case) or if we're on a no-layout route
          if (!isNoLayoutRoute && !hasUserData && !isAutoLoginCompleted) {
            signOut();
          } else if (hasUserData) {
            // Handle auto-login case where we have user data but no oidc-info
            const user = JSON.parse(userInfo['user']);
            setUserDetails(user);
            localStorage.setItem("userType", user.UserType);
            const finalData = calculateInitials(
              user?.FirstName ? user?.FirstName.substring(0, 1).toUpperCase() : '',
              user?.LastName ? user?.LastName.substring(0, 1).toUpperCase() : ''
            );
            initialsData = finalData;
          }
        }
      } catch (error) {
        console.error('Failed to fetch user details:', error);
        userManager.signoutRedirect();
      } finally {
        setIsLoading(false);
      }
    };
  
    startTransition(() => {
      initializeUser();
    });
  }, [loggedOut]);
  
  const GetUserRoles = async () => {
    try {
      const rolesData = await getRolesByUser();
      console.log(rolesData);
      const dist = rolesData.reduce((acc: { [x: string]: any[]; }, curr: { AccountId: string | number; RoleName: any; }) => {
        if (!acc[curr.AccountId]) {
          acc[curr.AccountId] = [];
        }
        if (!acc[curr.AccountId].includes(curr.RoleName)) {
          acc[curr.AccountId].push(curr.RoleName);
        }
        return acc;
      }, {});

      setUserRoles(dist);
              
      
    } catch (e) {
      
    }
  }
  
  




  const signOut = () => {
   
    const logeduserType = localStorage.getItem('userType');
   setLoggedOut(true);
   startTransition(() => {
     setUser(null);
     setUserDetails(null);
     localStorage.clear();
     document.cookie.split(";").forEach(cookie => {
       const [name] = cookie.split("=");
       document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
     });
     localStorage.setItem('logout-event', Date.now().toString());
     if (logeduserType?.toLowerCase() !== "superadmin") {
       navigate("/login");
     } else {
       navigate("/admin/adminlogin");
     }
   });
};

  

  return (
    <AuthContext.Provider value={{ user, userDetails, signOut,loggedOut,userRoles,isLoading }}>
      {children}
    </AuthContext.Provider>
    );
    
};

export const useAuth = (): AuthContextType => {
  let context = useContext(AuthContext);

  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  if (context?.user) {
    const userInfo = JSON.parse(localStorage.getItem("userInfo") || '{}');
    userLocalData["oidc-info"] = JSON.stringify(context.user);    

    if (userInfo['user']) {
      userLocalData["user"] =  JSON.stringify(userInfo['user'])
    }   
  } 
  else {
    const userInfo = JSON.parse(localStorage.getItem("userInfo") || '{}');
    if (userInfo['oidc-info'] && userInfo['user']) {
      context = { ...context, user: JSON.parse(userInfo['oidc-info']) };
      context.userDetails =  JSON.parse(userInfo['user'])
      context.loggedOut = false;
    }
  }

  return context;
};

export {initialsData}