import React, { useState, Suspense } from 'react';
import { Box, Typography, Container, CircularProgress, Button } from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  FilterList,
  CalendarToday,
  People,
  Star
} from '@mui/icons-material';
import Card from '../common/Card';
import {
  getFeedbackAnalyticsWithStoredAccount,
  FeedbackAnalyticsResponse,
  getGuideAnalyticsWithStoredAccount,
  GuideAnalyticsResponse
} from '../../services/DashboardService';

const DashboardContainer = styled(Box)({
  backgroundColor: '#f8fafc',
  minHeight: '100vh',
});

const FilterButton = styled(Button)({
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
  minWidth: '140px',
  justifyContent: 'space-between',
  padding: '8px 12px',
  backgroundColor: 'transparent',
  color: '#3b82f6',
  border: '1px solid #3b82f6',
  borderRadius: '8px',
  textTransform: 'none',
  fontSize: '14px',
  fontWeight: 'medium',
  '&:hover': {
    backgroundColor: '#eff6ff',
    borderColor: '#2563eb',
    color: '#2563eb',
  },
});

const ModernDashboard: React.FC = () => {
  const [timeFilter, setTimeFilter] = useState('7d');
  const [timeFilterOpen, setTimeFilterOpen] = useState(false);
  const [feedbackData, setFeedbackData] = useState<FeedbackAnalyticsResponse | null>(null);
  const [guideData, setGuideData] = useState<GuideAnalyticsResponse | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const timeFilterOptions = [
    { label: 'Last 7 days', value: '7d' },
    { label: 'Last 30 days', value: '30d' },
    { label: 'Last 90 days', value: '90d' },
    { label: 'Last year', value: '1y' },
  ];

  // Convert timeFilter to days
  const getDaysFromFilter = (filter: string): number => {
    switch (filter) {
      case '7d': return 7;
      case '30d': return 30;
      case '90d': return 90;
      case '1y': return 365;
      default: return 7;
    }
  };

  // Fetch both feedback and guide analytics data
  React.useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      setFeedbackData(null);
      setGuideData(null);
      try {
        const days = getDaysFromFilter(timeFilter);

        // Fetch both APIs in parallel
        const [feedbackResponse, guideResponse] = await Promise.all([
          getFeedbackAnalyticsWithStoredAccount(days),
          getGuideAnalyticsWithStoredAccount(days)
        ]);

        setFeedbackData(feedbackResponse);
        setGuideData(guideResponse);
      } catch (err) {
        setError('Failed to fetch analytics data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [timeFilter]);

  return (
    <div className='qadpt-web'>
      <div className='qadpt-webcontent'>
        <DashboardContainer>
          <Container maxWidth="xl" sx={{ py: 3 }}>
            {/* Header Section */}
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              mb: 4,
              pb: 3,
              borderBottom: '1px solid #e2e8f0'
            }}>
              {/* Left Side - Title and Subtitle */}
              <Box>
                <Typography variant="h4" fontWeight="bold" sx={{ color: '#1e293b', mb: 0.5 }}>
                  Digital Adoption Platform
                </Typography>
                <Typography variant="body2" sx={{ color: '#64748b' }}>
                  Admin Dashboard - Guide Creation & Analytics
                </Typography>
              </Box>

              {/* Right Side - Time Filter */}
              <Box sx={{ position: 'relative' }}>
                <FilterButton
                  onClick={() => setTimeFilterOpen(!timeFilterOpen)}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <CalendarToday sx={{ fontSize: '16px' }} />
                    <Typography variant="body2">
                      {timeFilterOptions.find(opt => opt.value === timeFilter)?.label}
                    </Typography>
                  </Box>
                  <FilterList sx={{ fontSize: '16px' }} />
                </FilterButton>

                {/* Dropdown Menu */}
                {timeFilterOpen && (
                  <Box sx={{
                    position: 'absolute',
                    top: '100%',
                    right: 0,
                    mt: 1,
                    minWidth: '200px',
                    backgroundColor: 'white',
                    borderRadius: 'var(--radius-md)',
                    border: '1px solid #e2e8f0',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                    zIndex: 1000
                  }}>
                    {timeFilterOptions.map((option, index) => {
                      const isSelected = timeFilter === option.value;
                      return (
                        <Box
                          key={option.value}
                          onClick={(e) => {
                            e.stopPropagation();
                            setTimeFilter(option.value);
                            setTimeFilterOpen(false);
                          }}
                          sx={{
                            p: 2,
                            cursor: 'pointer',
                            backgroundColor: isSelected ? '#f1f5f9' : 'transparent',
                            color: isSelected ? '#1e293b' : '#64748b',
                            fontWeight: isSelected ? 'medium' : 'normal',
                            borderRadius: index === 0 ? 'var(--radius-md) var(--radius-md) 0 0' :
                                         index === timeFilterOptions.length - 1 ? '0 0 var(--radius-md) var(--radius-md)' : '0',
                            '&:hover': {
                              backgroundColor: isSelected ? '#f1f5f9' : '#f8fafc'
                            }
                          }}
                        >
                          <Typography variant="body2">
                            {option.label}
                          </Typography>
                        </Box>
                      );
                    })}
                  </Box>
                )}
              </Box>
            </Box>

            {/* Combined Dashboard Content */}

            {/* From Overview Tab: Active Users, Dona Interactions, Overall Satisfaction */}
            {/* TODO: These metric cards need real API data - currently using static placeholders */}
            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: 2, mb: 3 }}>
              {/* Active Users */}
              <Box sx={{
                p: 3,
                backgroundColor: '#eff6ff',
                borderRadius: '8px',
                border: '1px solid #bfdbfe',
                textAlign: 'left',
                minHeight: '120px',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                }
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body2" sx={{ color: '#64748b', fontSize: '13px', fontWeight: 'medium' }}>
                    Active Users
                  </Typography>
                  <Box sx={{
                    width: 20,
                    height: 20,
                    backgroundColor: '#3b82f6',
                    borderRadius: '4px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <People sx={{ fontSize: '12px', color: 'white' }} />
                  </Box>
                </Box>
                <Typography variant="h3" fontWeight="bold" sx={{ color: '#1e293b', mb: 1, fontSize: '28px' }}>
                  --
                </Typography>
                <Typography variant="caption" sx={{ color: '#64748b', fontSize: '11px' }}>
                  API data needed
                </Typography>
              </Box>

              {/* Dona Interactions */}
              <Box sx={{
                p: 3,
                backgroundColor: '#faf5ff',
                borderRadius: '8px',
                border: '1px solid #e9d5ff',
                textAlign: 'left',
                minHeight: '120px',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                }
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body2" sx={{ color: '#64748b', fontSize: '13px', fontWeight: 'medium' }}>
                    Dona Interactions
                  </Typography>
                  <Box sx={{
                    width: 20,
                    height: 20,
                    backgroundColor: '#a855f7',
                    borderRadius: '4px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Typography sx={{ fontSize: '12px', color: 'white' }}>💬</Typography>
                  </Box>
                </Box>
                <Typography variant="h3" fontWeight="bold" sx={{ color: '#1e293b', mb: 1, fontSize: '28px' }}>
                  --
                </Typography>
                <Typography variant="caption" sx={{ color: '#64748b', fontSize: '11px' }}>
                  API data needed
                </Typography>
              </Box>

              {/* Overall Satisfaction */}
              <Box sx={{
                p: 3,
                backgroundColor: '#fffbeb',
                borderRadius: '8px',
                border: '1px solid #fed7aa',
                textAlign: 'left',
                minHeight: '120px',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                }
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body2" sx={{ color: '#64748b', fontSize: '13px', fontWeight: 'medium' }}>
                    Overall Satisfaction
                  </Typography>
                  <Box sx={{
                    width: 20,
                    height: 20,
                    backgroundColor: '#f59e0b',
                    borderRadius: '4px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Star sx={{ fontSize: '12px', color: 'white' }} />
                  </Box>
                </Box>
                <Typography variant="h3" fontWeight="bold" sx={{ color: '#1e293b', mb: 1, fontSize: '28px' }}>
                  --
                </Typography>
                <Typography variant="caption" sx={{ color: '#64748b', fontSize: '11px' }}>
                  API data needed
                </Typography>
              </Box>


            </Box>

            {/* Row 2: Analytics Overview (full width) */}
            {/* ✅ USING REAL API DATA - guideData.GuideTypes */}
            <Box sx={{ mb: 4 }}>
              <Card title="Analytics Overview" subtitle="Select a guide type to view detailed analytics" padding="lg">
                {loading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
                    <Typography variant="body2" color="text.secondary">Loading...</Typography>
                  </Box>
                ) : error ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
                    <Typography variant="body2" color="error">{error}</Typography>
                  </Box>
                ) : guideData?.GuideTypes ? (
                  <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: 3, mb: 4 }}>
                    {/* Tours */}
                    <Box sx={{
                      p: 3,
                      border: '1px solid #e2e8f0',
                      borderRadius: '12px',
                      backgroundColor: 'white',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                      }
                    }}>
                      <Box sx={{  display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
                        <Box sx={{
                          width: 20,
                          height: 20,
                          borderRadius: '4px',
                          backgroundColor: '#3b82f6',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}>
                          <Typography sx={{ fontSize: '10px', color: 'white' }}>🎯</Typography>
                        </Box>
                        <Typography variant="h6" fontWeight="bold" sx={{ color: '#1e293b', fontSize: '14px' }}>
                          Tours
                        </Typography>
                      </Box>

                      {/* Status Indicators */}
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', gap: 1, mb: 2 }}>
                        <Box sx={{
                          textAlign: 'center',
                          flex: 1,
                          p: 1,
                          backgroundColor: '#f0fdf4',
                          borderRadius: '6px',
                          border: '1px solid #bbf7d0'
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5, mb: 0.5 }}>
                            <Box sx={{ width: 6, height: 6, borderRadius: '50%', backgroundColor: '#22c55e' }} />
                            <Typography variant="caption" sx={{ fontSize: '9px', color: '#16a34a', fontWeight: 'medium' }}>
                              Active
                            </Typography>
                          </Box>
                          <Typography variant="body2" sx={{ fontSize: '12px', color: '#16a34a', fontWeight: 'bold' }}>
                            {guideData.GuideTypes.Tours.Active}
                          </Typography>
                        </Box>
                        <Box sx={{
                          textAlign: 'center',
                          flex: 1,
                          p: 1,
                          backgroundColor: '#fffbeb',
                          borderRadius: '6px',
                          border: '1px solid #fed7aa'
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5, mb: 0.5 }}>
                            <Box sx={{ width: 6, height: 6, borderRadius: '50%', backgroundColor: '#f59e0b' }} />
                            <Typography variant="caption" sx={{ fontSize: '9px', color: '#d97706', fontWeight: 'medium' }}>
                              Draft
                            </Typography>
                          </Box>
                          <Typography variant="body2" sx={{ fontSize: '12px', color: '#d97706', fontWeight: 'bold' }}>
                            {guideData.GuideTypes.Tours.Draft}
                          </Typography>
                        </Box>
                        <Box sx={{
                          textAlign: 'center',
                          flex: 1,
                          p: 1,
                          backgroundColor: '#f8fafc',
                          borderRadius: '6px',
                          border: '1px solid #e2e8f0'
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5, mb: 0.5 }}>
                            <Box sx={{ width: 6, height: 6, borderRadius: '50%', backgroundColor: '#94a3b8' }} />
                            <Typography variant="caption" sx={{ fontSize: '9px', color: '#64748b', fontWeight: 'medium' }}>
                              Inactive
                            </Typography>
                          </Box>
                          <Typography variant="body2" sx={{ fontSize: '12px', color: '#64748b', fontWeight: 'bold' }}>
                            {guideData.GuideTypes.Tours.InActive}
                          </Typography>
                        </Box>
                      </Box>

                      {/* Trend */}
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Typography variant="caption" sx={{ color: '#64748b', fontSize: '10px' }}>
                          Trend
                        </Typography>
                        <Typography variant="caption" sx={{
                          color: guideData.GuideTypes.Tours.Trend >= 0 ? '#22c55e' : '#ef4444',
                          fontSize: '10px',
                          fontWeight: 'medium',
                          display: 'flex',
                          alignItems: 'center',
                          gap: 0.5
                        }}>
                          {guideData.GuideTypes.Tours.Trend >= 0 ? '↗' : '↘'} {Math.abs(guideData.GuideTypes.Tours.Trend)}%
                        </Typography>
                      </Box>
                    </Box>

                    {/* Banners */}
                    <Box sx={{
                      p: 3,
                      border: '1px solid #e2e8f0',
                      borderRadius: '12px',
                      backgroundColor: 'white',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                      }
                    }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
                        <Box sx={{
                          width: 20,
                          height: 20,
                          borderRadius: '4px',
                          backgroundColor: '#ef4444',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}>
                          <Typography sx={{ fontSize: '10px', color: 'white' }}>📢</Typography>
                        </Box>
                        <Typography variant="h6" fontWeight="bold" sx={{ color: '#1e293b', fontSize: '14px' }}>
                          Banners
                        </Typography>
                      </Box>

                      {/* Status Indicators */}
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', gap: 1, mb: 2 }}>
                        <Box sx={{
                          textAlign: 'center',
                          flex: 1,
                          p: 1,
                          backgroundColor: '#f0fdf4',
                          borderRadius: '6px',
                          border: '1px solid #bbf7d0'
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5, mb: 0.5 }}>
                            <Box sx={{ width: 6, height: 6, borderRadius: '50%', backgroundColor: '#22c55e' }} />
                            <Typography variant="caption" sx={{ fontSize: '9px', color: '#16a34a', fontWeight: 'medium' }}>
                              Active
                            </Typography>
                          </Box>
                          <Typography variant="body2" sx={{ fontSize: '12px', color: '#16a34a', fontWeight: 'bold' }}>
                            {guideData.GuideTypes.Banners.Active}
                          </Typography>
                        </Box>
                        <Box sx={{
                          textAlign: 'center',
                          flex: 1,
                          p: 1,
                          backgroundColor: '#fffbeb',
                          borderRadius: '6px',
                          border: '1px solid #fed7aa'
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5, mb: 0.5 }}>
                            <Box sx={{ width: 6, height: 6, borderRadius: '50%', backgroundColor: '#f59e0b' }} />
                            <Typography variant="caption" sx={{ fontSize: '9px', color: '#d97706', fontWeight: 'medium' }}>
                              Draft
                            </Typography>
                          </Box>
                          <Typography variant="body2" sx={{ fontSize: '12px', color: '#d97706', fontWeight: 'bold' }}>
                            {guideData.GuideTypes.Banners.Draft}
                          </Typography>
                        </Box>
                        <Box sx={{
                          textAlign: 'center',
                          flex: 1,
                          p: 1,
                          backgroundColor: '#f8fafc',
                          borderRadius: '6px',
                          border: '1px solid #e2e8f0'
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5, mb: 0.5 }}>
                            <Box sx={{ width: 6, height: 6, borderRadius: '50%', backgroundColor: '#94a3b8' }} />
                            <Typography variant="caption" sx={{ fontSize: '9px', color: '#64748b', fontWeight: 'medium' }}>
                              Inactive
                            </Typography>
                          </Box>
                          <Typography variant="body2" sx={{ fontSize: '12px', color: '#64748b', fontWeight: 'bold' }}>
                            {guideData.GuideTypes.Banners.InActive}
                          </Typography>
                        </Box>
                      </Box>

                      {/* Trend */}
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Typography variant="caption" sx={{ color: '#64748b', fontSize: '10px' }}>
                          Trend
                        </Typography>
                        <Typography variant="caption" sx={{
                          color: guideData.GuideTypes.Banners.Trend >= 0 ? '#22c55e' : '#ef4444',
                          fontSize: '10px',
                          fontWeight: 'medium',
                          display: 'flex',
                          alignItems: 'center',
                          gap: 0.5
                        }}>
                          {guideData.GuideTypes.Banners.Trend >= 0 ? '↗' : '↘'} {Math.abs(guideData.GuideTypes.Banners.Trend)}%
                        </Typography>
                      </Box>
                    </Box>

                    {/* Tooltips */}
                    <Box sx={{
                      p: 3,
                      border: '1px solid #e2e8f0',
                      borderRadius: '12px',
                      backgroundColor: 'white',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                      }
                    }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
                        <Box sx={{
                          width: 20,
                          height: 20,
                          borderRadius: '4px',
                          backgroundColor: '#f59e0b',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}>
                          <Typography sx={{ fontSize: '10px', color: 'white' }}>💡</Typography>
                        </Box>
                        <Typography variant="h6" fontWeight="bold" sx={{ color: '#1e293b', fontSize: '14px' }}>
                          Tooltips
                        </Typography>
                      </Box>

                      {/* Status Indicators */}
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', gap: 1, mb: 2 }}>
                        <Box sx={{
                          textAlign: 'center',
                          flex: 1,
                          p: 1,
                          backgroundColor: '#f0fdf4',
                          borderRadius: '6px',
                          border: '1px solid #bbf7d0'
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5, mb: 0.5 }}>
                            <Box sx={{ width: 6, height: 6, borderRadius: '50%', backgroundColor: '#22c55e' }} />
                            <Typography variant="caption" sx={{ fontSize: '9px', color: '#16a34a', fontWeight: 'medium' }}>
                              Active
                            </Typography>
                          </Box>
                          <Typography variant="body2" sx={{ fontSize: '12px', color: '#16a34a', fontWeight: 'bold' }}>
                            {guideData.GuideTypes.Tooltips.Active}
                          </Typography>
                        </Box>
                        <Box sx={{
                          textAlign: 'center',
                          flex: 1,
                          p: 1,
                          backgroundColor: '#fffbeb',
                          borderRadius: '6px',
                          border: '1px solid #fed7aa'
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5, mb: 0.5 }}>
                            <Box sx={{ width: 6, height: 6, borderRadius: '50%', backgroundColor: '#f59e0b' }} />
                            <Typography variant="caption" sx={{ fontSize: '9px', color: '#d97706', fontWeight: 'medium' }}>
                              Draft
                            </Typography>
                          </Box>
                          <Typography variant="body2" sx={{ fontSize: '12px', color: '#d97706', fontWeight: 'bold' }}>
                            {guideData.GuideTypes.Tooltips.Draft}
                          </Typography>
                        </Box>
                        <Box sx={{
                          textAlign: 'center',
                          flex: 1,
                          p: 1,
                          backgroundColor: '#f8fafc',
                          borderRadius: '6px',
                          border: '1px solid #e2e8f0'
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5, mb: 0.5 }}>
                            <Box sx={{ width: 6, height: 6, borderRadius: '50%', backgroundColor: '#94a3b8' }} />
                            <Typography variant="caption" sx={{ fontSize: '9px', color: '#64748b', fontWeight: 'medium' }}>
                              Inactive
                            </Typography>
                          </Box>
                          <Typography variant="body2" sx={{ fontSize: '12px', color: '#64748b', fontWeight: 'bold' }}>
                            {guideData.GuideTypes.Tooltips.InActive}
                          </Typography>
                        </Box>
                      </Box>

                      {/* Trend */}
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Typography variant="caption" sx={{ color: '#64748b', fontSize: '10px' }}>
                          Trend
                        </Typography>
                        <Typography variant="caption" sx={{
                          color: guideData.GuideTypes.Tooltips.Trend >= 0 ? '#22c55e' : '#ef4444',
                          fontSize: '10px',
                          fontWeight: 'medium',
                          display: 'flex',
                          alignItems: 'center',
                          gap: 0.5
                        }}>
                          {guideData.GuideTypes.Tooltips.Trend >= 0 ? '↗' : '↘'} {Math.abs(guideData.GuideTypes.Tooltips.Trend)}%
                        </Typography>
                      </Box>
                    </Box>

                    {/* Announcements */}
                    <Box sx={{
                      p: 3,
                      border: '1px solid #e2e8f0',
                      borderRadius: '12px',
                      backgroundColor: 'white',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                      }
                    }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
                        <Box sx={{
                          width: 20,
                          height: 20,
                          borderRadius: '4px',
                          backgroundColor: '#10b981',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}>
                          <Typography sx={{ fontSize: '10px', color: 'white' }}>📣</Typography>
                        </Box>
                        <Typography variant="h6" fontWeight="bold" sx={{ color: '#1e293b', fontSize: '14px' }}>
                          Announcements
                        </Typography>
                      </Box>

                      {/* Status Indicators */}
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', gap: 1, mb: 2 }}>
                        <Box sx={{
                          textAlign: 'center',
                          flex: 1,
                          p: 1,
                          backgroundColor: '#f0fdf4',
                          borderRadius: '6px',
                          border: '1px solid #bbf7d0'
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5, mb: 0.5 }}>
                            <Box sx={{ width: 6, height: 6, borderRadius: '50%', backgroundColor: '#22c55e' }} />
                            <Typography variant="caption" sx={{ fontSize: '9px', color: '#16a34a', fontWeight: 'medium' }}>
                              Active
                            </Typography>
                          </Box>
                          <Typography variant="body2" sx={{ fontSize: '12px', color: '#16a34a', fontWeight: 'bold' }}>
                            {guideData.GuideTypes.Announcements.Active}
                          </Typography>
                        </Box>
                        <Box sx={{
                          textAlign: 'center',
                          flex: 1,
                          p: 1,
                          backgroundColor: '#fffbeb',
                          borderRadius: '6px',
                          border: '1px solid #fed7aa'
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5, mb: 0.5 }}>
                            <Box sx={{ width: 6, height: 6, borderRadius: '50%', backgroundColor: '#f59e0b' }} />
                            <Typography variant="caption" sx={{ fontSize: '9px', color: '#d97706', fontWeight: 'medium' }}>
                              Draft
                            </Typography>
                          </Box>
                          <Typography variant="body2" sx={{ fontSize: '12px', color: '#d97706', fontWeight: 'bold' }}>
                            {guideData.GuideTypes.Announcements.Draft}
                          </Typography>
                        </Box>
                        <Box sx={{
                          textAlign: 'center',
                          flex: 1,
                          p: 1,
                          backgroundColor: '#f8fafc',
                          borderRadius: '6px',
                          border: '1px solid #e2e8f0'
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5, mb: 0.5 }}>
                            <Box sx={{ width: 6, height: 6, borderRadius: '50%', backgroundColor: '#94a3b8' }} />
                            <Typography variant="caption" sx={{ fontSize: '9px', color: '#64748b', fontWeight: 'medium' }}>
                              Inactive
                            </Typography>
                          </Box>
                          <Typography variant="body2" sx={{ fontSize: '12px', color: '#64748b', fontWeight: 'bold' }}>
                            {guideData.GuideTypes.Announcements.InActive}
                          </Typography>
                        </Box>
                      </Box>

                      {/* Trend */}
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Typography variant="caption" sx={{ color: '#64748b', fontSize: '10px' }}>
                          Trend
                        </Typography>
                        <Typography variant="caption" sx={{
                          color: guideData.GuideTypes.Announcements.Trend >= 0 ? '#22c55e' : '#ef4444',
                          fontSize: '10px',
                          fontWeight: 'medium',
                          display: 'flex',
                          alignItems: 'center',
                          gap: 0.5
                        }}>
                          {guideData.GuideTypes.Announcements.Trend >= 0 ? '↗' : '↘'} {Math.abs(guideData.GuideTypes.Announcements.Trend)}%
                        </Typography>
                      </Box>
                    </Box>

                    {/* Checklists */}
                    <Box sx={{
                      p: 3,
                      border: '1px solid #e2e8f0',
                      borderRadius: '12px',
                      backgroundColor: 'white',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                      }
                    }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
                        <Box sx={{
                          width: 20,
                          height: 20,
                          borderRadius: '4px',
                          backgroundColor: '#06b6d4',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}>
                          <Typography sx={{ fontSize: '10px', color: 'white' }}>✅</Typography>
                        </Box>
                        <Typography variant="h6" fontWeight="bold" sx={{ color: '#1e293b', fontSize: '14px' }}>
                          Checklists
                        </Typography>
                      </Box>

                      {/* Status Indicators */}
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', gap: 1, mb: 2 }}>
                        <Box sx={{
                          textAlign: 'center',
                          flex: 1,
                          p: 1,
                          backgroundColor: '#f0fdf4',
                          borderRadius: '6px',
                          border: '1px solid #bbf7d0'
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5, mb: 0.5 }}>
                            <Box sx={{ width: 6, height: 6, borderRadius: '50%', backgroundColor: '#22c55e' }} />
                            <Typography variant="caption" sx={{ fontSize: '9px', color: '#16a34a', fontWeight: 'medium' }}>
                              Active
                            </Typography>
                          </Box>
                          <Typography variant="body2" sx={{ fontSize: '12px', color: '#16a34a', fontWeight: 'bold' }}>
                            {guideData.GuideTypes.Checklists.Active}
                          </Typography>
                        </Box>
                        <Box sx={{
                          textAlign: 'center',
                          flex: 1,
                          p: 1,
                          backgroundColor: '#fffbeb',
                          borderRadius: '6px',
                          border: '1px solid #fed7aa'
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5, mb: 0.5 }}>
                            <Box sx={{ width: 6, height: 6, borderRadius: '50%', backgroundColor: '#f59e0b' }} />
                            <Typography variant="caption" sx={{ fontSize: '9px', color: '#d97706', fontWeight: 'medium' }}>
                              Draft
                            </Typography>
                          </Box>
                          <Typography variant="body2" sx={{ fontSize: '12px', color: '#d97706', fontWeight: 'bold' }}>
                            {guideData.GuideTypes.Checklists.Draft}
                          </Typography>
                        </Box>
                        <Box sx={{
                          textAlign: 'center',
                          flex: 1,
                          p: 1,
                          backgroundColor: '#f8fafc',
                          borderRadius: '6px',
                          border: '1px solid #e2e8f0'
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5, mb: 0.5 }}>
                            <Box sx={{ width: 6, height: 6, borderRadius: '50%', backgroundColor: '#94a3b8' }} />
                            <Typography variant="caption" sx={{ fontSize: '9px', color: '#64748b', fontWeight: 'medium' }}>
                              Inactive
                            </Typography>
                          </Box>
                          <Typography variant="body2" sx={{ fontSize: '12px', color: '#64748b', fontWeight: 'bold' }}>
                            {guideData.GuideTypes.Checklists.InActive}
                          </Typography>
                        </Box>
                      </Box>

                      {/* Trend */}
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Typography variant="caption" sx={{ color: '#64748b', fontSize: '10px' }}>
                          Trend
                        </Typography>
                        <Typography variant="caption" sx={{
                          color: guideData.GuideTypes.Checklists.Trend >= 0 ? '#22c55e' : '#ef4444',
                          fontSize: '10px',
                          fontWeight: 'medium',
                          display: 'flex',
                          alignItems: 'center',
                          gap: 0.5
                        }}>
                          {guideData.GuideTypes.Checklists.Trend >= 0 ? '↗' : '↘'} {Math.abs(guideData.GuideTypes.Checklists.Trend)}%
                        </Typography>
                      </Box>
                    </Box>

                    {/* Hotspots */}
                    <Box sx={{
                      p: 3,
                      border: '1px solid #e2e8f0',
                      borderRadius: '12px',
                      backgroundColor: 'white',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                      }
                    }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
                        <Box sx={{
                          width: 20,
                          height: 20,
                          borderRadius: '4px',
                          backgroundColor: '#8b5cf6',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}>
                          <Typography sx={{ fontSize: '10px', color: 'white' }}>📍</Typography>
                        </Box>
                        <Typography variant="h6" fontWeight="bold" sx={{ color: '#1e293b', fontSize: '14px' }}>
                          Hotspots
                        </Typography>
                      </Box>

                      {/* Status Indicators */}
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', gap: 1, mb: 2 }}>
                        <Box sx={{
                          textAlign: 'center',
                          flex: 1,
                          p: 1,
                          backgroundColor: '#f0fdf4',
                          borderRadius: '6px',
                          border: '1px solid #bbf7d0'
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5, mb: 0.5 }}>
                            <Box sx={{ width: 6, height: 6, borderRadius: '50%', backgroundColor: '#22c55e' }} />
                            <Typography variant="caption" sx={{ fontSize: '9px', color: '#16a34a', fontWeight: 'medium' }}>
                              Active
                            </Typography>
                          </Box>
                          <Typography variant="body2" sx={{ fontSize: '12px', color: '#16a34a', fontWeight: 'bold' }}>
                            {guideData.GuideTypes.Hotspots.Active}
                          </Typography>
                        </Box>
                        <Box sx={{
                          textAlign: 'center',
                          flex: 1,
                          p: 1,
                          backgroundColor: '#fffbeb',
                          borderRadius: '6px',
                          border: '1px solid #fed7aa'
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5, mb: 0.5 }}>
                            <Box sx={{ width: 6, height: 6, borderRadius: '50%', backgroundColor: '#f59e0b' }} />
                            <Typography variant="caption" sx={{ fontSize: '9px', color: '#d97706', fontWeight: 'medium' }}>
                              Draft
                            </Typography>
                          </Box>
                          <Typography variant="body2" sx={{ fontSize: '12px', color: '#d97706', fontWeight: 'bold' }}>
                            {guideData.GuideTypes.Hotspots.Draft}
                          </Typography>
                        </Box>
                        <Box sx={{
                          textAlign: 'center',
                          flex: 1,
                          p: 1,
                          backgroundColor: '#f8fafc',
                          borderRadius: '6px',
                          border: '1px solid #e2e8f0'
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5, mb: 0.5 }}>
                            <Box sx={{ width: 6, height: 6, borderRadius: '50%', backgroundColor: '#94a3b8' }} />
                            <Typography variant="caption" sx={{ fontSize: '9px', color: '#64748b', fontWeight: 'medium' }}>
                              Inactive
                            </Typography>
                          </Box>
                          <Typography variant="body2" sx={{ fontSize: '12px', color: '#64748b', fontWeight: 'bold' }}>
                            {guideData.GuideTypes.Hotspots.InActive}
                          </Typography>
                        </Box>
                      </Box>

                      {/* Trend */}
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Typography variant="caption" sx={{ color: '#64748b', fontSize: '10px' }}>
                          Trend
                        </Typography>
                        <Typography variant="caption" sx={{
                          color: guideData.GuideTypes.Hotspots.Trend >= 0 ? '#22c55e' : '#ef4444',
                          fontSize: '10px',
                          fontWeight: 'medium',
                          display: 'flex',
                          alignItems: 'center',
                          gap: 0.5
                        }}>
                          {guideData.GuideTypes.Hotspots.Trend >= 0 ? '↗' : '↘'} {Math.abs(guideData.GuideTypes.Hotspots.Trend)}%
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                ) : (
                  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
                    <Typography variant="body2" color="text.secondary">No guide data available</Typography>
                  </Box>
                )}
              </Card>
            </Box>

            {/* Row 3: User Activity Trends and User Satisfaction Ratings */}
            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 'var(--spacing-6)', mb: 4 }}>
              {/* User Activity Trends */}
              <Card title="📈 User Activity Trends" subtitle="Active, retained, and total users over time" padding="lg">
                {/* TODO: This chart needs real API data - currently static placeholder */}
                <Box sx={{
                  height: '300px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: '#f8fafc',
                  borderRadius: 'var(--radius-md)',
                  border: '2px dashed #e2e8f0'
                }}>
                  <Typography variant="h6" sx={{ color: '#64748b', mb: 2 }}>
                    📊 Chart Placeholder
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#64748b', textAlign: 'center', maxWidth: '300px' }}>
                    User Activity Trends chart needs to be connected to real API data for Active, Retained, and Total users over time.
                  </Typography>
                  <Box sx={{ mt: 3, display: 'flex', gap: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box sx={{ width: 12, height: 12, backgroundColor: '#3b82f6', borderRadius: '50%' }} />
                      <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b' }}>Active</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box sx={{ width: 12, height: 12, backgroundColor: '#10b981', borderRadius: '50%' }} />
                      <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b' }}>Retained</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box sx={{ width: 12, height: 12, backgroundColor: '#8b5cf6', borderRadius: '50%' }} />
                      <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b' }}>Total</Typography>
                    </Box>
                  </Box>
                </Box>
              </Card>

              {/* User Satisfaction Ratings */}
              {/* ✅ USING REAL API DATA - feedbackData.Feedback */}
              <Card title="⭐ User Satisfaction Ratings" padding="lg">
                {loading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
                    <Typography variant="body2" color="text.secondary">Loading...</Typography>
                  </Box>
                ) : error ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
                    <Typography variant="body2" color="error">{error}</Typography>
                  </Box>
                ) : feedbackData ? (
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>
                    {/* Excellent (5★) */}
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
                        <Box sx={{ width: 8, height: 8, backgroundColor: '#10b981', borderRadius: '50%' }} />
                        <Typography variant="body2" fontWeight="medium">
                          Excellent (5★)
                        </Typography>
                      </Box>
                      <Box sx={{
                        flex: 1,
                        height: 8,
                        backgroundColor: 'var(--color-gray-200)',
                        borderRadius: 'var(--radius-full)',
                        overflow: 'hidden',
                        mr: 2
                      }}>
                        <Box sx={{
                          width: `${(feedbackData.Feedback.Excellent / (feedbackData.Feedback.Excellent + feedbackData.Feedback.Good + feedbackData.Feedback.Average + feedbackData.Feedback.Poor + feedbackData.Feedback.VeryPoor)) * 100}%`,
                          height: '100%',
                          backgroundColor: '#10b981',
                          borderRadius: 'var(--radius-full)'
                        }} />
                      </Box>
                      <Typography variant="body2" fontWeight="bold" sx={{ minWidth: 40, textAlign: 'right' }}>
                        {feedbackData.Feedback.Excellent}
                      </Typography>
                    </Box>

                    {/* Good (4★) */}
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
                        <Box sx={{ width: 8, height: 8, backgroundColor: '#84cc16', borderRadius: '50%' }} />
                        <Typography variant="body2" fontWeight="medium">
                          Good (4★)
                        </Typography>
                      </Box>
                      <Box sx={{
                        flex: 1,
                        height: 8,
                        backgroundColor: 'var(--color-gray-200)',
                        borderRadius: 'var(--radius-full)',
                        overflow: 'hidden',
                        mr: 2
                      }}>
                        <Box sx={{
                          width: `${(feedbackData.Feedback.Good / (feedbackData.Feedback.Excellent + feedbackData.Feedback.Good + feedbackData.Feedback.Average + feedbackData.Feedback.Poor + feedbackData.Feedback.VeryPoor)) * 100}%`,
                          height: '100%',
                          backgroundColor: '#84cc16',
                          borderRadius: 'var(--radius-full)'
                        }} />
                      </Box>
                      <Typography variant="body2" fontWeight="bold" sx={{ minWidth: 40, textAlign: 'right' }}>
                        {feedbackData.Feedback.Good}
                      </Typography>
                    </Box>

                    {/* Average (3★) */}
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
                        <Box sx={{ width: 8, height: 8, backgroundColor: '#f59e0b', borderRadius: '50%' }} />
                        <Typography variant="body2" fontWeight="medium">
                          Average (3★)
                        </Typography>
                      </Box>
                      <Box sx={{
                        flex: 1,
                        height: 8,
                        backgroundColor: 'var(--color-gray-200)',
                        borderRadius: 'var(--radius-full)',
                        overflow: 'hidden',
                        mr: 2
                      }}>
                        <Box sx={{
                          width: `${(feedbackData.Feedback.Average / (feedbackData.Feedback.Excellent + feedbackData.Feedback.Good + feedbackData.Feedback.Average + feedbackData.Feedback.Poor + feedbackData.Feedback.VeryPoor)) * 100}%`,
                          height: '100%',
                          backgroundColor: '#f59e0b',
                          borderRadius: 'var(--radius-full)'
                        }} />
                      </Box>
                      <Typography variant="body2" fontWeight="bold" sx={{ minWidth: 40, textAlign: 'right' }}>
                        {feedbackData.Feedback.Average}
                      </Typography>
                    </Box>

                    {/* Poor (2★) */}
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
                        <Box sx={{ width: 8, height: 8, backgroundColor: '#f97316', borderRadius: '50%' }} />
                        <Typography variant="body2" fontWeight="medium">
                          Poor (2★)
                        </Typography>
                      </Box>
                      <Box sx={{
                        flex: 1,
                        height: 8,
                        backgroundColor: 'var(--color-gray-200)',
                        borderRadius: 'var(--radius-full)',
                        overflow: 'hidden',
                        mr: 2
                      }}>
                        <Box sx={{
                          width: `${(feedbackData.Feedback.Poor / (feedbackData.Feedback.Excellent + feedbackData.Feedback.Good + feedbackData.Feedback.Average + feedbackData.Feedback.Poor + feedbackData.Feedback.VeryPoor)) * 100}%`,
                          height: '100%',
                          backgroundColor: '#f97316',
                          borderRadius: 'var(--radius-full)'
                        }} />
                      </Box>
                      <Typography variant="body2" fontWeight="bold" sx={{ minWidth: 40, textAlign: 'right' }}>
                        {feedbackData.Feedback.Poor}
                      </Typography>
                    </Box>

                    {/* Very Poor (1★) */}
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
                        <Box sx={{ width: 8, height: 8, backgroundColor: '#ef4444', borderRadius: '50%' }} />
                        <Typography variant="body2" fontWeight="medium">
                          Very Poor (1★)
                        </Typography>
                      </Box>
                      <Box sx={{
                        flex: 1,
                        height: 8,
                        backgroundColor: 'var(--color-gray-200)',
                        borderRadius: 'var(--radius-full)',
                        overflow: 'hidden',
                        mr: 2
                      }}>
                        <Box sx={{
                          width: `${(feedbackData.Feedback.VeryPoor / (feedbackData.Feedback.Excellent + feedbackData.Feedback.Good + feedbackData.Feedback.Average + feedbackData.Feedback.Poor + feedbackData.Feedback.VeryPoor)) * 100}%`,
                          height: '100%',
                          backgroundColor: '#ef4444',
                          borderRadius: 'var(--radius-full)'
                        }} />
                      </Box>
                      <Typography variant="body2" fontWeight="bold" sx={{ minWidth: 40, textAlign: 'right' }}>
                        {feedbackData.Feedback.VeryPoor}
                      </Typography>
                    </Box>

                    {/* Summary Cards */}
                    <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: 2, mt: 3 }}>
                      <Box sx={{
                        p: 2,
                        backgroundColor: '#f0fdf4',
                        borderRadius: 'var(--radius-md)',
                        textAlign: 'center'
                      }}>
                        <Typography variant="h5" fontWeight="bold" color="#16a34a">
                          {feedbackData.Percentage.Positive.toFixed(1)}%
                        </Typography>
                        <Typography variant="caption" color="#16a34a">
                          Positive
                        </Typography>
                      </Box>
                      <Box sx={{
                        p: 2,
                        backgroundColor: '#fffbeb',
                        borderRadius: 'var(--radius-md)',
                        textAlign: 'center'
                      }}>
                        <Typography variant="h5" fontWeight="bold" color="#d97706">
                          {feedbackData.Percentage.Neutral.toFixed(1)}%
                        </Typography>
                        <Typography variant="caption" color="#d97706">
                          Neutral
                        </Typography>
                      </Box>
                      <Box sx={{
                        p: 2,
                        backgroundColor: '#fef2f2',
                        borderRadius: 'var(--radius-md)',
                        textAlign: 'center'
                      }}>
                        <Typography variant="h5" fontWeight="bold" color="#dc2626">
                          {feedbackData.Percentage.Negative.toFixed(1)}%
                        </Typography>
                        <Typography variant="caption" color="#dc2626">
                          Negative
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                ) : (
                  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
                    <Typography variant="body2" color="text.secondary">No data available</Typography>
                  </Box>
                )}
              </Card>
            </Box>

            {/* Row 4: Dona and Satisfaction Trend */}
            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 'var(--spacing-6)', mb: 4 }}>
              {/* Dona - TODO: This section needs real API data */}
              <Card title="💬 Dona - Q&A Specialist" padding="lg">
                <Box sx={{
                  height: '300px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: '#faf5ff',
                  borderRadius: 'var(--radius-md)',
                  border: '2px dashed #e9d5ff'
                }}>
                  <Typography variant="h6" sx={{ color: '#8b5cf6', mb: 2 }}>
                    🤖 AI Agent Placeholder
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#64748b', textAlign: 'center', maxWidth: '300px' }}>
                    Dona AI Agent metrics need to be connected to real API data for Q&A responses, success rate, and quality score.
                  </Typography>
                </Box>
              </Card>

              {/* Satisfaction Trend */}
              {/* ✅ USING REAL API DATA - feedbackData.Trend */}
              <Card title="📈 Satisfaction Trend" padding="lg">
                {loading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
                    <Typography variant="body2" color="text.secondary">Loading...</Typography>
                  </Box>
                ) : error ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
                    <Typography variant="body2" color="error">{error}</Typography>
                  </Box>
                ) : feedbackData?.Trend ? (
                  <Box sx={{
                    height: '300px',
                    width: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    backgroundColor: '#f8fafc',
                    borderRadius: 'var(--radius-md)',
                    position: 'relative',
                    overflow: 'auto',
                    '&::-webkit-scrollbar': {
                      height: '6px',
                    },
                    '&::-webkit-scrollbar-track': {
                      backgroundColor: '#f1f5f9',
                      borderRadius: '3px',
                    },
                    '&::-webkit-scrollbar-thumb': {
                      backgroundColor: '#cbd5e1',
                      borderRadius: '3px',
                      '&:hover': {
                        backgroundColor: '#94a3b8',
                      },
                    },
                  }}>
                    <SatisfactionTrendChart feedbackData={feedbackData} timeFilter={timeFilter} />
                  </Box>
                ) : (
                  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
                    <Typography variant="body2" color="text.secondary">No trend data available</Typography>
                  </Box>
                )}
              </Card>
            </Box>







          </Container>
        </DashboardContainer>
      </div>
    </div>
  );
};

// Satisfaction Trend Chart Component
interface SatisfactionTrendChartProps {
  feedbackData: FeedbackAnalyticsResponse;
  timeFilter: string;
}

const SatisfactionTrendChart: React.FC<SatisfactionTrendChartProps> = ({ feedbackData, timeFilter }) => {
  const trendEntries = Object.entries(feedbackData.Trend);

  // Validate data
  if (!trendEntries.length) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
        <Typography variant="body2" color="text.secondary">No trend data available</Typography>
      </Box>
    );
  }

  const dataPointCount = trendEntries.length;

  // Smart sizing based on data point count - better space utilization
  let chartWidth: number;
  let spacing: number;
  let labelRotation = false;

  if (dataPointCount <= 4) {
    // 7 days, 30 days (4 weeks each) - increased horizontal spacing
    chartWidth = 750; // Increased width to accommodate larger spacing
    spacing = 200; // Significantly increased spacing for better distribution
  } else if (dataPointCount <= 6) {
    // 90 days (3 months) - increased spacing with rotation
    spacing = 80; // Increased from 55 to 80
    chartWidth = 100 + (dataPointCount * spacing) + 50;
    labelRotation = true;
  } else {
    // 1 year (12 months) - increased spacing with horizontal scroll
    spacing = 65; // Increased from 45 to 65
    chartWidth = 100 + (dataPointCount * spacing) + 50;
    labelRotation = true;
  }

  const chartHeight = 200;
  const leftMargin = 40; // Reduced left margin for more chart space
  const rightMargin = 20; // Reduced right margin for more chart space
  const bottomMargin = labelRotation ? 50 : 30;

  // Function to format period labels based on time range
  const formatPeriodLabel = (period: string) => {
    if (dataPointCount > 8) {
      // For 1 year data - show very abbreviated month names
      if (period.includes('2024') || period.includes('2025')) {
        const parts = period.split(' ');
        if (parts.length >= 2) {
          const monthName = parts[0];
          const year = parts[1];
          const shortMonth = monthName.substring(0, 3); // Jan, Feb, etc.
          const shortYear = year.substring(2); // 24, 25
          return `${shortMonth}\n'${shortYear}`; // Stack year below month
        }
      }
      return period.length > 4 ? period.substring(0, 4) : period;
    } else if (dataPointCount > 4) {
      // For 90 days - show month names
      return period.length > 6 ? period.substring(0, 6) : period;
    }
    // For 7/30 days - show full names but truncate if too long
    return period.length > 8 ? period.substring(0, 8) : period;
  };

  return (
    <div style={{
      width: '100%',
      height: '100%',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      overflow: dataPointCount > 8 ? 'auto' : 'visible'
    }}>
      <svg
        width="100%" // Always use full width for better filling
        height="250"
        viewBox={`0 0 ${chartWidth} ${240 + bottomMargin}`}
        style={{
          maxWidth: '100%',
          height: '250px'
        }}
        preserveAspectRatio="xMidYMid meet"
      >
        {/* Grid lines */}
        <defs>
          <pattern
            id={`feedbackGrid-${timeFilter}`}
            width={dataPointCount <= 4 ? (chartWidth - leftMargin - rightMargin) / Math.max(dataPointCount - 1, 1) : spacing}
            height="40"
            patternUnits="userSpaceOnUse"
          >
            <path
              d={`M ${dataPointCount <= 4 ? (chartWidth - leftMargin - rightMargin) / Math.max(dataPointCount - 1, 1) : spacing} 0 L 0 0 0 40`}
              fill="none"
              stroke="#e2e8f0"
              strokeWidth="0.5"
            />
          </pattern>
        </defs>
        <rect width={chartWidth - rightMargin} height={chartHeight} fill={`url(#feedbackGrid-${timeFilter})`} x={leftMargin} />

        {/* Axes */}
        <line x1={leftMargin} y1="40" x2={leftMargin} y2={chartHeight + 20} stroke="#e2e8f0" strokeWidth="1"/>
        <line x1={leftMargin} y1={chartHeight + 20} x2={chartWidth - rightMargin} y2={chartHeight + 20} stroke="#e2e8f0" strokeWidth="1"/>

        {/* Y-axis labels */}
        {(() => {
          const maxCount = Math.max(...Object.values(feedbackData.Trend).map(t => t.TotalCount), 1);

          // Create proper Y-axis scale with unique values
          let yLabels = [];
          if (maxCount <= 5) {
            // For small values, show each integer
            for (let i = maxCount; i >= 0; i--) {
              yLabels.push({ value: i, label: i.toString() });
            }
          } else {
            // For larger values, create 5 evenly spaced labels
            const step = Math.ceil(maxCount / 4);
            const uniqueValues = new Set<number>();

            // Add 0 first
            uniqueValues.add(0);

            // Add stepped values
            for (let i = 1; i <= 4; i++) {
              const value = Math.min(i * step, maxCount);
              uniqueValues.add(value);
            }

            // Ensure max value is included
            uniqueValues.add(maxCount);

            // Convert to sorted array
            yLabels = Array.from(uniqueValues)
              .sort((a, b) => b - a) // Sort descending
              .map(value => ({ value, label: value.toString() }));
          }

          return yLabels.map((item, index) => (
            <text
              key={index}
              x={leftMargin - 5} // Adjusted for smaller left margin
              y={40 + (index * (chartHeight - 20) / Math.max(yLabels.length - 1, 1))}
              fontSize="10"
              fill="#64748b"
              textAnchor="end"
              dominantBaseline="middle"
            >
              {item.label}
            </text>
          ));
        })()}

        {/* X-axis labels and trend visualization */}
        {trendEntries.map(([period, data], index) => {
          let xPos: number;
          if (dataPointCount <= 4) {
            // For 7-day view, distribute points evenly across the available width
            const availableChartWidth = chartWidth - leftMargin - rightMargin;
            xPos = leftMargin + (index * (availableChartWidth / Math.max(dataPointCount - 1, 1)));
          } else {
            xPos = leftMargin + (index * spacing);
          }

          const maxCount = Math.max(...Object.values(feedbackData.Trend).map(t => t.TotalCount), 1);
          const yPos = chartHeight + 20 - ((data.TotalCount / maxCount) * (chartHeight - 20));

          // Format period label based on data range
          const displayPeriod = formatPeriodLabel(period);

          return (
            <g key={period}>
              {/* X-axis label */}
              {dataPointCount > 8 && displayPeriod.includes('\n') ? (
                // Multi-line label for 1-year view
                <>
                  <text
                    x={xPos}
                    y={chartHeight + 35}
                    fontSize="9"
                    fill="#64748b"
                    textAnchor="middle"
                    fontWeight="bold"
                  >
                    {displayPeriod.split('\n')[0]}
                  </text>
                  <text
                    x={xPos}
                    y={chartHeight + 45}
                    fontSize="8"
                    fill="#64748b"
                    textAnchor="middle"
                    fontWeight="bold"
                  >
                    {displayPeriod.split('\n')[1]}
                  </text>
                </>
              ) : (
                // Single line label for other views
                <text
                  x={xPos}
                  y={chartHeight + 35}
                  fontSize={dataPointCount > 8 ? "8" : "11"}
                  fill="#64748b"
                  textAnchor="middle"
                  fontWeight="bold"
                  transform={labelRotation && !displayPeriod.includes('\n') ? `rotate(-45, ${xPos}, ${chartHeight + 35})` : ''}
                >
                  {displayPeriod.replace('\n', ' ')}
                </text>
              )}

              {/* Data point */}
              <circle
                cx={xPos}
                cy={yPos}
                r={dataPointCount > 8 ? "4" : "5"}
                fill={
                  data.TrendIndicator === 'increase' ? '#10b981' :
                  data.TrendIndicator === 'decrease' ? '#ef4444' : '#64748b'
                }
                stroke="white"
                strokeWidth="2"
              />

              {/* Value label - only show if space allows */}
              {(dataPointCount <= 8 || data.TotalCount > 0) && (
                <text
                  x={xPos}
                  y={yPos - 12}
                  fontSize={dataPointCount > 8 ? "8" : "9"}
                  fill={
                    data.TrendIndicator === 'increase' ? '#10b981' :
                    data.TrendIndicator === 'decrease' ? '#ef4444' : '#64748b'
                  }
                  textAnchor="middle"
                  fontWeight="bold"
                >
                  {data.TotalCount}
                </text>
              )}

              {/* Trend indicator - simplified for 1 year view */}
              {dataPointCount <= 8 && (
                <text
                  x={xPos}
                  y={yPos + 20}
                  fontSize="8"
                  fill={
                    data.TrendIndicator === 'increase' ? '#10b981' :
                    data.TrendIndicator === 'decrease' ? '#ef4444' : '#64748b'
                  }
                  textAnchor="middle"
                >
                  {data.TrendIndicator === 'increase' ? '↗' :
                   data.TrendIndicator === 'decrease' ? '↘' : '→'}
                  {Math.abs(data.ChangePercentage)}%
                </text>
              )}
                  {/* Connect points with lines if not the first point */}
            {index > 0 && (
              <line
                x1={dataPointCount <= 4 ?
                  leftMargin + ((index - 1) * ((chartWidth - leftMargin - rightMargin) / Math.max(dataPointCount - 1, 1))) :
                  leftMargin + ((index - 1) * spacing)
                }
                y1={chartHeight + 20 - ((trendEntries[index - 1][1].TotalCount / maxCount) * (chartHeight - 20))}
                x2={xPos}
                y2={yPos}
                stroke="#94a3b8"
                strokeWidth="2"
                strokeLinecap="round"
              />
            )}
            </g>
          );
        })}
      </svg>

      {/* Add summary info for 1-year view */}
      {dataPointCount > 8 && (
        <Box sx={{
          position: 'absolute',
          bottom: 5,
          left: '50%',
          transform: 'translateX(-50%)',
          display: 'flex',
          gap: 1.5, // Reduced gap
          backgroundColor: 'rgba(255,255,255,0.95)',
          padding: '4px 8px', // Reduced padding
          borderRadius: '4px',
          border: '1px solid #e2e8f0',
          fontSize: '10px'
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Box sx={{ width: 6, height: 6, backgroundColor: '#10b981', borderRadius: '50%' }} />
            <Typography variant="caption" sx={{ fontSize: '9px' }}>Inc</Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Box sx={{ width: 6, height: 6, backgroundColor: '#ef4444', borderRadius: '50%' }} />
            <Typography variant="caption" sx={{ fontSize: '9px' }}>Dec</Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Box sx={{ width: 6, height: 6, backgroundColor: '#64748b', borderRadius: '50%' }} />
            <Typography variant="caption" sx={{ fontSize: '9px' }}>Stable</Typography>
          </Box>
        </Box>
      )}
    </div>
  );
};

export default ModernDashboard;