{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptWeb\\\\src\\\\components\\\\account\\\\AccountCreate.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { v4 as uuidv4 } from \"uuid\";\nimport { getAllOrganizations } from \"../../services/OrganizationService\";\nimport { SubmitCreateAccount } from \"../../services/AccountService\";\nimport { TextField, Button, Snackbar, Alert, Grid, FormControl } from \"@mui/material\";\nimport { useSnackbar } from \"../../SnackbarContext\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useAuth } from \"../auth/AuthProvider\";\nimport { useTranslation } from \"react-i18next\";\nimport { useRtl } from \"../../RtlContext\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CreateAccount = props => {\n  _s();\n  var _userDetails$Organiza;\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    setLoading,\n    setModels,\n    showPopup,\n    setShowPopup,\n    orderByField,\n    filters\n  } = props;\n  // const [showPopup, setShowPopup] = useState(false);\n  const [snackbarOpen, setSnackbarOpen] = useState(false);\n  const [snackbarMessage, setSnackbarMessage] = useState(\"\");\n  const [snackbarSeverity, setSnackbarSeverity] = useState(\"success\");\n  const {\n    signOut,\n    userDetails\n  } = useAuth();\n  const [OrganizationId, setOrganizationId] = useState((_userDetails$Organiza = userDetails === null || userDetails === void 0 ? void 0 : userDetails.OrganizationId) !== null && _userDetails$Organiza !== void 0 ? _userDetails$Organiza : \"\");\n  const [skip, setskip] = useState(\"0\");\n  const [top, settop] = useState(\"100\");\n  const [totalcount, setTotalcount] = useState(0);\n  const {\n    openSnackbar\n  } = useSnackbar();\n  const [errors, setErrors] = useState({\n    AccountName: \"\",\n    DomainUrl: \"\"\n  });\n  const navigate = useNavigate();\n  const {\n    isRtl\n  } = useRtl();\n  const generateCustomUserId = () => {\n    const now = new Date();\n    const day = String(now.getDate()).padStart(2, \"0\");\n    const month = String(now.getMonth() + 1).padStart(2, \"0\");\n    const year = now.getFullYear();\n    const datePart = `${day}${month}${year}`;\n    const guidPart = uuidv4();\n    return `${datePart}-${guidPart}`;\n  };\n  const [inputs, setInputs] = useState({\n    AccountId: generateCustomUserId(),\n    AccountName: \"\",\n    CreatedBy: userDetails === null || userDetails === void 0 ? void 0 : userDetails.UserName,\n    AccountType: \"\",\n    CreatedDate: \"\",\n    OrganizationId: userDetails === null || userDetails === void 0 ? void 0 : userDetails.OrganizationId,\n    UpdatedBy: \"\",\n    Active: Boolean(true),\n    DomainUrl: \"\",\n    Rtl: false,\n    IsAIEnabled: false\n  });\n  const [organizations, setOrganizations] = useState([]);\n  const [selectedOrganizationId, setSelectedOrganizationId] = useState(OrganizationId);\n  const openPopup = () => {\n    setShowPopup(true);\n    handleOrganizationDropdownOpen();\n  };\n  const handleSubmit = event => {\n    event.preventDefault();\n  };\n  const alphanumericRegex = /^[a-zA-Z0-9]*$/;\n  const handleChange = event => {\n    const {\n      name,\n      value\n    } = event.target;\n    let error = \"\";\n    let processedValue = value;\n    if (name === \"AccountType\") {\n      const selectedOrganization = organizations.find(org => org.Name === value);\n      if (selectedOrganization) {\n        setInputs(values => ({\n          ...values,\n          AccountType: value,\n          OrganizationId: OrganizationId // Placeholder ID\n        }));\n      }\n    } else if (name === \"AccountName\") {\n      // Allow only letters and spaces, remove special characters and numbers\n      processedValue = value.replace(/[^a-zA-Z\\s]/g, \"\");\n\n      // Validate trimmed value but keep original spacing for user experience\n      const trimmedValue = processedValue.trim();\n      if (trimmedValue === \"\") {\n        error = translate(\"Account Name cannot be only spaces.\");\n      } else if (trimmedValue.length < 3) {\n        error = translate(\"Account Name must be at least 3 characters.\");\n      } else if (trimmedValue.length > 50) {\n        error = translate(\"Account Name cannot exceed 50 characters.\");\n      }\n    } else if (name === \"DomainUrl\") {\n      // Trim spaces for domain URL\n      processedValue = value.trim();\n      const domainPattern = /^(https?:\\/\\/)?([a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,}(\\/)?$/;\n\n      // Check if value matches a valid full domain URL format\n      if (processedValue && !domainPattern.test(processedValue)) {\n        error = translate(\"Please enter a valid full domain URL (e.g., https://example.com).\");\n      }\n    }\n\n    // Update the state with the processed value\n    setInputs(prev => ({\n      ...prev,\n      [name]: processedValue\n    }));\n\n    // Set error state\n    setErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n  };\n\n  // In CreateAccount component\n\n  // const handleChange = (event: ChangeEvent<HTMLInputElement>) => {\n  // \tconst { name, value } = event.target;\n  // \tlet error = \"\";\n  // \tlet processedValue = value;\n\n  // \tif (name === \"AccountType\") {\n  // \t\tconst selectedOrganization = organizations.find((org) => org.Name === value);\n  // \t\tif (selectedOrganization) {\n  // \t\t\tsetInputs((values) => ({\n  // \t\t\t\t...values,\n  // \t\t\t\tAccountType: value,\n  // \t\t\t\tOrganizationId: \"********-*********-134dc53c-f123-4655-aa39-0529fa976863\", // Placeholder ID\n  // \t\t\t}));\n  // \t\t}\n  // \t} else if (name === \"AccountName\") {\n  // \t\t// Allow only letters and spaces, remove special characters and numbers\n  // \t\tprocessedValue = value.replace(/[^a-zA-Z\\s]/g, \"\");\n\n  // \t\t// Check if the length is less than 5 characters\n  // \t\tif (processedValue.length < 5) {\n  // \t\t\terror = \"Account Name must be at least 5 characters.\";\n  // \t\t}\n  // \t}\n\n  // \t// Update the state with the processed value\n  // \tsetInputs((prev) => ({ ...prev, [name]: processedValue }));\n\n  // \t// Set error state\n  // \tsetErrors((prev) => ({ ...prev, [name]: error }));\n  // };\n\n  const handleSubmitAccount = async event => {\n    event.preventDefault();\n\n    // Trim leading and trailing spaces from AccountName and DomainUrl before validation and submission\n    const trimmedAccountName = inputs.AccountName.trim();\n    const trimmedDomainUrl = inputs.DomainUrl.trim();\n    const updatedInputs = {\n      ...inputs,\n      AccountName: trimmedAccountName,\n      DomainUrl: trimmedDomainUrl\n    };\n    const newErrors = {};\n    let isValid = true;\n\n    // Validate AccountName\n    if (!trimmedAccountName) {\n      newErrors.AccountName = translate(\"Account Name is required.\");\n      isValid = false;\n    } else if (trimmedAccountName.length < 3) {\n      newErrors.AccountName = translate(\"Account Name must be at least 3 characters.\");\n      isValid = false;\n    } else if (trimmedAccountName.length > 50) {\n      newErrors.AccountName = translate(\"Account Name cannot exceed 50 characters.\");\n      isValid = false;\n    } else if (/[^a-zA-Z\\s]/g.test(trimmedAccountName)) {\n      newErrors.AccountName = translate(\"Account Name can only contain letters and spaces.\");\n      isValid = false;\n    }\n\n    // Validate DomainUrl\n    if (!trimmedDomainUrl) {\n      newErrors.DomainUrl = translate(\"Domain Url is required.\");\n      isValid = false;\n    } else {\n      const domainPattern = /^(https?:\\/\\/)?([a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,}(\\/)?$/;\n      if (!domainPattern.test(trimmedDomainUrl)) {\n        newErrors.DomainUrl = translate(\"Please enter a valid full domain URL (e.g., https://example.com).\");\n        isValid = false;\n      }\n    }\n    setErrors(newErrors);\n    if (!isValid) {\n      // Show first error in snackbar\n      const firstError = newErrors.AccountName || newErrors.DomainUrl;\n      if (firstError) {\n        setSnackbarMessage(firstError);\n        setSnackbarSeverity(\"error\");\n        setSnackbarOpen(true);\n      }\n      return;\n    }\n    const redirectUrl = `/${OrganizationId}/accounts`;\n    setTimeout(() => {\n      navigate(redirectUrl);\n    }, 3000);\n    const newInputs = {\n      ...updatedInputs,\n      AccountId: generateCustomUserId(),\n      // Generate a new ID on submit\n      Active: true,\n      // Ensure Rtl value is included (it's already in inputs, but being explicit here)\n      Rtl: inputs.Rtl,\n      IsAIEnabled: inputs.IsAIEnabled\n    };\n    setLoading(true);\n    try {\n      SubmitCreateAccount(setLoading, setShowPopup, setModels, newInputs, OrganizationId, skip, top, setTotalcount, openSnackbar, orderByField, filters);\n      //openSnackbar(\"User  created successfully!\", \"success\");\n    } catch (error) {\n      //openSnackbar(\"Failed to create user .\", \"error\");\n    }\n  };\n  const isAccountNameValid = name => {\n    const processedValue = name.replace(/[^a-zA-Z\\s]/g, \"\");\n    const trimmedValue = processedValue.trim();\n    return trimmedValue.length >= 3 && trimmedValue.length <= 50;\n  };\n  const handleOrganizationDropdownOpen = async () => {\n    try {\n      const response = await getAllOrganizations(setOrganizations, setLoading);\n    } catch (error) {\n      console.error(\"Error fetching organizations:\", error);\n      setSnackbarMessage(translate(\"Error fetching organizations:\") + ` ${error.message}`);\n      setSnackbarSeverity(\"error\");\n      setSnackbarOpen(true);\n    }\n  };\n  const handleSelectChange = event => {\n    const selectedName = event.target.value;\n    const selectedOrganization = organizations.find(org => org.Name === selectedName);\n    if (selectedOrganization) {\n      setSelectedOrganizationId(OrganizationId);\n    }\n  };\n  const handleSnackbarClose = () => {\n    setSnackbarOpen(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [showPopup ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-accountcreatepopup\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-title-sec\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-title\",\n            children: translate('Create Account')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 6\n          }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n            onClick: () => setShowPopup(false),\n            className: \"qadpt-closeicon\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            x: \"0px\",\n            y: \"0px\",\n            width: \"24\",\n            height: \"24\",\n            viewBox: \"0 0 50 50\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M 7.71875 6.28125 L 6.28125 7.71875 L 23.5625 25 L 6.28125 42.28125 L 7.71875 43.71875 L 25 26.4375 L 42.28125 43.71875 L 43.71875 42.28125 L 26.4375 25 L 43.71875 7.71875 L 42.28125 6.28125 L 25 23.5625 Z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 6\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-accountcreatefield\",\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              children: [/*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"account-name\",\n                  children: translate('Account Name')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 9\n                }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                  id: \"account-name\",\n                  name: \"AccountName\",\n                  required: true,\n                  value: inputs.AccountName,\n                  onChange: handleChange,\n                  placeholder: translate('E.g., Google'),\n                  helperText: errors.AccountName,\n                  variant: \"outlined\",\n                  error: !!errors.AccountName,\n                  inputProps: {\n                    maxLength: 60\n                  },\n                  className: \"qadpt-acctfield\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 9\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 7\n              }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"account-name\",\n                  children: translate('Domain Url')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 9\n                }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                  id: \"domain-url\",\n                  name: \"DomainUrl\",\n                  required: true,\n                  value: inputs.DomainUrl,\n                  onChange: handleChange,\n                  placeholder: translate('Domain Url Here'),\n                  helperText: errors.DomainUrl,\n                  variant: \"outlined\",\n                  error: !!errors.DomainUrl,\n                  inputProps: {\n                    maxLength: 50\n                  },\n                  className: \"qadpt-acctfield\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 9\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 7\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-txtfld\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    margin: \"10px 0\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      marginLeft: '10px'\n                    },\n                    children: \"RTL\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 11\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"toggle-switch\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      checked: inputs.Rtl // true = RTL, false = LTR\n                      ,\n                      onChange: e => setInputs(prev => ({\n                        ...prev,\n                        Rtl: e.target.checked\n                      })),\n                      name: translate(\"Rtl\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 342,\n                      columnNumber: 4\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"slider\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 353,\n                      columnNumber: 4\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 341,\n                    columnNumber: 3\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 7\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-txtfld\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    margin: \"10px 0\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      marginLeft: '10px'\n                    },\n                    children: \"Dona\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"toggle-switch\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      checked: inputs.IsAIEnabled,\n                      onChange: e => setInputs(prev => ({\n                        ...prev,\n                        IsAIEnabled: e.target.checked\n                      })),\n                      name: translate(\"Dona\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 366,\n                      columnNumber: 13\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"slider\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 377,\n                      columnNumber: 13\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 365,\n                    columnNumber: 9\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 6\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-account-buttons\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            className: `qadpt-save-btn ${!isAccountNameValid(inputs.AccountName) ? 'invalid' : ''}`,\n            onClick: handleSubmitAccount\n            //disabled={!isAccountNameValid(inputs.AccountName)}\n            ,\n            children: translate('Save')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 5\n    }, this) : \"\", /*#__PURE__*/_jsxDEV(Snackbar, {\n      className: \"qadpt-accountalert\",\n      open: snackbarOpen,\n      autoHideDuration: 4000,\n      onClose: handleSnackbarClose,\n      anchorOrigin: {\n        vertical: \"top\",\n        horizontal: \"center\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleSnackbarClose,\n        severity: snackbarSeverity\n        // sx={{ width: \"100%\" }}\n        ,\n        children: snackbarMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 403,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 281,\n    columnNumber: 3\n  }, this);\n};\n_s(CreateAccount, \"ZJLmagBqEn9RlD5if86hVo3xJ/8=\", false, function () {\n  return [useTranslation, useAuth, useSnackbar, useNavigate, useRtl];\n});\n_c = CreateAccount;\nexport default CreateAccount;\nvar _c;\n$RefreshReg$(_c, \"CreateAccount\");", "map": {"version": 3, "names": ["React", "useState", "v4", "uuidv4", "getAllOrganizations", "SubmitCreateAccount", "TextField", "<PERSON><PERSON>", "Snackbar", "<PERSON><PERSON>", "Grid", "FormControl", "useSnackbar", "useNavigate", "useAuth", "useTranslation", "useRtl", "jsxDEV", "_jsxDEV", "CreateAccount", "props", "_s", "_userDetails$Organiza", "t", "translate", "setLoading", "setModels", "showPopup", "setShowPopup", "orderByField", "filters", "snackbarOpen", "setSnackbarOpen", "snackbarMessage", "setSnackbarMessage", "snackbarSeverity", "setSnackbarSeverity", "signOut", "userDetails", "OrganizationId", "setOrganizationId", "skip", "setskip", "top", "settop", "totalcount", "setTotalcount", "openSnackbar", "errors", "setErrors", "Account<PERSON><PERSON>", "DomainUrl", "navigate", "isRtl", "generateCustomUserId", "now", "Date", "day", "String", "getDate", "padStart", "month", "getMonth", "year", "getFullYear", "datePart", "g<PERSON><PERSON><PERSON>", "inputs", "setInputs", "AccountId", "CreatedBy", "UserName", "AccountType", "CreatedDate", "UpdatedBy", "Active", "Boolean", "Rtl", "IsAIEnabled", "organizations", "setOrganizations", "selectedOrganizationId", "setSelectedOrganizationId", "openPopup", "handleOrganizationDropdownOpen", "handleSubmit", "event", "preventDefault", "alphanumericRegex", "handleChange", "name", "value", "target", "error", "processedValue", "selectedOrganization", "find", "org", "Name", "values", "replace", "trimmedValue", "trim", "length", "domainPattern", "test", "prev", "handleSubmitAccount", "trimmedAccountName", "trimmedDomainUrl", "updatedInputs", "newErrors", "<PERSON><PERSON><PERSON><PERSON>", "firstError", "redirectUrl", "setTimeout", "newInputs", "isAccountNameValid", "response", "console", "message", "handleSelectChange", "<PERSON><PERSON><PERSON>", "handleSnackbarClose", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "xmlns", "x", "y", "width", "height", "viewBox", "d", "container", "item", "fullWidth", "required", "htmlFor", "id", "onChange", "placeholder", "helperText", "variant", "inputProps", "max<PERSON><PERSON><PERSON>", "style", "display", "justifyContent", "margin", "marginLeft", "type", "checked", "e", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/components/account/AccountCreate.tsx"], "sourcesContent": ["import React, { useState, useEffect, ChangeEvent } from \"react\";\r\nimport { v4 as uuidv4 } from \"uuid\";\r\nimport { getAllOrganizations } from \"../../services/OrganizationService\";\r\nimport { SubmitCreateAccount } from \"../../services/AccountService\";\r\nimport { TextField, Select, MenuItem, Button, IconButton, Snackbar, Alert, Grid, FormControl, Switch, FormControlLabel, Radio, RadioGroup } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport { useSnackbar } from \"../../SnackbarContext\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { useAuth } from \"../auth/AuthProvider\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport { useRtl } from \"../../RtlContext\";\r\n\r\ntype InputFields = {\r\n\tAccountName: string;\r\n\tDomainUrl: string;\r\n\tAccountType: string;\r\n\tOrganizationId: string;\r\n\tAccountId: string;\r\n\tActive: boolean;\r\n\tRtl: boolean;\r\n\tIsAIEnabled: boolean;\r\n};\r\ntype ErrorFields = Partial<InputFields>;\r\n\r\nconst CreateAccount = (props: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst { setLoading, setModels, showPopup, setShowPopup, orderByField, filters } = props;\r\n\t// const [showPopup, setShowPopup] = useState(false);\r\n\tconst [snackbarOpen, setSnackbarOpen] = useState(false);\r\n\tconst [snackbarMessage, setSnackbarMessage] = useState(\"\");\r\n\tconst [snackbarSeverity, setSnackbarSeverity] = useState<\"success\" | \"error\">(\"success\");\r\n\tconst { signOut, userDetails } = useAuth();\r\n\tconst [OrganizationId, setOrganizationId] = useState(userDetails?.OrganizationId??\"\");\r\n\tconst [skip, setskip] = useState(\"0\");\r\n\tconst [top, settop] = useState(\"100\");\r\n\tconst [totalcount, setTotalcount] = useState(0);\r\n\tconst { openSnackbar } = useSnackbar();\r\n\tconst [errors, setErrors] = useState<ErrorFields>({\r\n\t\tAccountName: \"\",\r\n\t\tDomainUrl: \"\"\r\n\t});\r\n\tconst navigate = useNavigate();\r\n\tconst { isRtl } = useRtl();\r\n\r\n\tconst generateCustomUserId = () => {\r\n\t\tconst now = new Date();\r\n\t\tconst day = String(now.getDate()).padStart(2, \"0\");\r\n\t\tconst month = String(now.getMonth() + 1).padStart(2, \"0\");\r\n\t\tconst year = now.getFullYear();\r\n\t\tconst datePart = `${day}${month}${year}`;\r\n\t\tconst guidPart = uuidv4();\r\n\t\treturn `${datePart}-${guidPart}`;\r\n\t};\r\n\r\n\tconst [inputs, setInputs] = useState({\r\n\t\tAccountId: generateCustomUserId(),\r\n\t\tAccountName: \"\",\r\n\t\tCreatedBy: userDetails?.UserName,\r\n\t\tAccountType: \"\",\r\n\t\tCreatedDate: \"\",\r\n\t\tOrganizationId: userDetails?.OrganizationId,\r\n\t\tUpdatedBy: \"\",\r\n\t\tActive: Boolean(true),\r\n\t\tDomainUrl: \"\",\r\n\t\tRtl: false,\r\n\t\tIsAIEnabled: false\r\n\t});\t\r\n\tconst [organizations, setOrganizations] = useState<any[]>([]);\r\n\tconst [selectedOrganizationId, setSelectedOrganizationId] = useState(\r\n\t\tOrganizationId\r\n\t);\r\n\r\n\tconst openPopup = () => {\r\n\t\tsetShowPopup(true);\r\n\t\thandleOrganizationDropdownOpen();\r\n\t};\r\n\tconst handleSubmit = (event: any) => {\r\n\t\tevent.preventDefault();\r\n\t};\r\n\tconst alphanumericRegex = /^[a-zA-Z0-9]*$/;\r\n\r\n\tconst handleChange = (event: ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst { name, value } = event.target;\r\n\t\tlet error = \"\";\r\n\t\tlet processedValue = value;\r\n\r\n\t\tif (name === \"AccountType\") {\r\n\t\t\tconst selectedOrganization = organizations.find((org) => org.Name === value);\r\n\t\t\tif (selectedOrganization) {\r\n\t\t\t\tsetInputs((values) => ({\r\n\t\t\t\t\t...values,\r\n\t\t\t\t\tAccountType: value,\r\n\t\t\t\t\tOrganizationId: OrganizationId, // Placeholder ID\r\n\t\t\t\t}));\r\n\t\t\t}\r\n\t\t} else if (name === \"AccountName\") {\r\n\t\t\t// Allow only letters and spaces, remove special characters and numbers\r\n\t\t\tprocessedValue = value.replace(/[^a-zA-Z\\s]/g, \"\");\r\n\r\n\t\t\t// Validate trimmed value but keep original spacing for user experience\r\n\t\t\tconst trimmedValue = processedValue.trim();\r\n\t\t\tif (trimmedValue === \"\") {\r\n\t\t\t\terror = translate(\"Account Name cannot be only spaces.\");\r\n\t\t\t} else if (trimmedValue.length < 3) {\r\n\t\t\t\terror = translate(\"Account Name must be at least 3 characters.\");\r\n\t\t\t} else if (trimmedValue.length > 50) {\r\n\t\t\t\terror = translate(\"Account Name cannot exceed 50 characters.\");\r\n\t\t\t}\r\n\t\t} else if (name === \"DomainUrl\") {\r\n\t\t\t// Trim spaces for domain URL\r\n\t\t\tprocessedValue = value.trim();\r\n\t\t\tconst domainPattern = /^(https?:\\/\\/)?([a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,}(\\/)?$/;\r\n\r\n\t\t\t// Check if value matches a valid full domain URL format\r\n\t\t\tif (processedValue && !domainPattern.test(processedValue)) {\r\n\t\t\t\terror = translate(\"Please enter a valid full domain URL (e.g., https://example.com).\");\r\n\t\t\t}\r\n\r\n\t\t}\r\n\t\t\r\n\r\n\t\t// Update the state with the processed value\r\n\t\tsetInputs((prev) => ({ ...prev, [name]: processedValue }));\r\n\r\n\t\t// Set error state\r\n\t\tsetErrors((prev) => ({ ...prev, [name]: error }));\r\n\t};\r\n\r\n\t// In CreateAccount component\r\n\r\n\t// const handleChange = (event: ChangeEvent<HTMLInputElement>) => {\r\n\t// \tconst { name, value } = event.target;\r\n\t// \tlet error = \"\";\r\n\t// \tlet processedValue = value;\r\n\r\n\t// \tif (name === \"AccountType\") {\r\n\t// \t\tconst selectedOrganization = organizations.find((org) => org.Name === value);\r\n\t// \t\tif (selectedOrganization) {\r\n\t// \t\t\tsetInputs((values) => ({\r\n\t// \t\t\t\t...values,\r\n\t// \t\t\t\tAccountType: value,\r\n\t// \t\t\t\tOrganizationId: \"********-*********-134dc53c-f123-4655-aa39-0529fa976863\", // Placeholder ID\r\n\t// \t\t\t}));\r\n\t// \t\t}\r\n\t// \t} else if (name === \"AccountName\") {\r\n\t// \t\t// Allow only letters and spaces, remove special characters and numbers\r\n\t// \t\tprocessedValue = value.replace(/[^a-zA-Z\\s]/g, \"\");\r\n\r\n\t// \t\t// Check if the length is less than 5 characters\r\n\t// \t\tif (processedValue.length < 5) {\r\n\t// \t\t\terror = \"Account Name must be at least 5 characters.\";\r\n\t// \t\t}\r\n\t// \t}\r\n\r\n\t// \t// Update the state with the processed value\r\n\t// \tsetInputs((prev) => ({ ...prev, [name]: processedValue }));\r\n\r\n\t// \t// Set error state\r\n\t// \tsetErrors((prev) => ({ ...prev, [name]: error }));\r\n\t// };\r\n\r\n\tconst handleSubmitAccount = async (event: any) => {\r\n\t\tevent.preventDefault();\r\n\r\n\t\t// Trim leading and trailing spaces from AccountName and DomainUrl before validation and submission\r\n\t\tconst trimmedAccountName = inputs.AccountName.trim();\r\n\t\tconst trimmedDomainUrl = inputs.DomainUrl.trim();\r\n\t\tconst updatedInputs = {\r\n\t\t\t...inputs,\r\n\t\t\tAccountName: trimmedAccountName,\r\n\t\t\tDomainUrl: trimmedDomainUrl\r\n\t\t};\r\n\r\n\t\tconst newErrors: ErrorFields = {};\r\n\t\tlet isValid = true;\r\n\r\n\t\t// Validate AccountName\r\n\t\tif (!trimmedAccountName) {\r\n\t\t\tnewErrors.AccountName = translate(\"Account Name is required.\");\r\n\t\t\tisValid = false;\r\n\t\t} else if (trimmedAccountName.length < 3) {\r\n\t\t\tnewErrors.AccountName = translate(\"Account Name must be at least 3 characters.\");\r\n\t\t\tisValid = false;\r\n\t\t} else if (trimmedAccountName.length > 50) {\r\n\t\t\tnewErrors.AccountName = translate(\"Account Name cannot exceed 50 characters.\");\r\n\t\t\tisValid = false;\r\n\t\t} else if (/[^a-zA-Z\\s]/g.test(trimmedAccountName)) {\r\n\t\t\tnewErrors.AccountName = translate(\"Account Name can only contain letters and spaces.\");\r\n\t\t\tisValid = false;\r\n\t\t}\r\n\r\n\t\t// Validate DomainUrl\r\n\t\tif (!trimmedDomainUrl) {\r\n\t\t\tnewErrors.DomainUrl = translate(\"Domain Url is required.\");\r\n\t\t\tisValid = false;\r\n\t\t} else {\r\n\t\t\tconst domainPattern = /^(https?:\\/\\/)?([a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,}(\\/)?$/;\r\n\t\t\tif (!domainPattern.test(trimmedDomainUrl)) {\r\n\t\t\t\tnewErrors.DomainUrl = translate(\"Please enter a valid full domain URL (e.g., https://example.com).\");\r\n\t\t\t\tisValid = false;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tsetErrors(newErrors);\r\n\r\n\t\tif (!isValid) {\r\n\t\t\t// Show first error in snackbar\r\n\t\t\tconst firstError = newErrors.AccountName || newErrors.DomainUrl;\r\n\t\t\tif (firstError) {\r\n\t\t\t\tsetSnackbarMessage(firstError);\r\n\t\t\t\tsetSnackbarSeverity(\"error\");\r\n\t\t\t\tsetSnackbarOpen(true);\r\n\t\t\t}\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tconst redirectUrl = `/${OrganizationId}/accounts`;\r\n\t\tsetTimeout(() => {\r\n\t\t\tnavigate(redirectUrl);\r\n\t\t}, 3000);\r\n\t\tconst newInputs = {\r\n\t\t\t...updatedInputs,\r\n\t\t\tAccountId: generateCustomUserId(), // Generate a new ID on submit\r\n\t\t\tActive: true,\r\n\t\t\t// Ensure Rtl value is included (it's already in inputs, but being explicit here)\r\n\t\t\tRtl: inputs.Rtl,\r\n\t\t\tIsAIEnabled: inputs.IsAIEnabled\r\n\t\t};\r\n\r\n\t\tsetLoading(true);\r\n\t\ttry {\r\n\t\t\tSubmitCreateAccount(\r\n\t\t\t\tsetLoading,\r\n\t\t\t\tsetShowPopup,\r\n\t\t\t\tsetModels,\r\n\t\t\t\tnewInputs,\r\n\t\t\t\tOrganizationId,\r\n\t\t\t\tskip,\r\n\t\t\t\ttop,\r\n\t\t\t\tsetTotalcount,\r\n\t\t\t\topenSnackbar,\r\n\t\t\t\torderByField,\r\n\t\t\t\tfilters\r\n\t\t\t);\r\n\t\t\t//openSnackbar(\"User  created successfully!\", \"success\");\r\n\t\t} catch (error) {\r\n\t\t//openSnackbar(\"Failed to create user .\", \"error\");\r\n\t\t}\r\n\t};\r\n\r\n\tconst isAccountNameValid = (name: string): boolean => {\r\n\t\tconst processedValue = name.replace(/[^a-zA-Z\\s]/g, \"\");\r\n\t\tconst trimmedValue = processedValue.trim();\r\n\t\treturn trimmedValue.length >= 3 && trimmedValue.length <= 50;\r\n\t};\r\n\r\n\tconst handleOrganizationDropdownOpen = async () => {\r\n\t\ttry {\r\n\t\t\tconst response = await getAllOrganizations(setOrganizations, setLoading);\r\n\t\t} catch (error: any) {\r\n\t\t\tconsole.error(\"Error fetching organizations:\", error);\r\n\t\t\tsetSnackbarMessage(translate(\"Error fetching organizations:\") + ` ${error.message}`);\r\n\t\t\tsetSnackbarSeverity(\"error\");\r\n\t\t\tsetSnackbarOpen(true);\r\n\t\t}\r\n\t};\r\n\tconst handleSelectChange = (event: any) => {\r\n\t\tconst selectedName = event.target.value;\r\n\t\tconst selectedOrganization = organizations.find((org) => org.Name === selectedName);\r\n\t\tif (selectedOrganization) {\r\n\t\t\tsetSelectedOrganizationId(OrganizationId);\r\n\t\t}\r\n\r\n\t};\r\n\r\n\tconst handleSnackbarClose = () => {\r\n\t\tsetSnackbarOpen(false);\r\n\t};\r\n\r\n\treturn (\r\n\t\t<div>\r\n\t\t\t{showPopup ? (\r\n\t\t\t\t<div className=\"qadpt-modal-overlay\">\r\n\t\t\t\t<div className=\"qadpt-accountcreatepopup\">\r\n\t\t\t\t  <div className=\"qadpt-title-sec\">\r\n\t\t\t\t\t<div className=\"qadpt-title\">{translate('Create Account')}</div>\r\n\t\t\t\t\t<svg\r\n\t\t\t\t\t  onClick={() => setShowPopup(false)}\r\n\t\t\t\t\t  className=\"qadpt-closeicon\"\r\n\t\t\t\t\t  xmlns=\"http://www.w3.org/2000/svg\"\r\n\t\t\t\t\t  x=\"0px\"\r\n\t\t\t\t\t  y=\"0px\"\r\n\t\t\t\t\t  width=\"24\"\r\n\t\t\t\t\t  height=\"24\"\r\n\t\t\t\t\t  viewBox=\"0 0 50 50\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t  <path d=\"M 7.71875 6.28125 L 6.28125 7.71875 L 23.5625 25 L 6.28125 42.28125 L 7.71875 43.71875 L 25 26.4375 L 42.28125 43.71875 L 43.71875 42.28125 L 26.4375 25 L 43.71875 7.71875 L 42.28125 6.28125 L 25 23.5625 Z\"></path>\r\n\t\t\t\t\t</svg>\r\n\t\t\t\t  </div>\r\n\t\t\t\t  <div className=\"qadpt-accountcreatefield\">\r\n\t\t\t\t\t<Grid container>\r\n\t\t\t\t\t  <Grid item>\r\n\t\t\t\t\t\t<FormControl fullWidth required>\r\n\t\t\t\t\t\t  <label htmlFor=\"account-name\">{translate('Account Name')}</label>\r\n\t\t\t\t\t\t  <TextField\r\n\t\t\t\t\t\t\tid=\"account-name\"\r\n\t\t\t\t\t\t\tname=\"AccountName\"\r\n\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\tvalue={inputs.AccountName}\r\n\t\t\t\t\t\t\tonChange={handleChange}\r\n\t\t\t\t\t\t\tplaceholder={translate('E.g., Google')}\r\n\t\t\t\t\t\t\thelperText={errors.AccountName}\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\terror={!!errors.AccountName}\r\n\t\t\t\t\t\t\t\t\t\t\tinputProps={{ maxLength: 60 }}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-acctfield\"\r\n\t\t\t\t\t\t  />\r\n\t\t\t\t\t\t</FormControl>\r\n\t\t\t\t\t\t<FormControl fullWidth required>\r\n\t\t\t\t\t\t  <label htmlFor=\"account-name\">{translate('Domain Url')}</label>\r\n\t\t\t\t\t\t  <TextField\r\n\t\t\t\t\t\t\tid=\"domain-url\"\r\n\t\t\t\t\t\t\tname=\"DomainUrl\"\r\n\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\tvalue={inputs.DomainUrl}\r\n\t\t\t\t\t\t\tonChange={handleChange}\r\n\t\t\t\t\t\t\tplaceholder={translate('Domain Url Here')}\r\n\t\t\t\t\t\t\thelperText={errors.DomainUrl}\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\terror={!!errors.DomainUrl}\r\n\t\t\t\t\t\t\tinputProps={{ maxLength: 50 }}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-acctfield\"\r\n\t\t\t\t\t\t  />\r\n\t\t\t\t\t\t</FormControl>\r\n\t\t\t\t\t\t<div className=\"qadpt-txtfld\">\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t<div style={{display:\"flex\",justifyContent:\"space-between\", margin: \"10px 0\"}}>\r\n\t\t\t\t\t\t\t\t\t\t<span style={{ marginLeft: '10px' }}>\r\n\t\t\tRTL\r\n\t\t</span>\r\n\t\t<label className=\"toggle-switch\">\r\n\t\t\t<input\r\n\t\t\t\ttype=\"checkbox\"\r\n\t\t\t\tchecked={inputs.Rtl} // true = RTL, false = LTR\r\n\t\t\t\tonChange={(e) =>\r\n\t\t\t\t\tsetInputs(prev => ({\r\n\t\t\t\t\t\t...prev,\r\n\t\t\t\t\t\tRtl: e.target.checked\r\n\t\t\t\t\t}))\r\n\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tname={translate(\"Rtl\")}\r\n\t\t\t/>\r\n\t\t\t<span className=\"slider\"></span>\r\n\t\t</label>\r\n\t\t\r\n\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-txtfld\">\r\n                                       \r\n                                        <div style={{display:\"flex\",justifyContent:\"space-between\", margin: \"10px 0\"}}>\r\n                                        <span style={{ marginLeft: '10px' }}>\r\n            Dona\r\n        </span>\r\n        <label className=\"toggle-switch\">\r\n            <input\r\n                type=\"checkbox\"\r\n                checked={inputs.IsAIEnabled} \r\n                onChange={(e) =>\r\n                    setInputs(prev => ({\r\n                        ...prev,\r\n                        IsAIEnabled: e.target.checked\r\n                    }))\r\n                }\r\n                                                    name={translate(\"Dona\")}\r\n            />\r\n            <span className=\"slider\"></span>\r\n        </label>\r\n       \r\n    </div>\r\n</div>\r\n\r\n\t\t\t\t\t  </Grid>\r\n\t\t\t\t\t</Grid>\r\n\t\t\t\t  </div>\r\n\t\t\t\t  <div className=\"qadpt-account-buttons\">\r\n      <Button\r\n        className={`qadpt-save-btn ${\r\n          !isAccountNameValid(inputs.AccountName) ? 'invalid' : ''\r\n        }`}\r\n        onClick={handleSubmitAccount}\r\n\t\t\t\t\t\t//disabled={!isAccountNameValid(inputs.AccountName)}\r\n      >\r\n        {translate('Save')}\r\n      </Button>\r\n    </div>\r\n\t\t\t\t</div>\r\n\t\t\t  </div>\r\n\t\t\t  \r\n\t\t\t) : (\r\n\t\t\t\t\"\"\r\n\t\t\t)}\r\n\t\t\t<Snackbar\r\n\t\t\t\tclassName=\"qadpt-accountalert\"\r\n\t\t\t\topen={snackbarOpen}\r\n\t\t\t\tautoHideDuration={4000}\r\n\t\t\t\tonClose={handleSnackbarClose}\r\n\t\t\t\tanchorOrigin={{ vertical: \"top\", horizontal: \"center\" }}\r\n\t\t\t>\r\n\t\t\t\t<Alert\r\n\t\t\t\t\tonClose={handleSnackbarClose}\r\n\t\t\t\t\tseverity={snackbarSeverity}\r\n\t\t\t\t\t// sx={{ width: \"100%\" }}\r\n\t\t\t\t>\r\n\t\t\t\t\t{snackbarMessage}\r\n\t\t\t\t</Alert>\r\n\t\t\t</Snackbar>\r\n\t\t</div>\r\n\t);\r\n};\r\n\r\nexport default CreateAccount;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAgC,OAAO;AAC/D,SAASC,EAAE,IAAIC,MAAM,QAAQ,MAAM;AACnC,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,mBAAmB,QAAQ,+BAA+B;AACnE,SAASC,SAAS,EAAoBC,MAAM,EAAcC,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAEC,WAAW,QAAqD,eAAe;AAEhK,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,MAAM,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAc1C,MAAMC,aAAa,GAAIC,KAAU,IAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACrC,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGT,cAAc,CAAC,CAAC;EACzC,MAAM;IAAEU,UAAU;IAAEC,SAAS;IAAEC,SAAS;IAAEC,YAAY;IAAEC,YAAY;IAAEC;EAAQ,CAAC,GAAGV,KAAK;EACvF;EACA,MAAM,CAACW,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACgC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACkC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnC,QAAQ,CAAsB,SAAS,CAAC;EACxF,MAAM;IAAEoC,OAAO;IAAEC;EAAY,CAAC,GAAGxB,OAAO,CAAC,CAAC;EAC1C,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAGvC,QAAQ,EAAAqB,qBAAA,GAACgB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEC,cAAc,cAAAjB,qBAAA,cAAAA,qBAAA,GAAE,EAAE,CAAC;EACrF,MAAM,CAACmB,IAAI,EAAEC,OAAO,CAAC,GAAGzC,QAAQ,CAAC,GAAG,CAAC;EACrC,MAAM,CAAC0C,GAAG,EAAEC,MAAM,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACrC,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM;IAAE8C;EAAa,CAAC,GAAGnC,WAAW,CAAC,CAAC;EACtC,MAAM,CAACoC,MAAM,EAAEC,SAAS,CAAC,GAAGhD,QAAQ,CAAc;IACjDiD,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE;EACZ,CAAC,CAAC;EACF,MAAMC,QAAQ,GAAGvC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEwC;EAAM,CAAC,GAAGrC,MAAM,CAAC,CAAC;EAE1B,MAAMsC,oBAAoB,GAAGA,CAAA,KAAM;IAClC,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,GAAG,GAAGC,MAAM,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAClD,MAAMC,KAAK,GAAGH,MAAM,CAACH,GAAG,CAACO,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACzD,MAAMG,IAAI,GAAGR,GAAG,CAACS,WAAW,CAAC,CAAC;IAC9B,MAAMC,QAAQ,GAAG,GAAGR,GAAG,GAAGI,KAAK,GAAGE,IAAI,EAAE;IACxC,MAAMG,QAAQ,GAAG/D,MAAM,CAAC,CAAC;IACzB,OAAO,GAAG8D,QAAQ,IAAIC,QAAQ,EAAE;EACjC,CAAC;EAED,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGnE,QAAQ,CAAC;IACpCoE,SAAS,EAAEf,oBAAoB,CAAC,CAAC;IACjCJ,WAAW,EAAE,EAAE;IACfoB,SAAS,EAAEhC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiC,QAAQ;IAChCC,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,EAAE;IACflC,cAAc,EAAED,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEC,cAAc;IAC3CmC,SAAS,EAAE,EAAE;IACbC,MAAM,EAAEC,OAAO,CAAC,IAAI,CAAC;IACrBzB,SAAS,EAAE,EAAE;IACb0B,GAAG,EAAE,KAAK;IACVC,WAAW,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG/E,QAAQ,CAAQ,EAAE,CAAC;EAC7D,MAAM,CAACgF,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGjF,QAAQ,CACnEsC,cACD,CAAC;EAED,MAAM4C,SAAS,GAAGA,CAAA,KAAM;IACvBvD,YAAY,CAAC,IAAI,CAAC;IAClBwD,8BAA8B,CAAC,CAAC;EACjC,CAAC;EACD,MAAMC,YAAY,GAAIC,KAAU,IAAK;IACpCA,KAAK,CAACC,cAAc,CAAC,CAAC;EACvB,CAAC;EACD,MAAMC,iBAAiB,GAAG,gBAAgB;EAE1C,MAAMC,YAAY,GAAIH,KAAoC,IAAK;IAC9D,MAAM;MAAEI,IAAI;MAAEC;IAAM,CAAC,GAAGL,KAAK,CAACM,MAAM;IACpC,IAAIC,KAAK,GAAG,EAAE;IACd,IAAIC,cAAc,GAAGH,KAAK;IAE1B,IAAID,IAAI,KAAK,aAAa,EAAE;MAC3B,MAAMK,oBAAoB,GAAGhB,aAAa,CAACiB,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,IAAI,KAAKP,KAAK,CAAC;MAC5E,IAAII,oBAAoB,EAAE;QACzB3B,SAAS,CAAE+B,MAAM,KAAM;UACtB,GAAGA,MAAM;UACT3B,WAAW,EAAEmB,KAAK;UAClBpD,cAAc,EAAEA,cAAc,CAAE;QACjC,CAAC,CAAC,CAAC;MACJ;IACD,CAAC,MAAM,IAAImD,IAAI,KAAK,aAAa,EAAE;MAClC;MACAI,cAAc,GAAGH,KAAK,CAACS,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;;MAElD;MACA,MAAMC,YAAY,GAAGP,cAAc,CAACQ,IAAI,CAAC,CAAC;MAC1C,IAAID,YAAY,KAAK,EAAE,EAAE;QACxBR,KAAK,GAAGrE,SAAS,CAAC,qCAAqC,CAAC;MACzD,CAAC,MAAM,IAAI6E,YAAY,CAACE,MAAM,GAAG,CAAC,EAAE;QACnCV,KAAK,GAAGrE,SAAS,CAAC,6CAA6C,CAAC;MACjE,CAAC,MAAM,IAAI6E,YAAY,CAACE,MAAM,GAAG,EAAE,EAAE;QACpCV,KAAK,GAAGrE,SAAS,CAAC,2CAA2C,CAAC;MAC/D;IACD,CAAC,MAAM,IAAIkE,IAAI,KAAK,WAAW,EAAE;MAChC;MACAI,cAAc,GAAGH,KAAK,CAACW,IAAI,CAAC,CAAC;MAC7B,MAAME,aAAa,GAAG,qDAAqD;;MAE3E;MACA,IAAIV,cAAc,IAAI,CAACU,aAAa,CAACC,IAAI,CAACX,cAAc,CAAC,EAAE;QAC1DD,KAAK,GAAGrE,SAAS,CAAC,mEAAmE,CAAC;MACvF;IAED;;IAGA;IACA4C,SAAS,CAAEsC,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAE,CAAChB,IAAI,GAAGI;IAAe,CAAC,CAAC,CAAC;;IAE1D;IACA7C,SAAS,CAAEyD,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAE,CAAChB,IAAI,GAAGG;IAAM,CAAC,CAAC,CAAC;EAClD,CAAC;;EAED;;EAEA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA;EACA;EACA;;EAEA,MAAMc,mBAAmB,GAAG,MAAOrB,KAAU,IAAK;IACjDA,KAAK,CAACC,cAAc,CAAC,CAAC;;IAEtB;IACA,MAAMqB,kBAAkB,GAAGzC,MAAM,CAACjB,WAAW,CAACoD,IAAI,CAAC,CAAC;IACpD,MAAMO,gBAAgB,GAAG1C,MAAM,CAAChB,SAAS,CAACmD,IAAI,CAAC,CAAC;IAChD,MAAMQ,aAAa,GAAG;MACrB,GAAG3C,MAAM;MACTjB,WAAW,EAAE0D,kBAAkB;MAC/BzD,SAAS,EAAE0D;IACZ,CAAC;IAED,MAAME,SAAsB,GAAG,CAAC,CAAC;IACjC,IAAIC,OAAO,GAAG,IAAI;;IAElB;IACA,IAAI,CAACJ,kBAAkB,EAAE;MACxBG,SAAS,CAAC7D,WAAW,GAAG1B,SAAS,CAAC,2BAA2B,CAAC;MAC9DwF,OAAO,GAAG,KAAK;IAChB,CAAC,MAAM,IAAIJ,kBAAkB,CAACL,MAAM,GAAG,CAAC,EAAE;MACzCQ,SAAS,CAAC7D,WAAW,GAAG1B,SAAS,CAAC,6CAA6C,CAAC;MAChFwF,OAAO,GAAG,KAAK;IAChB,CAAC,MAAM,IAAIJ,kBAAkB,CAACL,MAAM,GAAG,EAAE,EAAE;MAC1CQ,SAAS,CAAC7D,WAAW,GAAG1B,SAAS,CAAC,2CAA2C,CAAC;MAC9EwF,OAAO,GAAG,KAAK;IAChB,CAAC,MAAM,IAAI,cAAc,CAACP,IAAI,CAACG,kBAAkB,CAAC,EAAE;MACnDG,SAAS,CAAC7D,WAAW,GAAG1B,SAAS,CAAC,mDAAmD,CAAC;MACtFwF,OAAO,GAAG,KAAK;IAChB;;IAEA;IACA,IAAI,CAACH,gBAAgB,EAAE;MACtBE,SAAS,CAAC5D,SAAS,GAAG3B,SAAS,CAAC,yBAAyB,CAAC;MAC1DwF,OAAO,GAAG,KAAK;IAChB,CAAC,MAAM;MACN,MAAMR,aAAa,GAAG,qDAAqD;MAC3E,IAAI,CAACA,aAAa,CAACC,IAAI,CAACI,gBAAgB,CAAC,EAAE;QAC1CE,SAAS,CAAC5D,SAAS,GAAG3B,SAAS,CAAC,mEAAmE,CAAC;QACpGwF,OAAO,GAAG,KAAK;MAChB;IACD;IAEA/D,SAAS,CAAC8D,SAAS,CAAC;IAEpB,IAAI,CAACC,OAAO,EAAE;MACb;MACA,MAAMC,UAAU,GAAGF,SAAS,CAAC7D,WAAW,IAAI6D,SAAS,CAAC5D,SAAS;MAC/D,IAAI8D,UAAU,EAAE;QACf/E,kBAAkB,CAAC+E,UAAU,CAAC;QAC9B7E,mBAAmB,CAAC,OAAO,CAAC;QAC5BJ,eAAe,CAAC,IAAI,CAAC;MACtB;MACA;IACD;IAEA,MAAMkF,WAAW,GAAG,IAAI3E,cAAc,WAAW;IACjD4E,UAAU,CAAC,MAAM;MAChB/D,QAAQ,CAAC8D,WAAW,CAAC;IACtB,CAAC,EAAE,IAAI,CAAC;IACR,MAAME,SAAS,GAAG;MACjB,GAAGN,aAAa;MAChBzC,SAAS,EAAEf,oBAAoB,CAAC,CAAC;MAAE;MACnCqB,MAAM,EAAE,IAAI;MACZ;MACAE,GAAG,EAAEV,MAAM,CAACU,GAAG;MACfC,WAAW,EAAEX,MAAM,CAACW;IACrB,CAAC;IAEDrD,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACHpB,mBAAmB,CAClBoB,UAAU,EACVG,YAAY,EACZF,SAAS,EACT0F,SAAS,EACT7E,cAAc,EACdE,IAAI,EACJE,GAAG,EACHG,aAAa,EACbC,YAAY,EACZlB,YAAY,EACZC,OACD,CAAC;MACD;IACD,CAAC,CAAC,OAAO+D,KAAK,EAAE;MAChB;IAAA;EAED,CAAC;EAED,MAAMwB,kBAAkB,GAAI3B,IAAY,IAAc;IACrD,MAAMI,cAAc,GAAGJ,IAAI,CAACU,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;IACvD,MAAMC,YAAY,GAAGP,cAAc,CAACQ,IAAI,CAAC,CAAC;IAC1C,OAAOD,YAAY,CAACE,MAAM,IAAI,CAAC,IAAIF,YAAY,CAACE,MAAM,IAAI,EAAE;EAC7D,CAAC;EAED,MAAMnB,8BAA8B,GAAG,MAAAA,CAAA,KAAY;IAClD,IAAI;MACH,MAAMkC,QAAQ,GAAG,MAAMlH,mBAAmB,CAAC4E,gBAAgB,EAAEvD,UAAU,CAAC;IACzE,CAAC,CAAC,OAAOoE,KAAU,EAAE;MACpB0B,OAAO,CAAC1B,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD3D,kBAAkB,CAACV,SAAS,CAAC,+BAA+B,CAAC,GAAG,IAAIqE,KAAK,CAAC2B,OAAO,EAAE,CAAC;MACpFpF,mBAAmB,CAAC,OAAO,CAAC;MAC5BJ,eAAe,CAAC,IAAI,CAAC;IACtB;EACD,CAAC;EACD,MAAMyF,kBAAkB,GAAInC,KAAU,IAAK;IAC1C,MAAMoC,YAAY,GAAGpC,KAAK,CAACM,MAAM,CAACD,KAAK;IACvC,MAAMI,oBAAoB,GAAGhB,aAAa,CAACiB,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,IAAI,KAAKwB,YAAY,CAAC;IACnF,IAAI3B,oBAAoB,EAAE;MACzBb,yBAAyB,CAAC3C,cAAc,CAAC;IAC1C;EAED,CAAC;EAED,MAAMoF,mBAAmB,GAAGA,CAAA,KAAM;IACjC3F,eAAe,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,oBACCd,OAAA;IAAA0G,QAAA,GACEjG,SAAS,gBACTT,OAAA;MAAK2G,SAAS,EAAC,qBAAqB;MAAAD,QAAA,eACpC1G,OAAA;QAAK2G,SAAS,EAAC,0BAA0B;QAAAD,QAAA,gBACvC1G,OAAA;UAAK2G,SAAS,EAAC,iBAAiB;UAAAD,QAAA,gBACjC1G,OAAA;YAAK2G,SAAS,EAAC,aAAa;YAAAD,QAAA,EAAEpG,SAAS,CAAC,gBAAgB;UAAC;YAAAsG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChE/G,OAAA;YACEgH,OAAO,EAAEA,CAAA,KAAMtG,YAAY,CAAC,KAAK,CAAE;YACnCiG,SAAS,EAAC,iBAAiB;YAC3BM,KAAK,EAAC,4BAA4B;YAClCC,CAAC,EAAC,KAAK;YACPC,CAAC,EAAC,KAAK;YACPC,KAAK,EAAC,IAAI;YACVC,MAAM,EAAC,IAAI;YACXC,OAAO,EAAC,WAAW;YAAAZ,QAAA,eAEnB1G,OAAA;cAAMuH,CAAC,EAAC;YAA+M;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5N,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACN/G,OAAA;UAAK2G,SAAS,EAAC,0BAA0B;UAAAD,QAAA,eAC1C1G,OAAA,CAACR,IAAI;YAACgI,SAAS;YAAAd,QAAA,eACb1G,OAAA,CAACR,IAAI;cAACiI,IAAI;cAAAf,QAAA,gBACX1G,OAAA,CAACP,WAAW;gBAACiI,SAAS;gBAACC,QAAQ;gBAAAjB,QAAA,gBAC7B1G,OAAA;kBAAO4H,OAAO,EAAC,cAAc;kBAAAlB,QAAA,EAAEpG,SAAS,CAAC,cAAc;gBAAC;kBAAAsG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjE/G,OAAA,CAACZ,SAAS;kBACXyI,EAAE,EAAC,cAAc;kBACjBrD,IAAI,EAAC,aAAa;kBAClBmD,QAAQ;kBACRlD,KAAK,EAAExB,MAAM,CAACjB,WAAY;kBAC1B8F,QAAQ,EAAEvD,YAAa;kBACvBwD,WAAW,EAAEzH,SAAS,CAAC,cAAc,CAAE;kBACvC0H,UAAU,EAAElG,MAAM,CAACE,WAAY;kBAC/BiG,OAAO,EAAC,UAAU;kBAClBtD,KAAK,EAAE,CAAC,CAAC7C,MAAM,CAACE,WAAY;kBACxBkG,UAAU,EAAE;oBAAEC,SAAS,EAAE;kBAAG,CAAE;kBAClCxB,SAAS,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC,eACd/G,OAAA,CAACP,WAAW;gBAACiI,SAAS;gBAACC,QAAQ;gBAAAjB,QAAA,gBAC7B1G,OAAA;kBAAO4H,OAAO,EAAC,cAAc;kBAAAlB,QAAA,EAAEpG,SAAS,CAAC,YAAY;gBAAC;kBAAAsG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/D/G,OAAA,CAACZ,SAAS;kBACXyI,EAAE,EAAC,YAAY;kBACfrD,IAAI,EAAC,WAAW;kBAChBmD,QAAQ;kBACRlD,KAAK,EAAExB,MAAM,CAAChB,SAAU;kBACxB6F,QAAQ,EAAEvD,YAAa;kBACvBwD,WAAW,EAAEzH,SAAS,CAAC,iBAAiB,CAAE;kBAC1C0H,UAAU,EAAElG,MAAM,CAACG,SAAU;kBAC7BgG,OAAO,EAAC,UAAU;kBAClBtD,KAAK,EAAE,CAAC,CAAC7C,MAAM,CAACG,SAAU;kBAC1BiG,UAAU,EAAE;oBAAEC,SAAS,EAAE;kBAAG,CAAE;kBAC9BxB,SAAS,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC,eACd/G,OAAA;gBAAK2G,SAAS,EAAC,cAAc;gBAAAD,QAAA,eAEzB1G,OAAA;kBAAKoI,KAAK,EAAE;oBAACC,OAAO,EAAC,MAAM;oBAACC,cAAc,EAAC,eAAe;oBAAEC,MAAM,EAAE;kBAAQ,CAAE;kBAAA7B,QAAA,gBAC9E1G,OAAA;oBAAMoI,KAAK,EAAE;sBAAEI,UAAU,EAAE;oBAAO,CAAE;oBAAA9B,QAAA,EAAC;kBAE7C;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACP/G,OAAA;oBAAO2G,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC/B1G,OAAA;sBACCyI,IAAI,EAAC,UAAU;sBACfC,OAAO,EAAEzF,MAAM,CAACU,GAAI,CAAC;sBAAA;sBACrBmE,QAAQ,EAAGa,CAAC,IACXzF,SAAS,CAACsC,IAAI,KAAK;wBAClB,GAAGA,IAAI;wBACP7B,GAAG,EAAEgF,CAAC,CAACjE,MAAM,CAACgE;sBACf,CAAC,CAAC,CACF;sBACQlE,IAAI,EAAElE,SAAS,CAAC,KAAK;oBAAE;sBAAAsG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChC,CAAC,eACF/G,OAAA;sBAAM2G,SAAS,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eAEN/G,OAAA;gBAAK2G,SAAS,EAAC,cAAc;gBAAAD,QAAA,eAEE1G,OAAA;kBAAKoI,KAAK,EAAE;oBAACC,OAAO,EAAC,MAAM;oBAACC,cAAc,EAAC,eAAe;oBAAEC,MAAM,EAAE;kBAAQ,CAAE;kBAAA7B,QAAA,gBAC9E1G,OAAA;oBAAMoI,KAAK,EAAE;sBAAEI,UAAU,EAAE;oBAAO,CAAE;oBAAA9B,QAAA,EAAC;kBAErE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACP/G,OAAA;oBAAO2G,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC5B1G,OAAA;sBACIyI,IAAI,EAAC,UAAU;sBACfC,OAAO,EAAEzF,MAAM,CAACW,WAAY;sBAC5BkE,QAAQ,EAAGa,CAAC,IACRzF,SAAS,CAACsC,IAAI,KAAK;wBACf,GAAGA,IAAI;wBACP5B,WAAW,EAAE+E,CAAC,CAACjE,MAAM,CAACgE;sBAC1B,CAAC,CAAC,CACL;sBACmClE,IAAI,EAAElE,SAAS,CAAC,MAAM;oBAAE;sBAAAsG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/D,CAAC,eACF/G,OAAA;sBAAM2G,SAAS,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACN/G,OAAA;UAAK2G,SAAS,EAAC,uBAAuB;UAAAD,QAAA,eACtC1G,OAAA,CAACX,MAAM;YACLsH,SAAS,EAAE,kBACT,CAACR,kBAAkB,CAAClD,MAAM,CAACjB,WAAW,CAAC,GAAG,SAAS,GAAG,EAAE,EACvD;YACHgF,OAAO,EAAEvB;YACX;YAAA;YAAAiB,QAAA,EAEGpG,SAAS,CAAC,MAAM;UAAC;YAAAsG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,GAGP,EACA,eACD/G,OAAA,CAACV,QAAQ;MACRqH,SAAS,EAAC,oBAAoB;MAC9BiC,IAAI,EAAE/H,YAAa;MACnBgI,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAErC,mBAAoB;MAC7BsC,YAAY,EAAE;QAAEC,QAAQ,EAAE,KAAK;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAvC,QAAA,eAExD1G,OAAA,CAACT,KAAK;QACLuJ,OAAO,EAAErC,mBAAoB;QAC7ByC,QAAQ,EAAEjI;QACV;QAAA;QAAAyF,QAAA,EAEC3F;MAAe;QAAA6F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAER,CAAC;AAAC5G,EAAA,CA3YIF,aAAa;EAAA,QACOJ,cAAc,EAMND,OAAO,EAKfF,WAAW,EAKnBC,WAAW,EACVG,MAAM;AAAA;AAAAqJ,EAAA,GAlBnBlJ,aAAa;AA6YnB,eAAeA,aAAa;AAAC,IAAAkJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}