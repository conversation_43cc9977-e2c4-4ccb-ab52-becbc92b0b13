{"ast": null, "code": "// axiosInterceptor.ts\nimport axios from 'axios';\nimport userManager from '../components/auth/UseAuth';\nexport const adminUrl = process.env.REACT_APP_ADMIN_API;\nexport const userUrl = process.env.REACT_APP_USER_API;\nexport const idsUrl = process.env.REACT_APP_IDS_API;\nconst adminApiService = axios.create({\n  baseURL: adminUrl\n});\nconst userApiService = axios.create({\n  baseURL: userUrl\n});\nconst idsApiService = axios.create({\n  baseURL: idsUrl\n});\nidsApiService.interceptors.request.use(async config => {\n  const user = await userManager.getUser();\n  const token = (user === null || user === void 0 ? void 0 : user.access_token) || localStorage.getItem(\"access_token\");\n  if (token) {\n    config.headers['Authorization'] = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\nadminApiService.interceptors.request.use(async config => {\n  const user = await userManager.getUser();\n  const token = (user === null || user === void 0 ? void 0 : user.access_token) || localStorage.getItem(\"access_token\");\n  if (token) {\n    config.headers['Authorization'] = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\nuserApiService.interceptors.request.use(async config => {\n  const user = await userManager.getUser();\n  const token = (user === null || user === void 0 ? void 0 : user.access_token) || localStorage.getItem(\"access_token\");\n  if (token) {\n    config.headers['Authorization'] = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\nconst SESSION_EXPIRED_KEY = 'session_expired_message';\nexport const setupInterceptors = (navigate, showSnackbar) => {\n  const handleError = error => {\n    if (error.response && (error.response.status === 401 || error.response.status === 403)) {\n      // Check if we're in auto-login flow - don't clear tokens during auto-login\n      const urlParams = new URLSearchParams(window.location.search);\n      const hasAutoLoginParams = urlParams.has('token') && urlParams.has('user_id') && urlParams.has('org_id');\n      const isAutoLoginCompleted = sessionStorage.getItem('autoLoginCompleted') === 'true';\n      const isOnLoginPage = window.location.pathname.includes('/login');\n\n      // Also check if we have valid userInfo indicating a recent auto-login\n      const userInfo = localStorage.getItem('userInfo');\n      const hasValidUserInfo = userInfo && userInfo !== '{}' && userInfo !== 'null';\n      let hasAutoLoginUserData = false;\n      if (hasValidUserInfo) {\n        try {\n          const parsedUserInfo = JSON.parse(userInfo);\n          hasAutoLoginUserData = parsedUserInfo['user'] && parsedUserInfo['oidc-info'];\n        } catch (e) {\n          // Ignore parse errors\n        }\n      }\n\n      // Check if this is a free trial token\n      const isFreeTrialToken = sessionStorage.getItem('isFreeTrialToken') === 'true';\n\n      // Don't clear tokens if we're in auto-login flow, just completed it, have auto-login user data, or using free trial token\n      if (hasAutoLoginParams || isAutoLoginCompleted || hasAutoLoginUserData || isFreeTrialToken) {\n        return Promise.reject(error);\n      }\n\n      // Set session expired flag before navigation\n      sessionStorage.setItem(SESSION_EXPIRED_KEY, 'true');\n\n      // Show immediate feedback\n      showSnackbar('Session has expired', 'error');\n\n      // Clear user data\n      localStorage.removeItem('access_token');\n      localStorage.removeItem('userInfo');\n\n      // Navigate to login page\n      navigate('/login');\n    }\n    return Promise.reject(error);\n  };\n  idsApiService.interceptors.response.use(response => response, handleError);\n  adminApiService.interceptors.response.use(response => response, handleError);\n  userApiService.interceptors.response.use(response => response, handleError);\n};\n\n// Helper function to check and clear session expired message\nexport const checkSessionExpired = () => {\n  const sessionExpired = sessionStorage.getItem(SESSION_EXPIRED_KEY) === 'true';\n  if (sessionExpired) {\n    sessionStorage.removeItem(SESSION_EXPIRED_KEY);\n  }\n  return sessionExpired;\n};\nexport { userApiService, adminApiService, idsApiService };", "map": {"version": 3, "names": ["axios", "userManager", "adminUrl", "process", "env", "REACT_APP_ADMIN_API", "userUrl", "REACT_APP_USER_API", "idsUrl", "REACT_APP_IDS_API", "adminApiService", "create", "baseURL", "userApiService", "idsApiService", "interceptors", "request", "use", "config", "user", "getUser", "token", "access_token", "localStorage", "getItem", "headers", "error", "Promise", "reject", "SESSION_EXPIRED_KEY", "setupInterceptors", "navigate", "showSnackbar", "handleError", "response", "status", "urlParams", "URLSearchParams", "window", "location", "search", "hasAutoLoginParams", "has", "isAutoLoginCompleted", "sessionStorage", "isOnLoginPage", "pathname", "includes", "userInfo", "hasValidUserInfo", "hasAutoLoginUserData", "parsedUserInfo", "JSON", "parse", "e", "isFreeTrialToken", "setItem", "removeItem", "checkSessionExpired", "sessionExpired"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/services/APIService.tsx"], "sourcesContent": ["// axiosInterceptor.ts\r\nimport axios from 'axios';\r\nimport userManager from '../components/auth/UseAuth';\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { Alert } from '@mui/material';\r\n\r\n\r\nexport const adminUrl = process.env.REACT_APP_ADMIN_API;\r\nexport const userUrl = process.env.REACT_APP_USER_API;\r\nexport const idsUrl = process.env.REACT_APP_IDS_API;\r\n\r\nexport type JToken = any;\r\n\r\nconst adminApiService = axios.create({    \r\n  baseURL: adminUrl, \r\n});\r\n\r\nconst userApiService = axios.create({    \r\n  baseURL: userUrl,  \r\n});\r\n\r\nconst idsApiService = axios.create({\r\n  baseURL:idsUrl,\r\n})\r\n\r\nidsApiService.interceptors.request.use(\r\n  async (config) => {\r\n    const user = await userManager.getUser();\r\n    const token = user?.access_token || localStorage.getItem(\"access_token\");\r\n        if (token) {\r\n      config.headers['Authorization'] = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nadminApiService.interceptors.request.use(\r\n  async (config) => {\r\n    const user = await userManager.getUser();\r\n    const token = user?.access_token || localStorage.getItem(\"access_token\");\r\n        if (token) {\r\n      config.headers['Authorization'] = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n\r\nuserApiService.interceptors.request.use(\r\n  async (config) => {\r\n    const user = await userManager.getUser();\r\n    const token = user?.access_token || localStorage.getItem(\"access_token\");\r\n        if (token) {\r\n      config.headers['Authorization'] = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nconst SESSION_EXPIRED_KEY = 'session_expired_message';\r\n\r\nexport const setupInterceptors = (\r\n  navigate: ReturnType<typeof useNavigate>,\r\n  showSnackbar: (message: string, severity?: 'success' | 'error' | 'warning' | 'info') => void\r\n) => {\r\n  const handleError = (error: any) => {\r\n    if (error.response && (error.response.status === 401 || error.response.status === 403)) {\r\n      // Check if we're in auto-login flow - don't clear tokens during auto-login\r\n      const urlParams = new URLSearchParams(window.location.search);\r\n      const hasAutoLoginParams = urlParams.has('token') && urlParams.has('user_id') && urlParams.has('org_id');\r\n      const isAutoLoginCompleted = sessionStorage.getItem('autoLoginCompleted') === 'true';\r\n      const isOnLoginPage = window.location.pathname.includes('/login');\r\n\r\n      // Also check if we have valid userInfo indicating a recent auto-login\r\n      const userInfo = localStorage.getItem('userInfo');\r\n      const hasValidUserInfo = userInfo && userInfo !== '{}' && userInfo !== 'null';\r\n      let hasAutoLoginUserData = false;\r\n\r\n      if (hasValidUserInfo) {\r\n        try {\r\n          const parsedUserInfo = JSON.parse(userInfo);\r\n          hasAutoLoginUserData = parsedUserInfo['user'] && parsedUserInfo['oidc-info'];\r\n        } catch (e) {\r\n          // Ignore parse errors\r\n        }\r\n      }\r\n\r\n      // Check if this is a free trial token\r\n      const isFreeTrialToken = sessionStorage.getItem('isFreeTrialToken') === 'true';\r\n\r\n      // Don't clear tokens if we're in auto-login flow, just completed it, have auto-login user data, or using free trial token\r\n      if (hasAutoLoginParams || isAutoLoginCompleted || hasAutoLoginUserData || isFreeTrialToken) {\r\n\r\n        return Promise.reject(error);\r\n      }\r\n\r\n      // Set session expired flag before navigation\r\n      sessionStorage.setItem(SESSION_EXPIRED_KEY, 'true');\r\n\r\n      // Show immediate feedback\r\n      showSnackbar('Session has expired', 'error');\r\n\r\n      // Clear user data\r\n      localStorage.removeItem('access_token');\r\n      localStorage.removeItem('userInfo');\r\n\r\n      // Navigate to login page\r\n      navigate('/login');\r\n    }\r\n    return Promise.reject(error);\r\n  };\r\n\r\n  idsApiService.interceptors.response.use(\r\n    (response) => response,\r\n    handleError\r\n  );\r\n\r\n  adminApiService.interceptors.response.use(\r\n    (response) => response,\r\n    handleError\r\n  );\r\n\r\n  userApiService.interceptors.response.use(\r\n    (response) => response,\r\n    handleError\r\n  );\r\n};\r\n\r\n// Helper function to check and clear session expired message\r\nexport const checkSessionExpired = (): boolean => {\r\n  const sessionExpired = sessionStorage.getItem(SESSION_EXPIRED_KEY) === 'true';\r\n  if (sessionExpired) {\r\n    sessionStorage.removeItem(SESSION_EXPIRED_KEY);\r\n  }\r\n  return sessionExpired;\r\n};\r\n\r\nexport { userApiService, adminApiService, idsApiService };"], "mappings": "AAAA;AACA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,WAAW,MAAM,4BAA4B;AAKpD,OAAO,MAAMC,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,mBAAmB;AACvD,OAAO,MAAMC,OAAO,GAAGH,OAAO,CAACC,GAAG,CAACG,kBAAkB;AACrD,OAAO,MAAMC,MAAM,GAAGL,OAAO,CAACC,GAAG,CAACK,iBAAiB;AAInD,MAAMC,eAAe,GAAGV,KAAK,CAACW,MAAM,CAAC;EACnCC,OAAO,EAAEV;AACX,CAAC,CAAC;AAEF,MAAMW,cAAc,GAAGb,KAAK,CAACW,MAAM,CAAC;EAClCC,OAAO,EAAEN;AACX,CAAC,CAAC;AAEF,MAAMQ,aAAa,GAAGd,KAAK,CAACW,MAAM,CAAC;EACjCC,OAAO,EAACJ;AACV,CAAC,CAAC;AAEFM,aAAa,CAACC,YAAY,CAACC,OAAO,CAACC,GAAG,CACpC,MAAOC,MAAM,IAAK;EAChB,MAAMC,IAAI,GAAG,MAAMlB,WAAW,CAACmB,OAAO,CAAC,CAAC;EACxC,MAAMC,KAAK,GAAG,CAAAF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEG,YAAY,KAAIC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;EACpE,IAAIH,KAAK,EAAE;IACbH,MAAM,CAACO,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUJ,KAAK,EAAE;EACrD;EACA,OAAOH,MAAM;AACf,CAAC,EACAQ,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAEDhB,eAAe,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACtC,MAAOC,MAAM,IAAK;EAChB,MAAMC,IAAI,GAAG,MAAMlB,WAAW,CAACmB,OAAO,CAAC,CAAC;EACxC,MAAMC,KAAK,GAAG,CAAAF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEG,YAAY,KAAIC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;EACpE,IAAIH,KAAK,EAAE;IACbH,MAAM,CAACO,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUJ,KAAK,EAAE;EACrD;EACA,OAAOH,MAAM;AACf,CAAC,EACAQ,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAGDb,cAAc,CAACE,YAAY,CAACC,OAAO,CAACC,GAAG,CACrC,MAAOC,MAAM,IAAK;EAChB,MAAMC,IAAI,GAAG,MAAMlB,WAAW,CAACmB,OAAO,CAAC,CAAC;EACxC,MAAMC,KAAK,GAAG,CAAAF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEG,YAAY,KAAIC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;EACpE,IAAIH,KAAK,EAAE;IACbH,MAAM,CAACO,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUJ,KAAK,EAAE;EACrD;EACA,OAAOH,MAAM;AACf,CAAC,EACAQ,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMG,mBAAmB,GAAG,yBAAyB;AAErD,OAAO,MAAMC,iBAAiB,GAAGA,CAC/BC,QAAwC,EACxCC,YAA4F,KACzF;EACH,MAAMC,WAAW,GAAIP,KAAU,IAAK;IAClC,IAAIA,KAAK,CAACQ,QAAQ,KAAKR,KAAK,CAACQ,QAAQ,CAACC,MAAM,KAAK,GAAG,IAAIT,KAAK,CAACQ,QAAQ,CAACC,MAAM,KAAK,GAAG,CAAC,EAAE;MACtF;MACA,MAAMC,SAAS,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;MAC7D,MAAMC,kBAAkB,GAAGL,SAAS,CAACM,GAAG,CAAC,OAAO,CAAC,IAAIN,SAAS,CAACM,GAAG,CAAC,SAAS,CAAC,IAAIN,SAAS,CAACM,GAAG,CAAC,QAAQ,CAAC;MACxG,MAAMC,oBAAoB,GAAGC,cAAc,CAACpB,OAAO,CAAC,oBAAoB,CAAC,KAAK,MAAM;MACpF,MAAMqB,aAAa,GAAGP,MAAM,CAACC,QAAQ,CAACO,QAAQ,CAACC,QAAQ,CAAC,QAAQ,CAAC;;MAEjE;MACA,MAAMC,QAAQ,GAAGzB,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;MACjD,MAAMyB,gBAAgB,GAAGD,QAAQ,IAAIA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,MAAM;MAC7E,IAAIE,oBAAoB,GAAG,KAAK;MAEhC,IAAID,gBAAgB,EAAE;QACpB,IAAI;UACF,MAAME,cAAc,GAAGC,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC;UAC3CE,oBAAoB,GAAGC,cAAc,CAAC,MAAM,CAAC,IAAIA,cAAc,CAAC,WAAW,CAAC;QAC9E,CAAC,CAAC,OAAOG,CAAC,EAAE;UACV;QAAA;MAEJ;;MAEA;MACA,MAAMC,gBAAgB,GAAGX,cAAc,CAACpB,OAAO,CAAC,kBAAkB,CAAC,KAAK,MAAM;;MAE9E;MACA,IAAIiB,kBAAkB,IAAIE,oBAAoB,IAAIO,oBAAoB,IAAIK,gBAAgB,EAAE;QAE1F,OAAO5B,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;MAC9B;;MAEA;MACAkB,cAAc,CAACY,OAAO,CAAC3B,mBAAmB,EAAE,MAAM,CAAC;;MAEnD;MACAG,YAAY,CAAC,qBAAqB,EAAE,OAAO,CAAC;;MAE5C;MACAT,YAAY,CAACkC,UAAU,CAAC,cAAc,CAAC;MACvClC,YAAY,CAACkC,UAAU,CAAC,UAAU,CAAC;;MAEnC;MACA1B,QAAQ,CAAC,QAAQ,CAAC;IACpB;IACA,OAAOJ,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;EAC9B,CAAC;EAEDZ,aAAa,CAACC,YAAY,CAACmB,QAAQ,CAACjB,GAAG,CACpCiB,QAAQ,IAAKA,QAAQ,EACtBD,WACF,CAAC;EAEDvB,eAAe,CAACK,YAAY,CAACmB,QAAQ,CAACjB,GAAG,CACtCiB,QAAQ,IAAKA,QAAQ,EACtBD,WACF,CAAC;EAEDpB,cAAc,CAACE,YAAY,CAACmB,QAAQ,CAACjB,GAAG,CACrCiB,QAAQ,IAAKA,QAAQ,EACtBD,WACF,CAAC;AACH,CAAC;;AAED;AACA,OAAO,MAAMyB,mBAAmB,GAAGA,CAAA,KAAe;EAChD,MAAMC,cAAc,GAAGf,cAAc,CAACpB,OAAO,CAACK,mBAAmB,CAAC,KAAK,MAAM;EAC7E,IAAI8B,cAAc,EAAE;IAClBf,cAAc,CAACa,UAAU,CAAC5B,mBAAmB,CAAC;EAChD;EACA,OAAO8B,cAAc;AACvB,CAAC;AAED,SAAS9C,cAAc,EAAEH,eAAe,EAAEI,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}