/* Image upload section styles */
.qadpt-imageupload {
    width: 100%;
    height: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    cursor: pointer;
    gap: 10px;
}

.qadpt-imageupload img {
    width: 100%;
    height: 40px;
    object-fit: cover;
}

.qadpt-imageupload .upload-container {
    /* display: flex; */
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.qadpt-imageupload .icon-text {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 10px;
    align-items: center;
}

.qadpt-imageupload .icon-text h6 {
    text-align: center;
}

.qadpt-imageupload .icon-row {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-direction: column;
    justify-content: center;
}

.qadpt-imageupload .icon-row span {
    cursor: pointer;
}

.qadpt-imageupload .icon-row svg {
    height: 30px;
    width: 30px;
}

.qadpt-imageupload input[type="file"] {
    display: none;
}

/* Image popup styles */
.qadpt-imagepopup {
    height: 44px;
    width: 500px;
    margin-left: auto;
    margin-right: auto;
    margin-top: 65px !important;
}

.qadpt-imagepopup .qadpt-imagepopup-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0 10px;
}

.qadpt-imagepopup .qadpt-imagepopup-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    cursor: pointer;
}

.qadpt-imagepopup .qadpt-imagepopup-text {
    font-size: 12px;
}

.qadpt-imagepopup .qadpt-imagepopup-upload {
    display: none;
}
