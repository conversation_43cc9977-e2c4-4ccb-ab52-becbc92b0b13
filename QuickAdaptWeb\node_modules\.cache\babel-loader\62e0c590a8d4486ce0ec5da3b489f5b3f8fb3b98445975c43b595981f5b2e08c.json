{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptWeb\\\\src\\\\components\\\\account\\\\AccountEdit.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { getAllOrganizations } from \"../../services/OrganizationService\";\nimport { SubmitAccountDetails, fetchAccountsById } from \"../../services/AccountService\";\nimport { TextField, Button, Grid, FormControl } from \"@mui/material\";\nimport { useSnackbar } from \"../../SnackbarContext\";\nimport { useAuth } from \"../auth/AuthProvider\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EditAccount = props => {\n  _s();\n  var _userDetails$Organiza;\n  const {\n    showEditPopup,\n    setShowEditPopup,\n    accountidedit,\n    setModels,\n    setLoading,\n    setTotalcount,\n    orderByField,\n    filters\n  } = props;\n  const [organizations, setOrganizations] = useState([]);\n  const [snackbarOpen, setSnackbarOpen] = useState(false);\n  const [snackbarMessage, setSnackbarMessage] = useState(\"\");\n  const [snackbarSeverity, setSnackbarSeverity] = useState(\"success\");\n  const [skip] = useState(\"0\");\n  const [top] = useState(\"30\");\n  const {\n    openSnackbar\n  } = useSnackbar();\n  const {\n    userDetails\n  } = useAuth();\n  const [OrganizationId, setOrganizationId] = useState((_userDetails$Organiza = userDetails === null || userDetails === void 0 ? void 0 : userDetails.OrganizationId) !== null && _userDetails$Organiza !== void 0 ? _userDetails$Organiza : \"\");\n  const [, setUser] = useState(null);\n  const [isValid, setIsValid] = useState(true);\n  const [hasChanges, setHasChanges] = useState(false);\n  //const [totalcount, setTotalcount] = useState(0);\n  // const [paginationModel, setPaginationModel] = useState<GridPaginationModel>({\n  // \tpage: 0,\n  // \tpageSize: 10,\n  // });\n  const [errors, setErrors] = useState({\n    AccountName: \"\",\n    DomainUrl: \"\"\n  });\n  const [originalAccountDetails, setOriginalAccountDetails] = useState({\n    AccountId: \"\",\n    AccountName: \"\",\n    AccountType: \"\",\n    CreatedBy: \"\",\n    CreatedDate: \"\",\n    OrganizationId: \"\",\n    UpdatedBy: userDetails === null || userDetails === void 0 ? void 0 : userDetails.UserName,\n    UpdatedDate: \"\",\n    Active: Boolean(true),\n    DomainUrl: \"\",\n    Rtl: false,\n    IsAIEnabled: false\n  });\n  const [AccountDetails, setAccountDetails] = useState({\n    AccountId: \"\",\n    AccountName: \"\",\n    AccountType: \"\",\n    CreatedBy: \"\",\n    CreatedDate: \"\",\n    OrganizationId: \"\",\n    UpdatedBy: userDetails === null || userDetails === void 0 ? void 0 : userDetails.UserName,\n    UpdatedDate: \"\",\n    Active: Boolean(true),\n    DomainUrl: \"\",\n    IsAIEnabled: false,\n    Rtl: false\n  });\n  const {\n    t: translate\n  } = useTranslation();\n  useEffect(() => {\n    if (showEditPopup) {\n      fetchAccountDetails(accountidedit);\n    }\n  }, [showEditPopup]);\n  // Validate inputs whenever AccountDetails change\n  useEffect(() => {\n    const newErrors = {};\n\n    // Account Name Validation\n    if (AccountDetails.AccountName) {\n      const trimmedAccountName = AccountDetails.AccountName.trim();\n      if (!trimmedAccountName) {\n        newErrors.AccountName = translate(\"Account Name cannot be only spaces.\");\n      } else if (trimmedAccountName.length < 3) {\n        newErrors.AccountName = translate(\"Account Name must be at least 3 characters.\");\n      } else if (trimmedAccountName.length > 50) {\n        newErrors.AccountName = translate(\"Account Name cannot exceed 50 characters.\");\n      } else if (/[^a-zA-Z\\s]/g.test(trimmedAccountName)) {\n        newErrors.AccountName = translate(\"Account Name can only contain letters and spaces.\");\n      } else {\n        newErrors.AccountName = \"\";\n      }\n    }\n\n    // Domain URL Validation\n    if (AccountDetails.DomainUrl) {\n      const trimmedDomainUrl = AccountDetails.DomainUrl.trim();\n      const domainPattern = /^(https?:\\/\\/)?([a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,}(\\/)?$/;\n      if (!domainPattern.test(trimmedDomainUrl)) {\n        newErrors.DomainUrl = translate(\"Please enter a valid full domain URL (e.g., https://example.com).\");\n      } else {\n        newErrors.DomainUrl = \"\";\n      }\n    }\n    setErrors(prev => ({\n      ...prev,\n      ...newErrors\n    }));\n  }, [AccountDetails]);\n  useEffect(() => {\n    const userInfoString = localStorage.getItem(\"userInfo\");\n    if (userInfoString) {\n      try {\n        const userInfo = JSON.parse(userInfoString);\n        if (userInfo['user']) {\n          const parsedUser = JSON.parse(userInfo['user']);\n          setUser(parsedUser);\n          if (parsedUser) {\n            var _parsedUser$Organizat;\n            const OrgId = (_parsedUser$Organizat = parsedUser.OrganizationId) !== null && _parsedUser$Organizat !== void 0 ? _parsedUser$Organizat : '';\n            setOrganizationId(OrgId);\n          }\n        }\n      } catch (error) {\n        console.error(\"Error parsing userInfo: \", error);\n      }\n    } else if (userDetails) {\n      setUser(userDetails);\n      if (userDetails) {\n        var _userDetails$Organiza2;\n        const OrgId = (_userDetails$Organiza2 = userDetails.OrganizationId) !== null && _userDetails$Organiza2 !== void 0 ? _userDetails$Organiza2 : '';\n        setOrganizationId(OrgId);\n      }\n    }\n  }, []);\n\n  // Function to check if current account details differ from original data\n  const checkForChanges = currentDetails => {\n    const fieldsToCompare = ['AccountName', 'DomainUrl', 'IsAIEnabled', 'Rtl'];\n    return fieldsToCompare.some(field => currentDetails[field] !== originalAccountDetails[field]);\n  };\n  const fetchAccountDetails = async id => {\n    try {\n      const responseData = await fetchAccountsById(id);\n      if (!responseData) {\n        throw new Error(\"Network response was not ok\");\n      }\n      const accountData = {\n        AccountId: responseData.AccountId,\n        AccountName: responseData.AccountName,\n        AccountType: responseData.AccountType,\n        CreatedBy: responseData.CreatedBy,\n        CreatedDate: responseData.CreatedDate,\n        OrganizationId: responseData.OrganizationId,\n        UpdatedBy: userDetails === null || userDetails === void 0 ? void 0 : userDetails.UserName,\n        UpdatedDate: new Date().toUTCString(),\n        Active: true,\n        DomainUrl: responseData.DomainUrl,\n        IsAIEnabled: responseData.IsAIEnabled,\n        Rtl: responseData.Rtl || false\n      };\n\n      // Set both original and current data\n      setOriginalAccountDetails(accountData);\n      setAccountDetails(accountData);\n\n      // Reset change tracking\n      setHasChanges(false);\n    } catch (error) {\n      console.error(\"Failed to fetch user details:\", error);\n    }\n  };\n  const handleOrganizationDropdownOpen = async () => {\n    try {\n      const response = await getAllOrganizations(setOrganizations, setLoading);\n    } catch (error) {\n      console.error(\"Error fetching organizations:\", error);\n    }\n  };\n  const alphanumericRegex = /^[a-zA-Z0-9]*$/;\n  const handleChange = event => {\n    const {\n      name,\n      value\n    } = event.target;\n    let error = \"\";\n    let processedValue = value;\n    if (name === \"AccountType\") {\n      const selectedOrganization = organizations.find(org => org.Name === value);\n      if (selectedOrganization) {\n        const newAccountDetails = {\n          ...AccountDetails,\n          AccountType: value,\n          OrganizationId: OrganizationId\n        };\n        setAccountDetails(newAccountDetails);\n        setHasChanges(checkForChanges(newAccountDetails));\n        return;\n      }\n    } else if (name === \"AccountName\") {\n      // Allow only letters and spaces, remove special characters and numbers\n      processedValue = value.replace(/[^a-zA-Z\\s]/g, \"\");\n\n      // Validate trimmed value but keep original spacing for user experience\n      const trimmedValue = processedValue.trim();\n      if (trimmedValue.length < 3) {\n        error = translate(\"Account Name must be at least 3 characters.\");\n      } else if (trimmedValue.length > 50) {\n        error = translate(\"Account Name cannot exceed 50 characters.\");\n      } else if (trimmedValue === \"\") {\n        error = translate(\"Account Name cannot be only spaces.\");\n      }\n    } else if (name === \"DomainUrl\") {\n      // Trim spaces for domain URL\n      processedValue = value.trim();\n      const domainPattern = /^(https?:\\/\\/)?([a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,}(\\/)?$/;\n      if (processedValue && !domainPattern.test(processedValue)) {\n        error = translate(\"Please enter a valid full domain URL (e.g., https://example.com).\");\n      }\n    }\n    const newAccountDetails = {\n      ...AccountDetails,\n      [name]: processedValue\n    };\n    setAccountDetails(newAccountDetails);\n    setErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Check for changes after updating account details\n    setHasChanges(checkForChanges(newAccountDetails));\n  };\n  const handleSwitchChange = event => {\n    const {\n      name,\n      checked\n    } = event.target;\n    const newAccountDetails = {\n      ...AccountDetails,\n      [name]: checked\n    };\n    setAccountDetails(newAccountDetails);\n    setHasChanges(checkForChanges(newAccountDetails));\n  };\n\n  // const handleChange = (event: ChangeEvent<HTMLInputElement>) => {\n  // \tconst { name, value } = event.target;\n  // \tconst alphanumericWithSpacesRegex = /^[a-zA-Z0-9\\s]*$/; // Allow letters, numbers, and spaces\n  // \tlet error = \"\";\n  // \tlet processedValue = value;\n\n  // \tif (name === \"AccountType\") {\n  // \t\tconst selectedOrganization = organizations.find((org) => org.Name === value);\n  // \t\tif (selectedOrganization) {\n  // \t\t\tsetAccountDetails((values) => ({\n  // \t\t\t\t...values,\n  // \t\t\t\tAccountType: value,\n  // \t\t\t\tOrganizationId: \"********-*********-134dc53c-f123-4655-aa39-0529fa976863\", // Placeholder ID\n  // \t\t\t}));\n  // \t\t}\n  // \t} else if (name === \"AccountName\") {\n  // \t\t// Remove special characters\n  // \t\tprocessedValue = value.replace(/[^a-zA-Z0-9\\s]/g, \"\");\n  // \t}\n\n  // \tsetAccountDetails((prev) => ({ ...prev, [name]: processedValue }));\n\n  // \tif (name === \"AccountName\" && processedValue.length < 5) {\n  // \t\terror = \"Account Name must be at least 5 characters.\";\n  // \t} else {\n  // \t\tsetAccountDetails((values) => ({ ...values, [name]: value }));\n  // \t}\n  // \tsetErrors((prev) => ({ ...prev, [name]: error }));\n  // };\n\n  const handleSubmit = async e => {\n    e.preventDefault();\n\n    // Trim leading and trailing spaces from AccountName and DomainUrl before validation and submission\n    const trimmedAccountName = AccountDetails.AccountName.trim();\n    const trimmedDomainUrl = AccountDetails.DomainUrl.trim();\n    const updatedAccountDetails = {\n      ...AccountDetails,\n      AccountName: trimmedAccountName,\n      DomainUrl: trimmedDomainUrl\n    };\n    const newErrors = {};\n    let isValid = true;\n\n    // Validate AccountName\n    if (!trimmedAccountName) {\n      newErrors.AccountName = translate(\"Account Name is required.\");\n      isValid = false;\n    } else if (trimmedAccountName.length < 3) {\n      newErrors.AccountName = translate(\"Account Name must be at least 3 characters.\");\n      isValid = false;\n    } else if (trimmedAccountName.length > 50) {\n      newErrors.AccountName = translate(\"Account Name cannot exceed 50 characters.\");\n      isValid = false;\n    } else if (/[^a-zA-Z\\s]/g.test(trimmedAccountName)) {\n      newErrors.AccountName = translate(\"Account Name can only contain letters and spaces.\");\n      isValid = false;\n    }\n\n    // Validate DomainUrl\n    if (!trimmedDomainUrl) {\n      newErrors.DomainUrl = translate(\"Domain Url is required.\");\n      isValid = false;\n    } else {\n      const domainPattern = /^(https?:\\/\\/)?([a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,}(\\/)?$/;\n      if (!domainPattern.test(trimmedDomainUrl)) {\n        newErrors.DomainUrl = translate(\"Please enter a valid full domain URL (e.g., https://example.com).\");\n        isValid = false;\n      }\n    }\n    setErrors(newErrors);\n    if (!isValid) {\n      // Show first error in snackbar\n      const firstError = newErrors.AccountName || newErrors.DomainUrl;\n      if (firstError) {\n        setSnackbarMessage(firstError);\n        setSnackbarSeverity(\"error\");\n        setSnackbarOpen(true);\n      }\n      return;\n    }\n    setLoading(true);\n    try {\n      await SubmitAccountDetails(setLoading, setModels, setShowEditPopup, updatedAccountDetails, OrganizationId, skip, top, setTotalcount, openSnackbar, orderByField, filters);\n    } catch (error) {\n      // Handle error\n    }\n  };\n  const handleSnackbarClose = () => {\n    setSnackbarOpen(false);\n  };\n  return showEditPopup && /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"qadpt-modal-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-accountcreatepopup\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-title-sec\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-title\",\n          children: translate('Edit Account')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n          onClick: () => setShowEditPopup(false),\n          className: \"qadpt-closeicon\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          x: \"0px\",\n          y: \"0px\",\n          width: \"24\",\n          height: \"24\",\n          viewBox: \"0 0 50 50\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M 7.71875 6.28125 L 6.28125 7.71875 L 23.5625 25 L 6.28125 42.28125 L 7.71875 43.71875 L 25 26.4375 L 42.28125 43.71875 L 43.71875 42.28125 L 26.4375 25 L 43.71875 7.71875 L 42.28125 6.28125 L 25 23.5625 Z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-accountcreatefield\",\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              children: [/*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"edit-account-name\",\n                  children: translate(\"Account Name\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                  id: \"edit-account-name\",\n                  name: \"AccountName\",\n                  value: AccountDetails.AccountName,\n                  onChange: handleChange,\n                  placeholder: translate(\"Enter Account Name\"),\n                  helperText: errors.AccountName && translate(errors.AccountName),\n                  variant: \"outlined\",\n                  inputProps: {\n                    maxLength: 60\n                  },\n                  error: !!errors.AccountName,\n                  className: `qadpt-acctfield ${errors.AccountName ? 'qadpt-error' : ''}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 15\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"edit-account-domain\",\n                  children: translate(\"Domain Url\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                  id: \"edit-account-domain\",\n                  name: \"DomainUrl\",\n                  value: AccountDetails.DomainUrl,\n                  onChange: handleChange,\n                  placeholder: translate(\"Enter Domain Url\"),\n                  helperText: errors.DomainUrl && translate(errors.DomainUrl),\n                  variant: \"outlined\",\n                  error: !!errors.DomainUrl,\n                  inputProps: {\n                    maxLength: 50\n                  },\n                  className: `qadpt-acctfield ${errors.DomainUrl ? 'qadpt-error' : ''}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 15\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-txtfld\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    margin: \"10px 0\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      marginLeft: '10px'\n                    },\n                    children: translate(\"RTL\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 423,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"toggle-switch\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      checked: AccountDetails.Rtl,\n                      onChange: e => {\n                        const newValue = e.target.checked;\n                        setAccountDetails(prev => ({\n                          ...prev,\n                          Rtl: newValue\n                        }));\n                        setHasChanges(checkForChanges({\n                          ...AccountDetails,\n                          Rtl: newValue\n                        }));\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 427,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"slider\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 439,\n                      columnNumber: 19\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 426,\n                    columnNumber: 17\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-txtfld\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    margin: \"10px 0\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      marginLeft: '10px'\n                    },\n                    children: translate(\"Dona\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 446,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"toggle-switch\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      checked: AccountDetails.IsAIEnabled,\n                      onChange: e => {\n                        const newAccountDetails = {\n                          ...AccountDetails,\n                          IsAIEnabled: e.target.checked\n                        };\n                        setAccountDetails(newAccountDetails);\n                        setHasChanges(checkForChanges(newAccountDetails));\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 450,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"slider\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 462,\n                      columnNumber: 19\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 449,\n                    columnNumber: 17\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 13\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 11\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-account-buttons\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            className: \"qadpt-save-btn\",\n            type: \"submit\",\n            disabled: !hasChanges || Object.values(errors).some(error => error !== \"\"),\n            sx: {\n              '&.Mui-disabled': {\n                opacity: 0.5\n              }\n            },\n            children: translate(\"Save\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 471,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 5\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 3\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 367,\n    columnNumber: 4\n  }, this);\n};\n_s(EditAccount, \"F1qNiHhnOCt3VpXcilLSLSuqXsw=\", false, function () {\n  return [useSnackbar, useAuth, useTranslation];\n});\n_c = EditAccount;\nexport default EditAccount;\nvar _c;\n$RefreshReg$(_c, \"EditAccount\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "getAllOrganizations", "SubmitAccountDetails", "fetchAccountsById", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "useSnackbar", "useAuth", "useTranslation", "jsxDEV", "_jsxDEV", "EditAccount", "props", "_s", "_userDetails$Organiza", "showEditPopup", "setShowEditPopup", "accountidedit", "setModels", "setLoading", "setTotalcount", "orderByField", "filters", "organizations", "setOrganizations", "snackbarOpen", "setSnackbarOpen", "snackbarMessage", "setSnackbarMessage", "snackbarSeverity", "setSnackbarSeverity", "skip", "top", "openSnackbar", "userDetails", "OrganizationId", "setOrganizationId", "setUser", "<PERSON><PERSON><PERSON><PERSON>", "setIsValid", "has<PERSON><PERSON><PERSON>", "set<PERSON>as<PERSON><PERSON><PERSON>", "errors", "setErrors", "Account<PERSON><PERSON>", "DomainUrl", "originalAccountDetails", "setOriginalAccountDetails", "AccountId", "AccountType", "CreatedBy", "CreatedDate", "UpdatedBy", "UserName", "UpdatedDate", "Active", "Boolean", "Rtl", "IsAIEnabled", "AccountDetails", "setAccountDetails", "t", "translate", "fetchAccountDetails", "newErrors", "trimmedAccountName", "trim", "length", "test", "trimmedDomainUrl", "domainPattern", "prev", "userInfoString", "localStorage", "getItem", "userInfo", "JSON", "parse", "parsedUser", "_parsedUser$Organizat", "OrgId", "error", "console", "_userDetails$Organiza2", "checkForChanges", "currentDetails", "fieldsToCompare", "some", "field", "id", "responseData", "Error", "accountData", "Date", "toUTCString", "handleOrganizationDropdownOpen", "response", "alphanumericRegex", "handleChange", "event", "name", "value", "target", "processedValue", "selectedOrganization", "find", "org", "Name", "newAccountDetails", "replace", "trimmedValue", "handleSwitchChange", "checked", "handleSubmit", "e", "preventDefault", "updatedAccountDetails", "firstError", "handleSnackbarClose", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "xmlns", "x", "y", "width", "height", "viewBox", "d", "onSubmit", "container", "item", "fullWidth", "required", "htmlFor", "onChange", "placeholder", "helperText", "variant", "inputProps", "max<PERSON><PERSON><PERSON>", "style", "display", "justifyContent", "margin", "marginLeft", "type", "newValue", "disabled", "Object", "values", "sx", "opacity", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/components/account/AccountEdit.tsx"], "sourcesContent": ["import React, { useState, useEffect, ChangeEvent } from \"react\";\r\nimport { getAllOrganizations } from \"../../services/OrganizationService\";\r\nimport { SubmitAccountDetails, fetchAccountsById } from \"../../services/AccountService\";\r\nimport { \r\n\tTextField, \r\n\tButton, \r\n  Radio,\r\n  RadioGroup,\r\n  FormControlLabel,\r\n  Grid,\r\n  FormControl,\r\n} from \"@mui/material\";\r\nimport { useSnackbar } from \"../../SnackbarContext\";\r\nimport { useAuth } from \"../auth/AuthProvider\";\r\nimport { User } from \"../../models/User\";\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\ntype InputFields = {\r\n\tAccountName: string;\r\n\tDomainUrl: string;\r\n};\r\ntype ErrorFields = Partial<InputFields>;\r\n\r\nconst EditAccount = (props: any) => {\r\n\tconst {\r\n\t\tshowEditPopup,\r\n\t\tsetShowEditPopup,\r\n\t\taccountidedit,\r\n\t\tsetModels,\r\n\t\tsetLoading,\r\n\t\tsetTotalcount,\r\n\t\torderByField,\r\n\t\tfilters,\r\n\t} = props;\r\n\tconst [organizations, setOrganizations] = useState<any[]>([]);\r\n\tconst [snackbarOpen, setSnackbarOpen] = useState(false);\r\n\tconst [snackbarMessage, setSnackbarMessage] = useState(\"\");\r\n\tconst [snackbarSeverity, setSnackbarSeverity] = useState<\"success\" | \"error\">(\"success\");\r\n\tconst [skip] = useState(\"0\");\r\n\tconst [top] = useState(\"30\");\r\n\tconst { openSnackbar } = useSnackbar();\r\n\tconst { userDetails } = useAuth();\r\n\tconst [OrganizationId, setOrganizationId] = useState(userDetails?.OrganizationId ?? \"\");\r\n\tconst [, setUser] = useState<User | null>(null);\r\n\tconst [isValid, setIsValid] = useState(true);\r\n\tconst [hasChanges, setHasChanges] = useState(false);\r\n\t//const [totalcount, setTotalcount] = useState(0);\r\n\t// const [paginationModel, setPaginationModel] = useState<GridPaginationModel>({\r\n\t// \tpage: 0,\r\n\t// \tpageSize: 10,\r\n\t// });\r\n\tconst [errors, setErrors] = useState<ErrorFields>({\r\n\t\tAccountName: \"\",\r\n\t\tDomainUrl: \"\"\r\n\t});\r\n\r\n\tconst [originalAccountDetails, setOriginalAccountDetails] = useState({\r\n\t\tAccountId: \"\",\r\n\t\tAccountName: \"\",\r\n\t\tAccountType: \"\",\r\n\t\tCreatedBy: \"\",\r\n\t\tCreatedDate: \"\",\r\n\t\tOrganizationId: \"\",\r\n\t\tUpdatedBy: userDetails?.UserName,\r\n\t\tUpdatedDate: \"\",\r\n\t\tActive: Boolean(true),\r\n\t\tDomainUrl: \"\",\r\n\t\tRtl: false,\r\n\t\tIsAIEnabled: false\r\n\t});\r\n\r\n\tconst [AccountDetails, setAccountDetails] = useState({\r\n\t\tAccountId: \"\",\r\n\t\tAccountName: \"\",\r\n\t\tAccountType: \"\",\r\n\t\tCreatedBy: \"\",\r\n\t\tCreatedDate: \"\",\r\n\t\tOrganizationId: \"\",\r\n\t\tUpdatedBy: userDetails?.UserName,\r\n\t\tUpdatedDate: \"\",\r\n\t\tActive: Boolean(true),\r\n\t\tDomainUrl: \"\",\r\n\t\tIsAIEnabled: false,\r\n\t\tRtl: false\r\n\t});\r\n\r\n\tconst { t: translate } = useTranslation();\r\n\r\n\r\n\tuseEffect(() => {\r\n\t\tif (showEditPopup) {\r\n\t\t\tfetchAccountDetails(accountidedit);\r\n\t\t}\r\n\t}, [showEditPopup]);\r\n\t// Validate inputs whenever AccountDetails change\r\n\tuseEffect(() => {\r\n\t\tconst newErrors: ErrorFields = {};\r\n\r\n\t\t// Account Name Validation\r\n\t\tif (AccountDetails.AccountName) {\r\n\t\t\tconst trimmedAccountName = AccountDetails.AccountName.trim();\r\n\t\t\tif (!trimmedAccountName) {\r\n\t\t\t\tnewErrors.AccountName = translate(\"Account Name cannot be only spaces.\");\r\n\t\t\t} else if (trimmedAccountName.length < 3) {\r\n\t\t\t\tnewErrors.AccountName = translate(\"Account Name must be at least 3 characters.\");\r\n\t\t\t} else if (trimmedAccountName.length > 50) {\r\n\t\t\t\tnewErrors.AccountName = translate(\"Account Name cannot exceed 50 characters.\");\r\n\t\t\t} else if (/[^a-zA-Z\\s]/g.test(trimmedAccountName)) {\r\n\t\t\t\tnewErrors.AccountName = translate(\"Account Name can only contain letters and spaces.\");\r\n\t\t\t} else {\r\n\t\t\t\tnewErrors.AccountName = \"\";\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Domain URL Validation\r\n\t\tif (AccountDetails.DomainUrl) {\r\n\t\t\tconst trimmedDomainUrl = AccountDetails.DomainUrl.trim();\r\n\t\t\tconst domainPattern = /^(https?:\\/\\/)?([a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,}(\\/)?$/;\r\n\t\t\tif (!domainPattern.test(trimmedDomainUrl)) {\r\n\t\t\t\tnewErrors.DomainUrl = translate(\"Please enter a valid full domain URL (e.g., https://example.com).\");\r\n\t\t\t} else {\r\n\t\t\t\tnewErrors.DomainUrl = \"\";\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tsetErrors(prev => ({ ...prev, ...newErrors }));\r\n\t}, [AccountDetails]);\r\n\r\n\tuseEffect(() => {\r\n\t\tconst userInfoString = localStorage.getItem(\"userInfo\");\t\r\n\t\tif (userInfoString) { \r\n\t\t\ttry {\r\n\t\t\t\tconst userInfo = JSON.parse(userInfoString);\t\r\n\t\t\t\tif (userInfo['user']) {\r\n\t\t\t\t\tconst parsedUser = JSON.parse(userInfo['user']);\r\n\t\t\t\t\tsetUser(parsedUser);\t\r\n\t\t\t\t\tif (parsedUser) {\r\n\t\t\t\t\t\tconst OrgId = parsedUser.OrganizationId ?? '';\r\n\t\t\t\t\t\tsetOrganizationId(OrgId);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(\"Error parsing userInfo: \", error);\r\n\t\t\t}\r\n\t\t}\r\n\t\telse if (userDetails) {\r\n\t\t\tsetUser(userDetails);\t\r\n\t\t\tif (userDetails) {\r\n\t\t\t\tconst OrgId = userDetails.OrganizationId ?? '';\r\n\t\t\t\tsetOrganizationId(OrgId);\r\n\t\t\t}\r\n\t\t}\r\n\t}, []);\r\n\r\n\r\n\t// Function to check if current account details differ from original data\r\n\tconst checkForChanges = (currentDetails: any) => {\r\n\t\tconst fieldsToCompare = ['AccountName', 'DomainUrl', 'IsAIEnabled', 'Rtl'] as const;\r\n\t\treturn fieldsToCompare.some(field => currentDetails[field] !== originalAccountDetails[field]);\r\n\t};\r\n\r\n\tconst fetchAccountDetails = async (id: any) => {\r\n\t\ttry {\r\n\t\t\tconst responseData = await fetchAccountsById(id);\r\n\r\n\t\t\tif (!responseData) {\r\n\t\t\t\tthrow new Error(\"Network response was not ok\");\r\n\t\t\t}\r\n\r\n\t\t\tconst accountData = {\r\n\t\t\t\tAccountId: responseData.AccountId,\r\n\t\t\t\tAccountName: responseData.AccountName,\r\n\t\t\t\tAccountType: responseData.AccountType,\r\n\t\t\t\tCreatedBy: responseData.CreatedBy,\r\n\t\t\t\tCreatedDate: responseData.CreatedDate,\r\n\t\t\t\tOrganizationId: responseData.OrganizationId,\r\n\t\t\t\tUpdatedBy: userDetails?.UserName,\r\n\t\t\t\tUpdatedDate: new Date().toUTCString(),\r\n\t\t\t\tActive: true,\r\n\t\t\t\tDomainUrl: responseData.DomainUrl,\r\n\t\t\t\tIsAIEnabled: responseData.IsAIEnabled,\r\n\t\t\t\tRtl: responseData.Rtl || false\r\n\t\t\t};\r\n\r\n\t\t\t// Set both original and current data\r\n\t\t\tsetOriginalAccountDetails(accountData);\r\n\t\t\tsetAccountDetails(accountData);\r\n\r\n\t\t\t// Reset change tracking\r\n\t\t\tsetHasChanges(false);\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error(\"Failed to fetch user details:\", error);\r\n\t\t}\r\n\t};\r\n\tconst handleOrganizationDropdownOpen = async () => {\r\n\t\ttry {\r\n\t\t\tconst response = await getAllOrganizations(setOrganizations, setLoading);\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error(\"Error fetching organizations:\", error);\r\n\t\t}\r\n\t};\r\n\tconst alphanumericRegex = /^[a-zA-Z0-9]*$/;\r\n\tconst handleChange = (event: ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst { name, value } = event.target;\r\n\t\tlet error = \"\";\r\n\t\tlet processedValue = value;\r\n\r\n\t\tif (name === \"AccountType\") {\r\n\t\t\tconst selectedOrganization = organizations.find((org) => org.Name === value);\r\n\t\t\tif (selectedOrganization) {\r\n\t\t\t\tconst newAccountDetails = {\r\n\t\t\t\t\t...AccountDetails,\r\n\t\t\t\t\tAccountType: value,\r\n\t\t\t\t\tOrganizationId: OrganizationId\r\n\t\t\t\t};\r\n\t\t\t\tsetAccountDetails(newAccountDetails);\r\n\t\t\t\tsetHasChanges(checkForChanges(newAccountDetails));\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t} else if (name === \"AccountName\") {\r\n\t\t\t// Allow only letters and spaces, remove special characters and numbers\r\n\t\t\tprocessedValue = value.replace(/[^a-zA-Z\\s]/g, \"\");\r\n\r\n\t\t\t// Validate trimmed value but keep original spacing for user experience\r\n\t\t\tconst trimmedValue = processedValue.trim();\r\n\t\t\tif (trimmedValue.length < 3) {\r\n\t\t\t\terror = translate(\"Account Name must be at least 3 characters.\");\r\n\t\t\t} else if (trimmedValue.length > 50) {\r\n\t\t\t\terror = translate(\"Account Name cannot exceed 50 characters.\");\r\n\t\t\t} else if (trimmedValue === \"\") {\r\n\t\t\t\terror = translate(\"Account Name cannot be only spaces.\");\r\n\t\t\t}\r\n\t\t} else if (name === \"DomainUrl\") {\r\n\t\t\t// Trim spaces for domain URL\r\n\t\t\tprocessedValue = value.trim();\r\n\t\t\tconst domainPattern = /^(https?:\\/\\/)?([a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,}(\\/)?$/;\r\n\t\t\tif (processedValue && !domainPattern.test(processedValue)) {\r\n\t\t\t\terror = translate(\"Please enter a valid full domain URL (e.g., https://example.com).\");\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tconst newAccountDetails = { ...AccountDetails, [name]: processedValue };\r\n\t\tsetAccountDetails(newAccountDetails);\r\n\t\tsetErrors((prev) => ({ ...prev, [name]: error }));\r\n\r\n\t\t// Check for changes after updating account details\r\n\t\tsetHasChanges(checkForChanges(newAccountDetails));\r\n\t};\r\n\r\n\tconst handleSwitchChange = (event: ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst { name, checked } = event.target;\r\n\t\tconst newAccountDetails = { ...AccountDetails, [name]: checked };\r\n\t\tsetAccountDetails(newAccountDetails);\r\n\t\tsetHasChanges(checkForChanges(newAccountDetails));\r\n\t};\r\n\r\n\t// const handleChange = (event: ChangeEvent<HTMLInputElement>) => {\r\n\t// \tconst { name, value } = event.target;\r\n\t// \tconst alphanumericWithSpacesRegex = /^[a-zA-Z0-9\\s]*$/; // Allow letters, numbers, and spaces\r\n\t// \tlet error = \"\";\r\n\t// \tlet processedValue = value;\r\n\r\n\t// \tif (name === \"AccountType\") {\r\n\t// \t\tconst selectedOrganization = organizations.find((org) => org.Name === value);\r\n\t// \t\tif (selectedOrganization) {\r\n\t// \t\t\tsetAccountDetails((values) => ({\r\n\t// \t\t\t\t...values,\r\n\t// \t\t\t\tAccountType: value,\r\n\t// \t\t\t\tOrganizationId: \"********-*********-134dc53c-f123-4655-aa39-0529fa976863\", // Placeholder ID\r\n\t// \t\t\t}));\r\n\t// \t\t}\r\n\t// \t} else if (name === \"AccountName\") {\r\n\t// \t\t// Remove special characters\r\n\t// \t\tprocessedValue = value.replace(/[^a-zA-Z0-9\\s]/g, \"\");\r\n\t// \t}\r\n\r\n\t// \tsetAccountDetails((prev) => ({ ...prev, [name]: processedValue }));\r\n\r\n\t// \tif (name === \"AccountName\" && processedValue.length < 5) {\r\n\t// \t\terror = \"Account Name must be at least 5 characters.\";\r\n\t// \t} else {\r\n\t// \t\tsetAccountDetails((values) => ({ ...values, [name]: value }));\r\n\t// \t}\r\n\t// \tsetErrors((prev) => ({ ...prev, [name]: error }));\r\n\t// };\r\n\r\n\tconst handleSubmit = async (e: any) => {\r\n\t\te.preventDefault();\r\n\r\n\t\t// Trim leading and trailing spaces from AccountName and DomainUrl before validation and submission\r\n\t\tconst trimmedAccountName = AccountDetails.AccountName.trim();\r\n\t\tconst trimmedDomainUrl = AccountDetails.DomainUrl.trim();\r\n\t\tconst updatedAccountDetails = {\r\n\t\t\t...AccountDetails,\r\n\t\t\tAccountName: trimmedAccountName,\r\n\t\t\tDomainUrl: trimmedDomainUrl\r\n\t\t};\r\n\r\n\t\tconst newErrors: ErrorFields = {};\r\n\t\tlet isValid = true;\r\n\r\n\t\t// Validate AccountName\r\n\t\tif (!trimmedAccountName) {\r\n\t\t\tnewErrors.AccountName = translate(\"Account Name is required.\");\r\n\t\t\tisValid = false;\r\n\t\t} else if (trimmedAccountName.length < 3) {\r\n\t\t\tnewErrors.AccountName = translate(\"Account Name must be at least 3 characters.\");\r\n\t\t\tisValid = false;\r\n\t\t} else if (trimmedAccountName.length > 50) {\r\n\t\t\tnewErrors.AccountName = translate(\"Account Name cannot exceed 50 characters.\");\r\n\t\t\tisValid = false;\r\n\t\t} else if (/[^a-zA-Z\\s]/g.test(trimmedAccountName)) {\r\n\t\t\tnewErrors.AccountName = translate(\"Account Name can only contain letters and spaces.\");\r\n\t\t\tisValid = false;\r\n\t\t}\r\n\r\n\t\t// Validate DomainUrl\r\n\t\tif (!trimmedDomainUrl) {\r\n\t\t\tnewErrors.DomainUrl = translate(\"Domain Url is required.\");\r\n\t\t\tisValid = false;\r\n\t\t} else {\r\n\t\t\tconst domainPattern = /^(https?:\\/\\/)?([a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,}(\\/)?$/;\r\n\t\t\tif (!domainPattern.test(trimmedDomainUrl)) {\r\n\t\t\t\tnewErrors.DomainUrl = translate(\"Please enter a valid full domain URL (e.g., https://example.com).\");\r\n\t\t\t\tisValid = false;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tsetErrors(newErrors);\r\n\r\n\t\tif (!isValid) {\r\n\t\t\t// Show first error in snackbar\r\n\t\t\tconst firstError = newErrors.AccountName || newErrors.DomainUrl;\r\n\t\t\tif (firstError) {\r\n\t\t\t\tsetSnackbarMessage(firstError);\r\n\t\t\t\tsetSnackbarSeverity(\"error\");\r\n\t\t\t\tsetSnackbarOpen(true);\r\n\t\t\t}\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tsetLoading(true);\r\n\t\ttry {\r\n\t\t\tawait SubmitAccountDetails(\r\n\t\t\t\tsetLoading,\r\n\t\t\t\tsetModels,\r\n\t\t\t\tsetShowEditPopup,\r\n\t\t\t\tupdatedAccountDetails,\r\n\t\t\t\tOrganizationId,\r\n\t\t\t\tskip,\r\n\t\t\t\ttop,\r\n\t\t\t\tsetTotalcount,\r\n\t\t\t\topenSnackbar,\r\n\t\t\t\torderByField,\r\n\t\t\t\tfilters\r\n\t\t\t);\r\n\t\t} catch (error) {\r\n\t\t\t// Handle error\r\n\t\t}\r\n\t};\r\n\tconst handleSnackbarClose = () => {\r\n\t\tsetSnackbarOpen(false);\r\n\t};\r\n\r\n\treturn (\r\n\t\tshowEditPopup && (\r\n\t\t\t<div className=\"qadpt-modal-overlay\">\r\n  <div className=\"qadpt-accountcreatepopup\">\r\n    <div className=\"qadpt-title-sec\">\r\n      <div className=\"qadpt-title\">{translate('Edit Account')}</div>\r\n      <svg\r\n        onClick={() => setShowEditPopup(false)}\r\n        className=\"qadpt-closeicon\"\r\n        xmlns=\"http://www.w3.org/2000/svg\"\r\n        x=\"0px\"\r\n        y=\"0px\"\r\n        width=\"24\"\r\n        height=\"24\"\r\n        viewBox=\"0 0 50 50\"\r\n      >\r\n        <path d=\"M 7.71875 6.28125 L 6.28125 7.71875 L 23.5625 25 L 6.28125 42.28125 L 7.71875 43.71875 L 25 26.4375 L 42.28125 43.71875 L 43.71875 42.28125 L 26.4375 25 L 43.71875 7.71875 L 42.28125 6.28125 L 25 23.5625 Z\"></path>\r\n      </svg>\r\n    </div>\r\n\r\n    <form onSubmit={handleSubmit}>\r\n      <div className=\"qadpt-accountcreatefield\">\r\n        <Grid container>\r\n          <Grid item>\r\n            <FormControl fullWidth required>\r\n              <label htmlFor=\"edit-account-name\">{translate(\"Account Name\")}</label>\r\n              <TextField\r\n                id=\"edit-account-name\"\r\n                name=\"AccountName\"\r\n                value={AccountDetails.AccountName}\r\n                onChange={handleChange}\r\n                placeholder={translate(\"Enter Account Name\")}\r\n                helperText={errors.AccountName && translate(errors.AccountName)}\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\tinputProps={{ maxLength: 60 }}\r\n\t\t\t\t\t\t\t\t\t\t\terror={!!errors.AccountName}\r\n                className={`qadpt-acctfield ${errors.AccountName ? 'qadpt-error' : ''}`}\r\n              />\r\n            </FormControl>\r\n\r\n            <FormControl fullWidth required>\r\n              <label htmlFor=\"edit-account-domain\">{translate(\"Domain Url\")}</label>\r\n              <TextField\r\n                id=\"edit-account-domain\"\r\n                name=\"DomainUrl\"\r\n                value={AccountDetails.DomainUrl}\r\n                onChange={handleChange}\r\n                placeholder={translate(\"Enter Domain Url\")}\r\n                helperText={errors.DomainUrl && translate(errors.DomainUrl)}\r\n                variant=\"outlined\"\r\n                error={!!errors.DomainUrl}\r\n                inputProps={{ maxLength: 50 }}\r\n                className={`qadpt-acctfield ${errors.DomainUrl ? 'qadpt-error' : ''}`}\r\n              />\r\n            </FormControl>\r\n\r\n            <div className=\"qadpt-txtfld\">\r\n              <div style={{ display: \"flex\", justifyContent: \"space-between\", margin: \"10px 0\" }}>\r\n                <span style={{ marginLeft: '10px' }}>\r\n                  {translate(\"RTL\")}\r\n                </span>\r\n                <label className=\"toggle-switch\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={AccountDetails.Rtl}\r\n                    onChange={(e) => {\r\n                      const newValue = e.target.checked;\r\n                      setAccountDetails(prev => ({\r\n                        ...prev,\r\n                        Rtl: newValue\r\n                      }));\r\n                      setHasChanges(checkForChanges({ ...AccountDetails, Rtl: newValue }));\r\n                    }}\r\n                  />\r\n                  <span className=\"slider\"></span>\r\n                </label>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"qadpt-txtfld\">\r\n              <div style={{ display: \"flex\", justifyContent: \"space-between\", margin: \"10px 0\" }}>\r\n                <span style={{ marginLeft: '10px' }}>\r\n                  {translate(\"Dona\")}\r\n                </span>\r\n                <label className=\"toggle-switch\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={AccountDetails.IsAIEnabled}\r\n                    onChange={(e) => {\r\n                    const newAccountDetails = {\r\n                        ...AccountDetails,\r\n                        IsAIEnabled: e.target.checked\r\n                    };\r\n                    setAccountDetails(newAccountDetails);\r\n                    setHasChanges(checkForChanges(newAccountDetails));\r\n                    }}\r\n                  />\r\n                  <span className=\"slider\"></span>\r\n                </label>\r\n              </div>\r\n            </div>\r\n\r\n          </Grid>\r\n        </Grid>\r\n      </div>\r\n\r\n      <div className=\"qadpt-account-buttons\">\r\n        <Button\r\n          className=\"qadpt-save-btn\"\r\n          type=\"submit\"\r\n          disabled={!hasChanges || Object.values(errors).some(error => error !== \"\")}\r\n          sx={{\r\n            '&.Mui-disabled': {\r\n              opacity: 0.5\r\n            }\r\n          }}\r\n        >\r\n          {translate(\"Save\")}\r\n        </Button>\r\n      </div>\r\n    </form>\r\n  </div>\r\n</div>\r\n\t\t)\r\n\t);\r\n};\r\n\r\nexport default EditAccount;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAqB,OAAO;AAC/D,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,oBAAoB,EAAEC,iBAAiB,QAAQ,+BAA+B;AACvF,SACCC,SAAS,EACTC,MAAM,EAILC,IAAI,EACJC,WAAW,QACN,eAAe;AACtB,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,OAAO,QAAQ,sBAAsB;AAE9C,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ/C,MAAMC,WAAW,GAAIC,KAAU,IAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACnC,MAAM;IACLC,aAAa;IACbC,gBAAgB;IAChBC,aAAa;IACbC,SAAS;IACTC,UAAU;IACVC,aAAa;IACbC,YAAY;IACZC;EACD,CAAC,GAAGV,KAAK;EACT,MAAM,CAACW,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAQ,EAAE,CAAC;EAC7D,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8B,eAAe,EAAEC,kBAAkB,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACgC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjC,QAAQ,CAAsB,SAAS,CAAC;EACxF,MAAM,CAACkC,IAAI,CAAC,GAAGlC,QAAQ,CAAC,GAAG,CAAC;EAC5B,MAAM,CAACmC,GAAG,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAC5B,MAAM;IAAEoC;EAAa,CAAC,GAAG3B,WAAW,CAAC,CAAC;EACtC,MAAM;IAAE4B;EAAY,CAAC,GAAG3B,OAAO,CAAC,CAAC;EACjC,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAGvC,QAAQ,EAAAiB,qBAAA,GAACoB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEC,cAAc,cAAArB,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;EACvF,MAAM,GAAGuB,OAAO,CAAC,GAAGxC,QAAQ,CAAc,IAAI,CAAC;EAC/C,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2C,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACnD;EACA;EACA;EACA;EACA;EACA,MAAM,CAAC6C,MAAM,EAAEC,SAAS,CAAC,GAAG9C,QAAQ,CAAc;IACjD+C,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM,CAACC,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGlD,QAAQ,CAAC;IACpEmD,SAAS,EAAE,EAAE;IACbJ,WAAW,EAAE,EAAE;IACfK,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfhB,cAAc,EAAE,EAAE;IAClBiB,SAAS,EAAElB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEmB,QAAQ;IAChCC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAEC,OAAO,CAAC,IAAI,CAAC;IACrBX,SAAS,EAAE,EAAE;IACbY,GAAG,EAAE,KAAK;IACVC,WAAW,EAAE;EACd,CAAC,CAAC;EAEF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG/D,QAAQ,CAAC;IACpDmD,SAAS,EAAE,EAAE;IACbJ,WAAW,EAAE,EAAE;IACfK,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfhB,cAAc,EAAE,EAAE;IAClBiB,SAAS,EAAElB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEmB,QAAQ;IAChCC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAEC,OAAO,CAAC,IAAI,CAAC;IACrBX,SAAS,EAAE,EAAE;IACba,WAAW,EAAE,KAAK;IAClBD,GAAG,EAAE;EACN,CAAC,CAAC;EAEF,MAAM;IAAEI,CAAC,EAAEC;EAAU,CAAC,GAAGtD,cAAc,CAAC,CAAC;EAGzCV,SAAS,CAAC,MAAM;IACf,IAAIiB,aAAa,EAAE;MAClBgD,mBAAmB,CAAC9C,aAAa,CAAC;IACnC;EACD,CAAC,EAAE,CAACF,aAAa,CAAC,CAAC;EACnB;EACAjB,SAAS,CAAC,MAAM;IACf,MAAMkE,SAAsB,GAAG,CAAC,CAAC;;IAEjC;IACA,IAAIL,cAAc,CAACf,WAAW,EAAE;MAC/B,MAAMqB,kBAAkB,GAAGN,cAAc,CAACf,WAAW,CAACsB,IAAI,CAAC,CAAC;MAC5D,IAAI,CAACD,kBAAkB,EAAE;QACxBD,SAAS,CAACpB,WAAW,GAAGkB,SAAS,CAAC,qCAAqC,CAAC;MACzE,CAAC,MAAM,IAAIG,kBAAkB,CAACE,MAAM,GAAG,CAAC,EAAE;QACzCH,SAAS,CAACpB,WAAW,GAAGkB,SAAS,CAAC,6CAA6C,CAAC;MACjF,CAAC,MAAM,IAAIG,kBAAkB,CAACE,MAAM,GAAG,EAAE,EAAE;QAC1CH,SAAS,CAACpB,WAAW,GAAGkB,SAAS,CAAC,2CAA2C,CAAC;MAC/E,CAAC,MAAM,IAAI,cAAc,CAACM,IAAI,CAACH,kBAAkB,CAAC,EAAE;QACnDD,SAAS,CAACpB,WAAW,GAAGkB,SAAS,CAAC,mDAAmD,CAAC;MACvF,CAAC,MAAM;QACNE,SAAS,CAACpB,WAAW,GAAG,EAAE;MAC3B;IACD;;IAEA;IACA,IAAIe,cAAc,CAACd,SAAS,EAAE;MAC7B,MAAMwB,gBAAgB,GAAGV,cAAc,CAACd,SAAS,CAACqB,IAAI,CAAC,CAAC;MACxD,MAAMI,aAAa,GAAG,qDAAqD;MAC3E,IAAI,CAACA,aAAa,CAACF,IAAI,CAACC,gBAAgB,CAAC,EAAE;QAC1CL,SAAS,CAACnB,SAAS,GAAGiB,SAAS,CAAC,mEAAmE,CAAC;MACrG,CAAC,MAAM;QACNE,SAAS,CAACnB,SAAS,GAAG,EAAE;MACzB;IACD;IAEAF,SAAS,CAAC4B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,GAAGP;IAAU,CAAC,CAAC,CAAC;EAC/C,CAAC,EAAE,CAACL,cAAc,CAAC,CAAC;EAEpB7D,SAAS,CAAC,MAAM;IACf,MAAM0E,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IACvD,IAAIF,cAAc,EAAE;MACnB,IAAI;QACH,MAAMG,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,cAAc,CAAC;QAC3C,IAAIG,QAAQ,CAAC,MAAM,CAAC,EAAE;UACrB,MAAMG,UAAU,GAAGF,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,MAAM,CAAC,CAAC;UAC/CtC,OAAO,CAACyC,UAAU,CAAC;UACnB,IAAIA,UAAU,EAAE;YAAA,IAAAC,qBAAA;YACf,MAAMC,KAAK,IAAAD,qBAAA,GAAGD,UAAU,CAAC3C,cAAc,cAAA4C,qBAAA,cAAAA,qBAAA,GAAI,EAAE;YAC7C3C,iBAAiB,CAAC4C,KAAK,CAAC;UACzB;QACD;MACD,CAAC,CAAC,OAAOC,KAAK,EAAE;QACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MACjD;IACD,CAAC,MACI,IAAI/C,WAAW,EAAE;MACrBG,OAAO,CAACH,WAAW,CAAC;MACpB,IAAIA,WAAW,EAAE;QAAA,IAAAiD,sBAAA;QAChB,MAAMH,KAAK,IAAAG,sBAAA,GAAGjD,WAAW,CAACC,cAAc,cAAAgD,sBAAA,cAAAA,sBAAA,GAAI,EAAE;QAC9C/C,iBAAiB,CAAC4C,KAAK,CAAC;MACzB;IACD;EACD,CAAC,EAAE,EAAE,CAAC;;EAGN;EACA,MAAMI,eAAe,GAAIC,cAAmB,IAAK;IAChD,MAAMC,eAAe,GAAG,CAAC,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE,KAAK,CAAU;IACnF,OAAOA,eAAe,CAACC,IAAI,CAACC,KAAK,IAAIH,cAAc,CAACG,KAAK,CAAC,KAAK1C,sBAAsB,CAAC0C,KAAK,CAAC,CAAC;EAC9F,CAAC;EAED,MAAMzB,mBAAmB,GAAG,MAAO0B,EAAO,IAAK;IAC9C,IAAI;MACH,MAAMC,YAAY,GAAG,MAAMzF,iBAAiB,CAACwF,EAAE,CAAC;MAEhD,IAAI,CAACC,YAAY,EAAE;QAClB,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;MAC/C;MAEA,MAAMC,WAAW,GAAG;QACnB5C,SAAS,EAAE0C,YAAY,CAAC1C,SAAS;QACjCJ,WAAW,EAAE8C,YAAY,CAAC9C,WAAW;QACrCK,WAAW,EAAEyC,YAAY,CAACzC,WAAW;QACrCC,SAAS,EAAEwC,YAAY,CAACxC,SAAS;QACjCC,WAAW,EAAEuC,YAAY,CAACvC,WAAW;QACrChB,cAAc,EAAEuD,YAAY,CAACvD,cAAc;QAC3CiB,SAAS,EAAElB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEmB,QAAQ;QAChCC,WAAW,EAAE,IAAIuC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACrCvC,MAAM,EAAE,IAAI;QACZV,SAAS,EAAE6C,YAAY,CAAC7C,SAAS;QACjCa,WAAW,EAAEgC,YAAY,CAAChC,WAAW;QACrCD,GAAG,EAAEiC,YAAY,CAACjC,GAAG,IAAI;MAC1B,CAAC;;MAED;MACAV,yBAAyB,CAAC6C,WAAW,CAAC;MACtChC,iBAAiB,CAACgC,WAAW,CAAC;;MAE9B;MACAnD,aAAa,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACfC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACtD;EACD,CAAC;EACD,MAAMc,8BAA8B,GAAG,MAAAA,CAAA,KAAY;IAClD,IAAI;MACH,MAAMC,QAAQ,GAAG,MAAMjG,mBAAmB,CAACyB,gBAAgB,EAAEL,UAAU,CAAC;IACzE,CAAC,CAAC,OAAO8D,KAAK,EAAE;MACfC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACtD;EACD,CAAC;EACD,MAAMgB,iBAAiB,GAAG,gBAAgB;EAC1C,MAAMC,YAAY,GAAIC,KAAoC,IAAK;IAC9D,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,KAAK,CAACG,MAAM;IACpC,IAAIrB,KAAK,GAAG,EAAE;IACd,IAAIsB,cAAc,GAAGF,KAAK;IAE1B,IAAID,IAAI,KAAK,aAAa,EAAE;MAC3B,MAAMI,oBAAoB,GAAGjF,aAAa,CAACkF,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,IAAI,KAAKN,KAAK,CAAC;MAC5E,IAAIG,oBAAoB,EAAE;QACzB,MAAMI,iBAAiB,GAAG;UACzB,GAAGjD,cAAc;UACjBV,WAAW,EAAEoD,KAAK;UAClBlE,cAAc,EAAEA;QACjB,CAAC;QACDyB,iBAAiB,CAACgD,iBAAiB,CAAC;QACpCnE,aAAa,CAAC2C,eAAe,CAACwB,iBAAiB,CAAC,CAAC;QACjD;MACD;IACD,CAAC,MAAM,IAAIR,IAAI,KAAK,aAAa,EAAE;MAClC;MACAG,cAAc,GAAGF,KAAK,CAACQ,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;;MAElD;MACA,MAAMC,YAAY,GAAGP,cAAc,CAACrC,IAAI,CAAC,CAAC;MAC1C,IAAI4C,YAAY,CAAC3C,MAAM,GAAG,CAAC,EAAE;QAC5Bc,KAAK,GAAGnB,SAAS,CAAC,6CAA6C,CAAC;MACjE,CAAC,MAAM,IAAIgD,YAAY,CAAC3C,MAAM,GAAG,EAAE,EAAE;QACpCc,KAAK,GAAGnB,SAAS,CAAC,2CAA2C,CAAC;MAC/D,CAAC,MAAM,IAAIgD,YAAY,KAAK,EAAE,EAAE;QAC/B7B,KAAK,GAAGnB,SAAS,CAAC,qCAAqC,CAAC;MACzD;IACD,CAAC,MAAM,IAAIsC,IAAI,KAAK,WAAW,EAAE;MAChC;MACAG,cAAc,GAAGF,KAAK,CAACnC,IAAI,CAAC,CAAC;MAC7B,MAAMI,aAAa,GAAG,qDAAqD;MAC3E,IAAIiC,cAAc,IAAI,CAACjC,aAAa,CAACF,IAAI,CAACmC,cAAc,CAAC,EAAE;QAC1DtB,KAAK,GAAGnB,SAAS,CAAC,mEAAmE,CAAC;MACvF;IACD;IAEA,MAAM8C,iBAAiB,GAAG;MAAE,GAAGjD,cAAc;MAAE,CAACyC,IAAI,GAAGG;IAAe,CAAC;IACvE3C,iBAAiB,CAACgD,iBAAiB,CAAC;IACpCjE,SAAS,CAAE4B,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAE,CAAC6B,IAAI,GAAGnB;IAAM,CAAC,CAAC,CAAC;;IAEjD;IACAxC,aAAa,CAAC2C,eAAe,CAACwB,iBAAiB,CAAC,CAAC;EAClD,CAAC;EAED,MAAMG,kBAAkB,GAAIZ,KAAoC,IAAK;IACpE,MAAM;MAAEC,IAAI;MAAEY;IAAQ,CAAC,GAAGb,KAAK,CAACG,MAAM;IACtC,MAAMM,iBAAiB,GAAG;MAAE,GAAGjD,cAAc;MAAE,CAACyC,IAAI,GAAGY;IAAQ,CAAC;IAChEpD,iBAAiB,CAACgD,iBAAiB,CAAC;IACpCnE,aAAa,CAAC2C,eAAe,CAACwB,iBAAiB,CAAC,CAAC;EAClD,CAAC;;EAED;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,MAAMK,YAAY,GAAG,MAAOC,CAAM,IAAK;IACtCA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMlD,kBAAkB,GAAGN,cAAc,CAACf,WAAW,CAACsB,IAAI,CAAC,CAAC;IAC5D,MAAMG,gBAAgB,GAAGV,cAAc,CAACd,SAAS,CAACqB,IAAI,CAAC,CAAC;IACxD,MAAMkD,qBAAqB,GAAG;MAC7B,GAAGzD,cAAc;MACjBf,WAAW,EAAEqB,kBAAkB;MAC/BpB,SAAS,EAAEwB;IACZ,CAAC;IAED,MAAML,SAAsB,GAAG,CAAC,CAAC;IACjC,IAAI1B,OAAO,GAAG,IAAI;;IAElB;IACA,IAAI,CAAC2B,kBAAkB,EAAE;MACxBD,SAAS,CAACpB,WAAW,GAAGkB,SAAS,CAAC,2BAA2B,CAAC;MAC9DxB,OAAO,GAAG,KAAK;IAChB,CAAC,MAAM,IAAI2B,kBAAkB,CAACE,MAAM,GAAG,CAAC,EAAE;MACzCH,SAAS,CAACpB,WAAW,GAAGkB,SAAS,CAAC,6CAA6C,CAAC;MAChFxB,OAAO,GAAG,KAAK;IAChB,CAAC,MAAM,IAAI2B,kBAAkB,CAACE,MAAM,GAAG,EAAE,EAAE;MAC1CH,SAAS,CAACpB,WAAW,GAAGkB,SAAS,CAAC,2CAA2C,CAAC;MAC9ExB,OAAO,GAAG,KAAK;IAChB,CAAC,MAAM,IAAI,cAAc,CAAC8B,IAAI,CAACH,kBAAkB,CAAC,EAAE;MACnDD,SAAS,CAACpB,WAAW,GAAGkB,SAAS,CAAC,mDAAmD,CAAC;MACtFxB,OAAO,GAAG,KAAK;IAChB;;IAEA;IACA,IAAI,CAAC+B,gBAAgB,EAAE;MACtBL,SAAS,CAACnB,SAAS,GAAGiB,SAAS,CAAC,yBAAyB,CAAC;MAC1DxB,OAAO,GAAG,KAAK;IAChB,CAAC,MAAM;MACN,MAAMgC,aAAa,GAAG,qDAAqD;MAC3E,IAAI,CAACA,aAAa,CAACF,IAAI,CAACC,gBAAgB,CAAC,EAAE;QAC1CL,SAAS,CAACnB,SAAS,GAAGiB,SAAS,CAAC,mEAAmE,CAAC;QACpGxB,OAAO,GAAG,KAAK;MAChB;IACD;IAEAK,SAAS,CAACqB,SAAS,CAAC;IAEpB,IAAI,CAAC1B,OAAO,EAAE;MACb;MACA,MAAM+E,UAAU,GAAGrD,SAAS,CAACpB,WAAW,IAAIoB,SAAS,CAACnB,SAAS;MAC/D,IAAIwE,UAAU,EAAE;QACfzF,kBAAkB,CAACyF,UAAU,CAAC;QAC9BvF,mBAAmB,CAAC,OAAO,CAAC;QAC5BJ,eAAe,CAAC,IAAI,CAAC;MACtB;MACA;IACD;IAEAP,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACH,MAAMnB,oBAAoB,CACzBmB,UAAU,EACVD,SAAS,EACTF,gBAAgB,EAChBoG,qBAAqB,EACrBjF,cAAc,EACdJ,IAAI,EACJC,GAAG,EACHZ,aAAa,EACba,YAAY,EACZZ,YAAY,EACZC,OACD,CAAC;IACF,CAAC,CAAC,OAAO2D,KAAK,EAAE;MACf;IAAA;EAEF,CAAC;EACD,MAAMqC,mBAAmB,GAAGA,CAAA,KAAM;IACjC5F,eAAe,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,OACCX,aAAa,iBACZL,OAAA;IAAK6G,SAAS,EAAC,qBAAqB;IAAAC,QAAA,eACrC9G,OAAA;MAAK6G,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBACvC9G,OAAA;QAAK6G,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B9G,OAAA;UAAK6G,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAE1D,SAAS,CAAC,cAAc;QAAC;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9DlH,OAAA;UACEmH,OAAO,EAAEA,CAAA,KAAM7G,gBAAgB,CAAC,KAAK,CAAE;UACvCuG,SAAS,EAAC,iBAAiB;UAC3BO,KAAK,EAAC,4BAA4B;UAClCC,CAAC,EAAC,KAAK;UACPC,CAAC,EAAC,KAAK;UACPC,KAAK,EAAC,IAAI;UACVC,MAAM,EAAC,IAAI;UACXC,OAAO,EAAC,WAAW;UAAAX,QAAA,eAEnB9G,OAAA;YAAM0H,CAAC,EAAC;UAA+M;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5N,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlH,OAAA;QAAM2H,QAAQ,EAAEpB,YAAa;QAAAO,QAAA,gBAC3B9G,OAAA;UAAK6G,SAAS,EAAC,0BAA0B;UAAAC,QAAA,eACvC9G,OAAA,CAACN,IAAI;YAACkI,SAAS;YAAAd,QAAA,eACb9G,OAAA,CAACN,IAAI;cAACmI,IAAI;cAAAf,QAAA,gBACR9G,OAAA,CAACL,WAAW;gBAACmI,SAAS;gBAACC,QAAQ;gBAAAjB,QAAA,gBAC7B9G,OAAA;kBAAOgI,OAAO,EAAC,mBAAmB;kBAAAlB,QAAA,EAAE1D,SAAS,CAAC,cAAc;gBAAC;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtElH,OAAA,CAACR,SAAS;kBACRuF,EAAE,EAAC,mBAAmB;kBACtBW,IAAI,EAAC,aAAa;kBAClBC,KAAK,EAAE1C,cAAc,CAACf,WAAY;kBAClC+F,QAAQ,EAAEzC,YAAa;kBACvB0C,WAAW,EAAE9E,SAAS,CAAC,oBAAoB,CAAE;kBAC7C+E,UAAU,EAAEnG,MAAM,CAACE,WAAW,IAAIkB,SAAS,CAACpB,MAAM,CAACE,WAAW,CAAE;kBACrEkG,OAAO,EAAC,UAAU;kBAClBC,UAAU,EAAE;oBAAEC,SAAS,EAAE;kBAAG,CAAE;kBAC9B/D,KAAK,EAAE,CAAC,CAACvC,MAAM,CAACE,WAAY;kBACvB2E,SAAS,EAAE,mBAAmB7E,MAAM,CAACE,WAAW,GAAG,aAAa,GAAG,EAAE;gBAAG;kBAAA6E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC,eAEdlH,OAAA,CAACL,WAAW;gBAACmI,SAAS;gBAACC,QAAQ;gBAAAjB,QAAA,gBAC7B9G,OAAA;kBAAOgI,OAAO,EAAC,qBAAqB;kBAAAlB,QAAA,EAAE1D,SAAS,CAAC,YAAY;gBAAC;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtElH,OAAA,CAACR,SAAS;kBACRuF,EAAE,EAAC,qBAAqB;kBACxBW,IAAI,EAAC,WAAW;kBAChBC,KAAK,EAAE1C,cAAc,CAACd,SAAU;kBAChC8F,QAAQ,EAAEzC,YAAa;kBACvB0C,WAAW,EAAE9E,SAAS,CAAC,kBAAkB,CAAE;kBAC3C+E,UAAU,EAAEnG,MAAM,CAACG,SAAS,IAAIiB,SAAS,CAACpB,MAAM,CAACG,SAAS,CAAE;kBAC5DiG,OAAO,EAAC,UAAU;kBAClB7D,KAAK,EAAE,CAAC,CAACvC,MAAM,CAACG,SAAU;kBAC1BkG,UAAU,EAAE;oBAAEC,SAAS,EAAE;kBAAG,CAAE;kBAC9BzB,SAAS,EAAE,mBAAmB7E,MAAM,CAACG,SAAS,GAAG,aAAa,GAAG,EAAE;gBAAG;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC,eAEdlH,OAAA;gBAAK6G,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3B9G,OAAA;kBAAKuI,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEC,cAAc,EAAE,eAAe;oBAAEC,MAAM,EAAE;kBAAS,CAAE;kBAAA5B,QAAA,gBACjF9G,OAAA;oBAAMuI,KAAK,EAAE;sBAAEI,UAAU,EAAE;oBAAO,CAAE;oBAAA7B,QAAA,EACjC1D,SAAS,CAAC,KAAK;kBAAC;oBAAA2D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC,eACPlH,OAAA;oBAAO6G,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC9B9G,OAAA;sBACE4I,IAAI,EAAC,UAAU;sBACftC,OAAO,EAAErD,cAAc,CAACF,GAAI;sBAC5BkF,QAAQ,EAAGzB,CAAC,IAAK;wBACf,MAAMqC,QAAQ,GAAGrC,CAAC,CAACZ,MAAM,CAACU,OAAO;wBACjCpD,iBAAiB,CAACW,IAAI,KAAK;0BACzB,GAAGA,IAAI;0BACPd,GAAG,EAAE8F;wBACP,CAAC,CAAC,CAAC;wBACH9G,aAAa,CAAC2C,eAAe,CAAC;0BAAE,GAAGzB,cAAc;0BAAEF,GAAG,EAAE8F;wBAAS,CAAC,CAAC,CAAC;sBACtE;oBAAE;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACFlH,OAAA;sBAAM6G,SAAS,EAAC;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlH,OAAA;gBAAK6G,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3B9G,OAAA;kBAAKuI,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEC,cAAc,EAAE,eAAe;oBAAEC,MAAM,EAAE;kBAAS,CAAE;kBAAA5B,QAAA,gBACjF9G,OAAA;oBAAMuI,KAAK,EAAE;sBAAEI,UAAU,EAAE;oBAAO,CAAE;oBAAA7B,QAAA,EACjC1D,SAAS,CAAC,MAAM;kBAAC;oBAAA2D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC,eACPlH,OAAA;oBAAO6G,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC9B9G,OAAA;sBACE4I,IAAI,EAAC,UAAU;sBACftC,OAAO,EAAErD,cAAc,CAACD,WAAY;sBACpCiF,QAAQ,EAAGzB,CAAC,IAAK;wBACjB,MAAMN,iBAAiB,GAAG;0BACtB,GAAGjD,cAAc;0BACjBD,WAAW,EAAEwD,CAAC,CAACZ,MAAM,CAACU;wBAC1B,CAAC;wBACDpD,iBAAiB,CAACgD,iBAAiB,CAAC;wBACpCnE,aAAa,CAAC2C,eAAe,CAACwB,iBAAiB,CAAC,CAAC;sBACjD;oBAAE;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACFlH,OAAA;sBAAM6G,SAAS,EAAC;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENlH,OAAA;UAAK6G,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpC9G,OAAA,CAACP,MAAM;YACLoH,SAAS,EAAC,gBAAgB;YAC1B+B,IAAI,EAAC,QAAQ;YACbE,QAAQ,EAAE,CAAChH,UAAU,IAAIiH,MAAM,CAACC,MAAM,CAAChH,MAAM,CAAC,CAAC6C,IAAI,CAACN,KAAK,IAAIA,KAAK,KAAK,EAAE,CAAE;YAC3E0E,EAAE,EAAE;cACF,gBAAgB,EAAE;gBAChBC,OAAO,EAAE;cACX;YACF,CAAE;YAAApC,QAAA,EAED1D,SAAS,CAAC,MAAM;UAAC;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACF;AAEH,CAAC;AAAC/G,EAAA,CAldIF,WAAW;EAAA,QAiBSL,WAAW,EACZC,OAAO,EA6CNC,cAAc;AAAA;AAAAqJ,EAAA,GA/DlClJ,WAAW;AAodjB,eAAeA,WAAW;AAAC,IAAAkJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}