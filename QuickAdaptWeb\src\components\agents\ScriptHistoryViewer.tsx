import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Container,
  Box,
  IconButton,
  Tooltip,
  Button,
  CircularProgress,
  Snackbar,
  Alert,
  Typography,
  Paper,
  Chip
} from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { useTranslation } from "react-i18next";
import { isSidebarOpen, subscribe } from "../adminMenu/sidemenustate";
import { useAuth } from "../auth/AuthProvider";

// Define the interface for system prompt history items
interface SystemPromptHistoryItem {
  Id: string;
  SystemPromptId: string;
  SystemPrompt: string;
  CreatedDate: string;
  CreatedBy: string;
  Version: number;
}

const ScriptHistoryViewer = () => {
  const { t: translate } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  // const { userDetails } = useAuth(); // Not needed for read-only viewer
  const agentData = location.state?.agentData;
  const historyItem = location.state?.historyItem as SystemPromptHistoryItem;
  const [, setSidebarOpen] = useState(isSidebarOpen()); // Only need the setter for the subscription
  const [scriptContent, setScriptContent] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState<"success" | "error" | "info" | "warning">("info");

  useEffect(() => {
    const unsubscribe = subscribe(setSidebarOpen);
    return () => unsubscribe();
  }, []);

  useEffect(() => {
    if (historyItem) {
      loadHistoricalPrompt();
    }
  }, [historyItem]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadHistoricalPrompt = () => {
    if (!historyItem) return;

    try {
      setIsLoading(true);

      // Ensure the SystemPrompt is a string
      const promptContent = typeof historyItem.SystemPrompt === 'string'
        ? historyItem.SystemPrompt
        : JSON.stringify(historyItem.SystemPrompt);

      setScriptContent(promptContent);
    } catch (error) {
      console.error("Error loading system prompt:", error);
      setSnackbarMessage(translate("Failed to load system prompt"));
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackClick = () => {
    navigate("/settings/scripthistory", {
      state: { agentData }
    }); // Navigate back to the script history page
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <Container maxWidth={false} sx={{ px: 3 }}>
      <div className="qadpt-midpart setng-box">
        <div className="qadpt-content-block">
          <div>
            <div className="qadpt-head">
              <div className="qadpt-title-sec">
                {agentData && (
                  <IconButton
                    onClick={handleBackClick}
                    sx={{ right:"10px", gap:"10px"}}
                  >
                    <Tooltip title={translate("Back to Script History")}>
                      <ArrowBackIcon />
                    </Tooltip>
                    <div className="qadpt-title" style={{color:"black"}}>{translate("System Prompt Viewer")}</div>
                  </IconButton>
                )}
                <div className="qadpt-description" style={{marginLeft: "35px"}}>
                  {agentData ? `Viewing  prompt for ${agentData.BotName} ` : translate("View system prompt")}
                </div>
              </div>
            </div>

            <div style={{ position: 'relative',bottom:"50px" }}>
              <Box className="qadpt-content-box">
                {agentData && historyItem ? (
                  isLoading ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                      <CircularProgress />
                    </Box>
                  ) : (
                    <div>
                      <Box sx={{ mb: 2, display: 'flex', bottom: "20px", flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 0.5,bottom:"30px",left:"35px",position:"relative" }}>
                            <Typography variant="subtitle1" fontWeight="bold">
                              {translate("Prompt Details")}
                            </Typography>
                            
                          </Box>
                          <Box sx={{ display: 'flex', gap: 3,bottom:"70px",left:"35px",position:"relative" }}>
                            <Typography sx={{ display: 'flex', alignItems: 'center', whiteSpace:"nowrap", bottom:"22px", position:"relative"}}>
                              <strong>{translate("Bot Name")}:</strong>
                              <Box sx={{ ml: 1 }}>
                                {agentData.BotName}
                              </Box>
                            </Typography>
                            {/* <Typography variant="body2">
                              <strong>{translate("Version")}:</strong> {historyItem.Version}
                            </Typography> */}
                            <Typography variant="body2">
                              <strong>{translate("Created By")}:</strong> {historyItem.CreatedBy || "N/A"}
                            </Typography>
                            <Typography variant="body2">
                              <strong>{translate("Created")}:</strong> {formatDate(historyItem.CreatedDate)}
                            </Typography>
                          </Box>
                        </Box>
                      </Box>

                      <Paper
                        elevation={3}
                        sx={{
                          p: 2,
                          backgroundColor: '#f8f9fa',
                          border: '1px solid #e0e0e0',
                          borderRadius: '8px',
                          height: '55vh',
                          bottom: '170px',
                          position: 'relative',
                          width: '100%',
                          overflow: 'hidden',
                          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)',
                          '&:hover': {
                            boxShadow: '0 6px 10px rgba(0, 0, 0, 0.08)'
                          }
                        }}
                      >
                        <textarea
                          value={scriptContent}
                          readOnly={true}
                          disabled={false}
                          placeholder=""
                          style={{
                            width: '100%',
                            height: '100%',
                            padding: '16px',
                            fontFamily: 'Consolas, "Courier New", monospace',
                            fontSize: '14px',
                            lineHeight: '1.6',
                            backgroundColor: '#f8f9fa',
                            border: 'none',
                            borderRadius: '4px',
                            resize: 'none',
                            outline: 'none',
                            color: '#333',
                            cursor: 'default',
                            transition: 'all 0.2s ease-in-out'
                          }}
                        />
                      </Paper>
                    </div>
                  )
                ) : (
                  <div>
                    <p>{translate("No system prompt data available.")}</p>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={() => navigate("/settings/agents")}
                      sx={{ mt: 2 }}
                    >
                      {translate("Go to System Prompts")}
                    </Button>
                  </div>
                )}
              </Box>
            </div>
          </div>
        </div>
      </div>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbarSeverity}
          sx={{ width: '100%' }}
        >
          {translate(snackbarMessage)}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default ScriptHistoryViewer;
