import React, { useState , useEffect} from 'react';
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogTitle,
    IconButton,
    Grid,
    Select,
    MenuItem,
    TextField,
    Button,
    Typography,
    Badge,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import FilterListIcon from '@mui/icons-material/FilterList';
import DeleteIcon from '@mui/icons-material/Delete';
import AddBoxIcon from '@mui/icons-material/AddBox';
import ClearIcon from '@mui/icons-material/Clear';
import { isSidebarOpen, subscribe } from "../adminMenu/sidemenustate";
import { useTranslation } from 'react-i18next';
interface Filter {
    id: number;
    column: string;
    operator: string;
    value: string;
}

interface FilterPopupProps {
    columns: string[];
    onApplyFilters: (filters: Filter[]) => void;
}

const FilterPopup: React.FC<FilterPopupProps> = ({ columns, onApplyFilters }) => {
    const [sidebarO<PERSON>, setSidebarOpen] = useState(isSidebarOpen());
    const [open, setOpen] = useState(false);
    const [filters, setFilters] = useState<Filter[]>([
{ id: 1, column: '', operator: '', value: '' }    ]);
	const [appliedFilters, setAppliedFilters] = useState<Filter[]>([]);

	const activeFilterCount = appliedFilters.filter(filter =>
		filter.column !== "" &&
		filter.operator !== "" &&
		(filter.value.trim() !== "" || ["is empty", "is not empty", "is any"].includes(filter.operator))
	).length;

	useEffect(() => {
        const unsubscribe = subscribe(setSidebarOpen);
        return () => unsubscribe();
    }, []);

    const handleClickOpen = () => {
        setOpen(true);
    };

    const handleClose = () => {
        setOpen(false);
    };

    const handleColumnChange = (id: number, event: any) => {
        const newFilters = filters.map(filter => 
            filter.id === id ? { ...filter, column: event.target.value, value: '' } : filter
        );
        setFilters(newFilters);
    };

    const handleOperatorChange = (id: number, event: any) => {
        const newFilters = filters.map(filter => 
            filter.id === id 
            ? { ...filter, operator: event.target.value, value: event.target.value === 'is any' ? '' : filter.value } 
            : filter
        );
        setFilters(newFilters);
    };

    const handleValueChange = (id: number, event: any) => {
        const newFilters = filters.map(filter => 
            filter.id === id ? { ...filter, value: event.target.value } : filter
        );
        setFilters(newFilters);
    };

   const handleAddFilter = () => {
        setFilters([...filters, { id: filters.length + 1, column: '', operator: '', value: '' }]);

    };

const handleDeleteFilter = (id: number) => {
    // If there is only one filter, reset it back to the initial state
    if (filters.length === 1) {
        setFilters([{ id: 1, column: '', operator: '', value: '' }]);
		setAppliedFilters([]);
        onApplyFilters([]); // Apply empty filters as no filter should be active
    } else {
        // Otherwise, delete the selected filter
        setFilters(filters.filter(filter => filter.id !== id));
    }
};

    const handleClearAll = () => {
        setFilters([{ id: 1, column: '', operator: '', value: '' }]);
		setAppliedFilters([]);
        onApplyFilters([]);
        setOpen(false);
    };

// const handleRemoveAll = () => {
//     setFilters([{ id: 1, column: '', operator: '', value: '' }]);
// };

    const handleApplyFilters = () => {
		setAppliedFilters(filters);
        onApplyFilters(filters);
        setOpen(false);
    };

    const isApplyDisabled = filters.some(filter =>
		(filter.operator.trim() === '' || (filter.value.trim() === '' && !['is empty', 'is not empty', 'is any'].includes(filter.operator)))
    );
	const { t: translate } = useTranslation()

    return (
			<div>
				<IconButton
					sx={{ top: "20px" }}
					onClick={handleClickOpen}
				>
					<Badge
						badgeContent={activeFilterCount}
						color="primary"
					>
						<FilterListIcon sx={{ "&:focusVisible": { outline: "none" } }} />
					</Badge>
				<Typography sx={{ fontSize: "15px" }}>{translate("Filters")}</Typography>
				</IconButton>
				<Dialog
					open={open}
					onClose={handleClose}
					slotProps={{
						backdrop: { invisible: true },
					}}
					
				className="qadpt-rolesfltpopup"
				>
					<DialogTitle className='qadpt-title'>
					{translate("Filter")}
						<Grid
							container
							alignItems="center"
							justifyContent="flex-end"
						>
							
							<IconButton
								aria-label="close"
							onClick={handleClose}
							className='qadpt-close'
							>
								<CloseIcon />
							</IconButton>
						</Grid>
					</DialogTitle>
				<DialogContent sx={{ minWidth: "300px" }}>
                    <div className='qadpt-actions'>
                        {filters.length > 0 && (
                            <Button
                                startIcon={<ClearIcon />}
                                variant="text"
                                color="secondary"
                                onClick={handleClearAll}
                                disabled={filters.length === 0 || filters.every((filter) => filter.value.trim() === "")}
                            >
                                {translate("CLEAR ALL")}
                            </Button>
                        )}
					{/* <Button
								startIcon={<DeleteIcon />}
								variant="text"
								color="primary"
								onClick={handleRemoveAll}
							>
							{translate("REMOVE ALL")}

						</Button> */}
						</div>
							
						{filters.map((filter) => (
							<Grid
								container
								spacing={2}
								alignItems="center"
								key={filter.id}
								sx={{ marginBottom: "10px" }}
								className='qadpt-thrflt'
							>
								<Grid
									item
									xs={1}
								>
									<IconButton
										aria-label="delete"
										size="small"
										onClick={() => handleDeleteFilter(filter.id)}
									>
										<DeleteIcon sx={{ color: "red" }} />
									</IconButton>
								</Grid>
								<Grid
									item
									xs={3}
								>
									<label>{translate("Column")}</label>
									<Select
										value={filter.column}
										onChange={(event) => handleColumnChange(filter.id, event)}
										fullWidth
										variant="standard"
									>
										{columns.map((col) => (
											<MenuItem
												key={col}
												value={col}
											>
												{col}
											</MenuItem>
										))}
									</Select>
								</Grid>
								<Grid
									item
									xs={3}
								>
									<label>{translate("Operator")}</label>
									<Select
										value={filter.operator}
										onChange={(event) => handleOperatorChange(filter.id, event)}
										fullWidth
										variant="standard"
										sx={{
											// width: filter.operator === "is not empty" ? "150px" : "auto",
											width: "100%"
										}}
									>
										<MenuItem value="equals">{translate("equals")}</MenuItem>
										<MenuItem value="starts with">{translate("starts with")}</MenuItem>
										<MenuItem value="ends with">{translate("ends with")}</MenuItem>
										<MenuItem value="is empty">{translate("is empty")}</MenuItem>
										<MenuItem value="is not empty">{translate("is not empty")}</MenuItem>
										<MenuItem value="is any">{translate("is any")}</MenuItem>
									</Select>
								</Grid>
								<Grid
									item
									xs={5}
								
								>
									{filter.operator !== "is empty" && filter.operator !== "is not empty" && (
										<>
											<label>{translate("Value")}</label>
											<TextField
												value={filter.value}
												onChange={(event) => handleValueChange(filter.id, event)}
												placeholder={translate("Filter value")}
												disabled={!filter.column}
												fullWidth
												variant="standard"
											/>
										</>
									)}
								</Grid>
							</Grid>
						))}
						<Grid
							container
							spacing={2}
							justifyContent="space-between"
							sx={{ marginTop: "16px" }}
						>
							<Grid item>
								<Button
									startIcon={<AddBoxIcon />}
									variant="text"
									color="primary"
									onClick={handleAddFilter}
								>
								{translate("ADD FILTER")}
								</Button>
							</Grid>
							
						</Grid>
				</DialogContent>
				<div className='qadpt-footer'>
								<Button
									disabled={isApplyDisabled}
									variant="contained"
									color="primary"
									onClick={handleApplyFilters}
								>
						{translate("Apply")}
								</Button>
							</div>
				</Dialog>
			</div>
		);
};

export default FilterPopup;
