import React, { useState, useEffect } from 'react';
import { Con<PERSON>er, Box, Typography, TextField, Button, Link, IconButton, InputAdornment } from '@mui/material';
import Visibility from '@mui/icons-material/Visibility';
import { useAuth } from '../auth/AuthProvider';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import { superAdminLogin } from "../../services/SuperAdminLoginService"
import { GetUserDetails, encryptPassword } from '../../services/UserService';
import { JSEncrypt } from 'jsencrypt';
import { useNavigate } from "react-router-dom";
import { GetUserDetailsById } from '../../services/UserService';
import userManager from '../auth/UseAuth';
import { LoginUserInfo } from '../../models/LoginUserInfo';
import jwt_decode from "jwt-decode";
import { getAllUsers } from '../../services/UserService';
import { getOrganizationById } from '../../services/OrganizationService';
import { Organization } from "../../models/Organization";
import { User } from "../../models/User";
import { User as Users, UserManager } from 'oidc-client-ts';
import { FormHelperText } from '@mui/material';
import { LoginService } from "../../services/LoginService";
let SAinitialsData: string;
let userLocalData: { [key: string]: any } = {}
let userDetails: User;
export default function LoginPage() {
    const { user,signOut ,loggedOut} = useAuth();
    let UserId: string;
    let OrganizationId: string;
    const [showPassword, setShowPassword] = useState(false);
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [users, setUser] = useState<Users | null>(null);
    const [error, setError] = useState<string | null>(null);
    const [loginUserInfo, setLoginUserInfo] = useState<LoginUserInfo | undefined>(undefined);
    const [response, setresponse] = useState('');
    const [userIds, setuserId] = useState("");
    const [organizationDetails, setOrganizationDetails] = useState<Organization | null>(null);
    const [loginUserDetails, setUserDetails] = useState<User | null>(null);
    const handleClickShowPassword = () => {
        setShowPassword(!showPassword);
    };

    const handleEmailChange = (event: any) => {
        setEmail(event.target.value);
    };

    const handlePasswordChange = (event: any) => {
        setPassword(event.target.value);
    };
    const navigate = useNavigate();
    const handleSubmit = async () => {
        try {
            
            if (password === '' || password == null)
            {
                setError('password should not be empty');
            }
            else if (email === '' || email == null)
            {
                setError('email should not be empty');
            }
            else {
                const isEncryptionEnabled = process.env.REACT_APP_ENABLE_ENCRYPTION === 'true';
                const publicKey = process.env.REACT_APP_PUBLIC_ENCRYPT_KEY || '';
                const encryptor = new JSEncrypt();
                encryptor.setPublicKey(publicKey);
                const now = new Date().toISOString();
                const encryptedPassword = encryptor.encrypt(password + '|' + now.trim()).toString();
            if (!encryptedPassword) {
                setError('Enter correct password');
            }
            const organizationId = "1";
            const rememberLogin = true;
            const returnUrl = ""
                const response = await LoginService(email, isEncryptionEnabled ? encryptedPassword : password, organizationId, rememberLogin, returnUrl,"super","admin");
                if (response.access_token) {                    
                userLocalData["oidc-info"] = JSON.stringify(response)
                localStorage.setItem("access_token",response.access_token)          
                const userResponse = await GetUserDetails();
                setUserDetails(userResponse ? userResponse.data : null);
                const firstNameInitials =  userResponse?.data.FirstName &&  userResponse?.data.FirstName ? userResponse?.data.FirstName.substring(0, 1).toUpperCase() : '';
                const lastNameinitials =  userResponse?.data &&  userResponse?.data.LastName ? userResponse?.data.LastName.substring(0, 1).toUpperCase() : '';
                const finalData = firstNameInitials + lastNameinitials;
                SAinitialsData = finalData;
                localStorage.setItem("userType", userResponse?.data?.UserType ?? "");            
                userLocalData["user"] = JSON.stringify(userResponse?.data);
                localStorage.setItem("userInfo",JSON.stringify(userLocalData)) 
                    navigate("/superadmin/organizations", {
                        state: { userDetail: userResponse?.data, organizationDetails: userResponse?.data.OrganizationId }
                    });
                } else {
                    setError(response.error_description);
                }                
            }
        }
        catch (error) {
            console.error('Login failed:');
            setError('An unexpected error occurred.'); // Handle unexpected errors
        }
    };
   
    // async function GetLoginUserInfo(userResponse : User) {
    //     try {
    //         const firstNameInitials =  userResponse?.FirstName &&  userResponse?.FirstName ? userResponse?.FirstName.substring(0, 1).toUpperCase() : '';
    //         const lastNameinitials =  userResponse &&  userResponse?.LastName ? userResponse?.LastName.substring(0, 1).toUpperCase() : '';
    //         const finalData = firstNameInitials + lastNameinitials;
    //         SAinitialsData = finalData;
    //         localStorage.setItem("userType", userResponse?.UserType ?? "");
    //     } catch (error) {
    //         console.error('Error fetching user or organization details', error);
    //     }
    // }
    // useEffect(() => {
    //     let token = localStorage.getItem("access_token");
	// 	const userInfo = JSON.parse(localStorage.getItem("userInfo") || '{}');
	// 	if (userInfo['oidc-info'] && userInfo['user']) {
	// 		userDetails = JSON.parse(userInfo['user'])
	// 		token = userInfo['oidc-info'].access_token;
	// 	}
    //     if (token) {
    //         try {
    //             const loggedinUserInfo = jwt_decode<LoginUserInfo>(token);
    //             setLoginUserInfo(loggedinUserInfo);
    //             GetLoginUserInfo(userDetails);
    //             UserId = loggedinUserInfo.UserId;                
    //         } catch (error) {
    //             signOut();
    //         }
    //     }
    //     else {
    //         signOut();
    //     }

    // }, [user]);

    return (
        <Container maxWidth="sm" className="qadpt-superadminlogin">
            <Box mb={4} className="qadpt-brand-logo">
                <Typography variant="h3" className="qadpt-brand-logo-text">
                    QUICKADOPT
                </Typography>
            </Box>

            <Box className="qadpt-welcome-message">
                <Typography variant="h4" className="qadpt-welcome-message-text">
                    Welcome back
                </Typography>
            </Box>

            <Box className="qadpt-login-form">
                <Typography className="qadpt-form-label">
                    Email
                </Typography>
                <TextField
                    required
                    fullWidth
                    type="email"
                    id="email"
                    name="Email"
                    autoComplete="Email"
                    autoFocus
                    value={email}
                    onChange={handleEmailChange}
                    placeholder="eg, <EMAIL>"
                    className="qadpt-custom-input"
                />

                <Typography className="qadpt-form-label">
                    Password
                </Typography>
                <TextField
                    required
                    fullWidth
                    type={showPassword ? "text" : "password"}
                    id="password"
                    name="password"
                    autoComplete="password"
                    autoFocus
                    value={password}
                    onChange={handlePasswordChange}
                    placeholder="Enter your password"
                    className="qadpt-custom-input"
                    InputProps={{
                        endAdornment: (
                            <InputAdornment position="end">
                                <IconButton
                                    aria-label="toggle password visibility"
                                    onClick={handleClickShowPassword}
                                    edge="end"
                                >
                                    {/* {showPassword ? <VisibilityOff /> : <Visibility />} */}
                                    <i className={`fal ${showPassword ? "fa-eye-slash" : "fa-eye"}`}></i>

                                </IconButton>
                            </InputAdornment>
                        ),
                    }}
                   
                />
                {error && (
                    <FormHelperText error className="qadpt-text-danger">
                        {error}
                    </FormHelperText>
                )}

                <Button
                    type="button"
                    fullWidth
                    variant="contained"
                    className="qadpt-btn-default"
                    onClick={handleSubmit}
                > 
                        Continue
                </Button>
            </Box>

            <Box mt={12} className="qadpt-login-footer">
                <Typography variant="body2" className="qadpt-footer-text">
                    <Link sx={{ cursor: "pointer" }} className="qadpt-footer-link">Terms of use</Link> |
                    <Link sx={{ cursor: "pointer" }} className="qadpt-footer-link">Privacy Policy</Link>
                </Typography>
            </Box>
        </Container>
    );
}
export {SAinitialsData}