import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Container,
  Box,
  IconButton,
  Tooltip,
  Button,
  CircularProgress,
  Snackbar,
  Alert,
  Typography,
  Paper
} from "@mui/material";
import {
  DataGrid,
  GridColDef,
  GridRenderCellParams,
  GridPaginationModel,
} from "@mui/x-data-grid";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import VisibilityIcon from "@mui/icons-material/Visibility";
import { useTranslation } from "react-i18next";
import { isSidebarOpen, subscribe } from "../adminMenu/sidemenustate";
import { GetSystemPromptHistory } from "../../services/SystemPromtServices";
import { useAuth } from "../../components/auth/AuthProvider";
import loader from "../../assets/loader.gif";
import BorderColorOutlinedIcon from "@mui/icons-material/BorderColorOutlined";
import { formatDateTime } from "../common/TimeZoneConversion";
import { format } from 'date-fns';

// Define the interface for system prompt history items
interface SystemPromptHistoryItem {
  Id: string;
  SystemPromptId: string;
  SystemPrompt: string;
  CreatedDate: string;
  CreatedBy: string;
  Version: number;
}

const ScriptHistory = () => {
  const { t: translate } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const { userDetails } = useAuth();
  const OrganizationId = userDetails?.OrganizationId || "";
  const agentData = location.state?.agentData;
  const [, setSidebarOpen] = useState(isSidebarOpen()); // Only need the setter for the subscription
  const [historyItems, setHistoryItems] = useState<SystemPromptHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState<"success" | "error" | "info" | "warning">("info");
  const [totalCount, setTotalCount] = useState(0);
  const [paginationModel, setPaginationModel] = useState<GridPaginationModel>({
    page: 0,
    pageSize: 15,
  });

  useEffect(() => {
    const unsubscribe = subscribe(setSidebarOpen);
    return () => unsubscribe();
  }, []);

  useEffect(() => {
    if (agentData?.Id || agentData?.AccountId) {
      fetchHistoryData();
    }
  }, [agentData, paginationModel]); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchHistoryData = async () => {
    if (!agentData) return;

    try {
      setIsLoading(true);
      const agentId = agentData.Id || agentData.AccountId;
      const skip = paginationModel.page * paginationModel.pageSize;
      const top = paginationModel.pageSize;

      // Create a handler function for the API response
      const handleHistoryResponse = (data: any) => {
        if (data && Array.isArray(data)) {
          setHistoryItems(data);
          setTotalCount(data.length);
        } else if (data && !Array.isArray(data) && typeof data === 'object') {
          // If it's a single object, wrap it in an array
          setHistoryItems([data]);
          setTotalCount(1);
        } else {
          setHistoryItems([]);
          setTotalCount(0);
        }
        setIsLoading(false);
      };

      // Call the API to get the system prompt history
      GetSystemPromptHistory(
        handleHistoryResponse,
        setIsLoading,
        OrganizationId,
        agentId,
        agentData.AccountId,
        skip,
        top
      );
    } catch (error) {
      console.error("Error fetching system prompt history:", error);
      setSnackbarMessage(translate("Failed to load system prompt history"));
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
      setIsLoading(false);
    }
  };

  const handleBackClick = () => {
    navigate("/settings/agents"); // Navigate back to the agents page
  };

  const handleViewClick = (historyItem: SystemPromptHistoryItem) => {
    // Navigate to the historical system prompt viewer
    navigate("/settings/scripthistoryviewer", {
      state: {
        agentData: agentData,
        historyItem: historyItem
      }
    });
  };

  const handleCopyClick = (historyItem: SystemPromptHistoryItem) => {
    // Copy the system prompt to clipboard
    navigator.clipboard.writeText(historyItem.SystemPrompt)
      .then(() => {
        setSnackbarMessage(translate("System prompt copied to clipboard"));
        setSnackbarSeverity("success");
        setSnackbarOpen(true);
      })
      .catch((error) => {
        console.error("Error copying to clipboard:", error);
        setSnackbarMessage(translate("Failed to copy system prompt"));
        setSnackbarSeverity("error");
        setSnackbarOpen(true);
      });
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleString();
  };

  const columns: GridColDef[] = [
    // {
    //   field: "Version",
    //   headerName: translate("Version"),
    //   width: 100,
    //   flex: 0.5,
    //   disableColumnMenu: true,
    //   resizable: false,
    // },
    {
      field: "SystemPrompt",
      headerName: translate("System Prompt"),
      width: 350,
      flex: 2,
      disableColumnMenu: true,
      resizable: false,
      renderCell: (params) => {
        const systemPrompt = params.value || '';
        const truncatedText = systemPrompt.length > 20 ? `${systemPrompt.substring(0, 20)}...` : systemPrompt;

        return (
         <Tooltip title={systemPrompt} arrow>
                  <div
                    style={{
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    width: '100%' // Ensures it takes the full cell width
                    }}
                  >
                    {systemPrompt}
                  </div>
                  </Tooltip>
        );
      }
    },
    {
      field: "CreatedBy",
      headerName: translate("Created By"),
      width: 150,
      flex: 1,
      disableColumnMenu: true,
      resizable: false,
    },
    {
  field: "CreatedDate",
  headerName: translate("Created Date"),
  width: 180,
  flex: 1,
  disableColumnMenu: true,
  resizable: false,
  renderCell: (params) => {
    if (!params.value) return "";

    // Parse UTC timestamp
    const utcDate = new Date(params.value);

    // Convert to local time using offset
    const localTime = new Date(utcDate.getTime() + new Date().getTimezoneOffset() * -60000);

    return format(localTime, 'dd-MM-yyyy hh:mm a');
  }
},
    {
      field: "actions",
      headerName: translate("Actions"),
      sortable: false,
      width: 120,
      flex: 0.8,
      disableColumnMenu: true,
      resizable: false,
      renderCell: (params: GridRenderCellParams) => {
        const historyItem = params.row as SystemPromptHistoryItem;
        return (
          <Box className="qadpt-grd-act">
            <Tooltip arrow title={translate("View")}>
              <IconButton
                onClick={() => handleViewClick(historyItem)}
                size="small"
              >
                <VisibilityIcon style={{ color: "#7A7B7D" }} />
              </IconButton>
            </Tooltip>
            <Tooltip arrow title={translate("Copy")}>
              <IconButton
                onClick={() => handleCopyClick(historyItem)}
                size="small"
              >
                <ContentCopyIcon style={{ color: "#7A7B7D" }} />
              </IconButton>
            </Tooltip>
          </Box>
        );
      },
    },
  ];

  return (
    <Container>
      <div className="qadpt-midpart setng-box">
        <div className="qadpt-content-block">
          <div>
            <div className="qadpt-head">
              <div className="qadpt-title-sec">
                {agentData && (
                  <IconButton
                    onClick={handleBackClick}
                    sx={{ marginRight: "10px" }} 
                  >
                    <Tooltip title={translate("Back to System Prompts")}>
                      <ArrowBackIcon />
                    </Tooltip>
                    <div className="qadpt-title" style={{color:"black"}}>{translate("System Prompt History")}</div>
                  </IconButton>
                )}
                
                <div className="qadpt-description" style={{position:"relative",left:"40px"}}>
                  {agentData ? `${translate("History for")} ${agentData.BotName}` : translate("View system prompt history")}
                </div>
              </div>
            </div>

            <div>
              <Box className="qadpt-content-box">
                {agentData ? (
                  <div>
                   <Box sx={{ mb: 2,left:"40px", bottom: "50px", position: "relative", display: "flex", alignItems: "center", gap: 10 }}>
  <Typography variant="subtitle1" fontWeight="bold" sx={{ whiteSpace: "nowrap" }}>
    {translate("Bot Name")}: {agentData.BotName}
  </Typography>
  <Typography>
    {translate("Created By")}: {agentData.CreatedBy || "N/A"}
  </Typography>
</Box>

                    <Paper
                      elevation={2}
                      sx={{
                        p: 2,
                        backgroundColor: '#fff',
                        border: '1px solid #ddd',
                        borderRadius: '4px',
                        height: '64vh',
                        bottom: '75px',
                        position:"relative"
                      }}
                    >
                      <DataGrid
                        className="qadpt-account-grd"
                        rows={historyItems}
                        columns={columns}
                        pagination
                        paginationMode="server"
                        getRowId={(row) => row.Id}
                        paginationModel={paginationModel}
                        onPaginationModelChange={setPaginationModel}
                        rowCount={totalCount}
                        pageSizeOptions={[15, 25, 50, 100]}
                        localeText={{
                          MuiTablePagination: {
                            labelRowsPerPage: translate("Records Per Page"),
                          },
                        }}
                        disableRowSelectionOnClick={true}
                        slotProps={{
                          pagination: {
                            sx: {
                              overflow: 'hidden',
                            },
                          },
                        }}
                      />
                      {isLoading && (
                        <div className="Loaderstyles">
                          <img
                            src={loader}
                            alt="Spinner"
                            className="LoaderSpinnerStyles"
                          />
                        </div>
                      )}
                      {!isLoading && historyItems.length === 0 && (
                        <Box sx={{ p: 3, textAlign: 'center' }}>
                          <Typography variant="body1">
                            {translate("No history records found for this system prompt.")}
                          </Typography>
                        </Box>
                      )}
                    </Paper>
                  </div>
                ) : (
                  <div>
                    <p>{translate("Select a system prompt from the System Prompts page to view its history.")}</p>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={() => navigate("/settings/agents")}
                      sx={{ mt: 2 }}
                    >
                      {translate("Go to System Prompts")}
                    </Button>
                  </div>
                )}
              </Box>
            </div>
          </div>
        </div>
      </div>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbarSeverity}
          sx={{ width: '100%' }}
        >
          {translate(snackbarMessage)}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default ScriptHistory;
