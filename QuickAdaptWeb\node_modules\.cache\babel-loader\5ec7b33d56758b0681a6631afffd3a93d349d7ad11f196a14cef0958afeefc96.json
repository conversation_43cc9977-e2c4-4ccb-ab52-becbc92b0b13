{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptWeb\\\\src\\\\routing\\\\Routings.tsx\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport { lazy } from \"react\";\nimport { Navigate, useRoutes } from \"react-router-dom\";\nimport NotificationSettings from \"../components/settings/NotificationSettings\";\nimport Builder from \"../components/guide/Builder\";\nimport { Dashboard } from \"@mui/icons-material\";\nimport AdminList from \"../components/organization/AdminList\";\nimport AdminPage from \"../components/organization/AdminPage\";\nimport Translater from \"../components/multilingual/Multilingual\";\nimport DomainSettings from \"../components/settings/DomainSettings\";\nimport { useAuth } from \"../components/auth/AuthProvider\";\nimport Audience from \"../components/audience/Audience\";\nimport Tours from \"../components/tours/Tours\";\nimport Announcements from \"../components/announcements/Announcements\";\nimport Banners from \"../components/banners/Banners\";\nimport Tooltip from \"../components/tooltips/Tooltips\";\nimport Hotspots from \"../components/hotspots/Hotspots\";\nimport Checklists from \"../components/checklists/Checklists\";\nimport Surveys from \"../components/surveys/Survey\";\nimport ProtectedRoute from \"./ProtectedRoute\";\nimport { useParams } from \"react-router\";\n\n//import { toLanguage } from \"../components/adminMenu/AdminMenu\"\nimport Scripts from \"../components/agents/Scripts\";\nimport ScriptHistory from \"../components/agents/ScriptHistory\";\nimport ScriptHistoryViewer from \"../components/agents/ScriptHistoryViewer\";\nimport AgentsList from \"../components/agents/Agentslist\";\nimport ModernDashboard from \"../components/dashboard/ModernDashboard\";\nimport { checkSessionExpired } from \"../services/APIService\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AccountCreate = /*#__PURE__*/lazy(_c = () => import(\"../components/account/AccountCreate\"));\n_c2 = AccountCreate;\nconst AuditLogList = /*#__PURE__*/lazy(_c3 = () => import(\"../components/auditLog/AuditLogList\"));\n_c4 = AuditLogList;\nconst Login = /*#__PURE__*/lazy(_c5 = () => import(\"../components/login/login\"));\n_c6 = Login;\nconst OrganizationList = /*#__PURE__*/lazy(_c7 = () => import(\"../components/organization/OrganizationList\"));\n_c8 = OrganizationList;\nconst AccountsList = /*#__PURE__*/lazy(_c9 = () => import(\"../components/account/AccountList\"));\n_c0 = AccountsList;\nconst GuidesList = /*#__PURE__*/lazy(_c1 = () => import(\"../components/guide/GuideList\"));\n_c10 = GuidesList;\nconst UserList = /*#__PURE__*/lazy(_c11 = () => import(\"../components/user/UserList\"));\n_c12 = UserList;\nconst Teamsetting = /*#__PURE__*/lazy(_c13 = () => import(\"../components/settings/TeamSettings\"));\n_c14 = Teamsetting;\nconst Settings = /*#__PURE__*/lazy(_c15 = () => import(\"../components/settings/Settings\"));\n_c16 = Settings;\nconst Training = /*#__PURE__*/lazy(_c17 = () => import(\"../components/training/Training\"));\n_c18 = Training;\nconst InstallSetting = /*#__PURE__*/lazy(_c19 = () => import(\"../components/settings/InstallSettings\"));\n_c20 = InstallSetting;\nconst UnInstall = /*#__PURE__*/lazy(_c21 = () => import(\"../components/settings/UnInstall\"));\n_c22 = UnInstall;\nconst BillingSetting = /*#__PURE__*/lazy(_c23 = () => import(\"../components/settings/BillingSettings\"));\n_c24 = BillingSetting;\nconst RightSettings = /*#__PURE__*/lazy(_c25 = () => import(\"../components/settings/RightSettings\"));\n_c26 = RightSettings;\nconst SuperAdminAuditLogList = /*#__PURE__*/lazy(_c27 = () => import(\"../components/auditLog/SuperAdminAuditLogList\"));\n_c28 = SuperAdminAuditLogList;\nconst Callback = /*#__PURE__*/lazy(_c29 = () => import(\"../services/Callback\"));\n_c30 = Callback;\nconst DashBoard = /*#__PURE__*/lazy(_c31 = () => import(\"../components/dashboard/Dashboard\"));\n_c32 = DashBoard;\nconst FileList = /*#__PURE__*/lazy(_c33 = () => import(\"../components/fileManagement/FileList\"));\n_c34 = FileList;\nconst ThemeSettings = /*#__PURE__*/lazy(_c35 = () => import(\"../components/settings/ThemeSettings\"));\n_c36 = ThemeSettings;\nconst Forgotpassword = /*#__PURE__*/lazy(_c37 = () => import(\"../components/login/Forgotpassword\"));\n_c38 = Forgotpassword;\nconst ResetPassword = /*#__PURE__*/lazy(_c39 = () => import(\"../components/login/ResetPassword\"));\n_c40 = ResetPassword;\nconst AdminLoginPage = /*#__PURE__*/lazy(_c41 = () => import(\"../components/login/Superadminloginpage\"));\n_c42 = AdminLoginPage;\nconst ExpiredLink = /*#__PURE__*/lazy(_c43 = () => import(\"../components/login/Expiredlink\"));\n// const AnnouncementSettings = lazy(() => import(\"../components/announcements/AnnouncementSettings\"));\n_c44 = ExpiredLink;\nconst WebAppSettingspage = /*#__PURE__*/lazy(_c45 = () => import(\"../components/webappsettingspage/WebAppSettings\"));\n// const TooltipSettings = lazy(() => import(\"../components/tooltips/TooltipSettings\"));\n// const ToursSettings = lazy(() => import(\"../components/tours/ToursSettings\"));\n_c46 = WebAppSettingspage;\nconst Routing = () => {\n  _s();\n  const {\n    user,\n    userDetails\n  } = useAuth();\n  const {\n    passwordLogId\n  } = useParams();\n  // const initialBanner: Banner = {\n  const routes = useRoutes([\n  // Public routes\n  {\n    path: \"/login\",\n    element: (() => {\n      // Check for auto-login parameters\n      const urlParams = new URLSearchParams(window.location.search);\n      const hasAutoLoginParams = urlParams.has('token') && urlParams.has('user_id') && urlParams.has('org_id');\n      const isAutoLoginCompleted = sessionStorage.getItem('autoLoginCompleted') === 'true';\n\n      // If we have auto-login params or auto-login is completed, show Login component to handle the flow\n      if (hasAutoLoginParams || isAutoLoginCompleted) {\n        return /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 14\n        }, this);\n      }\n\n      // Otherwise, check normal token-based redirect logic\n      return localStorage.getItem(\"access_token\") && !checkSessionExpired() ? /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 7\n      }, this) : /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 7\n      }, this);\n    })()\n  }, {\n    path: \"/callback\",\n    element: /*#__PURE__*/_jsxDEV(Callback, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 13\n    }, this)\n  }, {\n    path: \"/forgotpassword\",\n    element: /*#__PURE__*/_jsxDEV(Forgotpassword, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 13\n    }, this)\n  }, {\n    path: \"/resetpassword/:passwordLogId\",\n    element: /*#__PURE__*/_jsxDEV(ResetPassword, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 13\n    }, this)\n  }, {\n    path: \"/linkexpired\",\n    element: /*#__PURE__*/_jsxDEV(ExpiredLink, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 13\n    }, this)\n  }, {\n    path: \"/uninstall\",\n    element: /*#__PURE__*/_jsxDEV(UnInstall, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 13\n    }, this)\n  }, {\n    path: \"/admin/adminlogin\",\n    element: /*#__PURE__*/_jsxDEV(AdminLoginPage, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 13\n    }, this)\n  }, {\n    path: \"/\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"admin\", \"user\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"\",\n      element: /*#__PURE__*/_jsxDEV(ModernDashboard, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 36\n      }, this)\n    }]\n  }, {\n    path: \"*\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"admin\", \"user\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"\",\n      element: /*#__PURE__*/_jsxDEV(ModernDashboard, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 36\n      }, this)\n    }]\n  },\n  // Protected routes for Admin and User\n  {\n    path: \"/dashboard\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"admin\", \"user\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"\",\n      element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 36\n      }, this)\n    }]\n  }, {\n    path: \"/modern-dashboard\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"admin\", \"user\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"\",\n      element: /*#__PURE__*/_jsxDEV(ModernDashboard, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 36\n      }, this)\n    }]\n  }, {\n    path: \"/settings\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"admin\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"\",\n      element: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 36\n      }, this)\n    }]\n  }, {\n    path: \"/auditlog\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"admin\", \"user\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"\",\n      element: /*#__PURE__*/_jsxDEV(AuditLogList, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 36\n      }, this)\n    }]\n  }, {\n    path: \"superadmin\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"superadmin\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"organizations\",\n      element: /*#__PURE__*/_jsxDEV(OrganizationList, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 15\n      }, this)\n    }, {\n      path: \"*\",\n      element: /*#__PURE__*/_jsxDEV(OrganizationList, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 15\n      }, this)\n    }, {\n      path: \":organizationId/createadmin\",\n      element: /*#__PURE__*/_jsxDEV(AdminPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 15\n      }, this)\n    }, {\n      path: \"adminList\",\n      element: /*#__PURE__*/_jsxDEV(AdminList, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 15\n      }, this)\n    }, {\n      path: \"auditlogs\",\n      element: /*#__PURE__*/_jsxDEV(SuperAdminAuditLogList, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 15\n      }, this)\n    }, {\n      path: \"/superadmin/Multilingual\",\n      element: /*#__PURE__*/_jsxDEV(Translater, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 15\n      }, this)\n    }]\n  }, {\n    path: \"/createaccount\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"admin\", \"user\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"\",\n      element: /*#__PURE__*/_jsxDEV(AccountCreate, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 36\n      }, this)\n    }]\n  }, {\n    path: \"/guides\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"admin\", \"user\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"\",\n      element: /*#__PURE__*/_jsxDEV(GuidesList, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 36\n      }, this)\n    }]\n  }, {\n    path: \"/:organizationId\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"admin\", \"user\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"accounts\",\n      element: /*#__PURE__*/_jsxDEV(AccountsList, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 15\n      }, this)\n    }, {\n      path: \"team\",\n      element: /*#__PURE__*/_jsxDEV(UserList, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 15\n      }, this)\n    }]\n  }, {\n    path: \"/user\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"admin\", \"user\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"\",\n      element: /*#__PURE__*/_jsxDEV(UserList, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 36\n      }, this)\n    }]\n  }, {\n    path: \"/filelist\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"admin\", \"user\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"\",\n      element: /*#__PURE__*/_jsxDEV(FileList, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 36\n      }, this)\n    }]\n  }, {\n    path: \"/guide\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"admin\", \"user\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"\",\n      element: /*#__PURE__*/_jsxDEV(GuidesList, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 36\n      }, this)\n    }]\n  }, {\n    path: \"/dashboard\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"admin\", \"user\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"\",\n      element: /*#__PURE__*/_jsxDEV(DashBoard, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 36\n      }, this)\n    }]\n  }, {\n    path: \"/ThemeSettings\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"admin\", \"user\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"\",\n      element: /*#__PURE__*/_jsxDEV(ThemeSettings, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 36\n      }, this)\n    }]\n  }, {\n    path: \"/activitylog\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"admin\", \"user\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"\",\n      element: /*#__PURE__*/_jsxDEV(AuditLogList, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 36\n      }, this)\n    }]\n  }, {\n    path: \"/settings\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"admin\", \"user\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"rights\",\n      element: /*#__PURE__*/_jsxDEV(RightSettings, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 15\n      }, this)\n    }, {\n      path: \"domain\",\n      element: /*#__PURE__*/_jsxDEV(DomainSettings, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 15\n      }, this)\n    }, {\n      path: \"install\",\n      element: /*#__PURE__*/_jsxDEV(InstallSetting, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 15\n      }, this)\n    }, {\n      path: \"domain\",\n      element: /*#__PURE__*/_jsxDEV(DomainSettings, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 15\n      }, this)\n    }, {\n      path: \"notifications\",\n      element: /*#__PURE__*/_jsxDEV(NotificationSettings, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 15\n      }, this)\n    }, {\n      path: \"billing\",\n      element: /*#__PURE__*/_jsxDEV(BillingSetting, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 15\n      }, this)\n    }, {\n      path: \"scripts\",\n      element: /*#__PURE__*/_jsxDEV(Scripts, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 15\n      }, this)\n    }, {\n      path: \"scripthistory\",\n      element: /*#__PURE__*/_jsxDEV(ScriptHistory, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 15\n      }, this)\n    }, {\n      path: \"scripthistoryviewer\",\n      element: /*#__PURE__*/_jsxDEV(ScriptHistoryViewer, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 15\n      }, this)\n    }, {\n      path: \"agents\",\n      element: /*#__PURE__*/_jsxDEV(AgentsList, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 15\n      }, this)\n    }, {\n      path: \"training\",\n      element: /*#__PURE__*/_jsxDEV(Training, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 15\n      }, this)\n    }]\n  }, {\n    path: \"/Builder\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"admin\", \"user\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"\",\n      element: /*#__PURE__*/_jsxDEV(Builder, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 36\n      }, this)\n    }]\n  }, {\n    path: \"/audience\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"admin\", \"user\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"\",\n      element: /*#__PURE__*/_jsxDEV(Audience, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 36\n      }, this)\n    }]\n  }, {\n    path: \"/tours\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"admin\", \"user\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"\",\n      element: /*#__PURE__*/_jsxDEV(Tours, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 36\n      }, this)\n    }]\n  }, {\n    path: \"/announcements\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"admin\", \"user\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"\",\n      element: /*#__PURE__*/_jsxDEV(Announcements, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 36\n      }, this)\n    }]\n  }, {\n    path: \"/:guideName/:guideId/settings\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"admin\", \"user\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"\",\n      element: /*#__PURE__*/_jsxDEV(WebAppSettingspage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 36\n      }, this)\n    }]\n  }, {\n    path: \"/tooltips\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"admin\", \"user\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"\",\n      element: /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 36\n      }, this)\n    }]\n  }, {\n    path: \"/hotspots\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"admin\", \"user\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"\",\n      element: /*#__PURE__*/_jsxDEV(Hotspots, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 36\n      }, this)\n    }]\n  }, {\n    path: \"/banners\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"admin\", \"user\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"\",\n      element: /*#__PURE__*/_jsxDEV(Banners, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 36\n      }, this)\n    }]\n  }, {\n    path: \"/checklists\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"admin\", \"user\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"\",\n      element: /*#__PURE__*/_jsxDEV(Checklists, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 36\n      }, this)\n    }]\n  }, {\n    path: \"/surveys\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"admin\", \"user\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"\",\n      element: /*#__PURE__*/_jsxDEV(Surveys, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 36\n      }, this)\n    }]\n  }, {\n    path: \"/analytics-dashboard\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      allowedUserTypes: [\"admin\", \"user\"]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 338,\n      columnNumber: 13\n    }, this),\n    children: [{\n      path: \"\",\n      element: /*#__PURE__*/_jsxDEV(ModernDashboard, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 36\n      }, this)\n    }]\n  }\n  // {\n  // \tpath: \"/agents\",\n  // \telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\n  // \tchildren: [{ path: \"\", element: <AgentsList /> }],\n  // },\n  ]);\n  return routes;\n};\n_s(Routing, \"V6tA0C5pNhgTZSXw1QWqe9R4Lgo=\", false, function () {\n  return [useAuth, useParams, useRoutes];\n});\n_c47 = Routing;\nexport default Routing;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31, _c32, _c33, _c34, _c35, _c36, _c37, _c38, _c39, _c40, _c41, _c42, _c43, _c44, _c45, _c46, _c47;\n$RefreshReg$(_c, \"AccountCreate$lazy\");\n$RefreshReg$(_c2, \"AccountCreate\");\n$RefreshReg$(_c3, \"AuditLogList$lazy\");\n$RefreshReg$(_c4, \"AuditLogList\");\n$RefreshReg$(_c5, \"Login$lazy\");\n$RefreshReg$(_c6, \"Login\");\n$RefreshReg$(_c7, \"OrganizationList$lazy\");\n$RefreshReg$(_c8, \"OrganizationList\");\n$RefreshReg$(_c9, \"AccountsList$lazy\");\n$RefreshReg$(_c0, \"AccountsList\");\n$RefreshReg$(_c1, \"GuidesList$lazy\");\n$RefreshReg$(_c10, \"GuidesList\");\n$RefreshReg$(_c11, \"UserList$lazy\");\n$RefreshReg$(_c12, \"UserList\");\n$RefreshReg$(_c13, \"Teamsetting$lazy\");\n$RefreshReg$(_c14, \"Teamsetting\");\n$RefreshReg$(_c15, \"Settings$lazy\");\n$RefreshReg$(_c16, \"Settings\");\n$RefreshReg$(_c17, \"Training$lazy\");\n$RefreshReg$(_c18, \"Training\");\n$RefreshReg$(_c19, \"InstallSetting$lazy\");\n$RefreshReg$(_c20, \"InstallSetting\");\n$RefreshReg$(_c21, \"UnInstall$lazy\");\n$RefreshReg$(_c22, \"UnInstall\");\n$RefreshReg$(_c23, \"BillingSetting$lazy\");\n$RefreshReg$(_c24, \"BillingSetting\");\n$RefreshReg$(_c25, \"RightSettings$lazy\");\n$RefreshReg$(_c26, \"RightSettings\");\n$RefreshReg$(_c27, \"SuperAdminAuditLogList$lazy\");\n$RefreshReg$(_c28, \"SuperAdminAuditLogList\");\n$RefreshReg$(_c29, \"Callback$lazy\");\n$RefreshReg$(_c30, \"Callback\");\n$RefreshReg$(_c31, \"DashBoard$lazy\");\n$RefreshReg$(_c32, \"DashBoard\");\n$RefreshReg$(_c33, \"FileList$lazy\");\n$RefreshReg$(_c34, \"FileList\");\n$RefreshReg$(_c35, \"ThemeSettings$lazy\");\n$RefreshReg$(_c36, \"ThemeSettings\");\n$RefreshReg$(_c37, \"Forgotpassword$lazy\");\n$RefreshReg$(_c38, \"Forgotpassword\");\n$RefreshReg$(_c39, \"ResetPassword$lazy\");\n$RefreshReg$(_c40, \"ResetPassword\");\n$RefreshReg$(_c41, \"AdminLoginPage$lazy\");\n$RefreshReg$(_c42, \"AdminLoginPage\");\n$RefreshReg$(_c43, \"ExpiredLink$lazy\");\n$RefreshReg$(_c44, \"ExpiredLink\");\n$RefreshReg$(_c45, \"WebAppSettingspage$lazy\");\n$RefreshReg$(_c46, \"WebAppSettingspage\");\n$RefreshReg$(_c47, \"Routing\");", "map": {"version": 3, "names": ["React", "lazy", "Navigate", "useRoutes", "NotificationSettings", "Builder", "Dashboard", "AdminList", "AdminPage", "Translater", "DomainSettings", "useAuth", "Audience", "Tours", "Announcements", "Banners", "<PERSON><PERSON><PERSON>", "Hotspots", "Checklists", "Surveys", "ProtectedRoute", "useParams", "<PERSON><PERSON><PERSON>", "ScriptHistory", "ScriptHistoryViewer", "AgentsList", "ModernDashboard", "checkSessionExpired", "jsxDEV", "_jsxDEV", "AccountCreate", "_c", "_c2", "AuditLogList", "_c3", "_c4", "<PERSON><PERSON>", "_c5", "_c6", "OrganizationList", "_c7", "_c8", "AccountsList", "_c9", "_c0", "GuidesList", "_c1", "_c10", "UserList", "_c11", "_c12", "Teamsetting", "_c13", "_c14", "Settings", "_c15", "_c16", "Training", "_c17", "_c18", "InstallSetting", "_c19", "_c20", "UnInstall", "_c21", "_c22", "BillingSetting", "_c23", "_c24", "RightSettings", "_c25", "_c26", "SuperAdminAuditLogList", "_c27", "_c28", "Callback", "_c29", "_c30", "DashBoard", "_c31", "_c32", "FileList", "_c33", "_c34", "ThemeSettings", "_c35", "_c36", "Forgotpassword", "_c37", "_c38", "ResetPassword", "_c39", "_c40", "AdminLoginPage", "_c41", "_c42", "ExpiredLink", "_c43", "_c44", "WebAppSettingspage", "_c45", "_c46", "Routing", "_s", "user", "userDetails", "passwordLogId", "routes", "path", "element", "urlParams", "URLSearchParams", "window", "location", "search", "hasAutoLoginParams", "has", "isAutoLoginCompleted", "sessionStorage", "getItem", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "localStorage", "to", "replace", "allowedUserTypes", "children", "_c47", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/routing/Routings.tsx"], "sourcesContent": ["import React from \"react\"\r\nimport { lazy } from \"react\"\r\nimport { Navigate, Outlet, useRoutes } from \"react-router-dom\"\r\nimport { useLocation, useNavigate } from \"react-router\"\r\nimport Home from \"../components/common/Home\"\r\nimport NotificationSettings from \"../components/settings/NotificationSettings\"\r\nimport ProfileSettings from \"../components/settings/ProfileSettings\"\r\nimport Builder from \"../components/guide/Builder\"\r\nimport { Dashboard, Domain } from \"@mui/icons-material\"\r\nimport AdminList from \"../components/organization/AdminList\"\r\nimport AdminPage from \"../components/organization/AdminPage\"\r\nimport Translater from \"../components/multilingual/Multilingual\"\r\nimport DomainSettings from \"../components/settings/DomainSettings\"\r\nimport { useAuth } from \"../components/auth/AuthProvider\"\r\nimport CodeInstall from \"../components/settings/InstallSettings\"\r\nimport Audience from \"../components/audience/Audience\"\r\nimport Tours from \"../components/tours/Tours\"\r\nimport Announcements from \"../components/announcements/Announcements\"\r\nimport Banners from \"../components/banners/Banners\"\r\nimport Tooltip from \"../components/tooltips/Tooltips\"\r\nimport Hotspots from \"../components/hotspots/Hotspots\"\r\nimport Checklists from \"../components/checklists/Checklists\"\r\nimport Surveys from \"../components/surveys/Survey\";\r\nimport ProtectedRoute from \"./ProtectedRoute\";\r\nimport { useParams } from \"react-router\";\r\n\r\n//import { toLanguage } from \"../components/adminMenu/AdminMenu\"\r\nimport Scripts from \"../components/agents/Scripts\"\r\nimport ScriptHistory from \"../components/agents/ScriptHistory\"\r\nimport ScriptHistoryViewer from \"../components/agents/ScriptHistoryViewer\"\r\nimport AgentsList from \"../components/agents/Agentslist\"\r\nimport ModernDashboard from \"../components/dashboard/ModernDashboard\"\r\nimport { checkSessionExpired } from \"../services/APIService\"\r\nconst AccountCreate = lazy(() => import(\"../components/account/AccountCreate\"));\r\nconst AuditLogList = lazy(() => import(\"../components/auditLog/AuditLogList\"));\r\nconst Login = lazy(() => import(\"../components/login/login\"));\r\nconst OrganizationList = lazy(() => import(\"../components/organization/OrganizationList\"));\r\nconst AccountsList = lazy(() => import(\"../components/account/AccountList\"));\r\nconst GuidesList = lazy(() => import(\"../components/guide/GuideList\"));\r\nconst UserList = lazy(() => import(\"../components/user/UserList\"));\r\nconst Teamsetting = lazy(() => import(\"../components/settings/TeamSettings\"));\r\nconst Settings = lazy(() => import(\"../components/settings/Settings\"));\r\nconst Training = lazy(() => import(\"../components/training/Training\"));\r\nconst InstallSetting = lazy(() => import(\"../components/settings/InstallSettings\"));\r\nconst UnInstall = lazy(() => import(\"../components/settings/UnInstall\"));\r\nconst BillingSetting = lazy(() => import(\"../components/settings/BillingSettings\"));\r\nconst RightSettings = lazy(() => import(\"../components/settings/RightSettings\"));\r\nconst SuperAdminAuditLogList = lazy(() => import(\"../components/auditLog/SuperAdminAuditLogList\"));\r\nconst Callback = lazy(() => import(\"../services/Callback\"));\r\nconst DashBoard = lazy(() => import(\"../components/dashboard/Dashboard\"));\r\nconst FileList = lazy(() => import(\"../components/fileManagement/FileList\"));\r\nconst ThemeSettings = lazy(() => import(\"../components/settings/ThemeSettings\"));\r\nconst Forgotpassword = lazy(() => import(\"../components/login/Forgotpassword\"));\r\nconst ResetPassword = lazy(() => import(\"../components/login/ResetPassword\"));\r\nconst AdminLoginPage = lazy(() => import(\"../components/login/Superadminloginpage\"));\r\nconst ExpiredLink = lazy(() => import(\"../components/login/Expiredlink\"));\r\n// const AnnouncementSettings = lazy(() => import(\"../components/announcements/AnnouncementSettings\"));\r\nconst WebAppSettingspage = lazy(() => import(\"../components/webappsettingspage/WebAppSettings\"));\r\n// const TooltipSettings = lazy(() => import(\"../components/tooltips/TooltipSettings\"));\r\n// const ToursSettings = lazy(() => import(\"../components/tours/ToursSettings\"));\r\nconst Routing = () => {\r\n\tconst { user, userDetails } = useAuth();\r\n\tconst { passwordLogId } = useParams();\r\n\t// const initialBanner: Banner = {\r\n  const routes = useRoutes([\r\n\t\t// Public routes\r\n{\r\n  path: \"/login\",\r\n  element: (() => {\r\n    // Check for auto-login parameters\r\n    const urlParams = new URLSearchParams(window.location.search);\r\n    const hasAutoLoginParams = urlParams.has('token') && urlParams.has('user_id') && urlParams.has('org_id');\r\n    const isAutoLoginCompleted = sessionStorage.getItem('autoLoginCompleted') === 'true';\r\n\r\n    // If we have auto-login params or auto-login is completed, show Login component to handle the flow\r\n    if (hasAutoLoginParams || isAutoLoginCompleted) {\r\n      return <Login />;\r\n    }\r\n\r\n    // Otherwise, check normal token-based redirect logic\r\n    return localStorage.getItem(\"access_token\") && !checkSessionExpired() ? (\r\n      <Navigate to=\"/\" replace />\r\n    ) : (\r\n      <Login />\r\n    );\r\n  })(),\r\n},\r\n\r\n\r\n\r\n\t\t{\r\n\t\t\tpath: \"/callback\",\r\n\t\t\telement: <Callback />,\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/forgotpassword\",\r\n\t\t\telement: <Forgotpassword />,\r\n\t\t},\r\n\r\n\t\t{\r\n\t\t\tpath: \"/resetpassword/:passwordLogId\",\r\n\t\t\telement: <ResetPassword />,\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/linkexpired\",\r\n\t\t\telement: <ExpiredLink />,\r\n\t \t },\r\n\t  \t{\r\n\t\t\tpath: \"/uninstall\",\r\n\t\t\telement: <UnInstall />,\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/admin/adminlogin\",\r\n\t\t\telement: <AdminLoginPage />,\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <ModernDashboard /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"*\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <ModernDashboard /> }],\r\n\t\t},\r\n\r\n\t\t// Protected routes for Admin and User\r\n\t\t{\r\n\t\t\tpath: \"/dashboard\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <Dashboard /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/modern-dashboard\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <ModernDashboard /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/settings\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <Settings /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/auditlog\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <AuditLogList /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"superadmin\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"superadmin\"]} />,\r\n\t\t\tchildren: [\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"organizations\",\r\n\t\t\t\t\telement: <OrganizationList />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"*\",\r\n\t\t\t\t\telement: <OrganizationList />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \":organizationId/createadmin\",\r\n\t\t\t\t\telement: <AdminPage />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"adminList\",\r\n\t\t\t\t\telement: <AdminList />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"auditlogs\",\r\n\t\t\t\t\telement: <SuperAdminAuditLogList />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"/superadmin/Multilingual\",\r\n\t\t\t\t\telement: <Translater />\r\n\t\t\t\t},\r\n\t\t\t],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/createaccount\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <AccountCreate /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/guides\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <GuidesList /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/:organizationId\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"accounts\",\r\n\t\t\t\t\telement: <AccountsList />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"team\",\r\n\t\t\t\t\telement: <UserList />,\r\n\t\t\t\t},\r\n\t\t\t],\r\n\t\t},\r\n\r\n\t\t{\r\n\t\t\tpath: \"/user\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <UserList /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/filelist\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <FileList /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/guide\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <GuidesList /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/dashboard\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <DashBoard /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/ThemeSettings\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <ThemeSettings /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/activitylog\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <AuditLogList /> }],\r\n\t\t},\r\n\r\n\t\t{\r\n\t\t\tpath: \"/settings\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"rights\",\r\n\t\t\t\t\telement: <RightSettings />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"domain\",\r\n\t\t\t\t\telement: <DomainSettings />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"install\",\r\n\t\t\t\t\telement: <InstallSetting />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"domain\",\r\n\t\t\t\t\telement: <DomainSettings />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"notifications\",\r\n\t\t\t\t\telement: <NotificationSettings />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"billing\",\r\n\t\t\t\t\telement: <BillingSetting />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"scripts\",\r\n\t\t\t\t\telement: <Scripts />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"scripthistory\",\r\n\t\t\t\t\telement: <ScriptHistory />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"scripthistoryviewer\",\r\n\t\t\t\t\telement: <ScriptHistoryViewer />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"agents\",\r\n\t\t\t\t\telement: <AgentsList />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"training\",\r\n\t\t\t\t\telement: <Training />,\r\n\t\t\t\t},\r\n\t\t\t],\r\n\t\t},\r\n\r\n\t\t{\r\n\t\t\tpath: \"/Builder\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <Builder /> }],\r\n\t\t},\r\n\r\n\t\t{\r\n\t\t\tpath: \"/audience\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <Audience /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/tours\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <Tours /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/announcements\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <Announcements /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/:guideName/:guideId/settings\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <WebAppSettingspage /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/tooltips\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <Tooltip /> }],\r\n\t  \t},\r\n\t  \t{\r\n\t\t\tpath: \"/hotspots\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <Hotspots/> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/banners\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <Banners /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/checklists\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <Checklists /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/surveys\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <Surveys /> }],\r\n\t  },\r\n\t\t{\r\n\t\t\tpath: \"/analytics-dashboard\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <ModernDashboard /> }],\r\n\t\t},\r\n\t\t// {\r\n\t\t// \tpath: \"/agents\",\r\n\t\t// \telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t// \tchildren: [{ path: \"\", element: <AgentsList /> }],\r\n\t\t// },\r\n\t]);\r\n\r\n  return routes;\r\n};\r\n\r\nexport default Routing;\r\n\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,OAAO;AAC5B,SAASC,QAAQ,EAAUC,SAAS,QAAQ,kBAAkB;AAG9D,OAAOC,oBAAoB,MAAM,6CAA6C;AAE9E,OAAOC,OAAO,MAAM,6BAA6B;AACjD,SAASC,SAAS,QAAgB,qBAAqB;AACvD,OAAOC,SAAS,MAAM,sCAAsC;AAC5D,OAAOC,SAAS,MAAM,sCAAsC;AAC5D,OAAOC,UAAU,MAAM,yCAAyC;AAChE,OAAOC,cAAc,MAAM,uCAAuC;AAClE,SAASC,OAAO,QAAQ,iCAAiC;AAEzD,OAAOC,QAAQ,MAAM,iCAAiC;AACtD,OAAOC,KAAK,MAAM,2BAA2B;AAC7C,OAAOC,aAAa,MAAM,2CAA2C;AACrE,OAAOC,OAAO,MAAM,+BAA+B;AACnD,OAAOC,OAAO,MAAM,iCAAiC;AACrD,OAAOC,QAAQ,MAAM,iCAAiC;AACtD,OAAOC,UAAU,MAAM,qCAAqC;AAC5D,OAAOC,OAAO,MAAM,8BAA8B;AAClD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,SAAS,QAAQ,cAAc;;AAExC;AACA,OAAOC,OAAO,MAAM,8BAA8B;AAClD,OAAOC,aAAa,MAAM,oCAAoC;AAC9D,OAAOC,mBAAmB,MAAM,0CAA0C;AAC1E,OAAOC,UAAU,MAAM,iCAAiC;AACxD,OAAOC,eAAe,MAAM,yCAAyC;AACrE,SAASC,mBAAmB,QAAQ,wBAAwB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAC5D,MAAMC,aAAa,gBAAG7B,IAAI,CAAA8B,EAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC,CAAC;AAACC,GAAA,GAA1EF,aAAa;AACnB,MAAMG,YAAY,gBAAGhC,IAAI,CAAAiC,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC,CAAC;AAACC,GAAA,GAAzEF,YAAY;AAClB,MAAMG,KAAK,gBAAGnC,IAAI,CAAAoC,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC;AAACC,GAAA,GAAxDF,KAAK;AACX,MAAMG,gBAAgB,gBAAGtC,IAAI,CAAAuC,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,6CAA6C,CAAC,CAAC;AAACC,GAAA,GAArFF,gBAAgB;AACtB,MAAMG,YAAY,gBAAGzC,IAAI,CAAA0C,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAAC;AAACC,GAAA,GAAvEF,YAAY;AAClB,MAAMG,UAAU,gBAAG5C,IAAI,CAAA6C,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC;AAACC,IAAA,GAAjEF,UAAU;AAChB,MAAMG,QAAQ,gBAAG/C,IAAI,CAAAgD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAAC;AAACC,IAAA,GAA7DF,QAAQ;AACd,MAAMG,WAAW,gBAAGlD,IAAI,CAAAmD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC,CAAC;AAACC,IAAA,GAAxEF,WAAW;AACjB,MAAMG,QAAQ,gBAAGrD,IAAI,CAAAsD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC;AAACC,IAAA,GAAjEF,QAAQ;AACd,MAAMG,QAAQ,gBAAGxD,IAAI,CAAAyD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC;AAACC,IAAA,GAAjEF,QAAQ;AACd,MAAMG,cAAc,gBAAG3D,IAAI,CAAA4D,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAAC;AAACC,IAAA,GAA9EF,cAAc;AACpB,MAAMG,SAAS,gBAAG9D,IAAI,CAAA+D,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC,CAAC;AAACC,IAAA,GAAnEF,SAAS;AACf,MAAMG,cAAc,gBAAGjE,IAAI,CAAAkE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAAC;AAACC,IAAA,GAA9EF,cAAc;AACpB,MAAMG,aAAa,gBAAGpE,IAAI,CAAAqE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC,CAAC;AAACC,IAAA,GAA3EF,aAAa;AACnB,MAAMG,sBAAsB,gBAAGvE,IAAI,CAAAwE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,+CAA+C,CAAC,CAAC;AAACC,IAAA,GAA7FF,sBAAsB;AAC5B,MAAMG,QAAQ,gBAAG1E,IAAI,CAAA2E,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,IAAA,GAAtDF,QAAQ;AACd,MAAMG,SAAS,gBAAG7E,IAAI,CAAA8E,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAAC;AAACC,IAAA,GAApEF,SAAS;AACf,MAAMG,QAAQ,gBAAGhF,IAAI,CAAAiF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC,CAAC;AAACC,IAAA,GAAvEF,QAAQ;AACd,MAAMG,aAAa,gBAAGnF,IAAI,CAAAoF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC,CAAC;AAACC,IAAA,GAA3EF,aAAa;AACnB,MAAMG,cAAc,gBAAGtF,IAAI,CAAAuF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC,CAAC;AAACC,IAAA,GAA1EF,cAAc;AACpB,MAAMG,aAAa,gBAAGzF,IAAI,CAAA0F,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAAC;AAACC,IAAA,GAAxEF,aAAa;AACnB,MAAMG,cAAc,gBAAG5F,IAAI,CAAA6F,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,yCAAyC,CAAC,CAAC;AAACC,IAAA,GAA/EF,cAAc;AACpB,MAAMG,WAAW,gBAAG/F,IAAI,CAAAgG,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC;AACzE;AAAAC,IAAA,GADMF,WAAW;AAEjB,MAAMG,kBAAkB,gBAAGlG,IAAI,CAAAmG,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,iDAAiD,CAAC,CAAC;AAChG;AACA;AAAAC,IAAA,GAFMF,kBAAkB;AAGxB,MAAMG,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC,IAAI;IAAEC;EAAY,CAAC,GAAG9F,OAAO,CAAC,CAAC;EACvC,MAAM;IAAE+F;EAAc,CAAC,GAAGrF,SAAS,CAAC,CAAC;EACrC;EACC,MAAMsF,MAAM,GAAGxG,SAAS,CAAC;EACzB;EACF;IACEyG,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,CAAC,MAAM;MACd;MACA,MAAMC,SAAS,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;MAC7D,MAAMC,kBAAkB,GAAGL,SAAS,CAACM,GAAG,CAAC,OAAO,CAAC,IAAIN,SAAS,CAACM,GAAG,CAAC,SAAS,CAAC,IAAIN,SAAS,CAACM,GAAG,CAAC,QAAQ,CAAC;MACxG,MAAMC,oBAAoB,GAAGC,cAAc,CAACC,OAAO,CAAC,oBAAoB,CAAC,KAAK,MAAM;;MAEpF;MACA,IAAIJ,kBAAkB,IAAIE,oBAAoB,EAAE;QAC9C,oBAAOxF,OAAA,CAACO,KAAK;UAAAoF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAClB;;MAEA;MACA,OAAOC,YAAY,CAACL,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC5F,mBAAmB,CAAC,CAAC,gBACnEE,OAAA,CAAC3B,QAAQ;QAAC2H,EAAE,EAAC,GAAG;QAACC,OAAO;MAAA;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAE3B9F,OAAA,CAACO,KAAK;QAAAoF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CACT;IACH,CAAC,EAAE;EACL,CAAC,EAIC;IACCf,IAAI,EAAE,WAAW;IACjBC,OAAO,eAAEhF,OAAA,CAAC8C,QAAQ;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACrB,CAAC,EACD;IACCf,IAAI,EAAE,iBAAiB;IACvBC,OAAO,eAAEhF,OAAA,CAAC0D,cAAc;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC3B,CAAC,EAED;IACCf,IAAI,EAAE,+BAA+B;IACrCC,OAAO,eAAEhF,OAAA,CAAC6D,aAAa;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC1B,CAAC,EACD;IACCf,IAAI,EAAE,cAAc;IACpBC,OAAO,eAAEhF,OAAA,CAACmE,WAAW;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACtB,CAAC,EACD;IACDf,IAAI,EAAE,YAAY;IAClBC,OAAO,eAAEhF,OAAA,CAACkC,SAAS;MAAAyD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACtB,CAAC,EACD;IACCf,IAAI,EAAE,mBAAmB;IACzBC,OAAO,eAAEhF,OAAA,CAACgE,cAAc;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC3B,CAAC,EACD;IACCf,IAAI,EAAE,GAAG;IACTC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChEK,QAAQ,EAAE,CAAC;MAAEpB,IAAI,EAAE,EAAE;MAAEC,OAAO,eAAEhF,OAAA,CAACH,eAAe;QAAA8F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;EACtD,CAAC,EACD;IACCf,IAAI,EAAE,GAAG;IACTC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChEK,QAAQ,EAAE,CAAC;MAAEpB,IAAI,EAAE,EAAE;MAAEC,OAAO,eAAEhF,OAAA,CAACH,eAAe;QAAA8F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;EACtD,CAAC;EAED;EACA;IACCf,IAAI,EAAE,YAAY;IAClBC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChEK,QAAQ,EAAE,CAAC;MAAEpB,IAAI,EAAE,EAAE;MAAEC,OAAO,eAAEhF,OAAA,CAACvB,SAAS;QAAAkH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;EAChD,CAAC,EACD;IACCf,IAAI,EAAE,mBAAmB;IACzBC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChEK,QAAQ,EAAE,CAAC;MAAEpB,IAAI,EAAE,EAAE;MAAEC,OAAO,eAAEhF,OAAA,CAACH,eAAe;QAAA8F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;EACtD,CAAC,EACD;IACCf,IAAI,EAAE,WAAW;IACjBC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,OAAO;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxDK,QAAQ,EAAE,CAAC;MAAEpB,IAAI,EAAE,EAAE;MAAEC,OAAO,eAAEhF,OAAA,CAACyB,QAAQ;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;EAC/C,CAAC,EACD;IACCf,IAAI,EAAE,WAAW;IACjBC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChEK,QAAQ,EAAE,CAAC;MAAEpB,IAAI,EAAE,EAAE;MAAEC,OAAO,eAAEhF,OAAA,CAACI,YAAY;QAAAuF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;EACnD,CAAC,EACD;IACCf,IAAI,EAAE,YAAY;IAClBC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,YAAY;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC7DK,QAAQ,EAAE,CACT;MACCpB,IAAI,EAAE,eAAe;MACrBC,OAAO,eAAEhF,OAAA,CAACU,gBAAgB;QAAAiF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAC7B,CAAC,EACD;MACCf,IAAI,EAAE,GAAG;MACTC,OAAO,eAAEhF,OAAA,CAACU,gBAAgB;QAAAiF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAC7B,CAAC,EACD;MACCf,IAAI,EAAE,6BAA6B;MACnCC,OAAO,eAAEhF,OAAA,CAACrB,SAAS;QAAAgH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IACtB,CAAC,EACD;MACCf,IAAI,EAAE,WAAW;MACjBC,OAAO,eAAEhF,OAAA,CAACtB,SAAS;QAAAiH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IACtB,CAAC,EACD;MACCf,IAAI,EAAE,WAAW;MACjBC,OAAO,eAAEhF,OAAA,CAAC2C,sBAAsB;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IACnC,CAAC,EACD;MACCf,IAAI,EAAE,0BAA0B;MAChCC,OAAO,eAAEhF,OAAA,CAACpB,UAAU;QAAA+G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IACvB,CAAC;EAEH,CAAC,EACD;IACCf,IAAI,EAAE,gBAAgB;IACtBC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChEK,QAAQ,EAAE,CAAC;MAAEpB,IAAI,EAAE,EAAE;MAAEC,OAAO,eAAEhF,OAAA,CAACC,aAAa;QAAA0F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;EACpD,CAAC,EACD;IACCf,IAAI,EAAE,SAAS;IACfC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChEK,QAAQ,EAAE,CAAC;MAAEpB,IAAI,EAAE,EAAE;MAAEC,OAAO,eAAEhF,OAAA,CAACgB,UAAU;QAAA2E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;EACjD,CAAC,EACD;IACCf,IAAI,EAAE,kBAAkB;IACxBC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChEK,QAAQ,EAAE,CACT;MACCpB,IAAI,EAAE,UAAU;MAChBC,OAAO,eAAEhF,OAAA,CAACa,YAAY;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IACzB,CAAC,EACD;MACCf,IAAI,EAAE,MAAM;MACZC,OAAO,eAAEhF,OAAA,CAACmB,QAAQ;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IACrB,CAAC;EAEH,CAAC,EAED;IACCf,IAAI,EAAE,OAAO;IACbC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChEK,QAAQ,EAAE,CAAC;MAAEpB,IAAI,EAAE,EAAE;MAAEC,OAAO,eAAEhF,OAAA,CAACmB,QAAQ;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;EAC/C,CAAC,EACD;IACCf,IAAI,EAAE,WAAW;IACjBC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChEK,QAAQ,EAAE,CAAC;MAAEpB,IAAI,EAAE,EAAE;MAAEC,OAAO,eAAEhF,OAAA,CAACoD,QAAQ;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;EAC/C,CAAC,EACD;IACCf,IAAI,EAAE,QAAQ;IACdC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChEK,QAAQ,EAAE,CAAC;MAAEpB,IAAI,EAAE,EAAE;MAAEC,OAAO,eAAEhF,OAAA,CAACgB,UAAU;QAAA2E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;EACjD,CAAC,EACD;IACCf,IAAI,EAAE,YAAY;IAClBC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChEK,QAAQ,EAAE,CAAC;MAAEpB,IAAI,EAAE,EAAE;MAAEC,OAAO,eAAEhF,OAAA,CAACiD,SAAS;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;EAChD,CAAC,EACD;IACCf,IAAI,EAAE,gBAAgB;IACtBC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChEK,QAAQ,EAAE,CAAC;MAAEpB,IAAI,EAAE,EAAE;MAAEC,OAAO,eAAEhF,OAAA,CAACuD,aAAa;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;EACpD,CAAC,EACD;IACCf,IAAI,EAAE,cAAc;IACpBC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChEK,QAAQ,EAAE,CAAC;MAAEpB,IAAI,EAAE,EAAE;MAAEC,OAAO,eAAEhF,OAAA,CAACI,YAAY;QAAAuF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;EACnD,CAAC,EAED;IACCf,IAAI,EAAE,WAAW;IACjBC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChEK,QAAQ,EAAE,CACT;MACCpB,IAAI,EAAE,QAAQ;MACdC,OAAO,eAAEhF,OAAA,CAACwC,aAAa;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAC1B,CAAC,EACD;MACCf,IAAI,EAAE,QAAQ;MACdC,OAAO,eAAEhF,OAAA,CAACnB,cAAc;QAAA8G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAC3B,CAAC,EACD;MACCf,IAAI,EAAE,SAAS;MACfC,OAAO,eAAEhF,OAAA,CAAC+B,cAAc;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAC3B,CAAC,EACD;MACCf,IAAI,EAAE,QAAQ;MACdC,OAAO,eAAEhF,OAAA,CAACnB,cAAc;QAAA8G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAC3B,CAAC,EACD;MACCf,IAAI,EAAE,eAAe;MACrBC,OAAO,eAAEhF,OAAA,CAACzB,oBAAoB;QAAAoH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IACjC,CAAC,EACD;MACCf,IAAI,EAAE,SAAS;MACfC,OAAO,eAAEhF,OAAA,CAACqC,cAAc;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAC3B,CAAC,EACD;MACCf,IAAI,EAAE,SAAS;MACfC,OAAO,eAAEhF,OAAA,CAACP,OAAO;QAAAkG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IACpB,CAAC,EACD;MACCf,IAAI,EAAE,eAAe;MACrBC,OAAO,eAAEhF,OAAA,CAACN,aAAa;QAAAiG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAC1B,CAAC,EACD;MACCf,IAAI,EAAE,qBAAqB;MAC3BC,OAAO,eAAEhF,OAAA,CAACL,mBAAmB;QAAAgG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAChC,CAAC,EACD;MACCf,IAAI,EAAE,QAAQ;MACdC,OAAO,eAAEhF,OAAA,CAACJ,UAAU;QAAA+F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IACvB,CAAC,EACD;MACCf,IAAI,EAAE,UAAU;MAChBC,OAAO,eAAEhF,OAAA,CAAC4B,QAAQ;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IACrB,CAAC;EAEH,CAAC,EAED;IACCf,IAAI,EAAE,UAAU;IAChBC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChEK,QAAQ,EAAE,CAAC;MAAEpB,IAAI,EAAE,EAAE;MAAEC,OAAO,eAAEhF,OAAA,CAACxB,OAAO;QAAAmH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;EAC9C,CAAC,EAED;IACCf,IAAI,EAAE,WAAW;IACjBC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChEK,QAAQ,EAAE,CAAC;MAAEpB,IAAI,EAAE,EAAE;MAAEC,OAAO,eAAEhF,OAAA,CAACjB,QAAQ;QAAA4G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;EAC/C,CAAC,EACD;IACCf,IAAI,EAAE,QAAQ;IACdC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChEK,QAAQ,EAAE,CAAC;MAAEpB,IAAI,EAAE,EAAE;MAAEC,OAAO,eAAEhF,OAAA,CAAChB,KAAK;QAAA2G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;EAC5C,CAAC,EACD;IACCf,IAAI,EAAE,gBAAgB;IACtBC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChEK,QAAQ,EAAE,CAAC;MAAEpB,IAAI,EAAE,EAAE;MAAEC,OAAO,eAAEhF,OAAA,CAACf,aAAa;QAAA0G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;EACpD,CAAC,EACD;IACCf,IAAI,EAAE,+BAA+B;IACrCC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChEK,QAAQ,EAAE,CAAC;MAAEpB,IAAI,EAAE,EAAE;MAAEC,OAAO,eAAEhF,OAAA,CAACsE,kBAAkB;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;EACzD,CAAC,EACD;IACCf,IAAI,EAAE,WAAW;IACjBC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChEK,QAAQ,EAAE,CAAC;MAAEpB,IAAI,EAAE,EAAE;MAAEC,OAAO,eAAEhF,OAAA,CAACb,OAAO;QAAAwG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;EAC5C,CAAC,EACD;IACDf,IAAI,EAAE,WAAW;IACjBC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChEK,QAAQ,EAAE,CAAC;MAAEpB,IAAI,EAAE,EAAE;MAAEC,OAAO,eAAEhF,OAAA,CAACZ,QAAQ;QAAAuG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAE,CAAC;EAC9C,CAAC,EACD;IACCf,IAAI,EAAE,UAAU;IAChBC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChEK,QAAQ,EAAE,CAAC;MAAEpB,IAAI,EAAE,EAAE;MAAEC,OAAO,eAAEhF,OAAA,CAACd,OAAO;QAAAyG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;EAC9C,CAAC,EACD;IACCf,IAAI,EAAE,aAAa;IACnBC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChEK,QAAQ,EAAE,CAAC;MAAEpB,IAAI,EAAE,EAAE;MAAEC,OAAO,eAAEhF,OAAA,CAACX,UAAU;QAAAsG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;EACjD,CAAC,EACD;IACCf,IAAI,EAAE,UAAU;IAChBC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChEK,QAAQ,EAAE,CAAC;MAAEpB,IAAI,EAAE,EAAE;MAAEC,OAAO,eAAEhF,OAAA,CAACV,OAAO;QAAAqG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;EAC7C,CAAC,EACF;IACCf,IAAI,EAAE,sBAAsB;IAC5BC,OAAO,eAAEhF,OAAA,CAACT,cAAc;MAAC2G,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChEK,QAAQ,EAAE,CAAC;MAAEpB,IAAI,EAAE,EAAE;MAAEC,OAAO,eAAEhF,OAAA,CAACH,eAAe;QAAA8F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;EACtD;EACA;EACA;EACA;EACA;EACA;EAAA,CACA,CAAC;EAED,OAAOhB,MAAM;AACf,CAAC;AAACJ,EAAA,CAhSID,OAAO;EAAA,QACkB3F,OAAO,EACXU,SAAS,EAEnBlB,SAAS;AAAA;AAAA8H,IAAA,GAJpB3B,OAAO;AAkSb,eAAeA,OAAO;AAAC,IAAAvE,EAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAA4B,IAAA;AAAAC,YAAA,CAAAnG,EAAA;AAAAmG,YAAA,CAAAlG,GAAA;AAAAkG,YAAA,CAAAhG,GAAA;AAAAgG,YAAA,CAAA/F,GAAA;AAAA+F,YAAA,CAAA7F,GAAA;AAAA6F,YAAA,CAAA5F,GAAA;AAAA4F,YAAA,CAAA1F,GAAA;AAAA0F,YAAA,CAAAzF,GAAA;AAAAyF,YAAA,CAAAvF,GAAA;AAAAuF,YAAA,CAAAtF,GAAA;AAAAsF,YAAA,CAAApF,GAAA;AAAAoF,YAAA,CAAAnF,IAAA;AAAAmF,YAAA,CAAAjF,IAAA;AAAAiF,YAAA,CAAAhF,IAAA;AAAAgF,YAAA,CAAA9E,IAAA;AAAA8E,YAAA,CAAA7E,IAAA;AAAA6E,YAAA,CAAA3E,IAAA;AAAA2E,YAAA,CAAA1E,IAAA;AAAA0E,YAAA,CAAAxE,IAAA;AAAAwE,YAAA,CAAAvE,IAAA;AAAAuE,YAAA,CAAArE,IAAA;AAAAqE,YAAA,CAAApE,IAAA;AAAAoE,YAAA,CAAAlE,IAAA;AAAAkE,YAAA,CAAAjE,IAAA;AAAAiE,YAAA,CAAA/D,IAAA;AAAA+D,YAAA,CAAA9D,IAAA;AAAA8D,YAAA,CAAA5D,IAAA;AAAA4D,YAAA,CAAA3D,IAAA;AAAA2D,YAAA,CAAAzD,IAAA;AAAAyD,YAAA,CAAAxD,IAAA;AAAAwD,YAAA,CAAAtD,IAAA;AAAAsD,YAAA,CAAArD,IAAA;AAAAqD,YAAA,CAAAnD,IAAA;AAAAmD,YAAA,CAAAlD,IAAA;AAAAkD,YAAA,CAAAhD,IAAA;AAAAgD,YAAA,CAAA/C,IAAA;AAAA+C,YAAA,CAAA7C,IAAA;AAAA6C,YAAA,CAAA5C,IAAA;AAAA4C,YAAA,CAAA1C,IAAA;AAAA0C,YAAA,CAAAzC,IAAA;AAAAyC,YAAA,CAAAvC,IAAA;AAAAuC,YAAA,CAAAtC,IAAA;AAAAsC,YAAA,CAAApC,IAAA;AAAAoC,YAAA,CAAAnC,IAAA;AAAAmC,YAAA,CAAAjC,IAAA;AAAAiC,YAAA,CAAAhC,IAAA;AAAAgC,YAAA,CAAA9B,IAAA;AAAA8B,YAAA,CAAA7B,IAAA;AAAA6B,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}