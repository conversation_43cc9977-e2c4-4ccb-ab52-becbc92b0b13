import React, { useState, useEffect, useContext } from "react";
import { NavLink, useLocation } from "react-router-dom";
import { isSidebarOpen, subscribe } from "../adminMenu/sidemenustate";
import { Container, List, ListItem, ListItemText, Typography, Box } from "@mui/material";
import { styled } from '@mui/material/styles';
import { useAuth } from "../auth/AuthProvider";
import { User } from "../../models/User";
import { useTranslation } from "react-i18next";
import Card from "../common/Card";
import { AccountContext } from "../account/AccountContext";


// Styled components for modern design
const ModernSidebar = styled(Box)<{ sidebarOpen: boolean }>(({ sidebarOpen }) => ({
  width: sidebarOpen ? '280px' : '80px',
  height: '100vh',
  backgroundColor: 'var(--color-white)',
  borderRight: '1px solid var(--color-gray-200)',
  transition: 'var(--transition-normal)',
  position: 'fixed',
  left: 0,
  top: '45px',
  zIndex: 10,
  boxShadow: 'var(--shadow-sm)',
}));

const SidebarHeader = styled(Box)({
  padding: 'var(--spacing-6) var(--spacing-4) var(--spacing-4)',
  borderBottom: '1px solid var(--color-gray-100)',
});

const SidebarTitle = styled(Typography)({
  fontSize: 'var(--font-size-lg)',
  fontWeight: 'var(--font-weight-semibold)',
  color: 'var(--color-gray-900)',
  margin: 0,
});

const ModernList = styled(List)({
  padding: 'var(--spacing-2) 0',
});

const ModernListItemWrapper = styled(NavLink)<{ active: boolean }>(({ active }) => ({
  margin: '0 var(--spacing-2)',
  borderRadius: 'var(--radius-md)',
  marginBottom: 'var(--spacing-1)',
  padding: 'var(--spacing-3) var(--spacing-4)',
  transition: 'var(--transition-fast)',
  textDecoration: 'none',
  display: 'block',
  cursor: 'pointer',

  ...(active && {
    backgroundColor: 'var(--color-primary-50)',
    borderLeft: '3px solid var(--color-primary-600)',
  }),

  '&:hover': {
    backgroundColor: active ? 'var(--color-primary-50)' : 'var(--color-gray-50)',
  },
}));

const ModernListItemText = styled(Typography)<{ active: boolean }>(({ active }) => ({
  fontSize: 'var(--font-size-sm)',
  fontWeight: active ? 'var(--font-weight-medium)' : 'var(--font-weight-normal)',
  color: active ? 'var(--color-primary-700)' : 'var(--color-gray-700)',
  transition: 'var(--transition-fast)',
}));

const Settings = () => {
	const [sidebarOpen, setSidebarOpen] = useState(true);
	const location = useLocation();
	const isHidden = location.pathname === "/settings/team";
	const { signOut, userDetails } = useAuth();
	const [OrganizationId, setOrganizationId] = useState(userDetails?.OrganizationId ?? "");
	const [userType, setUserType] = useState(userDetails?.UserType ?? "");
	const { roles } = useContext(AccountContext);

	const [user, setUser] = useState<User | null>(null);

	useEffect(() => {
		const unsubscribe = subscribe(setSidebarOpen);
		return () => unsubscribe();
	}, []);

	useEffect(() => {
		const userInfoString = localStorage.getItem("userInfo");
		if (userInfoString) {
			try {
				const userInfo = JSON.parse(userInfoString);
				if (userInfo['user']) {
					const parsedUser = JSON.parse(userInfo['user']);
					setUser(parsedUser);
					if (parsedUser) {
						const OrgId = parsedUser.OrganizationId ?? '';
						setOrganizationId(OrgId);
					}
				}
			} catch (error) {
				console.error("Error parsing userInfo: ", error);
			}
		}
		else if (userDetails) {
			setUser(userDetails);
			if (userDetails) {
				const OrgId = userDetails.OrganizationId ?? '';
				setOrganizationId(OrgId);
			}
		}
	}, []);

	const { t: translate } = useTranslation();

	const settingsItems = [
		{ text: translate('Team'), path: `/${OrganizationId}/team`, access: ['admin'] },
		{ text: translate('Roles'), path: `/${OrganizationId}/roles`, access: ['admin'] },
		{ text: translate('Account'), path: `/${OrganizationId}/accounts`, access: ['admin'] },
		{ text: translate('Multilingual'), path: "/settings/multilingual", access: ['admin','user'] },
		{ text: translate('Domain'), path: "/settings/domains", access: ['admin'] },
		{ text: translate('Rights'), path: "/settings/rights", access: ['admin','user'] },
		{ text: translate('Alerts'), path: "/settings/alerts", access: ['admin','user'] },
		{ text: translate('Billing'), path: "/settings/billing", access: ['admin'] },
		{ text: translate('Installation'), path: "/settings/install", access: ['admin','user'] },
		{ text: translate('Activity Log'), path: "/settings/activitylog", access: ['admin'] },
		{ text: translate('Agents'), path: "/settings/agents", access: ['admin','user'] },
		{ text: translate('Training'), path: "/settings/training", access: ['admin','user'] },
	];

	return (
		<>
			<Container className="sidemenubar">
				<Box className={`qadpt-setsidebar ${sidebarOpen ? "sidebar-open" : "sidebar-closed"}`}>
					<div className="qadpt-sidebarlist">{translate('Settings')}</div>
					<List className="qadpt-sidebarele">
						{[
							{ text: translate('Team'), path: `/${OrganizationId}/team`, access: ['admin'],roles:[] },
							{
								text: translate('Roles'), path: `/${OrganizationId}/roles`, access: ['admin'],roles:[]
							},
							{ text: translate('Account'), path: `/${OrganizationId}/accounts`, access: ['admin'],roles:[] },

							{
								text: translate('Domain'), path: "/settings/domains",
								access: ['admin'],roles:[]
							},
							{
								text: translate('Rights'), path: "/settings/rights",
								access: ['admin', 'user'],roles:["Account Admin","Editor","Publisher"]
							},
							{
								text: translate('Alerts'), path: "/settings/alerts",
								access: ['admin', 'user'],roles:["Account Admin","Editor","Publisher"]
							},
							{
								text: translate('Billing'), path: "/settings/billing",
								access: ['admin'],roles:["Account Admin","Editor","Publisher"]
							},
							{
								text: translate('Installation'), path: "/settings/install",
								access: ['admin', 'user'],roles:["Account Admin"]
							},
							{
								text: translate('Activity Log'), path: "/settings/activitylog",
								access: ['admin'],roles:["Account Admin","Editor","Publisher"]
							},
							{
								text: translate('Agents'), path: "/settings/agents",
								access: ['admin', 'user'],roles:["Account Admin","Editor","Publisher"]
							},
							{
								text: translate('Training'), path: "/settings/training",
								access: ['admin', 'user'],roles:["Account Admin","Editor","Publisher"]
							},
						].map((item, index) => (<>
							{item.access.includes(userType.toLocaleLowerCase()) && (userType.toLocaleLowerCase() == "user" ? item.roles.some(role => roles?.includes(role)): true) &&
								<ListItem
									key={index}
									component={NavLink}
									to={item.path}
									className={`qadpt-sidebarinput ${location.pathname === item.path ||
											(item.path === "/settings/agents" && (location.pathname === "/settings/scripts" || location.pathname === "/settings/scripthistory" || location.pathname === "/settings/scripthistoryviewer")) ||
											(item.path === `/${OrganizationId}/accounts` && location.pathname === `/${OrganizationId}/accounts/free-trial`)
											? "active" : ""
										}`}
								>
									<ListItemText
										primary={
											<Typography
												className={`qadpt-sidebarval ${location.pathname === item.path ||
														(item.path === "/settings/agents" && (location.pathname === "/settings/scripts" || location.pathname === "/settings/scripthistory" || location.pathname === "/settings/scripthistoryviewer")) ||
														(item.path === `/${OrganizationId}/accounts` && location.pathname === `/${OrganizationId}/accounts/free-trial`)
														? "active" : ""
													}`}
											// sx={{
											// 	fontFamily: "Poppins",
											// 	fontSize: "14px",
											// 	fontWeight: 400,
											// 	lineHeight: "21px",
											// 	letterSpacing: "0.3px",
											// 	textAlign: "left",
											// 	color: location.pathname === item.path ? "#ffffff" : "#202224",
											// 	marginLeft: "-2px",
											// }}
											>
												{item.text}
											</Typography>
										}
									/>
								</ListItem>
							}
						</>
						))}
					</List>
				</Box>
			</Container>
		</>
	);
};

export default Settings;
