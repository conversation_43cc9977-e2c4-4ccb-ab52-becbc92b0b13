{"ast": null, "code": "import quickadopt from \"./quickadopt.svg\";\nimport Dashboard from \"./dashboard.svg\";\nimport Auditlog from \"./auditlog.svg\";\nimport Checklist from \"./checklists.svg\";\nimport Filemanagement from \"./filemanagement.svg\";\nimport Helpcenter from \"./helpcenter.svg\";\nimport Hotspots from \"./hotspots.svg\";\nimport Installation from \"./installation.svg\";\nimport Journeys from \"./journeys.svg\";\nimport Notification from \"./notification.svg\";\nimport Settings from \"./settings.svg\";\nimport Themes from \"./themes.svg\";\nimport User from \"./user.svg\";\nimport Logout from \"./logout.svg\";\nimport Feedback from \"./feedback.svg\";\nimport Accounts from \"@mui/icons-material/GroupOutlined\";\nimport Audience from \"./Audience.svg\";\nimport Tours from \"./Tours.svg\";\nimport Announcements from \"./Announcements.svg\";\nimport Banners from \"./Banners.svg\";\nimport Tooltips from \"./Tooltip.svg\";\nimport Surveys from \"./Surveys.svg\";\nimport settings from \"./profilesettings.svg\";\nimport erroricon from \"./error.svg\";\nimport avatar from \"./avatar.svg\";\nimport Unblockaccount from \"./UnblockAccount.svg\";\nimport Keyvertical from \"./key_vertical.svg\";\nimport Mail from \"./Mail.svg\";\nimport Lockopen from \"./Lockopen.svg\";\n// import Delete from \"./delete.svg\";\n// import Edit from \"./editicon.svg\";\nimport Delete from \"./delete.svg\";\nimport logo from \"./logo.svg\";\nimport cloudoff from \"./cloudoff.svg\";\nimport QuickAdopttext from \"./QuickAdopttext.svg\";\nimport Fileedit from \"./Fileedit.svg\";\nimport clone from \"./clone.svg\";\nimport webbanner from \"./webbanner.svg\";\nimport Homepageimg from \"./Homepageimg.svg\";\nimport Wavinghand from \"./wavinghand.svg\";\nimport global from \"./global.svg\";\nimport profile from \"./profile.svg\";\nimport ArrowDown from \"./ArrowDown.svg\";\nimport Cursor from \"./Cursor.svg\";\nimport copy from \"./copy.svg\";\nimport DeleteRed from \"./DeleteRed.svg\";\nimport Trash from \"./trash.svg\";\nimport Replace from \"./Replace.svg\";\nimport PreviewImage from \"./PreviewImage.svg\";\nimport feedbackimg from \"./feedbackimg.svg\";\nimport Refresh from \"./Refresh.svg\";\nimport key from \"./key.svg\";\nimport openaikey from \"./openaikey.svg\";\n\n// SVG icons for image upload section\nexport const uploadfile = `\n<svg width=\"22\" height=\"17\" viewBox=\"0 0 22 17\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path d=\"M5.5 16.5C3.98333 16.5 2.6875 15.975 1.6125 14.925C0.5375 13.875 0 12.5917 0 11.075C0 9.775 0.391667 8.61667 1.175 7.6C1.95833 6.58333 2.98333 5.93333 4.25 5.65C4.66667 4.11667 5.5 2.875 6.75 1.925C8 0.975 9.41667 0.5 11 0.5C12.95 0.5 14.6042 1.17917 15.9625 2.5375C17.3208 3.89583 18 5.55 18 7.5C19.15 7.63333 20.1042 8.12917 20.8625 8.9875C21.6208 9.84583 22 10.85 22 12C22 13.25 21.5625 14.3125 20.6875 15.1875C19.8125 16.0625 18.75 16.5 17.5 16.5H12C11.45 16.5 10.9792 16.3042 10.5875 15.9125C10.1958 15.5208 10 15.05 10 14.5V9.35L8.4 10.9L7 9.5L11 5.5L15 9.5L13.6 10.9L12 9.35V14.5H17.5C18.2 14.5 18.7917 14.2583 19.275 13.775C19.7583 13.2917 20 12.7 20 12C20 11.3 19.7583 10.7083 19.275 10.225C18.7917 9.74167 18.2 9.5 17.5 9.5H16V7.5C16 6.11667 15.5125 4.9375 14.5375 3.9625C13.5625 2.9875 12.3833 2.5 11 2.5C9.61667 2.5 8.4375 2.9875 7.4625 3.9625C6.4875 4.9375 6 6.11667 6 7.5H5.5C4.53333 7.5 3.70833 7.84167 3.025 8.525C2.34167 9.20833 2 10.0333 2 11C2 11.9667 2.34167 12.7917 3.025 13.475C3.70833 14.1583 4.53333 14.5 5.5 14.5H8V16.5H5.5Z\" fill=\"#1C1B1F\"/>\n</svg>\n`;\nexport const hyperlink = `\n<svg width=\"45\" height=\"45\" viewBox=\"0 0 45 45\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<rect x=\"1\" y=\"1\" width=\"43\" height=\"43\" rx=\"14.5\" fill=\"white\"/>\n<rect x=\"1\" y=\"1\" width=\"43\" height=\"43\" rx=\"14.5\" stroke=\"#5F9EA0\"/>\n<path d=\"M21.5 27.5H17.5C16.1167 27.5 14.9375 27.0125 13.9625 26.0375C12.9875 25.0625 12.5 23.8833 12.5 22.5C12.5 21.1167 12.9875 19.9375 13.9625 18.9625C14.9375 17.9875 16.1167 17.5 17.5 17.5H21.5V19.5H17.5C16.6667 19.5 15.9583 19.7917 15.375 20.375C14.7917 20.9583 14.5 21.6667 14.5 22.5C14.5 23.3333 14.7917 24.0417 15.375 24.625C15.9583 25.2083 16.6667 25.5 17.5 25.5H21.5V27.5ZM18.5 23.5V21.5H26.5V23.5H18.5ZM23.5 27.5V25.5H27.5C28.3333 25.5 29.0417 25.2083 29.625 24.625C30.2083 24.0417 30.5 23.3333 30.5 22.5C30.5 21.6667 30.2083 20.9583 29.625 20.375C29.0417 19.7917 28.3333 19.5 27.5 19.5H23.5V17.5H27.5C28.8833 17.5 30.0625 17.9875 31.0375 18.9625C32.0125 19.9375 32.5 21.1167 32.5 22.5C32.5 23.8833 32.0125 25.0625 31.0375 26.0375C30.0625 27.0125 28.8833 27.5 27.5 27.5H23.5Z\" fill=\"#5F9EA0\"/>\n</svg>\n`;\nexport const files = `\n<svg width=\"49\" height=\"45\" viewBox=\"0 0 49 45\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<rect x=\"1\" y=\"1\" width=\"47\" height=\"43\" rx=\"14.5\" fill=\"white\"/>\n<rect x=\"1\" y=\"1\" width=\"47\" height=\"43\" rx=\"14.5\" stroke=\"#5F9EA0\"/>\n<mask id=\"mask0_2730_61115\" style=\"mask-type:alpha\" maskUnits=\"userSpaceOnUse\" x=\"12\" y=\"10\" width=\"25\" height=\"25\">\n<rect x=\"12.5\" y=\"10.5\" width=\"24\" height=\"24\" fill=\"#D9D9D9\"/>\n</mask>\n<g mask=\"url(#mask0_2730_61115)\">\n<path d=\"M15.5 31.5C14.95 31.5 14.4792 31.3042 14.0875 30.9125C13.6958 30.5208 13.5 30.05 13.5 29.5V16.5H15.5V29.5H32.5V31.5H15.5ZM19.5 27.5C18.95 27.5 18.4792 27.3042 18.0875 26.9125C17.6958 26.5208 17.5 26.05 17.5 25.5V14.5C17.5 13.95 17.6958 13.4792 18.0875 13.0875C18.4792 12.6958 18.95 12.5 19.5 12.5H24.5L26.5 14.5H33.5C34.05 14.5 34.5208 14.6958 34.9125 15.0875C35.3042 15.4792 35.5 15.95 35.5 16.5V25.5C35.5 26.05 35.3042 26.5208 34.9125 26.9125C34.5208 27.3042 34.05 27.5 33.5 27.5H19.5ZM19.5 25.5H33.5V16.5H25.675L23.675 14.5H19.5V25.5Z\" fill=\"#5F9EA0\"/>\n</g>\n</svg>\n`;\nexport const uploadicon = `\n<svg width=\"41\" height=\"45\" viewBox=\"0 0 41 45\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<rect x=\"1\" y=\"1\" width=\"39\" height=\"43\" rx=\"14.5\" fill=\"white\"/>\n<rect x=\"1\" y=\"1\" width=\"39\" height=\"43\" rx=\"14.5\" stroke=\"#5F9EA0\"/>\n<path d=\"M19.5 30.5V22.35L16.9 24.95L15.5 23.5L20.5 18.5L25.5 23.5L24.1 24.95L21.5 22.35V30.5H19.5ZM12.5 19.5V16.5C12.5 15.95 12.6958 15.4792 13.0875 15.0875C13.4792 14.6958 13.95 14.5 14.5 14.5H26.5C27.05 14.5 27.5208 14.6958 27.9125 15.0875C28.3042 15.4792 28.5 15.95 28.5 16.5V19.5H26.5V16.5H14.5V19.5H12.5Z\" fill=\"#5F9EA0\"/>\n</svg>\n`;\nexport const replaceimageicon = `\n<svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path d=\"M7 16V7.85L4.4 10.45L3 9L8 4L13 9L11.6 10.45L9 7.85V16H7ZM0 5V2C0 1.45 0.195833 0.979167 0.5875 0.5875C0.979167 0.195833 1.45 0 2 0H14C14.55 0 15.0208 0.195833 15.4125 0.5875C15.8042 0.979167 16 1.45 16 2V5H14V2H2V5H0Z\" fill=\"#1C1B1F\"/>\n</svg>\n`;\nexport const galleryicon = `\n<svg width=\"23\" height=\"14\" viewBox=\"0 0 23 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path d=\"M2.5 14C1.95 14 1.47917 13.8042 1.0875 13.4125C0.695833 13.0208 0.5 12.55 0.5 12V2C0.5 1.45 0.695833 0.979167 1.0875 0.5875C1.47917 0.195833 1.95 0 2.5 0H12.5C13.05 0 13.5208 0.195833 13.9125 0.5875C14.3042 0.979167 14.5 1.45 14.5 2V12C14.5 12.55 14.3042 13.0208 13.9125 13.4125C13.5208 13.8042 13.05 14 12.5 14H2.5ZM17.5 6C17.2167 6 16.9792 5.90417 16.7875 5.7125C16.5958 5.52083 16.5 5.28333 16.5 5V1C16.5 0.716667 16.5958 0.479167 16.7875 0.2875C16.9792 0.0958333 17.2167 0 17.5 0H21.5C21.7833 0 22.0208 0.0958333 22.2125 0.2875C22.4042 0.479167 22.5 0.716667 22.5 1V5C22.5 5.28333 22.4042 5.52083 22.2125 5.7125C22.0208 5.90417 21.7833 6 21.5 6H17.5ZM18.5 4H20.5V2H18.5V4ZM2.5 12H12.5V2H2.5V12ZM3.5 10H11.5L8.875 6.5L7 9L5.625 7.175L3.5 10ZM17.5 14C17.2167 14 16.9792 13.9042 16.7875 13.7125C16.5958 13.5208 16.5 13.2833 16.5 13V9C16.5 8.71667 16.5958 8.47917 16.7875 8.2875C16.9792 8.09583 17.2167 8 17.5 8H21.5C21.7833 8 22.0208 8.09583 22.2125 8.2875C22.4042 8.47917 22.5 8.71667 22.5 9V13C22.5 13.2833 22.4042 13.5208 22.2125 13.7125C22.0208 13.9042 21.7833 14 21.5 14H17.5ZM18.5 12H20.5V10H18.5V12Z\" fill=\"#1C1B1F\"/>\n</svg>\n`;\nexport { quickadopt, Dashboard, Audience, Tours, Announcements, Banners, Tooltips, Surveys, Auditlog, Checklist, Filemanagement, Helpcenter, Accounts, Hotspots, Installation, Journeys, Notification, Settings, Themes, User, Feedback, settings, Logout, avatar, erroricon, Unblockaccount, Keyvertical, Lockopen, Mail, Delete, logo, cloudoff, QuickAdopttext, Fileedit, clone, webbanner, Homepageimg, Wavinghand, global, profile, ArrowDown, Cursor, copy, DeleteRed, Replace, PreviewImage, feedbackimg, Refresh, key, openaikey, Trash\n// Edit,\n};", "map": {"version": 3, "names": ["quickadopt", "Dashboard", "Auditlog", "Checklist", "Filemanagement", "Helpcenter", "Hotspots", "Installation", "Journeys", "Notification", "Settings", "Themes", "User", "Logout", "<PERSON><PERSON><PERSON>", "Accounts", "Audience", "Tours", "Announcements", "Banners", "Tooltips", "Surveys", "settings", "erroricon", "avatar", "Unblockaccount", "Keyvertical", "Mail", "Lockopen", "Delete", "logo", "cloudoff", "QuickAdopttext", "Fileedit", "clone", "webbanner", "Homepageimg", "Wavinghand", "global", "profile", "ArrowDown", "<PERSON><PERSON><PERSON>", "copy", "DeleteRed", "Trash", "Replace", "PreviewImage", "feedbackimg", "Refresh", "key", "openaikey", "uploadfile", "hyperlink", "files", "uploadicon", "replaceimageicon", "galleryicon"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/assets/icons/icons.tsx"], "sourcesContent": ["import quickadopt from \"./quickadopt.svg\";\r\nimport Dashboard from \"./dashboard.svg\";\r\nimport Auditlog from \"./auditlog.svg\";\r\nimport Checklist from \"./checklists.svg\";\r\nimport Filemanagement from \"./filemanagement.svg\";\r\nimport Helpcenter from \"./helpcenter.svg\";\r\nimport Hotspots from \"./hotspots.svg\";\r\nimport Installation from \"./installation.svg\";\r\nimport Journeys from \"./journeys.svg\";\r\nimport Notification from \"./notification.svg\";\r\nimport Settings from \"./settings.svg\";\r\nimport Themes from \"./themes.svg\";\r\nimport User from \"./user.svg\";\r\nimport Logout from \"./logout.svg\";\r\nimport Feedback from \"./feedback.svg\";\r\nimport Accounts from \"@mui/icons-material/GroupOutlined\";\r\nimport Audience from \"./Audience.svg\";\r\nimport Tours from \"./Tours.svg\";\r\nimport Announcements from \"./Announcements.svg\";\r\nimport Banners from \"./Banners.svg\";\r\nimport Tooltips from \"./Tooltip.svg\";\r\nimport Surveys from \"./Surveys.svg\";\r\nimport settings from \"./profilesettings.svg\";\r\nimport erroricon from \"./error.svg\";\r\nimport avatar from \"./avatar.svg\";\r\nimport Unblockaccount from \"./UnblockAccount.svg\";\r\nimport Keyvertical from \"./key_vertical.svg\";\r\nimport Mail from \"./Mail.svg\";\r\nimport Lockopen from \"./Lockopen.svg\";\r\n// import Delete from \"./delete.svg\";\r\n// import Edit from \"./editicon.svg\";\r\nimport Delete from \"./delete.svg\";\r\nimport logo from \"./logo.svg\";\r\nimport cloudoff from \"./cloudoff.svg\";\r\nimport QuickAdopttext from \"./QuickAdopttext.svg\";\r\nimport Fileedit from \"./Fileedit.svg\";\r\nimport clone from \"./clone.svg\";\r\nimport webbanner from \"./webbanner.svg\";\r\nimport Homepageimg from \"./Homepageimg.svg\";\r\nimport Wavinghand from \"./wavinghand.svg\";\r\nimport global from \"./global.svg\";\r\nimport profile from \"./profile.svg\";\r\nimport ArrowDown from \"./ArrowDown.svg\";\r\nimport Cursor from \"./Cursor.svg\";\r\nimport copy from \"./copy.svg\";\r\nimport DeleteRed from \"./DeleteRed.svg\";\r\nimport Trash from \"./trash.svg\";\r\nimport Replace from \"./Replace.svg\";\r\nimport PreviewImage from \"./PreviewImage.svg\";\r\nimport feedbackimg from \"./feedbackimg.svg\";\r\nimport Refresh from \"./Refresh.svg\";\r\nimport key from \"./key.svg\";\r\nimport openaikey from \"./openaikey.svg\"\r\n\r\n// SVG icons for image upload section\r\nexport const uploadfile = `\r\n<svg width=\"22\" height=\"17\" viewBox=\"0 0 22 17\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n<path d=\"M5.5 16.5C3.98333 16.5 2.6875 15.975 1.6125 14.925C0.5375 13.875 0 12.5917 0 11.075C0 9.775 0.391667 8.61667 1.175 7.6C1.95833 6.58333 2.98333 5.93333 4.25 5.65C4.66667 4.11667 5.5 2.875 6.75 1.925C8 0.975 9.41667 0.5 11 0.5C12.95 0.5 14.6042 1.17917 15.9625 2.5375C17.3208 3.89583 18 5.55 18 7.5C19.15 7.63333 20.1042 8.12917 20.8625 8.9875C21.6208 9.84583 22 10.85 22 12C22 13.25 21.5625 14.3125 20.6875 15.1875C19.8125 16.0625 18.75 16.5 17.5 16.5H12C11.45 16.5 10.9792 16.3042 10.5875 15.9125C10.1958 15.5208 10 15.05 10 14.5V9.35L8.4 10.9L7 9.5L11 5.5L15 9.5L13.6 10.9L12 9.35V14.5H17.5C18.2 14.5 18.7917 14.2583 19.275 13.775C19.7583 13.2917 20 12.7 20 12C20 11.3 19.7583 10.7083 19.275 10.225C18.7917 9.74167 18.2 9.5 17.5 9.5H16V7.5C16 6.11667 15.5125 4.9375 14.5375 3.9625C13.5625 2.9875 12.3833 2.5 11 2.5C9.61667 2.5 8.4375 2.9875 7.4625 3.9625C6.4875 4.9375 6 6.11667 6 7.5H5.5C4.53333 7.5 3.70833 7.84167 3.025 8.525C2.34167 9.20833 2 10.0333 2 11C2 11.9667 2.34167 12.7917 3.025 13.475C3.70833 14.1583 4.53333 14.5 5.5 14.5H8V16.5H5.5Z\" fill=\"#1C1B1F\"/>\r\n</svg>\r\n`;\r\n\r\nexport const hyperlink =`\r\n<svg width=\"45\" height=\"45\" viewBox=\"0 0 45 45\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n<rect x=\"1\" y=\"1\" width=\"43\" height=\"43\" rx=\"14.5\" fill=\"white\"/>\r\n<rect x=\"1\" y=\"1\" width=\"43\" height=\"43\" rx=\"14.5\" stroke=\"#5F9EA0\"/>\r\n<path d=\"M21.5 27.5H17.5C16.1167 27.5 14.9375 27.0125 13.9625 26.0375C12.9875 25.0625 12.5 23.8833 12.5 22.5C12.5 21.1167 12.9875 19.9375 13.9625 18.9625C14.9375 17.9875 16.1167 17.5 17.5 17.5H21.5V19.5H17.5C16.6667 19.5 15.9583 19.7917 15.375 20.375C14.7917 20.9583 14.5 21.6667 14.5 22.5C14.5 23.3333 14.7917 24.0417 15.375 24.625C15.9583 25.2083 16.6667 25.5 17.5 25.5H21.5V27.5ZM18.5 23.5V21.5H26.5V23.5H18.5ZM23.5 27.5V25.5H27.5C28.3333 25.5 29.0417 25.2083 29.625 24.625C30.2083 24.0417 30.5 23.3333 30.5 22.5C30.5 21.6667 30.2083 20.9583 29.625 20.375C29.0417 19.7917 28.3333 19.5 27.5 19.5H23.5V17.5H27.5C28.8833 17.5 30.0625 17.9875 31.0375 18.9625C32.0125 19.9375 32.5 21.1167 32.5 22.5C32.5 23.8833 32.0125 25.0625 31.0375 26.0375C30.0625 27.0125 28.8833 27.5 27.5 27.5H23.5Z\" fill=\"#5F9EA0\"/>\r\n</svg>\r\n`;\r\n\r\nexport const files = `\r\n<svg width=\"49\" height=\"45\" viewBox=\"0 0 49 45\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n<rect x=\"1\" y=\"1\" width=\"47\" height=\"43\" rx=\"14.5\" fill=\"white\"/>\r\n<rect x=\"1\" y=\"1\" width=\"47\" height=\"43\" rx=\"14.5\" stroke=\"#5F9EA0\"/>\r\n<mask id=\"mask0_2730_61115\" style=\"mask-type:alpha\" maskUnits=\"userSpaceOnUse\" x=\"12\" y=\"10\" width=\"25\" height=\"25\">\r\n<rect x=\"12.5\" y=\"10.5\" width=\"24\" height=\"24\" fill=\"#D9D9D9\"/>\r\n</mask>\r\n<g mask=\"url(#mask0_2730_61115)\">\r\n<path d=\"M15.5 31.5C14.95 31.5 14.4792 31.3042 14.0875 30.9125C13.6958 30.5208 13.5 30.05 13.5 29.5V16.5H15.5V29.5H32.5V31.5H15.5ZM19.5 27.5C18.95 27.5 18.4792 27.3042 18.0875 26.9125C17.6958 26.5208 17.5 26.05 17.5 25.5V14.5C17.5 13.95 17.6958 13.4792 18.0875 13.0875C18.4792 12.6958 18.95 12.5 19.5 12.5H24.5L26.5 14.5H33.5C34.05 14.5 34.5208 14.6958 34.9125 15.0875C35.3042 15.4792 35.5 15.95 35.5 16.5V25.5C35.5 26.05 35.3042 26.5208 34.9125 26.9125C34.5208 27.3042 34.05 27.5 33.5 27.5H19.5ZM19.5 25.5H33.5V16.5H25.675L23.675 14.5H19.5V25.5Z\" fill=\"#5F9EA0\"/>\r\n</g>\r\n</svg>\r\n`;\r\n\r\nexport const uploadicon = `\r\n<svg width=\"41\" height=\"45\" viewBox=\"0 0 41 45\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n<rect x=\"1\" y=\"1\" width=\"39\" height=\"43\" rx=\"14.5\" fill=\"white\"/>\r\n<rect x=\"1\" y=\"1\" width=\"39\" height=\"43\" rx=\"14.5\" stroke=\"#5F9EA0\"/>\r\n<path d=\"M19.5 30.5V22.35L16.9 24.95L15.5 23.5L20.5 18.5L25.5 23.5L24.1 24.95L21.5 22.35V30.5H19.5ZM12.5 19.5V16.5C12.5 15.95 12.6958 15.4792 13.0875 15.0875C13.4792 14.6958 13.95 14.5 14.5 14.5H26.5C27.05 14.5 27.5208 14.6958 27.9125 15.0875C28.3042 15.4792 28.5 15.95 28.5 16.5V19.5H26.5V16.5H14.5V19.5H12.5Z\" fill=\"#5F9EA0\"/>\r\n</svg>\r\n`;\r\n\r\nexport const replaceimageicon = `\r\n<svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n<path d=\"M7 16V7.85L4.4 10.45L3 9L8 4L13 9L11.6 10.45L9 7.85V16H7ZM0 5V2C0 1.45 0.195833 0.979167 0.5875 0.5875C0.979167 0.195833 1.45 0 2 0H14C14.55 0 15.0208 0.195833 15.4125 0.5875C15.8042 0.979167 16 1.45 16 2V5H14V2H2V5H0Z\" fill=\"#1C1B1F\"/>\r\n</svg>\r\n`;\r\n\r\nexport const galleryicon = `\r\n<svg width=\"23\" height=\"14\" viewBox=\"0 0 23 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n<path d=\"M2.5 14C1.95 14 1.47917 13.8042 1.0875 13.4125C0.695833 13.0208 0.5 12.55 0.5 12V2C0.5 1.45 0.695833 0.979167 1.0875 0.5875C1.47917 0.195833 1.95 0 2.5 0H12.5C13.05 0 13.5208 0.195833 13.9125 0.5875C14.3042 0.979167 14.5 1.45 14.5 2V12C14.5 12.55 14.3042 13.0208 13.9125 13.4125C13.5208 13.8042 13.05 14 12.5 14H2.5ZM17.5 6C17.2167 6 16.9792 5.90417 16.7875 5.7125C16.5958 5.52083 16.5 5.28333 16.5 5V1C16.5 0.716667 16.5958 0.479167 16.7875 0.2875C16.9792 0.0958333 17.2167 0 17.5 0H21.5C21.7833 0 22.0208 0.0958333 22.2125 0.2875C22.4042 0.479167 22.5 0.716667 22.5 1V5C22.5 5.28333 22.4042 5.52083 22.2125 5.7125C22.0208 5.90417 21.7833 6 21.5 6H17.5ZM18.5 4H20.5V2H18.5V4ZM2.5 12H12.5V2H2.5V12ZM3.5 10H11.5L8.875 6.5L7 9L5.625 7.175L3.5 10ZM17.5 14C17.2167 14 16.9792 13.9042 16.7875 13.7125C16.5958 13.5208 16.5 13.2833 16.5 13V9C16.5 8.71667 16.5958 8.47917 16.7875 8.2875C16.9792 8.09583 17.2167 8 17.5 8H21.5C21.7833 8 22.0208 8.09583 22.2125 8.2875C22.4042 8.47917 22.5 8.71667 22.5 9V13C22.5 13.2833 22.4042 13.5208 22.2125 13.7125C22.0208 13.9042 21.7833 14 21.5 14H17.5ZM18.5 12H20.5V10H18.5V12Z\" fill=\"#1C1B1F\"/>\r\n</svg>\r\n`;\r\n\r\nexport {\r\n\tquickadopt,\r\n\tDashboard,\r\n\tAudience,\r\n\tTours,\r\n\tAnnouncements,\r\n\tBanners,\r\n\tTooltips,\r\n\tSurveys,\r\n\tAuditlog,\r\n\tChecklist,\r\n\tFilemanagement,\r\n\tHelpcenter,\r\n\tAccounts,\r\n\tHotspots,\r\n\tInstallation,\r\n\tJourneys,\r\n\tNotification,\r\n\tSettings,\r\n\tThemes,\r\n\tUser,\r\n\tFeedback,\r\n\tsettings,\r\n\tLogout,\r\n\tavatar,\r\n\terroricon,\r\n\tUnblockaccount,\r\n\tKeyvertical,\r\n\tLockopen,\r\n\tMail,\r\n\tDelete,\r\n\tlogo,\r\n\tcloudoff,\r\n\tQuickAdopttext,\r\n\tFileedit,\r\n\tclone,\r\n\twebbanner,\r\n\tHomepageimg,\r\n\tWavinghand,\r\n\tglobal,\r\n\tprofile,\r\n\tArrowDown,\r\n\tCursor,\r\n\tcopy,\r\n\tDeleteRed,\r\n\tReplace,\r\n\tPreviewImage,\r\n\tfeedbackimg,\r\n\tRefresh,\r\n\tkey,\r\n\topenaikey,\r\n\tTrash\r\n\t// Edit,\r\n};\r\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,SAAS,MAAM,kBAAkB;AACxC,OAAOC,cAAc,MAAM,sBAAsB;AACjD,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,QAAQ,MAAM,mCAAmC;AACxD,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,cAAc,MAAM,sBAAsB;AACjD,OAAOC,WAAW,MAAM,oBAAoB;AAC5C,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,QAAQ,MAAM,gBAAgB;AACrC;AACA;AACA,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,cAAc,MAAM,sBAAsB;AACjD,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,SAAS,MAAM,iBAAiB;;AAEvC;AACA,OAAO,MAAMC,UAAU,GAAG;AAC1B;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMC,SAAS,GAAE;AACxB;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMC,KAAK,GAAG;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMC,UAAU,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMC,WAAW,GAAG;AAC3B;AACA;AACA;AACA,CAAC;AAED,SACCxD,UAAU,EACVC,SAAS,EACTe,QAAQ,EACRC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPnB,QAAQ,EACRC,SAAS,EACTC,cAAc,EACdC,UAAU,EACVU,QAAQ,EACRT,QAAQ,EACRC,YAAY,EACZC,QAAQ,EACRC,YAAY,EACZC,QAAQ,EACRC,MAAM,EACNC,IAAI,EACJE,QAAQ,EACRQ,QAAQ,EACRT,MAAM,EACNW,MAAM,EACND,SAAS,EACTE,cAAc,EACdC,WAAW,EACXE,QAAQ,EACRD,IAAI,EACJE,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,QAAQ,EACRC,KAAK,EACLC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,OAAO,EACPC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,SAAS,EACTE,OAAO,EACPC,YAAY,EACZC,WAAW,EACXC,OAAO,EACPC,GAAG,EACHC,SAAS,EACTN;AACA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}