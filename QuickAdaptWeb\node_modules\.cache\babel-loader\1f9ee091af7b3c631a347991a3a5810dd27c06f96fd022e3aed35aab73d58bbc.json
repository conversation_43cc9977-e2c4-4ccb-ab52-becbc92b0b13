{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptWeb\\\\src\\\\components\\\\organization\\\\OrganizationcustomcolumnMenuItem.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, createElement as _createElement } from \"react\";\nimport TextField from \"@mui/material/TextField\";\nimport Checkbox from \"@mui/material/Checkbox\";\nimport { MenuItem, ListItemText, Button, Box, Paper } from \"@mui/material\";\nimport FilterAltIcon from \"@mui/icons-material/FilterAlt\";\nimport Autocomplete from \"@mui/material/Autocomplete\";\nimport { getOrganizations, getAllOrganizations, getOrganizationsData } from \"../../services/OrganizationService\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst OrganizationCustomColumnMenuItem = ({\n  column,\n  skip,\n  top,\n  OrganizationId,\n  setTotalcount,\n  orderByFields,\n  setModels,\n  sortModel,\n  setLoading,\n  filters,\n  setFilters,\n  options,\n  onSearch,\n  open,\n  hideMenu,\n  models,\n  colDef,\n  modelsData,\n  optionsModel,\n  paginationModel,\n  ...other\n}) => {\n  _s();\n  const [searchText, setSearchText] = useState(\"\");\n  const [selectedOptions, setSelectedOptions] = useState([]);\n  const [filteredOptions, setFilteredOptions] = useState(options);\n\n  // Maintain a list of all available names\n  const [allNames, setAllNames] = useState([]);\n  // State for all organization names (for Name column search)\n  const [allOrganizationNames, setAllOrganizationNames] = useState([]);\n  const [allNamesLoaded, setAllNamesLoaded] = useState(false);\n\n  // State for all organization types (for Type column search)\n  const [allOrganizationTypes, setAllOrganizationTypes] = useState([]);\n  const [allTypesLoaded, setAllTypesLoaded] = useState(false);\n  useEffect(() => {\n    const uniqueOptions = Array.from(new Set(options));\n    setFilteredOptions(uniqueOptions);\n\n    // For Name column, check if there's an existing filter\n    if (column === \"Name\") {\n      const existingFilter = filters.find(filter => filter.FieldName === column);\n      if (existingFilter) {\n        // If filter exists, show only the currently selected values as selected\n        const currentlySelected = existingFilter.Value.split(',').filter(val => val.trim());\n        setSelectedOptions(currentlySelected);\n        // But keep all available names in filteredOptions for display\n        if (allOrganizationNames.length > 0) {\n          setFilteredOptions(allOrganizationNames);\n        } else {\n          const currentPageNames = Array.from(new Set(models.map(model => model.Name)));\n          setFilteredOptions(currentPageNames);\n        }\n      } else {\n        // If no filter, select all current page names by default\n        const currentPageNames = Array.from(new Set(models.map(model => model.Name)));\n        setSelectedOptions(currentPageNames);\n        setFilteredOptions(currentPageNames);\n      }\n    }\n  }, [options, models, column, filters]);\n  useEffect(() => {\n    if (column === \"Type\") {\n      // Check if there's an existing filter for this column\n      const existingFilter = filters.find(filter => filter.FieldName === column);\n      if (existingFilter) {\n        // If filter exists, show only the currently selected values as selected\n        const currentlySelected = existingFilter.Value.split(',').filter(val => val.trim());\n        setSelectedOptions(currentlySelected);\n        // But keep all available types in filteredOptions for display\n        if (Array.isArray(optionsModel) && optionsModel.length > 0) {\n          const typeOptions = Array.from(new Set(optionsModel.map(model => model.Type)));\n          setFilteredOptions(typeOptions);\n        }\n      } else {\n        // If no filter, show all available types from optionsModel\n        if (Array.isArray(optionsModel) && optionsModel.length > 0) {\n          const typeOptions = Array.from(new Set(optionsModel.map(model => model.Type)));\n          setSelectedOptions(typeOptions);\n          setFilteredOptions(typeOptions);\n        }\n      }\n    }\n  }, [column, models, optionsModel, filters]);\n  const handleOptionChange = (event, value) => {\n    setSelectedOptions(value);\n  };\n  const handleCancelClick = event => {\n    hideMenu(event);\n  };\n  const handleClearFiltersClick = event => {\n    setSearchText(\"\");\n    setFilters([]);\n    getAllOrganizations(setModels, setLoading);\n    hideMenu(event);\n  };\n  const handleApplyClick = async event => {\n    const searchValue = selectedOptions.length ? selectedOptions : [searchText];\n    // If all org names/types are selected, treat as no filter\n    if (column === \"Name\" && allOrganizationNames.length > 0 && selectedOptions.length === allOrganizationNames.length) {\n      setFilters([]);\n      await getAllOrganizations(setModels, setLoading);\n      setIsDropdownOpen(false);\n      hideMenu(event);\n      return;\n    } else if (column === \"Type\" && allOrganizationTypes.length > 0 && selectedOptions.length === allOrganizationTypes.length) {\n      setFilters([]);\n      await getAllOrganizations(setModels, setLoading);\n      setIsDropdownOpen(false);\n      hideMenu(event);\n      return;\n    }\n    const combinedValue = searchValue.join(\",\");\n    const newFilter = {\n      FieldName: column,\n      ElementType: \"string\",\n      Condition: \"in\",\n      Value: combinedValue,\n      IsCustomField: false\n    };\n    const updatedFilters = [...filters];\n    const filterIndex = updatedFilters.findIndex(filter => filter.FieldName === column);\n    if (filterIndex !== -1) {\n      updatedFilters[filterIndex] = newFilter;\n    } else {\n      updatedFilters.push(newFilter);\n    }\n    setFilters(updatedFilters);\n    await getOrganizations(setModels, setLoading, 0, paginationModel.pageSize, setTotalcount, sortModel, updatedFilters);\n    setSelectedOptions(searchValue);\n    setIsDropdownOpen(true); // Do not close dropdown\n    hideMenu(event);\n  };\n  const handleSelectAll = () => {\n    let allToSelect = filteredOptions;\n    if (column === \"Name\" && allOrganizationNames.length > 0) {\n      allToSelect = allOrganizationNames;\n    } else if (column === \"Type\" && allOrganizationTypes.length > 0) {\n      allToSelect = allOrganizationTypes;\n    }\n    // Use all available options if loaded, otherwise fallback to filteredOptions\n    const newSelectedOptions = selectedOptions.length === allToSelect.length ? [] : allToSelect;\n    setSelectedOptions(newSelectedOptions);\n  };\n  const isClearFiltersDisabled = !searchText && selectedOptions.length === options.length;\n  const isSearchButtonDisabled = selectedOptions.length === 0 && !searchText;\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false);\n  // Fetch all organization names/types when menu is opened\n  const handleDropdownOpen = async () => {\n    setIsDropdownOpen(true);\n\n    // Check if there's an existing filter for the current column\n    const existingFilter = filters.find(filter => filter.FieldName === column);\n    if (column === \"Name\" && !allNamesLoaded) {\n      try {\n        const allOrgs = await getOrganizationsData(setModels, setLoading);\n        const allNamesList = Array.from(new Set((allOrgs || []).map(org => org.Name)));\n        setAllOrganizationNames(allNamesList);\n        setAllNamesLoaded(true);\n        setFilteredOptions(allNamesList);\n        if (!existingFilter) {\n          // Only set all selected if no filter is applied\n          setSelectedOptions(allNamesList);\n        }\n      } catch (error) {\n        // Optionally handle error\n      }\n    } else if (column === \"Name\" && allOrganizationNames.length > 0) {\n      setFilteredOptions(allOrganizationNames);\n      if (!existingFilter) {\n        // Only set all selected if no filter is applied\n        setSelectedOptions(allOrganizationNames);\n      }\n    } else if (column === \"Type\" && !allTypesLoaded) {\n      try {\n        const allOrgs = await getOrganizationsData(setModels, setLoading);\n        const allTypesList = Array.from(new Set((allOrgs || []).map(org => org.Type)));\n        setAllOrganizationTypes(allTypesList);\n        setAllTypesLoaded(true);\n        setFilteredOptions(allTypesList);\n        if (!existingFilter) {\n          // Only set all selected if no filter is applied\n          setSelectedOptions(allTypesList);\n        }\n      } catch (error) {\n        // Optionally handle error\n      }\n    } else if (column === \"Type\" && allOrganizationTypes.length > 0) {\n      setFilteredOptions(allOrganizationTypes);\n      if (!existingFilter) {\n        // Only set all selected if no filter is applied\n        setSelectedOptions(allOrganizationTypes);\n      }\n    }\n  };\n\n  // Compute options for Autocomplete: always show all available options\n  const getAutocompleteOptions = () => {\n    if (column === \"Name\") {\n      return allOrganizationNames.length > 0 ? allOrganizationNames : filteredOptions;\n    } else if (column === \"Type\") {\n      return allOrganizationTypes.length > 0 ? allOrganizationTypes : filteredOptions;\n    }\n    return filteredOptions;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"qadpt-org-filter\",\n    style: {\n      position: \"relative\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Autocomplete, {\n      multiple: true,\n      disableCloseOnSelect: true,\n      options: getAutocompleteOptions(),\n      value: selectedOptions,\n      open: isDropdownOpen,\n      onOpen: handleDropdownOpen,\n      onClose: () => setIsDropdownOpen(true),\n      onChange: handleOptionChange,\n      onInputChange: async (event, value) => {\n        setSearchText(value);\n        if (column === \"Name\" && value && !allNamesLoaded) {\n          try {\n            const allOrgs = await getOrganizationsData(setModels, setLoading);\n            const allNamesList = Array.from(new Set((allOrgs || []).map(org => org.Name)));\n            setAllOrganizationNames(allNamesList);\n            setAllNamesLoaded(true);\n          } catch (error) {\n            // Optionally handle error\n          }\n        } else if (column === \"Type\" && value && !allTypesLoaded) {\n          try {\n            const allOrgs = await getOrganizationsData(setModels, setLoading);\n            const allTypesList = Array.from(new Set((allOrgs || []).map(org => org.Type)));\n            setAllOrganizationTypes(allTypesList);\n            setAllTypesLoaded(true);\n          } catch (error) {\n            // Optionally handle error\n          }\n        }\n      }\n      // onOpen={() => setIsDropdownOpen(true)}\n      //onClose={() => setIsDropdownOpen(false)}\n      ,\n      renderOption: (props, option, {\n        selected\n      }) => /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [option === filteredOptions[0] && /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: handleSelectAll,\n          children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n            checked: (() => {\n              if (column === \"Name\" && allOrganizationNames.length > 0) {\n                return selectedOptions.length === allOrganizationNames.length;\n              } else if (column === \"Type\" && allOrganizationTypes.length > 0) {\n                return selectedOptions.length === allOrganizationTypes.length;\n              }\n              return selectedOptions.length === filteredOptions.length;\n            })(),\n            indeterminate: (() => {\n              const totalOptions = (() => {\n                if (column === \"Name\" && allOrganizationNames.length > 0) {\n                  return allOrganizationNames.length;\n                } else if (column === \"Type\" && allOrganizationTypes.length > 0) {\n                  return allOrganizationTypes.length;\n                }\n                return filteredOptions.length;\n              })();\n              return selectedOptions.length > 0 && selectedOptions.length < totalOptions;\n            })()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            primary: \"Select All\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 17\n          }, this)]\n        }, \"select-all\", true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_createElement(MenuItem, {\n          ...props,\n          key: option,\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }\n        }, /*#__PURE__*/_jsxDEV(Checkbox, {\n          checked: selectedOptions.includes(option)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n          primary: option\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 15\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 11\n      }, this),\n      renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n        ...params,\n        variant: \"outlined\",\n        label: \"Search\",\n        placeholder: \"Select Options\",\n        onKeyDown: event => {\n          if ([\"ArrowUp\", \"ArrowDown\", \"Enter\"].includes(event.key)) {\n            event.stopPropagation();\n          } else {\n            event.stopPropagation();\n          }\n        },\n        InputProps: {\n          ...params.InputProps,\n          endAdornment: /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: params.InputProps.endAdornment\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 17\n          }, this)\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 11\n      }, this),\n      PaperComponent: props => /*#__PURE__*/_jsxDEV(Paper, {\n        ...props,\n        style: {\n          position: \"absolute\",\n          top: \"100%\",\n          left: 0,\n          width: \"100%\",\n          zIndex: 10,\n          marginTop: \"4px\" // Small gap between input and dropdown\n        },\n        onWheel: event => event.stopPropagation()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 11\n      }, this),\n      ListboxProps: {\n        style: {\n          maxHeight: \"220px\",\n          overflowY: \"auto\"\n        }\n      },\n      renderTags: () => null\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"flex-end\",\n      mt: 2,\n      className: \"qadpt-btn\",\n      sx: {\n        display: \"flex\",\n        justifyContent: \"flex-end\",\n        padding: \"0 10px\",\n        marginTop: isDropdownOpen ? \"250px\" : \"10px\",\n        // Dynamically change marginTop\n        transition: \"margin-top 0.3s ease-in-out\" // Smooth transition\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        color: \"primary\",\n        onClick: handleApplyClick,\n        disabled: isSearchButtonDisabled,\n        children: \"OK\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        color: \"secondary\",\n        onClick: handleCancelClick,\n        style: {\n          marginLeft: \"8px\"\n        },\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleClearFiltersClick,\n        startIcon: /*#__PURE__*/_jsxDEV(FilterAltIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 62\n        }, this),\n        disabled: isClearFiltersDisabled,\n        style: {\n          marginLeft: \"8px\"\n        },\n        children: \"Clear Filters\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 258,\n    columnNumber: 5\n  }, this);\n};\n_s(OrganizationCustomColumnMenuItem, \"eW4ZFl/Q2Y4nA6lZhc9hBzX/ClE=\");\n_c = OrganizationCustomColumnMenuItem;\nexport default OrganizationCustomColumnMenuItem;\nvar _c;\n$RefreshReg$(_c, \"OrganizationCustomColumnMenuItem\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "createElement", "_createElement", "TextField", "Checkbox", "MenuItem", "ListItemText", "<PERSON><PERSON>", "Box", "Paper", "FilterAltIcon", "Autocomplete", "getOrganizations", "getAllOrganizations", "getOrganizationsData", "jsxDEV", "_jsxDEV", "OrganizationCustomColumnMenuItem", "column", "skip", "top", "OrganizationId", "setTotalcount", "order<PERSON><PERSON><PERSON><PERSON>s", "setModels", "sortModel", "setLoading", "filters", "setFilters", "options", "onSearch", "open", "hideMenu", "models", "colDef", "modelsData", "optionsModel", "paginationModel", "other", "_s", "searchText", "setSearchText", "selectedOptions", "setSelectedOptions", "filteredOptions", "setFilteredOptions", "allNames", "setAllNames", "allOrganizationNames", "setAllOrganizationNames", "allNamesLoaded", "setAllNamesLoaded", "allOrganizationTypes", "setAllOrganizationTypes", "allTypesLoaded", "setAllTypesLoaded", "uniqueOptions", "Array", "from", "Set", "existingFilter", "find", "filter", "FieldName", "currentlySelected", "Value", "split", "val", "trim", "length", "currentPageNames", "map", "model", "Name", "isArray", "typeOptions", "Type", "handleOptionChange", "event", "value", "handleCancelClick", "handleClearFiltersClick", "handleApplyClick", "searchValue", "setIsDropdownOpen", "combinedValue", "join", "newFilter", "ElementType", "Condition", "IsCustomField", "updatedFilters", "filterIndex", "findIndex", "push", "pageSize", "handleSelectAll", "allToSelect", "newSelectedOptions", "isClearFiltersDisabled", "isSearchButtonDisabled", "isDropdownOpen", "handleDropdownOpen", "allOrgs", "allNamesList", "org", "error", "allTypesList", "getAutocompleteOptions", "className", "style", "position", "children", "multiple", "disableCloseOnSelect", "onOpen", "onClose", "onChange", "onInputChange", "renderOption", "props", "option", "selected", "onClick", "checked", "indeterminate", "totalOptions", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "primary", "key", "__self", "__source", "includes", "renderInput", "params", "variant", "label", "placeholder", "onKeyDown", "stopPropagation", "InputProps", "endAdornment", "Fragment", "PaperComponent", "left", "width", "zIndex", "marginTop", "onWheel", "ListboxProps", "maxHeight", "overflowY", "renderTags", "display", "justifyContent", "mt", "sx", "padding", "transition", "color", "disabled", "marginLeft", "startIcon", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/components/organization/OrganizationcustomcolumnMenuItem.tsx"], "sourcesContent": ["\r\nimport React, { useState, useEffect } from \"react\";\r\nimport TextField from \"@mui/material/TextField\";\r\nimport Checkbox from \"@mui/material/Checkbox\";\r\nimport { MenuItem, ListItemText, Button, Box, Paper } from \"@mui/material\";\r\nimport FilterAltIcon from \"@mui/icons-material/FilterAlt\";\r\nimport Autocomplete from \"@mui/material/Autocomplete\";\r\nimport { getOrganizations, getAllOrganizations,getOrganizationsData } from \"../../services/OrganizationService\";\r\nimport styles from \"./OrganizationStyles.module.scss\";\r\n\r\nconst OrganizationCustomColumnMenuItem: React.FC<{\r\n  column: any;\r\n  skip: any;\r\n  top: any;\r\n  models: any[];\r\n  OrganizationId: any;\r\n  setTotalcount: any;\r\n  orderByFields: any;\r\n  setModels: any;\r\n  sortModel: any;\r\n  setLoading: any;\r\n  filters: any;\r\n  setFilters: any;\r\n  options: string[];\r\n  onSearch: (value: string[]) => void;\r\n  open: boolean;\r\n  hideMenu: (event: React.SyntheticEvent<Element, Event>) => void;\r\n  colDef: any;\r\n  modelsData: any[];\r\n  optionsModel: any;\r\n  paginationModel: any;\r\n}> = ({\r\n  column,\r\n  skip,\r\n  top,\r\n  OrganizationId,\r\n  setTotalcount,\r\n  orderByFields,\r\n  setModels,\r\n  sortModel,\r\n  setLoading,\r\n  filters,\r\n  setFilters,\r\n  options,\r\n  onSearch,\r\n  open,\r\n  hideMenu,\r\n  models,\r\n  colDef,\r\n  modelsData,\r\n  optionsModel,\r\n  paginationModel,\r\n  ...other\r\n}) => {\r\n  const [searchText, setSearchText] = useState(\"\");\r\n  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);\r\n  const [filteredOptions, setFilteredOptions] = useState<string[]>(options);\r\n\r\n  // Maintain a list of all available names\r\n  const [allNames, setAllNames] = useState<string[]>([]);\r\n  // State for all organization names (for Name column search)\r\n  const [allOrganizationNames, setAllOrganizationNames] = useState<string[]>([]);\r\n  const [allNamesLoaded, setAllNamesLoaded] = useState<boolean>(false);\r\n  \r\n  // State for all organization types (for Type column search)\r\n  const [allOrganizationTypes, setAllOrganizationTypes] = useState<string[]>([]);\r\n  const [allTypesLoaded, setAllTypesLoaded] = useState<boolean>(false);\r\n\r\n  useEffect(() => {\r\n    const uniqueOptions = Array.from(new Set(options));\r\n    setFilteredOptions(uniqueOptions);\r\n\r\n    // For Name column, check if there's an existing filter\r\n    if (column === \"Name\") {\r\n      const existingFilter = filters.find((filter: any) => filter.FieldName === column);\r\n      \r\n      if (existingFilter) {\r\n        // If filter exists, show only the currently selected values as selected\r\n        const currentlySelected = existingFilter.Value.split(',').filter((val: string) => val.trim());\r\n        setSelectedOptions(currentlySelected);\r\n        // But keep all available names in filteredOptions for display\r\n        if (allOrganizationNames.length > 0) {\r\n          setFilteredOptions(allOrganizationNames);\r\n        } else {\r\n          const currentPageNames = Array.from(new Set(models.map((model: any) => model.Name)));\r\n          setFilteredOptions(currentPageNames);\r\n        }\r\n      } else {\r\n        // If no filter, select all current page names by default\r\n        const currentPageNames = Array.from(new Set(models.map((model: any) => model.Name)));\r\n        setSelectedOptions(currentPageNames);\r\n        setFilteredOptions(currentPageNames);\r\n      }\r\n    }\r\n  }, [options, models, column, filters]);\r\n\r\n  useEffect(() => {\r\n    if (column === \"Type\") {\r\n      // Check if there's an existing filter for this column\r\n      const existingFilter = filters.find((filter: any) => filter.FieldName === column);\r\n      \r\n      if (existingFilter) {\r\n        // If filter exists, show only the currently selected values as selected\r\n        const currentlySelected = existingFilter.Value.split(',').filter((val: string) => val.trim());\r\n        setSelectedOptions(currentlySelected);\r\n        // But keep all available types in filteredOptions for display\r\n        if (Array.isArray(optionsModel) && optionsModel.length > 0) {\r\n          const typeOptions = Array.from(new Set(optionsModel.map((model: any) => model.Type)));\r\n          setFilteredOptions(typeOptions);\r\n        }\r\n      } else {\r\n        // If no filter, show all available types from optionsModel\r\n        if (Array.isArray(optionsModel) && optionsModel.length > 0) {\r\n          const typeOptions = Array.from(new Set(optionsModel.map((model: any) => model.Type)));\r\n          setSelectedOptions(typeOptions);\r\n          setFilteredOptions(typeOptions);\r\n        }\r\n      }\r\n    }\r\n  }, [column, models, optionsModel, filters]);\r\n\r\n  const handleOptionChange = (event: React.ChangeEvent<{}>, value: string[]) => {\r\n    setSelectedOptions(value);\r\n  };\r\n\r\n  const handleCancelClick = (event: React.SyntheticEvent<Element, Event>) => {\r\n    hideMenu(event);\r\n  };\r\n\r\n  const handleClearFiltersClick = (event: React.SyntheticEvent<Element, Event>) => {\r\n    setSearchText(\"\");\r\n    setFilters([]);\r\n    getAllOrganizations(setModels, setLoading);\r\n    hideMenu(event);\r\n  };\r\n\r\n  const handleApplyClick = async (event: React.SyntheticEvent<Element, Event>) => {\r\n    const searchValue = selectedOptions.length ? selectedOptions : [searchText];\r\n    // If all org names/types are selected, treat as no filter\r\n    if (column === \"Name\" && allOrganizationNames.length > 0 && selectedOptions.length === allOrganizationNames.length) {\r\n      setFilters([]);\r\n      await getAllOrganizations(setModels, setLoading);\r\n      setIsDropdownOpen(false);\r\n      hideMenu(event);\r\n      return;\r\n    } else if (column === \"Type\" && allOrganizationTypes.length > 0 && selectedOptions.length === allOrganizationTypes.length) {\r\n      setFilters([]);\r\n      await getAllOrganizations(setModels, setLoading);\r\n      setIsDropdownOpen(false);\r\n      hideMenu(event);\r\n      return;\r\n    }\r\n    const combinedValue = searchValue.join(\",\");\r\n    const newFilter = {\r\n      FieldName: column,\r\n      ElementType: \"string\",\r\n      Condition: \"in\",\r\n      Value: combinedValue,\r\n      IsCustomField: false,\r\n    };\r\n    const updatedFilters = [...filters];\r\n    const filterIndex = updatedFilters.findIndex(\r\n      (filter) => filter.FieldName === column\r\n    );\r\n    if (filterIndex !== -1) {\r\n      updatedFilters[filterIndex] = newFilter;\r\n    } else {\r\n      updatedFilters.push(newFilter);\r\n    }\r\n    setFilters(updatedFilters);\r\n    await getOrganizations(setModels, setLoading, 0, paginationModel.pageSize, setTotalcount, sortModel, updatedFilters);\r\n    setSelectedOptions(searchValue);\r\n    setIsDropdownOpen(true); // Do not close dropdown\r\n    hideMenu(event); \r\n  };\r\n\r\n  const handleSelectAll = () => {\r\n      let allToSelect: string[] = filteredOptions;\r\n  if (column === \"Name\" && allOrganizationNames.length > 0) {\r\n    allToSelect = allOrganizationNames;\r\n  } else if (column === \"Type\" && allOrganizationTypes.length > 0) {\r\n    allToSelect = allOrganizationTypes;\r\n  }\r\n    // Use all available options if loaded, otherwise fallback to filteredOptions\r\n    const newSelectedOptions =\r\n      selectedOptions.length === allToSelect.length ? [] : allToSelect;\r\n    setSelectedOptions(newSelectedOptions);\r\n  };\r\n\r\n  const isClearFiltersDisabled = !searchText && selectedOptions.length === options.length;\r\n  const isSearchButtonDisabled = selectedOptions.length === 0 && !searchText;\r\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false);\r\n  // Fetch all organization names/types when menu is opened\r\n  const handleDropdownOpen = async () => {\r\n    setIsDropdownOpen(true);\r\n    \r\n    // Check if there's an existing filter for the current column\r\n    const existingFilter = filters.find((filter: any) => filter.FieldName === column);\r\n    \r\n    if (column === \"Name\" && !allNamesLoaded) {\r\n      try {\r\n        const allOrgs = await getOrganizationsData(setModels, setLoading);\r\n        const allNamesList = Array.from(new Set((allOrgs || []).map((org: any) => org.Name)));\r\n        setAllOrganizationNames(allNamesList);\r\n        setAllNamesLoaded(true);\r\n        \r\n        setFilteredOptions(allNamesList);\r\n        if (!existingFilter) {\r\n          // Only set all selected if no filter is applied\r\n          setSelectedOptions(allNamesList);\r\n        }\r\n      } catch (error) {\r\n        // Optionally handle error\r\n      }\r\n    } else if (column === \"Name\" && allOrganizationNames.length > 0) {\r\n      setFilteredOptions(allOrganizationNames);\r\n      if (!existingFilter) {\r\n        // Only set all selected if no filter is applied\r\n        setSelectedOptions(allOrganizationNames);\r\n      }\r\n    } else if (column === \"Type\" && !allTypesLoaded) {\r\n      try {\r\n        const allOrgs = await getOrganizationsData(setModels, setLoading);\r\n        const allTypesList = Array.from(new Set((allOrgs || []).map((org: any) => org.Type)));\r\n        setAllOrganizationTypes(allTypesList);\r\n        setAllTypesLoaded(true);\r\n        \r\n        setFilteredOptions(allTypesList);\r\n        if (!existingFilter) {\r\n          // Only set all selected if no filter is applied\r\n          setSelectedOptions(allTypesList);\r\n        }\r\n      } catch (error) {\r\n        // Optionally handle error\r\n      }\r\n    } else if (column === \"Type\" && allOrganizationTypes.length > 0) {\r\n      setFilteredOptions(allOrganizationTypes);\r\n      if (!existingFilter) {\r\n        // Only set all selected if no filter is applied\r\n        setSelectedOptions(allOrganizationTypes);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Compute options for Autocomplete: always show all available options\r\n  const getAutocompleteOptions = () => {\r\n    if (column === \"Name\") {\r\n      return allOrganizationNames.length > 0 ? allOrganizationNames : filteredOptions;\r\n    } else if (column === \"Type\") {\r\n      return allOrganizationTypes.length > 0 ? allOrganizationTypes : filteredOptions;\r\n    }\r\n    return filteredOptions;\r\n  };\r\n\r\n\r\n  return (\r\n\r\n    <div className=\"qadpt-org-filter\" style={{ position: \"relative\" }}>\r\n      <Autocomplete\r\n        multiple\r\n        disableCloseOnSelect\r\n        options={getAutocompleteOptions()}\r\n        value={selectedOptions}\r\n        open={isDropdownOpen}\r\n        onOpen={handleDropdownOpen}\r\n        onClose={() => setIsDropdownOpen(true)}\r\n        onChange={handleOptionChange}\r\n        onInputChange={async (event, value) => {\r\n          setSearchText(value);\r\n          if (column === \"Name\" && value && !allNamesLoaded) {\r\n            try {\r\n              const allOrgs = await getOrganizationsData(setModels, setLoading);\r\n              const allNamesList = Array.from(new Set((allOrgs || []).map((org: any) => org.Name)));\r\n              setAllOrganizationNames(allNamesList);\r\n              setAllNamesLoaded(true);\r\n            } catch (error) {\r\n              // Optionally handle error\r\n            }\r\n          } else if (column === \"Type\" && value && !allTypesLoaded) {\r\n            try {\r\n              const allOrgs = await getOrganizationsData(setModels, setLoading);\r\n              const allTypesList = Array.from(new Set((allOrgs || []).map((org: any) => org.Type)));\r\n              setAllOrganizationTypes(allTypesList);\r\n              setAllTypesLoaded(true);\r\n            } catch (error) {\r\n              // Optionally handle error\r\n            }\r\n          }\r\n        }}\r\n        // onOpen={() => setIsDropdownOpen(true)}\r\n        //onClose={() => setIsDropdownOpen(false)}\r\n        renderOption={(props, option, { selected }) => (\r\n          <div>\r\n            {option === filteredOptions[0] && (\r\n              <MenuItem key=\"select-all\" onClick={handleSelectAll}>\r\n                <Checkbox\r\n                  checked={(() => {\r\n                    if (column === \"Name\" && allOrganizationNames.length > 0) {\r\n                      return selectedOptions.length === allOrganizationNames.length;\r\n                    } else if (column === \"Type\" && allOrganizationTypes.length > 0) {\r\n                      return selectedOptions.length === allOrganizationTypes.length;\r\n                    }\r\n                    return selectedOptions.length === filteredOptions.length;\r\n                  })()}\r\n                  indeterminate={(() => {\r\n                    const totalOptions = (() => {\r\n                      if (column === \"Name\" && allOrganizationNames.length > 0) {\r\n                        return allOrganizationNames.length;\r\n                      } else if (column === \"Type\" && allOrganizationTypes.length > 0) {\r\n                        return allOrganizationTypes.length;\r\n                      }\r\n                      return filteredOptions.length;\r\n                    })();\r\n                    return selectedOptions.length > 0 && selectedOptions.length < totalOptions;\r\n                  })()}\r\n                />\r\n                <ListItemText primary=\"Select All\" />\r\n              </MenuItem>\r\n            )}\r\n            <MenuItem {...props} key={option}>\r\n              <Checkbox checked={selectedOptions.includes(option)} />\r\n              <ListItemText primary={option} />\r\n            </MenuItem>\r\n          </div>\r\n        )}\r\n        renderInput={(params) => (\r\n          <TextField\r\n            {...params}\r\n            variant=\"outlined\"\r\n            label=\"Search\"\r\n            placeholder=\"Select Options\"\r\n            onKeyDown={(event) => {\r\n              if ([\"ArrowUp\", \"ArrowDown\", \"Enter\"].includes(event.key)) {\r\n                event.stopPropagation();\r\n              }\r\n              else\r\n              {\r\n                event.stopPropagation();\r\n              }\r\n            }}\r\n            InputProps={{\r\n              ...params.InputProps,\r\n              endAdornment: (\r\n                <React.Fragment>\r\n                  {params.InputProps.endAdornment}\r\n                </React.Fragment>\r\n              ),\r\n            }}\r\n          />\r\n        )}\r\n        PaperComponent={(props) => (\r\n          <Paper\r\n            {...props}\r\n            style={{\r\n              position: \"absolute\",\r\n              top: \"100%\",\r\n              left: 0,\r\n              width: \"100%\",\r\n              zIndex: 10,\r\n              marginTop: \"4px\", // Small gap between input and dropdown\r\n            }}\r\n            onWheel={(event) => event.stopPropagation()}\r\n          />\r\n        )}\r\n        ListboxProps={{\r\n          style: { maxHeight: \"220px\", overflowY: \"auto\" }, \r\n        }}\r\n        renderTags={() => null}\r\n      />\r\n    \r\n      <Box\r\n        display=\"flex\"\r\n        justifyContent=\"flex-end\"\r\n        mt={2}\r\n        className=\"qadpt-btn\"\r\n        sx={{\r\n          display: \"flex\",\r\n          justifyContent: \"flex-end\",\r\n          padding:\"0 10px\",\r\n          marginTop: isDropdownOpen ? \"250px\" : \"10px\", // Dynamically change marginTop\r\n          transition: \"margin-top 0.3s ease-in-out\", // Smooth transition\r\n        }}\r\n      >\r\n        <Button variant=\"outlined\" color=\"primary\" onClick={handleApplyClick} disabled={isSearchButtonDisabled}>\r\n          OK\r\n        </Button>\r\n        <Button variant=\"outlined\" color=\"secondary\" onClick={handleCancelClick} style={{ marginLeft: \"8px\" }}>\r\n          Cancel\r\n        </Button>\r\n        <Button onClick={handleClearFiltersClick} startIcon={<FilterAltIcon />} disabled={isClearFiltersDisabled} style={{ marginLeft: \"8px\" }}>\r\n          Clear Filters\r\n        </Button>\r\n      </Box>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default OrganizationCustomColumnMenuItem;\r\n"], "mappings": ";;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAAC,aAAA,IAAAC,cAAA,QAAQ,OAAO;AAClD,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,SAASC,QAAQ,EAAEC,YAAY,EAAEC,MAAM,EAAEC,GAAG,EAAEC,KAAK,QAAQ,eAAe;AAC1E,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,SAASC,gBAAgB,EAAEC,mBAAmB,EAACC,oBAAoB,QAAQ,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGhH,MAAMC,gCAqBJ,GAAGA,CAAC;EACJC,MAAM;EACNC,IAAI;EACJC,GAAG;EACHC,cAAc;EACdC,aAAa;EACbC,aAAa;EACbC,SAAS;EACTC,SAAS;EACTC,UAAU;EACVC,OAAO;EACPC,UAAU;EACVC,OAAO;EACPC,QAAQ;EACRC,IAAI;EACJC,QAAQ;EACRC,MAAM;EACNC,MAAM;EACNC,UAAU;EACVC,YAAY;EACZC,eAAe;EACf,GAAGC;AACL,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAG5C,QAAQ,CAAW,EAAE,CAAC;EACpE,MAAM,CAAC6C,eAAe,EAAEC,kBAAkB,CAAC,GAAG9C,QAAQ,CAAW8B,OAAO,CAAC;;EAEzE;EACA,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAW,EAAE,CAAC;EACtD;EACA,MAAM,CAACiD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlD,QAAQ,CAAW,EAAE,CAAC;EAC9E,MAAM,CAACmD,cAAc,EAAEC,iBAAiB,CAAC,GAAGpD,QAAQ,CAAU,KAAK,CAAC;;EAEpE;EACA,MAAM,CAACqD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtD,QAAQ,CAAW,EAAE,CAAC;EAC9E,MAAM,CAACuD,cAAc,EAAEC,iBAAiB,CAAC,GAAGxD,QAAQ,CAAU,KAAK,CAAC;EAEpEC,SAAS,CAAC,MAAM;IACd,MAAMwD,aAAa,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAAC9B,OAAO,CAAC,CAAC;IAClDgB,kBAAkB,CAACW,aAAa,CAAC;;IAEjC;IACA,IAAItC,MAAM,KAAK,MAAM,EAAE;MACrB,MAAM0C,cAAc,GAAGjC,OAAO,CAACkC,IAAI,CAAEC,MAAW,IAAKA,MAAM,CAACC,SAAS,KAAK7C,MAAM,CAAC;MAEjF,IAAI0C,cAAc,EAAE;QAClB;QACA,MAAMI,iBAAiB,GAAGJ,cAAc,CAACK,KAAK,CAACC,KAAK,CAAC,GAAG,CAAC,CAACJ,MAAM,CAAEK,GAAW,IAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC;QAC7FzB,kBAAkB,CAACqB,iBAAiB,CAAC;QACrC;QACA,IAAIhB,oBAAoB,CAACqB,MAAM,GAAG,CAAC,EAAE;UACnCxB,kBAAkB,CAACG,oBAAoB,CAAC;QAC1C,CAAC,MAAM;UACL,MAAMsB,gBAAgB,GAAGb,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAAC1B,MAAM,CAACsC,GAAG,CAAEC,KAAU,IAAKA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;UACpF5B,kBAAkB,CAACyB,gBAAgB,CAAC;QACtC;MACF,CAAC,MAAM;QACL;QACA,MAAMA,gBAAgB,GAAGb,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAAC1B,MAAM,CAACsC,GAAG,CAAEC,KAAU,IAAKA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;QACpF9B,kBAAkB,CAAC2B,gBAAgB,CAAC;QACpCzB,kBAAkB,CAACyB,gBAAgB,CAAC;MACtC;IACF;EACF,CAAC,EAAE,CAACzC,OAAO,EAAEI,MAAM,EAAEf,MAAM,EAAES,OAAO,CAAC,CAAC;EAEtC3B,SAAS,CAAC,MAAM;IACd,IAAIkB,MAAM,KAAK,MAAM,EAAE;MACrB;MACA,MAAM0C,cAAc,GAAGjC,OAAO,CAACkC,IAAI,CAAEC,MAAW,IAAKA,MAAM,CAACC,SAAS,KAAK7C,MAAM,CAAC;MAEjF,IAAI0C,cAAc,EAAE;QAClB;QACA,MAAMI,iBAAiB,GAAGJ,cAAc,CAACK,KAAK,CAACC,KAAK,CAAC,GAAG,CAAC,CAACJ,MAAM,CAAEK,GAAW,IAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC;QAC7FzB,kBAAkB,CAACqB,iBAAiB,CAAC;QACrC;QACA,IAAIP,KAAK,CAACiB,OAAO,CAACtC,YAAY,CAAC,IAAIA,YAAY,CAACiC,MAAM,GAAG,CAAC,EAAE;UAC1D,MAAMM,WAAW,GAAGlB,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACvB,YAAY,CAACmC,GAAG,CAAEC,KAAU,IAAKA,KAAK,CAACI,IAAI,CAAC,CAAC,CAAC;UACrF/B,kBAAkB,CAAC8B,WAAW,CAAC;QACjC;MACF,CAAC,MAAM;QACL;QACA,IAAIlB,KAAK,CAACiB,OAAO,CAACtC,YAAY,CAAC,IAAIA,YAAY,CAACiC,MAAM,GAAG,CAAC,EAAE;UAC1D,MAAMM,WAAW,GAAGlB,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACvB,YAAY,CAACmC,GAAG,CAAEC,KAAU,IAAKA,KAAK,CAACI,IAAI,CAAC,CAAC,CAAC;UACrFjC,kBAAkB,CAACgC,WAAW,CAAC;UAC/B9B,kBAAkB,CAAC8B,WAAW,CAAC;QACjC;MACF;IACF;EACF,CAAC,EAAE,CAACzD,MAAM,EAAEe,MAAM,EAAEG,YAAY,EAAET,OAAO,CAAC,CAAC;EAE3C,MAAMkD,kBAAkB,GAAGA,CAACC,KAA4B,EAAEC,KAAe,KAAK;IAC5EpC,kBAAkB,CAACoC,KAAK,CAAC;EAC3B,CAAC;EAED,MAAMC,iBAAiB,GAAIF,KAA2C,IAAK;IACzE9C,QAAQ,CAAC8C,KAAK,CAAC;EACjB,CAAC;EAED,MAAMG,uBAAuB,GAAIH,KAA2C,IAAK;IAC/ErC,aAAa,CAAC,EAAE,CAAC;IACjBb,UAAU,CAAC,EAAE,CAAC;IACdf,mBAAmB,CAACW,SAAS,EAAEE,UAAU,CAAC;IAC1CM,QAAQ,CAAC8C,KAAK,CAAC;EACjB,CAAC;EAED,MAAMI,gBAAgB,GAAG,MAAOJ,KAA2C,IAAK;IAC9E,MAAMK,WAAW,GAAGzC,eAAe,CAAC2B,MAAM,GAAG3B,eAAe,GAAG,CAACF,UAAU,CAAC;IAC3E;IACA,IAAItB,MAAM,KAAK,MAAM,IAAI8B,oBAAoB,CAACqB,MAAM,GAAG,CAAC,IAAI3B,eAAe,CAAC2B,MAAM,KAAKrB,oBAAoB,CAACqB,MAAM,EAAE;MAClHzC,UAAU,CAAC,EAAE,CAAC;MACd,MAAMf,mBAAmB,CAACW,SAAS,EAAEE,UAAU,CAAC;MAChD0D,iBAAiB,CAAC,KAAK,CAAC;MACxBpD,QAAQ,CAAC8C,KAAK,CAAC;MACf;IACF,CAAC,MAAM,IAAI5D,MAAM,KAAK,MAAM,IAAIkC,oBAAoB,CAACiB,MAAM,GAAG,CAAC,IAAI3B,eAAe,CAAC2B,MAAM,KAAKjB,oBAAoB,CAACiB,MAAM,EAAE;MACzHzC,UAAU,CAAC,EAAE,CAAC;MACd,MAAMf,mBAAmB,CAACW,SAAS,EAAEE,UAAU,CAAC;MAChD0D,iBAAiB,CAAC,KAAK,CAAC;MACxBpD,QAAQ,CAAC8C,KAAK,CAAC;MACf;IACF;IACA,MAAMO,aAAa,GAAGF,WAAW,CAACG,IAAI,CAAC,GAAG,CAAC;IAC3C,MAAMC,SAAS,GAAG;MAChBxB,SAAS,EAAE7C,MAAM;MACjBsE,WAAW,EAAE,QAAQ;MACrBC,SAAS,EAAE,IAAI;MACfxB,KAAK,EAAEoB,aAAa;MACpBK,aAAa,EAAE;IACjB,CAAC;IACD,MAAMC,cAAc,GAAG,CAAC,GAAGhE,OAAO,CAAC;IACnC,MAAMiE,WAAW,GAAGD,cAAc,CAACE,SAAS,CACzC/B,MAAM,IAAKA,MAAM,CAACC,SAAS,KAAK7C,MACnC,CAAC;IACD,IAAI0E,WAAW,KAAK,CAAC,CAAC,EAAE;MACtBD,cAAc,CAACC,WAAW,CAAC,GAAGL,SAAS;IACzC,CAAC,MAAM;MACLI,cAAc,CAACG,IAAI,CAACP,SAAS,CAAC;IAChC;IACA3D,UAAU,CAAC+D,cAAc,CAAC;IAC1B,MAAM/E,gBAAgB,CAACY,SAAS,EAAEE,UAAU,EAAE,CAAC,EAAEW,eAAe,CAAC0D,QAAQ,EAAEzE,aAAa,EAAEG,SAAS,EAAEkE,cAAc,CAAC;IACpHhD,kBAAkB,CAACwC,WAAW,CAAC;IAC/BC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;IACzBpD,QAAQ,CAAC8C,KAAK,CAAC;EACjB,CAAC;EAED,MAAMkB,eAAe,GAAGA,CAAA,KAAM;IAC1B,IAAIC,WAAqB,GAAGrD,eAAe;IAC/C,IAAI1B,MAAM,KAAK,MAAM,IAAI8B,oBAAoB,CAACqB,MAAM,GAAG,CAAC,EAAE;MACxD4B,WAAW,GAAGjD,oBAAoB;IACpC,CAAC,MAAM,IAAI9B,MAAM,KAAK,MAAM,IAAIkC,oBAAoB,CAACiB,MAAM,GAAG,CAAC,EAAE;MAC/D4B,WAAW,GAAG7C,oBAAoB;IACpC;IACE;IACA,MAAM8C,kBAAkB,GACtBxD,eAAe,CAAC2B,MAAM,KAAK4B,WAAW,CAAC5B,MAAM,GAAG,EAAE,GAAG4B,WAAW;IAClEtD,kBAAkB,CAACuD,kBAAkB,CAAC;EACxC,CAAC;EAED,MAAMC,sBAAsB,GAAG,CAAC3D,UAAU,IAAIE,eAAe,CAAC2B,MAAM,KAAKxC,OAAO,CAACwC,MAAM;EACvF,MAAM+B,sBAAsB,GAAG1D,eAAe,CAAC2B,MAAM,KAAK,CAAC,IAAI,CAAC7B,UAAU;EAC1E,MAAM,CAAC6D,cAAc,EAAEjB,iBAAiB,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EAC3D;EACA,MAAMuG,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrClB,iBAAiB,CAAC,IAAI,CAAC;;IAEvB;IACA,MAAMxB,cAAc,GAAGjC,OAAO,CAACkC,IAAI,CAAEC,MAAW,IAAKA,MAAM,CAACC,SAAS,KAAK7C,MAAM,CAAC;IAEjF,IAAIA,MAAM,KAAK,MAAM,IAAI,CAACgC,cAAc,EAAE;MACxC,IAAI;QACF,MAAMqD,OAAO,GAAG,MAAMzF,oBAAoB,CAACU,SAAS,EAAEE,UAAU,CAAC;QACjE,MAAM8E,YAAY,GAAG/C,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAAC,CAAC4C,OAAO,IAAI,EAAE,EAAEhC,GAAG,CAAEkC,GAAQ,IAAKA,GAAG,CAAChC,IAAI,CAAC,CAAC,CAAC;QACrFxB,uBAAuB,CAACuD,YAAY,CAAC;QACrCrD,iBAAiB,CAAC,IAAI,CAAC;QAEvBN,kBAAkB,CAAC2D,YAAY,CAAC;QAChC,IAAI,CAAC5C,cAAc,EAAE;UACnB;UACAjB,kBAAkB,CAAC6D,YAAY,CAAC;QAClC;MACF,CAAC,CAAC,OAAOE,KAAK,EAAE;QACd;MAAA;IAEJ,CAAC,MAAM,IAAIxF,MAAM,KAAK,MAAM,IAAI8B,oBAAoB,CAACqB,MAAM,GAAG,CAAC,EAAE;MAC/DxB,kBAAkB,CAACG,oBAAoB,CAAC;MACxC,IAAI,CAACY,cAAc,EAAE;QACnB;QACAjB,kBAAkB,CAACK,oBAAoB,CAAC;MAC1C;IACF,CAAC,MAAM,IAAI9B,MAAM,KAAK,MAAM,IAAI,CAACoC,cAAc,EAAE;MAC/C,IAAI;QACF,MAAMiD,OAAO,GAAG,MAAMzF,oBAAoB,CAACU,SAAS,EAAEE,UAAU,CAAC;QACjE,MAAMiF,YAAY,GAAGlD,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAAC,CAAC4C,OAAO,IAAI,EAAE,EAAEhC,GAAG,CAAEkC,GAAQ,IAAKA,GAAG,CAAC7B,IAAI,CAAC,CAAC,CAAC;QACrFvB,uBAAuB,CAACsD,YAAY,CAAC;QACrCpD,iBAAiB,CAAC,IAAI,CAAC;QAEvBV,kBAAkB,CAAC8D,YAAY,CAAC;QAChC,IAAI,CAAC/C,cAAc,EAAE;UACnB;UACAjB,kBAAkB,CAACgE,YAAY,CAAC;QAClC;MACF,CAAC,CAAC,OAAOD,KAAK,EAAE;QACd;MAAA;IAEJ,CAAC,MAAM,IAAIxF,MAAM,KAAK,MAAM,IAAIkC,oBAAoB,CAACiB,MAAM,GAAG,CAAC,EAAE;MAC/DxB,kBAAkB,CAACO,oBAAoB,CAAC;MACxC,IAAI,CAACQ,cAAc,EAAE;QACnB;QACAjB,kBAAkB,CAACS,oBAAoB,CAAC;MAC1C;IACF;EACF,CAAC;;EAED;EACA,MAAMwD,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI1F,MAAM,KAAK,MAAM,EAAE;MACrB,OAAO8B,oBAAoB,CAACqB,MAAM,GAAG,CAAC,GAAGrB,oBAAoB,GAAGJ,eAAe;IACjF,CAAC,MAAM,IAAI1B,MAAM,KAAK,MAAM,EAAE;MAC5B,OAAOkC,oBAAoB,CAACiB,MAAM,GAAG,CAAC,GAAGjB,oBAAoB,GAAGR,eAAe;IACjF;IACA,OAAOA,eAAe;EACxB,CAAC;EAGD,oBAEE5B,OAAA;IAAK6F,SAAS,EAAC,kBAAkB;IAACC,KAAK,EAAE;MAAEC,QAAQ,EAAE;IAAW,CAAE;IAAAC,QAAA,gBAChEhG,OAAA,CAACL,YAAY;MACXsG,QAAQ;MACRC,oBAAoB;MACpBrF,OAAO,EAAE+E,sBAAsB,CAAC,CAAE;MAClC7B,KAAK,EAAErC,eAAgB;MACvBX,IAAI,EAAEsE,cAAe;MACrBc,MAAM,EAAEb,kBAAmB;MAC3Bc,OAAO,EAAEA,CAAA,KAAMhC,iBAAiB,CAAC,IAAI,CAAE;MACvCiC,QAAQ,EAAExC,kBAAmB;MAC7ByC,aAAa,EAAE,MAAAA,CAAOxC,KAAK,EAAEC,KAAK,KAAK;QACrCtC,aAAa,CAACsC,KAAK,CAAC;QACpB,IAAI7D,MAAM,KAAK,MAAM,IAAI6D,KAAK,IAAI,CAAC7B,cAAc,EAAE;UACjD,IAAI;YACF,MAAMqD,OAAO,GAAG,MAAMzF,oBAAoB,CAACU,SAAS,EAAEE,UAAU,CAAC;YACjE,MAAM8E,YAAY,GAAG/C,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAAC,CAAC4C,OAAO,IAAI,EAAE,EAAEhC,GAAG,CAAEkC,GAAQ,IAAKA,GAAG,CAAChC,IAAI,CAAC,CAAC,CAAC;YACrFxB,uBAAuB,CAACuD,YAAY,CAAC;YACrCrD,iBAAiB,CAAC,IAAI,CAAC;UACzB,CAAC,CAAC,OAAOuD,KAAK,EAAE;YACd;UAAA;QAEJ,CAAC,MAAM,IAAIxF,MAAM,KAAK,MAAM,IAAI6D,KAAK,IAAI,CAACzB,cAAc,EAAE;UACxD,IAAI;YACF,MAAMiD,OAAO,GAAG,MAAMzF,oBAAoB,CAACU,SAAS,EAAEE,UAAU,CAAC;YACjE,MAAMiF,YAAY,GAAGlD,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAAC,CAAC4C,OAAO,IAAI,EAAE,EAAEhC,GAAG,CAAEkC,GAAQ,IAAKA,GAAG,CAAC7B,IAAI,CAAC,CAAC,CAAC;YACrFvB,uBAAuB,CAACsD,YAAY,CAAC;YACrCpD,iBAAiB,CAAC,IAAI,CAAC;UACzB,CAAC,CAAC,OAAOmD,KAAK,EAAE;YACd;UAAA;QAEJ;MACF;MACA;MACA;MAAA;MACAa,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM,EAAE;QAAEC;MAAS,CAAC,kBACxC1G,OAAA;QAAAgG,QAAA,GACGS,MAAM,KAAK7E,eAAe,CAAC,CAAC,CAAC,iBAC5B5B,OAAA,CAACX,QAAQ;UAAkBsH,OAAO,EAAE3B,eAAgB;UAAAgB,QAAA,gBAClDhG,OAAA,CAACZ,QAAQ;YACPwH,OAAO,EAAE,CAAC,MAAM;cACd,IAAI1G,MAAM,KAAK,MAAM,IAAI8B,oBAAoB,CAACqB,MAAM,GAAG,CAAC,EAAE;gBACxD,OAAO3B,eAAe,CAAC2B,MAAM,KAAKrB,oBAAoB,CAACqB,MAAM;cAC/D,CAAC,MAAM,IAAInD,MAAM,KAAK,MAAM,IAAIkC,oBAAoB,CAACiB,MAAM,GAAG,CAAC,EAAE;gBAC/D,OAAO3B,eAAe,CAAC2B,MAAM,KAAKjB,oBAAoB,CAACiB,MAAM;cAC/D;cACA,OAAO3B,eAAe,CAAC2B,MAAM,KAAKzB,eAAe,CAACyB,MAAM;YAC1D,CAAC,EAAE,CAAE;YACLwD,aAAa,EAAE,CAAC,MAAM;cACpB,MAAMC,YAAY,GAAG,CAAC,MAAM;gBAC1B,IAAI5G,MAAM,KAAK,MAAM,IAAI8B,oBAAoB,CAACqB,MAAM,GAAG,CAAC,EAAE;kBACxD,OAAOrB,oBAAoB,CAACqB,MAAM;gBACpC,CAAC,MAAM,IAAInD,MAAM,KAAK,MAAM,IAAIkC,oBAAoB,CAACiB,MAAM,GAAG,CAAC,EAAE;kBAC/D,OAAOjB,oBAAoB,CAACiB,MAAM;gBACpC;gBACA,OAAOzB,eAAe,CAACyB,MAAM;cAC/B,CAAC,EAAE,CAAC;cACJ,OAAO3B,eAAe,CAAC2B,MAAM,GAAG,CAAC,IAAI3B,eAAe,CAAC2B,MAAM,GAAGyD,YAAY;YAC5E,CAAC,EAAE;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACFlH,OAAA,CAACV,YAAY;YAAC6H,OAAO,EAAC;UAAY;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA,GAtBzB,YAAY;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuBhB,CACX,eACDhI,cAAA,CAACG,QAAQ;UAAA,GAAKmH,KAAK;UAAEY,GAAG,EAAEX,MAAO;UAAAY,MAAA;UAAAC,QAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,gBAC/BlH,OAAA,CAACZ,QAAQ;UAACwH,OAAO,EAAElF,eAAe,CAAC6F,QAAQ,CAACd,MAAM;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvDlH,OAAA,CAACV,YAAY;UAAC6H,OAAO,EAAEV;QAAO;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACL;MACFM,WAAW,EAAGC,MAAM,iBAClBzH,OAAA,CAACb,SAAS;QAAA,GACJsI,MAAM;QACVC,OAAO,EAAC,UAAU;QAClBC,KAAK,EAAC,QAAQ;QACdC,WAAW,EAAC,gBAAgB;QAC5BC,SAAS,EAAG/D,KAAK,IAAK;UACpB,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC,CAACyD,QAAQ,CAACzD,KAAK,CAACsD,GAAG,CAAC,EAAE;YACzDtD,KAAK,CAACgE,eAAe,CAAC,CAAC;UACzB,CAAC,MAED;YACEhE,KAAK,CAACgE,eAAe,CAAC,CAAC;UACzB;QACF,CAAE;QACFC,UAAU,EAAE;UACV,GAAGN,MAAM,CAACM,UAAU;UACpBC,YAAY,eACVhI,OAAA,CAAClB,KAAK,CAACmJ,QAAQ;YAAAjC,QAAA,EACZyB,MAAM,CAACM,UAAU,CAACC;UAAY;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB;QAEpB;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACD;MACFgB,cAAc,EAAG1B,KAAK,iBACpBxG,OAAA,CAACP,KAAK;QAAA,GACA+G,KAAK;QACTV,KAAK,EAAE;UACLC,QAAQ,EAAE,UAAU;UACpB3F,GAAG,EAAE,MAAM;UACX+H,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,EAAE;UACVC,SAAS,EAAE,KAAK,CAAE;QACpB,CAAE;QACFC,OAAO,EAAGzE,KAAK,IAAKA,KAAK,CAACgE,eAAe,CAAC;MAAE;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CACD;MACFsB,YAAY,EAAE;QACZ1C,KAAK,EAAE;UAAE2C,SAAS,EAAE,OAAO;UAAEC,SAAS,EAAE;QAAO;MACjD,CAAE;MACFC,UAAU,EAAEA,CAAA,KAAM;IAAK;MAAA5B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,eAEFlH,OAAA,CAACR,GAAG;MACFoJ,OAAO,EAAC,MAAM;MACdC,cAAc,EAAC,UAAU;MACzBC,EAAE,EAAE,CAAE;MACNjD,SAAS,EAAC,WAAW;MACrBkD,EAAE,EAAE;QACFH,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,UAAU;QAC1BG,OAAO,EAAC,QAAQ;QAChBV,SAAS,EAAEjD,cAAc,GAAG,OAAO,GAAG,MAAM;QAAE;QAC9C4D,UAAU,EAAE,6BAA6B,CAAE;MAC7C,CAAE;MAAAjD,QAAA,gBAEFhG,OAAA,CAACT,MAAM;QAACmI,OAAO,EAAC,UAAU;QAACwB,KAAK,EAAC,SAAS;QAACvC,OAAO,EAAEzC,gBAAiB;QAACiF,QAAQ,EAAE/D,sBAAuB;QAAAY,QAAA,EAAC;MAExG;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlH,OAAA,CAACT,MAAM;QAACmI,OAAO,EAAC,UAAU;QAACwB,KAAK,EAAC,WAAW;QAACvC,OAAO,EAAE3C,iBAAkB;QAAC8B,KAAK,EAAE;UAAEsD,UAAU,EAAE;QAAM,CAAE;QAAApD,QAAA,EAAC;MAEvG;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlH,OAAA,CAACT,MAAM;QAACoH,OAAO,EAAE1C,uBAAwB;QAACoF,SAAS,eAAErJ,OAAA,CAACN,aAAa;UAAAqH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAACiC,QAAQ,EAAEhE,sBAAuB;QAACW,KAAK,EAAE;UAAEsD,UAAU,EAAE;QAAM,CAAE;QAAApD,QAAA,EAAC;MAExI;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3F,EAAA,CAjYItB,gCAqBJ;AAAAqJ,EAAA,GArBIrJ,gCAqBJ;AA8WF,eAAeA,gCAAgC;AAAC,IAAAqJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}