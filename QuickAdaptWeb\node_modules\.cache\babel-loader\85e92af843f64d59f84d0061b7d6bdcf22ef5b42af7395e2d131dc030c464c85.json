{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptWeb\\\\src\\\\components\\\\login\\\\ResetPassword.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, TextField, Button, Typography, Box, Link, IconButton, InputAdornment } from '@mui/material';\nimport Visibility from '@mui/icons-material/Visibility';\nimport VisibilityOff from '@mui/icons-material/VisibilityOff';\nimport CheckCircleIcon from '@mui/icons-material/CheckCircle';\nimport { Resetpassword, encryptResetPassword } from '../../services/ResetpasswordService';\nimport { useLocation } from 'react-router-dom';\nimport { useSnackbar } from \"../../SnackbarContext\";\nimport FormHelperText from '@mui/material/FormHelperText';\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ResetPassword = () => {\n  _s();\n  const location = useLocation();\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [password, setPassword] = useState('');\n  const [confirmPassword, setConfirmPassword] = useState('');\n  const [passwordError, setPasswordError] = useState('');\n  const [confirmPasswordError, setConfirmPasswordError] = useState('');\n  const [passwordValidations, setPasswordValidations] = useState({\n    minLength: false,\n    hasUppercase: false,\n    hasSpecialChar: false,\n    hasNumber: false,\n    noSpaces: true\n  });\n  const [passwordsMatch, setPasswordsMatch] = useState(false);\n  const [isPasswordChanged, setIsPasswordChanged] = useState(false);\n  const {\n    openSnackbar\n  } = useSnackbar();\n  const pathname = location.pathname;\n  const segments = pathname.split('/');\n  const passwordLogId = segments[segments.length - 1];\n  const {\n    t: translate\n  } = useTranslation();\n  // Validate password criteria\n  const validatePassword = password => {\n    const validations = {\n      minLength: password.length >= 8,\n      hasUppercase: /[A-Z]/.test(password),\n      hasSpecialChar: /[!@#$%^&*]/.test(password),\n      hasNumber: /\\d/.test(password),\n      noSpaces: !/\\s/.test(password)\n    };\n    setPasswordValidations(validations);\n    setPasswordsMatch(password === confirmPassword);\n    return validations.minLength && validations.hasUppercase && validations.hasSpecialChar && validations.hasNumber && validations.noSpaces;\n  };\n\n  // Password change handler with validation\n  const handlePasswordChange = event => {\n    const newPassword = event.target.value;\n    setPassword(newPassword);\n    const isValid = validatePassword(newPassword);\n    if (!isValid) {\n      setPasswordError(!passwordValidations.noSpaces ? \"Spaces are not accepted.\" : \"Password must be at least 8 characters, contain 1 uppercase letter, 1 special character, 1 number, and no spaces.\");\n    } else {\n      setPasswordError('');\n    }\n    setPasswordsMatch(newPassword === confirmPassword);\n  };\n\n  // Confirm password change handler with validation\n  const handleConfirmPasswordChange = event => {\n    const newConfirmPassword = event.target.value;\n    setConfirmPassword(newConfirmPassword);\n    if (password !== newConfirmPassword) {\n      setConfirmPasswordError(\"Passwords do not match.\");\n    } else {\n      setConfirmPasswordError('');\n    }\n    setPasswordsMatch(password === newConfirmPassword && password.length >= 8);\n  };\n  const handleSubmit = async () => {\n    let isValid = true;\n    if (!password) {\n      setPasswordError('This field is required.');\n      isValid = false;\n    }\n    if (!confirmPassword) {\n      setConfirmPasswordError('This field is required.');\n      isValid = false;\n    }\n    if (isValid && passwordsMatch && !passwordError && !confirmPasswordError) {\n      const encryptedPassword = encryptResetPassword(password);\n      const encryptedReenteredPassword = encryptResetPassword(confirmPassword);\n      if (encryptedPassword && encryptedReenteredPassword) {\n        try {\n          const response = await Resetpassword(passwordLogId, encryptedPassword, encryptedReenteredPassword);\n          if (response && response.Success) {\n            setIsPasswordChanged(true);\n          } else {\n            openSnackbar(translate(response), \"error\");\n          }\n        } catch (error) {\n          console.error('Error during password reset:', error);\n        }\n      } else {\n        console.error('Encryption failed. Please try again.');\n      }\n    }\n    localStorage.clear();\n    document.cookie.split(\";\").forEach(cookie => {\n      const [name] = cookie.split(\"=\");\n      document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;\n    });\n    localStorage.setItem('logout-event', Date.now().toString());\n    sessionStorage.clear();\n  };\n  const disabled = !passwordsMatch || passwordError !== '' || confirmPasswordError !== '';\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"xs\",\n    className: \"qadpt-resetpassword\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 4,\n      className: \"qadpt-brand-logo\",\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h3\",\n        className: \"qadpt-brand-logo-text\",\n        gutterBottom: true,\n        children: \"QuickAdopt\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      children: [!isPasswordChanged ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-welcome-message\",\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            className: \"qadpt-welcome-message-text\",\n            children: \"Reset your password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            align: \"center\",\n            mb: 2,\n            sx: {\n              width: \"325px\",\n              height: \"38px\",\n              opacity: \"0.8\",\n              fontFamily: \"Poppins\",\n              fontSize: \"14px\",\n              fontWeight: 400,\n              lineHeight: \"19px\",\n              textAlign: \"center\",\n              color: \"#222222\"\n            },\n            children: \"Enter your new password below to change your password.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-login-form\",\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            className: \"qadpt-form-label\",\n            children: \"New Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            margin: \"normal\",\n            required: true,\n            fullWidth: true,\n            type: showPassword ? \"text\" : \"password\",\n            id: \"new-password\",\n            name: \"new-password\",\n            autoComplete: \"new-password\",\n            autoFocus: true,\n            value: password,\n            onChange: handlePasswordChange,\n            placeholder: \"min. 8 characters\",\n            className: \"qadpt-custom-input\",\n            error: !!passwordError,\n            InputProps: {\n              endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"end\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  \"aria-label\": \"toggle password visibility\",\n                  onClick: () => setShowPassword(!showPassword),\n                  edge: \"end\",\n                  children: showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOff, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 65\n                  }, this) : /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 85\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 41\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 29\n          }, this), passwordError ? /*#__PURE__*/_jsxDEV(FormHelperText, {\n            sx: {\n              color: 'red',\n              fontSize: '12px'\n            },\n            children: passwordError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 33\n          }, this) : \"\", /*#__PURE__*/_jsxDEV(Typography, {\n            className: \"qadpt-form-label\",\n            children: \"Re-enter New Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            margin: \"normal\",\n            required: true,\n            fullWidth: true,\n            type: showConfirmPassword ? \"text\" : \"password\",\n            id: \"confirm-password\",\n            name: \"confirm-password\",\n            autoComplete: \"confirm-password\",\n            autoFocus: true,\n            value: confirmPassword,\n            onChange: handleConfirmPasswordChange,\n            placeholder: \"Same as above\",\n            className: \"qadpt-custom-input\",\n            error: !!confirmPasswordError\n            //helperText={confirmPasswordError}\n            ,\n            InputProps: {\n              endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"end\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  \"aria-label\": \"toggle confirm password visibility\",\n                  onClick: () => setShowConfirmPassword(!showConfirmPassword),\n                  edge: \"end\",\n                  children: showConfirmPassword ? /*#__PURE__*/_jsxDEV(VisibilityOff, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 72\n                  }, this) : /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 92\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 41\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 29\n          }, this), confirmPasswordError ? /*#__PURE__*/_jsxDEV(FormHelperText, {\n            sx: {\n              color: 'red',\n              fontSize: '12px'\n            },\n            children: confirmPasswordError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 33\n          }, this) : \"\", (password || confirmPassword) && /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-passwordhint\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              className: \"qadpt-passwordhint-text\",\n              children: \"Your password must contain\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-passwordhint-container\",\n              children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                className: passwordValidations.minLength ? \"qadpt-checkicon-valid\" : \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-passwordhint-item\",\n                children: \"At least 8 characters\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"button\",\n            fullWidth: true,\n            variant: \"contained\",\n            onClick: handleSubmit,\n            disabled: !passwordsMatch || passwordError !== '' || confirmPasswordError !== '',\n            className: disabled ? \"qadpt-btn-disabled\" : \"qadpt-btn-default\",\n            children: \"Reset password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          mt: 12,\n          className: \"qadpt-login-footer\",\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            className: \"qadpt-footer-text\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              className: \"qadpt-footer-link\",\n              children: \"Terms of use\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 33\n            }, this), \" \", \"|\", \" \", /*#__PURE__*/_jsxDEV(Link, {\n              className: \"qadpt-footer-link\",\n              children: \"Privacy Policy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(Box, {\n        className: \"qadpt-pwd-changed\",\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          className: \"qadpt-pwd-title\",\n          children: \"Password Changed!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"textSecondary\",\n          className: \"qadpt-changed-msg\",\n          mt: \"10px\",\n          children: \"Your password has been changed successfully.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"button\",\n          variant: \"contained\",\n          className: \"qadpt-btn-default\",\n          href: \"https://app.quickadopt.in/Login\",\n          children: \"Back to QuickAdopt Platform\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        mt: 12,\n        className: \"qadpt-login-footer\",\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          className: \"qadpt-footer-text\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            href: \"/terms-of-use\",\n            className: \"qadpt-footer-link\",\n            children: \"Terms of use\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 25\n          }, this), \" \", \"|\", \" \", /*#__PURE__*/_jsxDEV(Link, {\n            href: \"/privacy-policy\",\n            className: \"qadpt-footer-link\",\n            children: \"Privacy Policy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 116,\n    columnNumber: 9\n  }, this);\n};\n_s(ResetPassword, \"9b0cLMMcsrf7xw5Se4ue5y0+QTQ=\", false, function () {\n  return [useLocation, useSnackbar, useTranslation];\n});\n_c = ResetPassword;\nexport default ResetPassword;\nvar _c;\n$RefreshReg$(_c, \"ResetPassword\");", "map": {"version": 3, "names": ["React", "useState", "Container", "TextField", "<PERSON><PERSON>", "Typography", "Box", "Link", "IconButton", "InputAdornment", "Visibility", "VisibilityOff", "CheckCircleIcon", "Resetpassword", "encryptResetPassword", "useLocation", "useSnackbar", "FormHelperText", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ResetPassword", "_s", "location", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "password", "setPassword", "confirmPassword", "setConfirmPassword", "passwordError", "setPasswordError", "confirmPasswordError", "setConfirmPasswordError", "passwordValidations", "setPasswordValidations", "<PERSON><PERSON><PERSON><PERSON>", "hasUppercase", "hasSpecialChar", "hasNumber", "noSpaces", "passwordsMatch", "setPasswordsMatch", "isPasswordChanged", "setIsPasswordChanged", "openSnackbar", "pathname", "segments", "split", "passwordLogId", "length", "t", "translate", "validatePassword", "validations", "test", "handlePasswordChange", "event", "newPassword", "target", "value", "<PERSON><PERSON><PERSON><PERSON>", "handleConfirmPasswordChange", "newConfirmPassword", "handleSubmit", "encryptedPassword", "encryptedReenteredPassword", "response", "Success", "error", "console", "localStorage", "clear", "document", "cookie", "for<PERSON>ach", "name", "setItem", "Date", "now", "toString", "sessionStorage", "disabled", "max<PERSON><PERSON><PERSON>", "className", "children", "mb", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "align", "sx", "width", "height", "opacity", "fontFamily", "fontSize", "fontWeight", "lineHeight", "textAlign", "margin", "required", "fullWidth", "type", "id", "autoComplete", "autoFocus", "onChange", "placeholder", "InputProps", "endAdornment", "position", "onClick", "edge", "mt", "href", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/components/login/ResetPassword.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Con<PERSON><PERSON>, TextField, Button, Typography, Box, Link, IconButton, InputAdornment } from '@mui/material';\r\nimport Visibility from '@mui/icons-material/Visibility';\r\nimport VisibilityOff from '@mui/icons-material/VisibilityOff';\r\nimport CheckCircleIcon from '@mui/icons-material/CheckCircle';\r\nimport { Resetpassword, encryptResetPassword } from '../../services/ResetpasswordService';\r\nimport { useLocation } from 'react-router-dom';\r\nimport { useSnackbar } from \"../../SnackbarContext\";\r\nimport FormHelperText from '@mui/material/FormHelperText';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst ResetPassword = () => {\r\n    const location = useLocation();\r\n    const [showPassword, setShowPassword] = useState(false);\r\n    const [showConfirmPassword, setShowConfirmPassword] = useState(false);\r\n    const [password, setPassword] = useState('');\r\n    const [confirmPassword, setConfirmPassword] = useState('');\r\n    const [passwordError, setPasswordError] = useState('');\r\n    const [confirmPasswordError, setConfirmPasswordError] = useState('');\r\n    const [passwordValidations, setPasswordValidations] = useState({\r\n        minLength: false,\r\n        hasUppercase: false,\r\n        hasSpecialChar: false,\r\n        hasNumber: false,\r\n        noSpaces: true,\r\n    });\r\n    const [passwordsMatch, setPasswordsMatch] = useState(false);\r\n    const [isPasswordChanged, setIsPasswordChanged] = useState(false);\r\n\r\n    const { openSnackbar } = useSnackbar();\r\n    const pathname = location.pathname;\r\n    const segments = pathname.split('/');\r\n    const passwordLogId = segments[segments.length - 1];\r\n    const { t: translate } = useTranslation();\r\n    // Validate password criteria\r\n    const validatePassword = (password: string) => {\r\n        const validations = {\r\n            minLength: password.length >= 8,\r\n            hasUppercase: /[A-Z]/.test(password),\r\n            hasSpecialChar: /[!@#$%^&*]/.test(password),\r\n            hasNumber: /\\d/.test(password),\r\n            noSpaces: !/\\s/.test(password),\r\n        };\r\n        setPasswordValidations(validations);\r\n        setPasswordsMatch(password === confirmPassword);\r\n        return validations.minLength && validations.hasUppercase && validations.hasSpecialChar && validations.hasNumber && validations.noSpaces;\r\n    };\r\n\r\n    // Password change handler with validation\r\n    const handlePasswordChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n        const newPassword = event.target.value;\r\n        setPassword(newPassword);\r\n        const isValid = validatePassword(newPassword);\r\n        if (!isValid) {\r\n            setPasswordError(\r\n                !passwordValidations.noSpaces\r\n                    ? \"Spaces are not accepted.\"\r\n                    : \"Password must be at least 8 characters, contain 1 uppercase letter, 1 special character, 1 number, and no spaces.\"\r\n            );\r\n        } else {\r\n            setPasswordError('');\r\n        }\r\n        setPasswordsMatch(newPassword === confirmPassword);\r\n    };\r\n\r\n    // Confirm password change handler with validation\r\n    const handleConfirmPasswordChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n        const newConfirmPassword = event.target.value;\r\n        setConfirmPassword(newConfirmPassword);\r\n        if (password !== newConfirmPassword) {\r\n            setConfirmPasswordError(\"Passwords do not match.\");\r\n        } else {\r\n            setConfirmPasswordError('');\r\n        }\r\n        setPasswordsMatch(password === newConfirmPassword && password.length >= 8);\r\n    };\r\n\r\n    const handleSubmit = async () => {\r\n        let isValid = true;\r\n        if (!password) {\r\n            setPasswordError('This field is required.');\r\n            isValid = false;\r\n        }\r\n        if (!confirmPassword) {\r\n            setConfirmPasswordError('This field is required.');\r\n            isValid = false;\r\n        }\r\n        if (isValid && passwordsMatch && !passwordError && !confirmPasswordError) {\r\n            const encryptedPassword = encryptResetPassword(password);\r\n            const encryptedReenteredPassword = encryptResetPassword(confirmPassword);\r\n            if (encryptedPassword && encryptedReenteredPassword) {\r\n                try {\r\n                    const response = await Resetpassword(passwordLogId, encryptedPassword, encryptedReenteredPassword);\r\n                    if (response && response.Success) {\r\n                        setIsPasswordChanged(true);\r\n                    } else {\r\n                        openSnackbar(translate(response), \"error\");\r\n                    }\r\n                } catch (error) {\r\n                    console.error('Error during password reset:', error);\r\n                }\r\n            } else {\r\n                console.error('Encryption failed. Please try again.');\r\n            }\r\n        }\r\n        localStorage.clear();\r\n        document.cookie.split(\";\").forEach(cookie => {\r\n            const [name] = cookie.split(\"=\");\r\n            document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;\r\n        });\r\n        localStorage.setItem('logout-event', Date.now().toString());\r\n        sessionStorage.clear();\r\n    };\r\n    const disabled = !passwordsMatch || passwordError !== '' || confirmPasswordError !== '';\r\n    return (\r\n        <Container maxWidth=\"xs\" className=\"qadpt-resetpassword\">\r\n            <Box mb={4} className=\"qadpt-brand-logo\">\r\n                <Typography variant=\"h3\" className=\"qadpt-brand-logo-text\" gutterBottom>\r\n                    QuickAdopt\r\n                </Typography>\r\n            </Box>\r\n            <Box>\r\n                {!isPasswordChanged ? (\r\n                    <>\r\n                        <Box className=\"qadpt-welcome-message\">\r\n                            <Typography variant=\"h4\" className=\"qadpt-welcome-message-text\">\r\n                                Reset your password\r\n                            </Typography>\r\n                            <Typography\r\n                                variant=\"body2\"\r\n                                color=\"textSecondary\"\r\n                                align=\"center\"\r\n                                mb={2}\r\n                                sx={{\r\n                                    width: \"325px\",\r\n                                    height: \"38px\",\r\n                                    opacity: \"0.8\",\r\n                                    fontFamily: \"Poppins\",\r\n                                    fontSize: \"14px\",\r\n                                    fontWeight: 400,\r\n                                    lineHeight: \"19px\",\r\n                                    textAlign: \"center\",\r\n                                    color: \"#222222\",\r\n                                }}\r\n                            >\r\n                                Enter your new password below to change your password.\r\n                            </Typography>\r\n                        </Box>\r\n\r\n                        <Box className=\"qadpt-login-form\">\r\n                            <Typography className=\"qadpt-form-label\">New Password</Typography>\r\n                            <TextField\r\n                                margin=\"normal\"\r\n                                required\r\n                                fullWidth\r\n                                type={showPassword ? \"text\" : \"password\"}\r\n                                id=\"new-password\"\r\n                                name=\"new-password\"\r\n                                autoComplete=\"new-password\"\r\n                                autoFocus\r\n                                value={password}\r\n                                onChange={handlePasswordChange}\r\n                                placeholder=\"min. 8 characters\"\r\n                                className=\"qadpt-custom-input\"\r\n                                error={!!passwordError}\r\n                                InputProps={{\r\n                                    endAdornment: (\r\n                                        <InputAdornment position=\"end\">\r\n                                            <IconButton\r\n                                                aria-label=\"toggle password visibility\"\r\n                                                onClick={() => setShowPassword(!showPassword)}\r\n                                                edge=\"end\"\r\n                                            >\r\n                                                {showPassword ? <VisibilityOff /> : <Visibility />}\r\n                                            </IconButton>\r\n                                        </InputAdornment>\r\n                                    ),\r\n                                }}\r\n                            />\r\n                            {passwordError ? (\r\n                                <FormHelperText sx={{ color: 'red', fontSize: '12px' }}>\r\n                                    {passwordError}\r\n\r\n                                </FormHelperText>\r\n                            ) : \"\"}\r\n\r\n                            <Typography className=\"qadpt-form-label\">Re-enter New Password</Typography>\r\n                            <TextField\r\n                                margin=\"normal\"\r\n                                required\r\n                                fullWidth\r\n                                type={showConfirmPassword ? \"text\" : \"password\"}\r\n                                id=\"confirm-password\"\r\n                                name=\"confirm-password\"\r\n                                autoComplete=\"confirm-password\"\r\n                                autoFocus\r\n                                value={confirmPassword}\r\n                                onChange={handleConfirmPasswordChange}\r\n                                placeholder=\"Same as above\"\r\n                                className=\"qadpt-custom-input\"\r\n                                error={!!confirmPasswordError}\r\n                                //helperText={confirmPasswordError}\r\n                                InputProps={{\r\n                                    endAdornment: (\r\n                                        <InputAdornment position=\"end\">\r\n                                            <IconButton\r\n                                                aria-label=\"toggle confirm password visibility\"\r\n                                                onClick={() => setShowConfirmPassword(!showConfirmPassword)}\r\n                                                edge=\"end\"\r\n                                            >\r\n                                                {showConfirmPassword ? <VisibilityOff /> : <Visibility />}\r\n                                            </IconButton>\r\n                                        </InputAdornment>\r\n                                    ),\r\n                                }}\r\n\r\n                            />\r\n                            {confirmPasswordError ? (\r\n                                <FormHelperText sx={{ color: 'red', fontSize: '12px' }}>\r\n                                    {confirmPasswordError}\r\n                                </FormHelperText>\r\n                            ) : \"\"}\r\n\r\n                            {(password || confirmPassword) && (\r\n                                <Box className=\"qadpt-passwordhint\">\r\n                                    <Typography className=\"qadpt-passwordhint-text\">Your password must contain</Typography>\r\n                                    <Box className=\"qadpt-passwordhint-container\">\r\n                                        <CheckCircleIcon className={passwordValidations.minLength ? \"qadpt-checkicon-valid\" : \"\"} />\r\n                                        <Typography className=\"qadpt-passwordhint-item\">At least 8 characters</Typography>\r\n                                    </Box>\r\n                                    {/* <Box className=\"qadpt-passwordhint-container\">\r\n                                        <CheckCircleIcon className={passwordValidations.hasUppercase ? \"qadpt-checkicon-valid\" : \"\"} />\r\n                                        <Typography className=\"qadpt-passwordhint-item\">1 capital letter</Typography>\r\n                                    </Box>\r\n                                    <Box className=\"qadpt-passwordhint-container\">\r\n                                        <CheckCircleIcon className={passwordValidations.hasSpecialChar ? \"qadpt-checkicon-valid\" : \"\"} />\r\n                                        <Typography className=\"qadpt-passwordhint-item\">1 special character</Typography>\r\n                                    </Box>\r\n                                    <Box className=\"qadpt-passwordhint-container\">\r\n                                        <CheckCircleIcon className={passwordValidations.hasNumber ? \"qadpt-checkicon-valid\" : \"\"} />\r\n                                        <Typography className=\"qadpt-passwordhint-item\">1 number</Typography>\r\n                                    </Box> */}\r\n                                </Box>\r\n                            )}\r\n\r\n                            <Button\r\n                                type=\"button\"\r\n                                fullWidth\r\n                                variant=\"contained\"\r\n                                onClick={handleSubmit}\r\n                                disabled={!passwordsMatch || passwordError !== '' || confirmPasswordError !== ''}\r\n                                className={disabled ? \"qadpt-btn-disabled\" : \"qadpt-btn-default\"}\r\n                            >\r\n                                Reset password\r\n                            </Button>\r\n                        </Box>\r\n\r\n                        <Box mt={12} className=\"qadpt-login-footer\">\r\n                            <Typography variant=\"body2\" className=\"qadpt-footer-text\">\r\n                                <Link className=\"qadpt-footer-link\">\r\n                                    Terms of use\r\n                                </Link>{\" \"}\r\n                                |{\" \"}\r\n                                <Link className=\"qadpt-footer-link\">\r\n                                    Privacy Policy\r\n                                </Link>\r\n                            </Typography>\r\n                        </Box>\r\n                    </>\r\n                ) : (\r\n                    <Box className=\"qadpt-pwd-changed\">\r\n                        <Typography variant=\"h5\" className=\"qadpt-pwd-title\">\r\n                            Password Changed!\r\n                        </Typography>\r\n                        <Typography variant=\"body2\" color=\"textSecondary\" className=\"qadpt-changed-msg\" mt=\"10px\">\r\n                            Your password has been changed successfully.\r\n                        </Typography>\r\n                        <Button\r\n                            type=\"button\"\r\n                            variant=\"contained\"\r\n                            className=\"qadpt-btn-default\"\r\n                            href=\"https://app.quickadopt.in/Login\"\r\n                        >\r\n                            Back to QuickAdopt Platform\r\n                        </Button>\r\n                    </Box>\r\n                )}\r\n                <Box mt={12} className=\"qadpt-login-footer\">\r\n                    <Typography variant=\"body2\" className=\"qadpt-footer-text\">\r\n                        <Link href=\"/terms-of-use\" className=\"qadpt-footer-link\">\r\n                            Terms of use\r\n                        </Link>{\" \"}\r\n                        |{\" \"}\r\n                        <Link href=\"/privacy-policy\" className=\"qadpt-footer-link\">\r\n                            Privacy Policy\r\n                        </Link>\r\n                    </Typography>\r\n                </Box>\r\n            </Box>\r\n        </Container>\r\n    );\r\n};\r\n\r\nexport default ResetPassword;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,SAAS,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAEC,GAAG,EAAEC,IAAI,EAAEC,UAAU,EAAEC,cAAc,QAAQ,eAAe;AAC/G,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,aAAa,MAAM,mCAAmC;AAC7D,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,SAASC,aAAa,EAAEC,oBAAoB,QAAQ,qCAAqC;AACzF,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,uBAAuB;AACnD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/C,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACW,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC2B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACiC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACqC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGtC,QAAQ,CAAC;IAC3DuC,SAAS,EAAE,KAAK;IAChBC,YAAY,EAAE,KAAK;IACnBC,cAAc,EAAE,KAAK;IACrBC,SAAS,EAAE,KAAK;IAChBC,QAAQ,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC8C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAEjE,MAAM;IAAEgD;EAAa,CAAC,GAAGjC,WAAW,CAAC,CAAC;EACtC,MAAMkC,QAAQ,GAAGzB,QAAQ,CAACyB,QAAQ;EAClC,MAAMC,QAAQ,GAAGD,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC;EACpC,MAAMC,aAAa,GAAGF,QAAQ,CAACA,QAAQ,CAACG,MAAM,GAAG,CAAC,CAAC;EACnD,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGtC,cAAc,CAAC,CAAC;EACzC;EACA,MAAMuC,gBAAgB,GAAI3B,QAAgB,IAAK;IAC3C,MAAM4B,WAAW,GAAG;MAChBlB,SAAS,EAAEV,QAAQ,CAACwB,MAAM,IAAI,CAAC;MAC/Bb,YAAY,EAAE,OAAO,CAACkB,IAAI,CAAC7B,QAAQ,CAAC;MACpCY,cAAc,EAAE,YAAY,CAACiB,IAAI,CAAC7B,QAAQ,CAAC;MAC3Ca,SAAS,EAAE,IAAI,CAACgB,IAAI,CAAC7B,QAAQ,CAAC;MAC9Bc,QAAQ,EAAE,CAAC,IAAI,CAACe,IAAI,CAAC7B,QAAQ;IACjC,CAAC;IACDS,sBAAsB,CAACmB,WAAW,CAAC;IACnCZ,iBAAiB,CAAChB,QAAQ,KAAKE,eAAe,CAAC;IAC/C,OAAO0B,WAAW,CAAClB,SAAS,IAAIkB,WAAW,CAACjB,YAAY,IAAIiB,WAAW,CAAChB,cAAc,IAAIgB,WAAW,CAACf,SAAS,IAAIe,WAAW,CAACd,QAAQ;EAC3I,CAAC;;EAED;EACA,MAAMgB,oBAAoB,GAAIC,KAA0C,IAAK;IACzE,MAAMC,WAAW,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK;IACtCjC,WAAW,CAAC+B,WAAW,CAAC;IACxB,MAAMG,OAAO,GAAGR,gBAAgB,CAACK,WAAW,CAAC;IAC7C,IAAI,CAACG,OAAO,EAAE;MACV9B,gBAAgB,CACZ,CAACG,mBAAmB,CAACM,QAAQ,GACvB,0BAA0B,GAC1B,mHACV,CAAC;IACL,CAAC,MAAM;MACHT,gBAAgB,CAAC,EAAE,CAAC;IACxB;IACAW,iBAAiB,CAACgB,WAAW,KAAK9B,eAAe,CAAC;EACtD,CAAC;;EAED;EACA,MAAMkC,2BAA2B,GAAIL,KAA0C,IAAK;IAChF,MAAMM,kBAAkB,GAAGN,KAAK,CAACE,MAAM,CAACC,KAAK;IAC7C/B,kBAAkB,CAACkC,kBAAkB,CAAC;IACtC,IAAIrC,QAAQ,KAAKqC,kBAAkB,EAAE;MACjC9B,uBAAuB,CAAC,yBAAyB,CAAC;IACtD,CAAC,MAAM;MACHA,uBAAuB,CAAC,EAAE,CAAC;IAC/B;IACAS,iBAAiB,CAAChB,QAAQ,KAAKqC,kBAAkB,IAAIrC,QAAQ,CAACwB,MAAM,IAAI,CAAC,CAAC;EAC9E,CAAC;EAED,MAAMc,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAIH,OAAO,GAAG,IAAI;IAClB,IAAI,CAACnC,QAAQ,EAAE;MACXK,gBAAgB,CAAC,yBAAyB,CAAC;MAC3C8B,OAAO,GAAG,KAAK;IACnB;IACA,IAAI,CAACjC,eAAe,EAAE;MAClBK,uBAAuB,CAAC,yBAAyB,CAAC;MAClD4B,OAAO,GAAG,KAAK;IACnB;IACA,IAAIA,OAAO,IAAIpB,cAAc,IAAI,CAACX,aAAa,IAAI,CAACE,oBAAoB,EAAE;MACtE,MAAMiC,iBAAiB,GAAGvD,oBAAoB,CAACgB,QAAQ,CAAC;MACxD,MAAMwC,0BAA0B,GAAGxD,oBAAoB,CAACkB,eAAe,CAAC;MACxE,IAAIqC,iBAAiB,IAAIC,0BAA0B,EAAE;QACjD,IAAI;UACA,MAAMC,QAAQ,GAAG,MAAM1D,aAAa,CAACwC,aAAa,EAAEgB,iBAAiB,EAAEC,0BAA0B,CAAC;UAClG,IAAIC,QAAQ,IAAIA,QAAQ,CAACC,OAAO,EAAE;YAC9BxB,oBAAoB,CAAC,IAAI,CAAC;UAC9B,CAAC,MAAM;YACHC,YAAY,CAACO,SAAS,CAACe,QAAQ,CAAC,EAAE,OAAO,CAAC;UAC9C;QACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;UACZC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACxD;MACJ,CAAC,MAAM;QACHC,OAAO,CAACD,KAAK,CAAC,sCAAsC,CAAC;MACzD;IACJ;IACAE,YAAY,CAACC,KAAK,CAAC,CAAC;IACpBC,QAAQ,CAACC,MAAM,CAAC1B,KAAK,CAAC,GAAG,CAAC,CAAC2B,OAAO,CAACD,MAAM,IAAI;MACzC,MAAM,CAACE,IAAI,CAAC,GAAGF,MAAM,CAAC1B,KAAK,CAAC,GAAG,CAAC;MAChCyB,QAAQ,CAACC,MAAM,GAAG,GAAGE,IAAI,mDAAmD;IAChF,CAAC,CAAC;IACFL,YAAY,CAACM,OAAO,CAAC,cAAc,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC;IAC3DC,cAAc,CAACT,KAAK,CAAC,CAAC;EAC1B,CAAC;EACD,MAAMU,QAAQ,GAAG,CAACzC,cAAc,IAAIX,aAAa,KAAK,EAAE,IAAIE,oBAAoB,KAAK,EAAE;EACvF,oBACIhB,OAAA,CAAClB,SAAS;IAACqF,QAAQ,EAAC,IAAI;IAACC,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBACpDrE,OAAA,CAACd,GAAG;MAACoF,EAAE,EAAE,CAAE;MAACF,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eACpCrE,OAAA,CAACf,UAAU;QAACsF,OAAO,EAAC,IAAI;QAACH,SAAS,EAAC,uBAAuB;QAACI,YAAY;QAAAH,QAAA,EAAC;MAExE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC,eACN5E,OAAA,CAACd,GAAG;MAAAmF,QAAA,GACC,CAAC1C,iBAAiB,gBACf3B,OAAA,CAAAE,SAAA;QAAAmE,QAAA,gBACIrE,OAAA,CAACd,GAAG;UAACkF,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBAClCrE,OAAA,CAACf,UAAU;YAACsF,OAAO,EAAC,IAAI;YAACH,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAEhE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb5E,OAAA,CAACf,UAAU;YACPsF,OAAO,EAAC,OAAO;YACfM,KAAK,EAAC,eAAe;YACrBC,KAAK,EAAC,QAAQ;YACdR,EAAE,EAAE,CAAE;YACNS,EAAE,EAAE;cACAC,KAAK,EAAE,OAAO;cACdC,MAAM,EAAE,MAAM;cACdC,OAAO,EAAE,KAAK;cACdC,UAAU,EAAE,SAAS;cACrBC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE,GAAG;cACfC,UAAU,EAAE,MAAM;cAClBC,SAAS,EAAE,QAAQ;cACnBV,KAAK,EAAE;YACX,CAAE;YAAAR,QAAA,EACL;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAEN5E,OAAA,CAACd,GAAG;UAACkF,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC7BrE,OAAA,CAACf,UAAU;YAACmF,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAClE5E,OAAA,CAACjB,SAAS;YACNyG,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRC,SAAS;YACTC,IAAI,EAAErF,YAAY,GAAG,MAAM,GAAG,UAAW;YACzCsF,EAAE,EAAC,cAAc;YACjBhC,IAAI,EAAC,cAAc;YACnBiC,YAAY,EAAC,cAAc;YAC3BC,SAAS;YACTlD,KAAK,EAAElC,QAAS;YAChBqF,QAAQ,EAAEvD,oBAAqB;YAC/BwD,WAAW,EAAC,mBAAmB;YAC/B5B,SAAS,EAAC,oBAAoB;YAC9Bf,KAAK,EAAE,CAAC,CAACvC,aAAc;YACvBmF,UAAU,EAAE;cACRC,YAAY,eACRlG,OAAA,CAACX,cAAc;gBAAC8G,QAAQ,EAAC,KAAK;gBAAA9B,QAAA,eAC1BrE,OAAA,CAACZ,UAAU;kBACP,cAAW,4BAA4B;kBACvCgH,OAAO,EAAEA,CAAA,KAAM7F,eAAe,CAAC,CAACD,YAAY,CAAE;kBAC9C+F,IAAI,EAAC,KAAK;kBAAAhC,QAAA,EAET/D,YAAY,gBAAGN,OAAA,CAACT,aAAa;oBAAAkF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAG5E,OAAA,CAACV,UAAU;oBAAAmF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAExB;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,EACD9D,aAAa,gBACVd,OAAA,CAACH,cAAc;YAACkF,EAAE,EAAE;cAAEF,KAAK,EAAE,KAAK;cAAEO,QAAQ,EAAE;YAAO,CAAE;YAAAf,QAAA,EAClDvD;UAAa;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEF,CAAC,GACjB,EAAE,eAEN5E,OAAA,CAACf,UAAU;YAACmF,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAqB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC3E5E,OAAA,CAACjB,SAAS;YACNyG,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRC,SAAS;YACTC,IAAI,EAAEnF,mBAAmB,GAAG,MAAM,GAAG,UAAW;YAChDoF,EAAE,EAAC,kBAAkB;YACrBhC,IAAI,EAAC,kBAAkB;YACvBiC,YAAY,EAAC,kBAAkB;YAC/BC,SAAS;YACTlD,KAAK,EAAEhC,eAAgB;YACvBmF,QAAQ,EAAEjD,2BAA4B;YACtCkD,WAAW,EAAC,eAAe;YAC3B5B,SAAS,EAAC,oBAAoB;YAC9Bf,KAAK,EAAE,CAAC,CAACrC;YACT;YAAA;YACAiF,UAAU,EAAE;cACRC,YAAY,eACRlG,OAAA,CAACX,cAAc;gBAAC8G,QAAQ,EAAC,KAAK;gBAAA9B,QAAA,eAC1BrE,OAAA,CAACZ,UAAU;kBACP,cAAW,oCAAoC;kBAC/CgH,OAAO,EAAEA,CAAA,KAAM3F,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;kBAC5D6F,IAAI,EAAC,KAAK;kBAAAhC,QAAA,EAET7D,mBAAmB,gBAAGR,OAAA,CAACT,aAAa;oBAAAkF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAG5E,OAAA,CAACV,UAAU;oBAAAmF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAExB;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEL,CAAC,EACD5D,oBAAoB,gBACjBhB,OAAA,CAACH,cAAc;YAACkF,EAAE,EAAE;cAAEF,KAAK,EAAE,KAAK;cAAEO,QAAQ,EAAE;YAAO,CAAE;YAAAf,QAAA,EAClDrD;UAAoB;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,GACjB,EAAE,EAEL,CAAClE,QAAQ,IAAIE,eAAe,kBACzBZ,OAAA,CAACd,GAAG;YAACkF,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBAC/BrE,OAAA,CAACf,UAAU;cAACmF,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAC;YAA0B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvF5E,OAAA,CAACd,GAAG;cAACkF,SAAS,EAAC,8BAA8B;cAAAC,QAAA,gBACzCrE,OAAA,CAACR,eAAe;gBAAC4E,SAAS,EAAElD,mBAAmB,CAACE,SAAS,GAAG,uBAAuB,GAAG;cAAG;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5F5E,OAAA,CAACf,UAAU;gBAACmF,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAAqB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaL,CACR,eAED5E,OAAA,CAAChB,MAAM;YACH2G,IAAI,EAAC,QAAQ;YACbD,SAAS;YACTnB,OAAO,EAAC,WAAW;YACnB6B,OAAO,EAAEpD,YAAa;YACtBkB,QAAQ,EAAE,CAACzC,cAAc,IAAIX,aAAa,KAAK,EAAE,IAAIE,oBAAoB,KAAK,EAAG;YACjFoD,SAAS,EAAEF,QAAQ,GAAG,oBAAoB,GAAG,mBAAoB;YAAAG,QAAA,EACpE;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAEN5E,OAAA,CAACd,GAAG;UAACoH,EAAE,EAAE,EAAG;UAAClC,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACvCrE,OAAA,CAACf,UAAU;YAACsF,OAAO,EAAC,OAAO;YAACH,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACrDrE,OAAA,CAACb,IAAI;cAACiF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAEpC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAAC,GAAG,EAAC,GACX,EAAC,GAAG,eACL5E,OAAA,CAACb,IAAI;cAACiF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAEpC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC;MAAA,eACR,CAAC,gBAEH5E,OAAA,CAACd,GAAG;QAACkF,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAC9BrE,OAAA,CAACf,UAAU;UAACsF,OAAO,EAAC,IAAI;UAACH,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAErD;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb5E,OAAA,CAACf,UAAU;UAACsF,OAAO,EAAC,OAAO;UAACM,KAAK,EAAC,eAAe;UAACT,SAAS,EAAC,mBAAmB;UAACkC,EAAE,EAAC,MAAM;UAAAjC,QAAA,EAAC;QAE1F;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb5E,OAAA,CAAChB,MAAM;UACH2G,IAAI,EAAC,QAAQ;UACbpB,OAAO,EAAC,WAAW;UACnBH,SAAS,EAAC,mBAAmB;UAC7BmC,IAAI,EAAC,iCAAiC;UAAAlC,QAAA,EACzC;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACR,eACD5E,OAAA,CAACd,GAAG;QAACoH,EAAE,EAAE,EAAG;QAAClC,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eACvCrE,OAAA,CAACf,UAAU;UAACsF,OAAO,EAAC,OAAO;UAACH,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBACrDrE,OAAA,CAACb,IAAI;YAACoH,IAAI,EAAC,eAAe;YAACnC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAEzD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAAC,GAAG,EAAC,GACX,EAAC,GAAG,eACL5E,OAAA,CAACb,IAAI;YAACoH,IAAI,EAAC,iBAAiB;YAACnC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAE3D;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEpB,CAAC;AAACxE,EAAA,CAlSID,aAAa;EAAA,QACER,WAAW,EAiBHC,WAAW,EAIXE,cAAc;AAAA;AAAA0G,EAAA,GAtBrCrG,aAAa;AAoSnB,eAAeA,aAAa;AAAC,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}