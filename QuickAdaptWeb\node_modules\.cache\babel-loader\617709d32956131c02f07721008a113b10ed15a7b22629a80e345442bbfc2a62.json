{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptWeb\\\\src\\\\components\\\\login\\\\Forgotpassword.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, TextField, Button, Typography, Link, Box } from '@mui/material';\nimport { sendForgotPasswordEmail } from '../../services/ForgotPasswordService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ForgotPassword() {\n  _s();\n  const [email, setEmail] = useState('');\n  const [submitted, setSubmitted] = useState(false);\n  const [Errorfiled, setErrorField] = useState(\" \");\n  const handleSubmit = async event => {\n    event.preventDefault();\n    try {\n      const response = await sendForgotPasswordEmail(email);\n      if (response.Success) {\n        setSubmitted(true);\n      } else {\n        setErrorField(response.ErrorMessage);\n      }\n    } catch (error) {\n      console.error(\"Error sending email\", error);\n    }\n  };\n  const handleEmailChange = e => {\n    setEmail(e.target.value);\n    setErrorField('');\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"xs\",\n    sx: {\n      marginTop: \"60px\"\n    },\n    children: !submitted ? /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        gutterBottom: true,\n        sx: {\n          fontFamily: 'Syncopate',\n          fontSize: '19px',\n          fontWeight: 700,\n          lineHeight: '19.78px',\n          letterSpacing: '0.3px',\n          textAlign: 'center',\n          color: \"#5F9EA0\"\n        },\n        children: \"QuickAdopt\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          marginTop: 8,\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          sx: {\n            fontFamily: \"Poppins\",\n            fontSize: \"24px\",\n            fontWeight: \"700\",\n            lineHeight: \"36px\",\n            letterSpacing: \"0.30000001192092896px\",\n            textAlign: \"center\"\n          },\n          children: \"Reset your password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"textSecondary\",\n          align: \"center\",\n          sx: {\n            fontFamily: \"Poppins\",\n            fontSize: \"14px\",\n            fontWeight: 400,\n            lineHeight: \"19px\",\n            letterSpacing: \"0.30000001192092896px\",\n            textAlign: \"center\",\n            color: \"#222222\"\n          },\n          children: \"Enter your email address and we\\u2019ll send you instructions to reset your password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: '100%',\n            mt: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontFamily: \"Poppins\",\n              fontSize: \"16px\",\n              fontWeight: 400,\n              lineHeight: \"23px\",\n              color: \"#444444\",\n              mb: -3,\n              textAlign: \"left\",\n              marginLeft: \"12px\"\n            },\n            children: \"Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            margin: \"normal\",\n            required: true,\n            fullWidth: true,\n            id: \"email\",\n            name: \"email\",\n            autoComplete: \"email\",\n            value: email,\n            onChange: handleEmailChange,\n            autoFocus: true,\n            error: !!Errorfiled,\n            helperText: Errorfiled,\n            sx: {\n              width: \"Fill (325px)px\",\n              height: \"Hug (48px)px\",\n              padding: \"12px 0px 0px 0px\",\n              borderRadius: \"6px 0px 0px 0px\",\n              border: \"1px 0px 0px 0px\",\n              opacity: \"0px\",\n              '& .MuiOutlinedInput-notchedOutline': {\n                borderRadius: \"15px\"\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          fullWidth: true,\n          variant: \"contained\",\n          sx: {\n            width: \"Fill (325px)px\",\n            height: \"Hug (44px)px\",\n            padding: \"10px 12px 10px 12px\",\n            gap: \"4px\",\n            borderRadius: \"15px\",\n            opacity: \"0px\",\n            background: \"#5F9EA0\"\n          },\n          children: \"Continue\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          href: process.env.REACT_APP_IDS_API,\n          variant: \"body2\",\n          align: \"center\",\n          sx: {\n            marginTop: 4,\n            color: '#6BB2A1',\n            textDecoration: 'none',\n            fontFamily: \"Poppins\",\n            fontSize: \"16px\",\n            fontWeight: 400,\n            lineHeight: \"24px\",\n            textAlign: \"center\"\n          },\n          children: \"Back to login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          mt: 5,\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            align: \"center\",\n            sx: {\n              marginTop: 9\n            },\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              sx: {\n                width: \"Fill (325px)px\",\n                height: \"Hug (24px)px\",\n                gap: \"10px\",\n                opacity: \"0px\",\n                textDecoration: 'none',\n                color: '#6BB2A1',\n                marginTop: 9,\n                fontFamily: \"Poppins\",\n                fontSize: \"16px\",\n                fontWeight: 400,\n                lineHeight: \"24px\",\n                textAlign: \"center\",\n                cursor: \"pointer\"\n              },\n              children: \"Terms of use\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 33\n            }, this), \" | \", /*#__PURE__*/_jsxDEV(Link, {\n              sx: {\n                width: \"Fill (325px)px\",\n                height: \"Hug (24px)px\",\n                gap: \"10px\",\n                opacity: \"0px\",\n                textDecoration: 'none',\n                color: '#6BB2A1',\n                marginTop: 9,\n                fontFamily: \"Poppins\",\n                fontSize: \"16px\",\n                fontWeight: 400,\n                lineHeight: \"24px\",\n                textAlign: \"center\",\n                cursor: \"pointer\"\n              },\n              children: \"Privacy Policy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 58\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xs\",\n      sx: {\n        marginTop: \"135px\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          marginTop: 8,\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          sx: {\n            fontFamily: \"Poppins\",\n            fontSize: \"24px\",\n            fontWeight: 700,\n            lineHeight: \"36px\",\n            letterSpacing: \"0.30000001192092896px\",\n            textAlign: \"center\"\n          },\n          children: \"Check your email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"textSecondary\",\n          align: \"center\",\n          sx: {\n            fontFamily: \"Poppins\",\n            fontSize: \"14px\",\n            fontWeight: 400,\n            lineHeight: \"19px\",\n            letterSpacing: \"0.30000001192092896px\",\n            textAlign: \"center\"\n          },\n          children: [\"Please check your email address \", email, \" for instructions to reset your password.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"contained\",\n          sx: {\n            width: \"Fill (325px)px\",\n            height: \"Hug (44px)px\",\n            padding: \"10px 12px 10px 12px\",\n            gap: \"4px\",\n            borderRadius: \"15px\",\n            background: \"#5F9EA0\",\n            marginTop: 3\n          },\n          onClick: handleSubmit,\n          children: \"Resend Email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          mt: 5,\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            align: \"center\",\n            sx: {\n              marginTop: 9\n            },\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              sx: {\n                width: \"Fill (325px)px\",\n                height: \"Hug (24px)px\",\n                gap: \"10px\",\n                opacity: \"0px\",\n                textDecoration: 'none',\n                color: '#6BB2A1',\n                marginTop: 9,\n                fontFamily: \"Poppins\",\n                fontSize: \"16px\",\n                fontWeight: 400,\n                lineHeight: \"273px\",\n                textAlign: \"center\"\n              },\n              children: \"Terms of use\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 37\n            }, this), \" | \", /*#__PURE__*/_jsxDEV(Link, {\n              sx: {\n                width: \"Fill (325px)px\",\n                height: \"Hug (24px)px\",\n                gap: \"10px\",\n                opacity: \"0px\",\n                textDecoration: 'none',\n                color: '#6BB2A1',\n                marginTop: 9,\n                fontFamily: \"Poppins\",\n                fontSize: \"16px\",\n                fontWeight: 400,\n                lineHeight: \"273px\",\n                textAlign: \"center\"\n              },\n              children: \"Privacy Policy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 62\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 33\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 25\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 21\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 9\n  }, this);\n}\n_s(ForgotPassword, \"uqd6D3abaUw/C5i+DsfgNeE8S9E=\");\n_c = ForgotPassword;\nexport default ForgotPassword;\nvar _c;\n$RefreshReg$(_c, \"ForgotPassword\");", "map": {"version": 3, "names": ["React", "useState", "Container", "TextField", "<PERSON><PERSON>", "Typography", "Link", "Box", "sendForgotPasswordEmail", "jsxDEV", "_jsxDEV", "ForgotPassword", "_s", "email", "setEmail", "submitted", "setSubmitted", "Errorfiled", "setErrorField", "handleSubmit", "event", "preventDefault", "response", "Success", "ErrorMessage", "error", "console", "handleEmailChange", "e", "target", "value", "max<PERSON><PERSON><PERSON>", "sx", "marginTop", "children", "onSubmit", "variant", "gutterBottom", "fontFamily", "fontSize", "fontWeight", "lineHeight", "letterSpacing", "textAlign", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "flexDirection", "alignItems", "align", "width", "mt", "mb", "marginLeft", "margin", "required", "fullWidth", "id", "name", "autoComplete", "onChange", "autoFocus", "helperText", "height", "padding", "borderRadius", "border", "opacity", "type", "gap", "background", "href", "process", "env", "REACT_APP_IDS_API", "textDecoration", "cursor", "onClick", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/components/login/Forgotpassword.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Con<PERSON><PERSON>, <PERSON><PERSON>ield, Button, Typography, Link, Box } from '@mui/material';\r\nimport { sendForgotPasswordEmail } from '../../services/ForgotPasswordService';\r\n\r\nfunction ForgotPassword() {\r\n    const [email, setEmail] = useState('');\r\n    const [submitted, setSubmitted] = useState(false);\r\n    const [Errorfiled, setErrorField] = useState(\" \");\r\n    const handleSubmit = async (event: React.FormEvent) => {\r\n        event.preventDefault();\r\n        try {\r\n            const response = await sendForgotPasswordEmail(email);\r\n            if (response.Success) {\r\n                setSubmitted(true);\r\n            }else{\r\n                setErrorField(response.ErrorMessage);\r\n            }\r\n        } catch (error) {\r\n            console.error(\"Error sending email\", error);\r\n        }\r\n    };\r\n    const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        setEmail(e.target.value);\r\n        setErrorField('');\r\n    };\r\n\r\n    return (\r\n        <Container maxWidth=\"xs\" sx={{ marginTop: \"60px\"}}>\r\n            {!submitted ? (\r\n                <form onSubmit={handleSubmit}>\r\n                    <Typography\r\n                        variant=\"h5\"\r\n                        gutterBottom\r\n                        sx={{\r\n                            fontFamily: 'Syncopate',\r\n                            fontSize: '19px',\r\n                            fontWeight: 700,\r\n                            lineHeight: '19.78px',\r\n                            letterSpacing: '0.3px',\r\n                            textAlign: 'center',\r\n                            color: \"#5F9EA0\",\r\n                        }}\r\n                    >\r\n                        QuickAdopt\r\n                    </Typography>\r\n\r\n                    <Box\r\n                        sx={{\r\n                            marginTop: 8,\r\n                            display: 'flex',\r\n                            flexDirection: 'column',\r\n                            alignItems: 'center',\r\n                        }}\r\n                    >\r\n                        <Typography variant=\"h5\" gutterBottom sx={{\r\n                            fontFamily: \"Poppins\",\r\n                            fontSize: \"24px\",\r\n                            fontWeight: \"700\",\r\n                            lineHeight: \"36px\",\r\n                            letterSpacing: \"0.30000001192092896px\",\r\n                            textAlign: \"center\"\r\n                        }}>\r\n                            Reset your password\r\n                        </Typography>\r\n                        <Typography variant=\"body2\" color=\"textSecondary\" align=\"center\" sx={{\r\n                            fontFamily: \"Poppins\",\r\n                            fontSize: \"14px\",\r\n                            fontWeight: 400,\r\n                            lineHeight: \"19px\",\r\n                            letterSpacing: \"0.30000001192092896px\",\r\n                            textAlign: \"center\",\r\n                            color: \"#222222\"\r\n                        }}>\r\n                            Enter your email address and we’ll send you instructions to reset your password\r\n                        </Typography>\r\n                        <Box sx={{ width: '100%', mt: 3 }}>\r\n                            <Typography variant=\"body2\" sx={{\r\n                                fontFamily: \"Poppins\",\r\n                                fontSize: \"16px\",\r\n                                fontWeight: 400,\r\n                                lineHeight: \"23px\",\r\n                                color: \"#444444\",\r\n                                mb: -3,\r\n                                textAlign: \"left\",\r\n                                marginLeft: \"12px\"\r\n                            }}>\r\n                                Email\r\n                            </Typography>\r\n                            <TextField\r\n                                margin=\"normal\"\r\n                                required\r\n                                fullWidth\r\n                                id=\"email\"\r\n                                name=\"email\"\r\n                                autoComplete=\"email\"\r\n                                value={email}\r\n                                onChange={handleEmailChange}\r\n                                autoFocus\r\n                                error={!!Errorfiled}\r\n                                helperText={Errorfiled}\r\n                                sx={{\r\n                                    width: \"Fill (325px)px\",\r\n                                    height: \"Hug (48px)px\",\r\n                                    padding: \"12px 0px 0px 0px\",\r\n                                    borderRadius: \"6px 0px 0px 0px\",\r\n                                    border: \"1px 0px 0px 0px\",\r\n                                    opacity: \"0px\",\r\n                                    '& .MuiOutlinedInput-notchedOutline': {\r\n                                        borderRadius: \"15px\",\r\n                                    },\r\n                                }}\r\n                            />\r\n                        </Box>\r\n                        <Button\r\n                            type=\"submit\"\r\n                            fullWidth\r\n                            variant=\"contained\"\r\n                            sx={{\r\n                                width: \"Fill (325px)px\",\r\n                                height: \"Hug (44px)px\",\r\n                                padding: \"10px 12px 10px 12px\",\r\n                                gap: \"4px\",\r\n                                borderRadius: \"15px\",\r\n                                opacity: \"0px\",\r\n                                background: \"#5F9EA0\"\r\n                            }}\r\n                        >\r\n                            Continue\r\n                        </Button>\r\n\r\n                        <Link href = {process.env.REACT_APP_IDS_API} variant=\"body2\" align=\"center\" sx={{\r\n                            marginTop: 4,\r\n                            color: '#6BB2A1',\r\n                            textDecoration: 'none',\r\n                            fontFamily: \"Poppins\",\r\n                            fontSize: \"16px\",\r\n                            fontWeight: 400,\r\n                            lineHeight: \"24px\",\r\n                            textAlign: \"center\"\r\n                        }}>\r\n                            Back to login\r\n                        </Link>\r\n                        <Box mt={5}>\r\n                            <Typography variant=\"body2\" color=\"textSecondary\" align=\"center\" sx={{ marginTop: 9 }} >\r\n                                <Link sx={{\r\n                                    width: \"Fill (325px)px\",\r\n                                    height: \"Hug (24px)px\",\r\n                                    gap: \"10px\",\r\n                                    opacity: \"0px\",\r\n                                    textDecoration: 'none',\r\n                                    color: '#6BB2A1',\r\n                                    marginTop: 9,\r\n                                    fontFamily: \"Poppins\",\r\n                                    fontSize: \"16px\",\r\n                                    fontWeight: 400,\r\n                                    lineHeight: \"24px\",\r\n                                    textAlign: \"center\",\r\n                                    cursor: \"pointer\"\r\n\r\n\r\n                                }}>Terms of use</Link> | <Link sx={{\r\n                                    width: \"Fill (325px)px\",\r\n                                    height: \"Hug (24px)px\",\r\n                                    gap: \"10px\",\r\n                                    opacity: \"0px\",\r\n                                    textDecoration: 'none',\r\n                                    color: '#6BB2A1',\r\n                                    marginTop: 9,\r\n                                    fontFamily: \"Poppins\",\r\n                                    fontSize: \"16px\",\r\n                                    fontWeight: 400,\r\n                                    lineHeight: \"24px\",\r\n                                    textAlign: \"center\",\r\n                                    cursor: \"pointer\"\r\n\r\n                                }} >Privacy Policy</Link>\r\n                            </Typography>\r\n                        </Box>\r\n                    </Box>\r\n                </form>\r\n            ) : (\r\n                    <Container maxWidth=\"xs\" sx={{ marginTop: \"135px\" }}>\r\n                        <Box\r\n                            sx={{\r\n                                marginTop: 8,\r\n                                display: 'flex',\r\n                                flexDirection: 'column',\r\n                                alignItems: 'center',\r\n                            }}\r\n                        >\r\n                            <Typography variant=\"h5\" sx={{\r\n                                fontFamily: \"Poppins\",\r\n                                fontSize: \"24px\",\r\n                                fontWeight: 700,\r\n                                lineHeight: \"36px\",\r\n                                letterSpacing: \"0.30000001192092896px\",\r\n                                textAlign: \"center\",\r\n\r\n                            }}>\r\n                                Check your email\r\n                            </Typography>\r\n                            <Typography variant=\"body2\" color=\"textSecondary\" align=\"center\" sx={{\r\n                                fontFamily: \"Poppins\",\r\n                                fontSize: \"14px\",\r\n                                fontWeight: 400,\r\n                                lineHeight: \"19px\",\r\n                                letterSpacing: \"0.30000001192092896px\",\r\n                                textAlign: \"center\"\r\n                            }}>\r\n                                Please check your email address {email} for instructions to reset your password.\r\n                            </Typography>\r\n                            <Button\r\n                                fullWidth\r\n                                variant=\"contained\"\r\n                                sx={{\r\n                                    width: \"Fill (325px)px\",\r\n                                    height: \"Hug (44px)px\",\r\n                                    padding: \"10px 12px 10px 12px\",\r\n                                    gap: \"4px\",\r\n                                    borderRadius: \"15px\",\r\n                                    background: \"#5F9EA0\",\r\n                                    marginTop: 3\r\n                                }}\r\n                                onClick={handleSubmit}\r\n                            >\r\n                                Resend Email\r\n                            </Button>\r\n                            <Box mt={5}>\r\n                                <Typography variant=\"body2\" color=\"textSecondary\" align=\"center\" sx={{ marginTop: 9 }} >\r\n                                    <Link sx={{\r\n                                        width: \"Fill (325px)px\",\r\n                                        height: \"Hug (24px)px\",\r\n                                        gap: \"10px\",\r\n                                        opacity: \"0px\",\r\n                                        textDecoration: 'none',\r\n                                        color: '#6BB2A1',\r\n                                        marginTop: 9,\r\n                                        fontFamily: \"Poppins\",\r\n                                        fontSize: \"16px\",\r\n                                        fontWeight: 400,\r\n                                        lineHeight: \"273px\",\r\n                                        textAlign: \"center\"\r\n\r\n                                    }}>Terms of use</Link> | <Link sx={{\r\n                                        width: \"Fill (325px)px\",\r\n                                        height: \"Hug (24px)px\",\r\n                                        gap: \"10px\",\r\n                                        opacity: \"0px\",\r\n                                        textDecoration: 'none',\r\n                                        color: '#6BB2A1',\r\n                                        marginTop: 9,\r\n                                        fontFamily: \"Poppins\",\r\n                                        fontSize: \"16px\",\r\n                                        fontWeight: 400,\r\n                                        lineHeight: \"273px\",\r\n                                        textAlign: \"center\"\r\n\r\n                                    }} >Privacy Policy</Link>\r\n                                </Typography>\r\n                            </Box>\r\n                        </Box>\r\n                    </Container>\r\n            )}\r\n        </Container >\r\n    );\r\n}\r\n\r\nexport default ForgotPassword;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,SAAS,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAEC,IAAI,EAAEC,GAAG,QAAQ,eAAe;AACnF,SAASC,uBAAuB,QAAQ,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/E,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,GAAG,CAAC;EACjD,MAAMkB,YAAY,GAAG,MAAOC,KAAsB,IAAK;IACnDA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtB,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMd,uBAAuB,CAACK,KAAK,CAAC;MACrD,IAAIS,QAAQ,CAACC,OAAO,EAAE;QAClBP,YAAY,CAAC,IAAI,CAAC;MACtB,CAAC,MAAI;QACDE,aAAa,CAACI,QAAQ,CAACE,YAAY,CAAC;MACxC;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAC/C;EACJ,CAAC;EACD,MAAME,iBAAiB,GAAIC,CAAsC,IAAK;IAClEd,QAAQ,CAACc,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;IACxBZ,aAAa,CAAC,EAAE,CAAC;EACrB,CAAC;EAED,oBACIR,OAAA,CAACR,SAAS;IAAC6B,QAAQ,EAAC,IAAI;IAACC,EAAE,EAAE;MAAEC,SAAS,EAAE;IAAM,CAAE;IAAAC,QAAA,EAC7C,CAACnB,SAAS,gBACPL,OAAA;MAAMyB,QAAQ,EAAEhB,YAAa;MAAAe,QAAA,gBACzBxB,OAAA,CAACL,UAAU;QACP+B,OAAO,EAAC,IAAI;QACZC,YAAY;QACZL,EAAE,EAAE;UACAM,UAAU,EAAE,WAAW;UACvBC,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,GAAG;UACfC,UAAU,EAAE,SAAS;UACrBC,aAAa,EAAE,OAAO;UACtBC,SAAS,EAAE,QAAQ;UACnBC,KAAK,EAAE;QACX,CAAE;QAAAV,QAAA,EACL;MAED;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbtC,OAAA,CAACH,GAAG;QACAyB,EAAE,EAAE;UACAC,SAAS,EAAE,CAAC;UACZgB,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBC,UAAU,EAAE;QAChB,CAAE;QAAAjB,QAAA,gBAEFxB,OAAA,CAACL,UAAU;UAAC+B,OAAO,EAAC,IAAI;UAACC,YAAY;UAACL,EAAE,EAAE;YACtCM,UAAU,EAAE,SAAS;YACrBC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjBC,UAAU,EAAE,MAAM;YAClBC,aAAa,EAAE,uBAAuB;YACtCC,SAAS,EAAE;UACf,CAAE;UAAAT,QAAA,EAAC;QAEH;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbtC,OAAA,CAACL,UAAU;UAAC+B,OAAO,EAAC,OAAO;UAACQ,KAAK,EAAC,eAAe;UAACQ,KAAK,EAAC,QAAQ;UAACpB,EAAE,EAAE;YACjEM,UAAU,EAAE,SAAS;YACrBC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,GAAG;YACfC,UAAU,EAAE,MAAM;YAClBC,aAAa,EAAE,uBAAuB;YACtCC,SAAS,EAAE,QAAQ;YACnBC,KAAK,EAAE;UACX,CAAE;UAAAV,QAAA,EAAC;QAEH;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbtC,OAAA,CAACH,GAAG;UAACyB,EAAE,EAAE;YAAEqB,KAAK,EAAE,MAAM;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAApB,QAAA,gBAC9BxB,OAAA,CAACL,UAAU;YAAC+B,OAAO,EAAC,OAAO;YAACJ,EAAE,EAAE;cAC5BM,UAAU,EAAE,SAAS;cACrBC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE,GAAG;cACfC,UAAU,EAAE,MAAM;cAClBG,KAAK,EAAE,SAAS;cAChBW,EAAE,EAAE,CAAC,CAAC;cACNZ,SAAS,EAAE,MAAM;cACjBa,UAAU,EAAE;YAChB,CAAE;YAAAtB,QAAA,EAAC;UAEH;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbtC,OAAA,CAACP,SAAS;YACNsD,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRC,SAAS;YACTC,EAAE,EAAC,OAAO;YACVC,IAAI,EAAC,OAAO;YACZC,YAAY,EAAC,OAAO;YACpBhC,KAAK,EAAEjB,KAAM;YACbkD,QAAQ,EAAEpC,iBAAkB;YAC5BqC,SAAS;YACTvC,KAAK,EAAE,CAAC,CAACR,UAAW;YACpBgD,UAAU,EAAEhD,UAAW;YACvBe,EAAE,EAAE;cACAqB,KAAK,EAAE,gBAAgB;cACvBa,MAAM,EAAE,cAAc;cACtBC,OAAO,EAAE,kBAAkB;cAC3BC,YAAY,EAAE,iBAAiB;cAC/BC,MAAM,EAAE,iBAAiB;cACzBC,OAAO,EAAE,KAAK;cACd,oCAAoC,EAAE;gBAClCF,YAAY,EAAE;cAClB;YACJ;UAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNtC,OAAA,CAACN,MAAM;UACHmE,IAAI,EAAC,QAAQ;UACbZ,SAAS;UACTvB,OAAO,EAAC,WAAW;UACnBJ,EAAE,EAAE;YACAqB,KAAK,EAAE,gBAAgB;YACvBa,MAAM,EAAE,cAAc;YACtBC,OAAO,EAAE,qBAAqB;YAC9BK,GAAG,EAAE,KAAK;YACVJ,YAAY,EAAE,MAAM;YACpBE,OAAO,EAAE,KAAK;YACdG,UAAU,EAAE;UAChB,CAAE;UAAAvC,QAAA,EACL;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETtC,OAAA,CAACJ,IAAI;UAACoE,IAAI,EAAIC,OAAO,CAACC,GAAG,CAACC,iBAAkB;UAACzC,OAAO,EAAC,OAAO;UAACgB,KAAK,EAAC,QAAQ;UAACpB,EAAE,EAAE;YAC5EC,SAAS,EAAE,CAAC;YACZW,KAAK,EAAE,SAAS;YAChBkC,cAAc,EAAE,MAAM;YACtBxC,UAAU,EAAE,SAAS;YACrBC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,GAAG;YACfC,UAAU,EAAE,MAAM;YAClBE,SAAS,EAAE;UACf,CAAE;UAAAT,QAAA,EAAC;QAEH;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPtC,OAAA,CAACH,GAAG;UAAC+C,EAAE,EAAE,CAAE;UAAApB,QAAA,eACPxB,OAAA,CAACL,UAAU;YAAC+B,OAAO,EAAC,OAAO;YAACQ,KAAK,EAAC,eAAe;YAACQ,KAAK,EAAC,QAAQ;YAACpB,EAAE,EAAE;cAAEC,SAAS,EAAE;YAAE,CAAE;YAAAC,QAAA,gBAClFxB,OAAA,CAACJ,IAAI;cAAC0B,EAAE,EAAE;gBACNqB,KAAK,EAAE,gBAAgB;gBACvBa,MAAM,EAAE,cAAc;gBACtBM,GAAG,EAAE,MAAM;gBACXF,OAAO,EAAE,KAAK;gBACdQ,cAAc,EAAE,MAAM;gBACtBlC,KAAK,EAAE,SAAS;gBAChBX,SAAS,EAAE,CAAC;gBACZK,UAAU,EAAE,SAAS;gBACrBC,QAAQ,EAAE,MAAM;gBAChBC,UAAU,EAAE,GAAG;gBACfC,UAAU,EAAE,MAAM;gBAClBE,SAAS,EAAE,QAAQ;gBACnBoC,MAAM,EAAE;cAGZ,CAAE;cAAA7C,QAAA,EAAC;YAAY;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,OAAG,eAAAtC,OAAA,CAACJ,IAAI;cAAC0B,EAAE,EAAE;gBAC/BqB,KAAK,EAAE,gBAAgB;gBACvBa,MAAM,EAAE,cAAc;gBACtBM,GAAG,EAAE,MAAM;gBACXF,OAAO,EAAE,KAAK;gBACdQ,cAAc,EAAE,MAAM;gBACtBlC,KAAK,EAAE,SAAS;gBAChBX,SAAS,EAAE,CAAC;gBACZK,UAAU,EAAE,SAAS;gBACrBC,QAAQ,EAAE,MAAM;gBAChBC,UAAU,EAAE,GAAG;gBACfC,UAAU,EAAE,MAAM;gBAClBE,SAAS,EAAE,QAAQ;gBACnBoC,MAAM,EAAE;cAEZ,CAAE;cAAA7C,QAAA,EAAE;YAAc;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,gBAEHtC,OAAA,CAACR,SAAS;MAAC6B,QAAQ,EAAC,IAAI;MAACC,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAQ,CAAE;MAAAC,QAAA,eAChDxB,OAAA,CAACH,GAAG;QACAyB,EAAE,EAAE;UACAC,SAAS,EAAE,CAAC;UACZgB,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBC,UAAU,EAAE;QAChB,CAAE;QAAAjB,QAAA,gBAEFxB,OAAA,CAACL,UAAU;UAAC+B,OAAO,EAAC,IAAI;UAACJ,EAAE,EAAE;YACzBM,UAAU,EAAE,SAAS;YACrBC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,GAAG;YACfC,UAAU,EAAE,MAAM;YAClBC,aAAa,EAAE,uBAAuB;YACtCC,SAAS,EAAE;UAEf,CAAE;UAAAT,QAAA,EAAC;QAEH;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbtC,OAAA,CAACL,UAAU;UAAC+B,OAAO,EAAC,OAAO;UAACQ,KAAK,EAAC,eAAe;UAACQ,KAAK,EAAC,QAAQ;UAACpB,EAAE,EAAE;YACjEM,UAAU,EAAE,SAAS;YACrBC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,GAAG;YACfC,UAAU,EAAE,MAAM;YAClBC,aAAa,EAAE,uBAAuB;YACtCC,SAAS,EAAE;UACf,CAAE;UAAAT,QAAA,GAAC,kCACiC,EAACrB,KAAK,EAAC,2CAC3C;QAAA;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbtC,OAAA,CAACN,MAAM;UACHuD,SAAS;UACTvB,OAAO,EAAC,WAAW;UACnBJ,EAAE,EAAE;YACAqB,KAAK,EAAE,gBAAgB;YACvBa,MAAM,EAAE,cAAc;YACtBC,OAAO,EAAE,qBAAqB;YAC9BK,GAAG,EAAE,KAAK;YACVJ,YAAY,EAAE,MAAM;YACpBK,UAAU,EAAE,SAAS;YACrBxC,SAAS,EAAE;UACf,CAAE;UACF+C,OAAO,EAAE7D,YAAa;UAAAe,QAAA,EACzB;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtC,OAAA,CAACH,GAAG;UAAC+C,EAAE,EAAE,CAAE;UAAApB,QAAA,eACPxB,OAAA,CAACL,UAAU;YAAC+B,OAAO,EAAC,OAAO;YAACQ,KAAK,EAAC,eAAe;YAACQ,KAAK,EAAC,QAAQ;YAACpB,EAAE,EAAE;cAAEC,SAAS,EAAE;YAAE,CAAE;YAAAC,QAAA,gBAClFxB,OAAA,CAACJ,IAAI;cAAC0B,EAAE,EAAE;gBACNqB,KAAK,EAAE,gBAAgB;gBACvBa,MAAM,EAAE,cAAc;gBACtBM,GAAG,EAAE,MAAM;gBACXF,OAAO,EAAE,KAAK;gBACdQ,cAAc,EAAE,MAAM;gBACtBlC,KAAK,EAAE,SAAS;gBAChBX,SAAS,EAAE,CAAC;gBACZK,UAAU,EAAE,SAAS;gBACrBC,QAAQ,EAAE,MAAM;gBAChBC,UAAU,EAAE,GAAG;gBACfC,UAAU,EAAE,OAAO;gBACnBE,SAAS,EAAE;cAEf,CAAE;cAAAT,QAAA,EAAC;YAAY;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,OAAG,eAAAtC,OAAA,CAACJ,IAAI;cAAC0B,EAAE,EAAE;gBAC/BqB,KAAK,EAAE,gBAAgB;gBACvBa,MAAM,EAAE,cAAc;gBACtBM,GAAG,EAAE,MAAM;gBACXF,OAAO,EAAE,KAAK;gBACdQ,cAAc,EAAE,MAAM;gBACtBlC,KAAK,EAAE,SAAS;gBAChBX,SAAS,EAAE,CAAC;gBACZK,UAAU,EAAE,SAAS;gBACrBC,QAAQ,EAAE,MAAM;gBAChBC,UAAU,EAAE,GAAG;gBACfC,UAAU,EAAE,OAAO;gBACnBE,SAAS,EAAE;cAEf,CAAE;cAAAT,QAAA,EAAE;YAAc;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAClB;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAErB;AAACpC,EAAA,CArQQD,cAAc;AAAAsE,EAAA,GAAdtE,cAAc;AAuQvB,eAAeA,cAAc;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}