// axiosInterceptor.ts
import axios from 'axios';
import userManager from '../components/auth/UseAuth';
import { useNavigate } from "react-router-dom";
import { Alert } from '@mui/material';


export const adminUrl = process.env.REACT_APP_ADMIN_API;
export const userUrl = process.env.REACT_APP_USER_API;
export const idsUrl = process.env.REACT_APP_IDS_API;

export type JToken = any;

const adminApiService = axios.create({    
  baseURL: adminUrl, 
});

const userApiService = axios.create({    
  baseURL: userUrl,  
});

const idsApiService = axios.create({
  baseURL:idsUrl,
})

idsApiService.interceptors.request.use(
  async (config) => {
    const user = await userManager.getUser();
    const token = user?.access_token || localStorage.getItem("access_token");
        if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

adminApiService.interceptors.request.use(
  async (config) => {
    const user = await userManager.getUser();
    const token = user?.access_token || localStorage.getItem("access_token");
        if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);


userApiService.interceptors.request.use(
  async (config) => {
    const user = await userManager.getUser();
    const token = user?.access_token || localStorage.getItem("access_token");
        if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

const SESSION_EXPIRED_KEY = 'session_expired_message';

export const setupInterceptors = (
  navigate: ReturnType<typeof useNavigate>,
  showSnackbar: (message: string, severity?: 'success' | 'error' | 'warning' | 'info') => void
) => {
  const handleError = (error: any) => {
    if (error.response && (error.response.status === 401 || error.response.status === 403)) {
      // Check if we're in auto-login flow - don't clear tokens during auto-login
      const urlParams = new URLSearchParams(window.location.search);
      const hasAutoLoginParams = urlParams.has('token') && urlParams.has('user_id') && urlParams.has('org_id');
      const isAutoLoginCompleted = sessionStorage.getItem('autoLoginCompleted') === 'true';
      const isOnLoginPage = window.location.pathname.includes('/login');

      // Also check if we have valid userInfo indicating a recent auto-login
      const userInfo = localStorage.getItem('userInfo');
      const hasValidUserInfo = userInfo && userInfo !== '{}' && userInfo !== 'null';
      let hasAutoLoginUserData = false;

      if (hasValidUserInfo) {
        try {
          const parsedUserInfo = JSON.parse(userInfo);
          hasAutoLoginUserData = parsedUserInfo['user'] && parsedUserInfo['oidc-info'];
        } catch (e) {
          // Ignore parse errors
        }
      }

      // Check if this is a free trial token
      const isFreeTrialToken = sessionStorage.getItem('isFreeTrialToken') === 'true';

      // Don't clear tokens if we're in auto-login flow, just completed it, have auto-login user data, or using free trial token
      if (hasAutoLoginParams || isAutoLoginCompleted || hasAutoLoginUserData || isFreeTrialToken) {

        return Promise.reject(error);
      }

      // Set session expired flag before navigation
      sessionStorage.setItem(SESSION_EXPIRED_KEY, 'true');

      // Show immediate feedback
      showSnackbar('Session has expired', 'error');

      // Clear user data
      localStorage.removeItem('access_token');
      localStorage.removeItem('userInfo');

      // Navigate to login page
      navigate('/login');
    }
    return Promise.reject(error);
  };

  idsApiService.interceptors.response.use(
    (response) => response,
    handleError
  );

  adminApiService.interceptors.response.use(
    (response) => response,
    handleError
  );

  userApiService.interceptors.response.use(
    (response) => response,
    handleError
  );
};

// Helper function to check and clear session expired message
export const checkSessionExpired = (): boolean => {
  const sessionExpired = sessionStorage.getItem(SESSION_EXPIRED_KEY) === 'true';
  if (sessionExpired) {
    sessionStorage.removeItem(SESSION_EXPIRED_KEY);
  }
  return sessionExpired;
};

export { userApiService, adminApiService, idsApiService };