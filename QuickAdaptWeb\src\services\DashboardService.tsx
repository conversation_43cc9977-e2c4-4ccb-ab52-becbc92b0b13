import { adminApiService } from "./APIService";

// Interface for the API response
export interface FeedbackAnalyticsResponse {
  Feedback: {
    Excellent: number;
    Good: number;
    Average: number;
    Poor: number;
    VeryPoor: number;
  };
  Percentage: {
    Positive: number;
    Neutral: number;
    Negative: number;
  };
  Trend: {
    [key: string]: {
      TotalCount: number;
      TrendIndicator: "stable" | "increase" | "decrease";
      ChangePercentage: number;
    };
  };
}

// Interface for the payload
export interface FeedbackAnalyticsPayload {
  AccountId: string;
  Days: number;
}

/**
 * Fetches feedback analytics data for dashboard
 * @param payload - The request payload containing AccountId and Days
 * @returns Promise<FeedbackAnalyticsResponse | null>
 */
export const getFeedbackAnalytics = async (
  payload: FeedbackAnalyticsPayload
): Promise<FeedbackAnalyticsResponse | null> => {
  try {
    // Check cache first
    const cachedData = getFromCache(feedbackCache, payload.AccountId, payload.Days);
    if (cachedData) {
      return cachedData;
    }

    const response = await adminApiService.get<FeedbackAnalyticsResponse>(
      "/Feedback/GetFeedbackAnalytics",
      {
        params: {
          accountId: payload.AccountId,
          days: payload.Days
        }
      }
    );

    // Update cache
    setInCache(feedbackCache, payload.AccountId, payload.Days, response.data);

    return response.data;
  } catch (error) {
    console.error("Error fetching feedback analytics:", error);
    throw error;
  }
};

/**
 * Helper function to get AccountId from localStorage
 * @returns string | null
 */
export const getCurrentAccountId = (): string | null => {
  return localStorage.getItem("CurrentAccountId");
};

/**
 * Fetches feedback analytics with AccountId from localStorage
 * @param days - Number of days for the analytics (7, 30, 90, or 365)
 * @returns Promise<FeedbackAnalyticsResponse | null>
 */
export const getFeedbackAnalyticsWithStoredAccount = async (
  days: number = 7
): Promise<FeedbackAnalyticsResponse | null> => {
  const accountId = getCurrentAccountId();
  
  if (!accountId) {
    console.error("No AccountId found in localStorage");
    return null;
  }

  const payload: FeedbackAnalyticsPayload = {
    AccountId: accountId,
    Days: days
  };

  return getFeedbackAnalytics(payload);
};

// Cache interfaces
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  accountId: string;
  days: number;
}

// Cache storage
const feedbackCache = new Map<string, CacheEntry<FeedbackAnalyticsResponse>>();
const guideCache = new Map<string, CacheEntry<GuideAnalyticsResponse>>();

// Cache configuration
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Cache utility functions
function generateCacheKey(accountId: string, days: number): string {
  return `${accountId}_${days}`;
}

function isCacheValid<T>(entry: CacheEntry<T>): boolean {
  return (Date.now() - entry.timestamp) < CACHE_DURATION;
}

function getFromCache<T>(cache: Map<string, CacheEntry<T>>, accountId: string, days: number): T | null {
  const key = generateCacheKey(accountId, days);
  const entry = cache.get(key);
  if (entry && isCacheValid(entry)) {
    return entry.data;
  }
  return null;
}

function setInCache<T>(cache: Map<string, CacheEntry<T>>, accountId: string, days: number, data: T): void {
  const key = generateCacheKey(accountId, days);
  cache.set(key, {
    data,
    timestamp: Date.now(),
    accountId,
    days
  });
}

// Interface for Guide Analytics API response
export interface GuideTypeData {
  Count: number;
  Active: number;
  Draft: number;
  InActive: number;
  Trend: number;
}

export interface GuideAnalyticsResponse {
  GuideTypes: {
    Hotspots: GuideTypeData;
    Tooltips: GuideTypeData;
    Checklists: GuideTypeData;
    Announcements: GuideTypeData;
    Banners: GuideTypeData;
    Tours: GuideTypeData;
  };
  GuideStatistics: {
    Hotspots: number;
    Tooltips: number;
    Checklists: number;
    Announcements: number;
    Banners: number;
    Tours: number;
  };
  CompletionStats: {
    CompletedGuides: number;
    TotalGuides: number;
    CompletionPercentage: number;
  };
  TopGuides: {
    GuideId: string;
    Name: string;
    GuideType: string;
    GuideStatus: string;
    Visited: boolean;
    CreatedDate: string;
  }[];
}

// Interface for Guide Analytics payload
export interface GuideAnalyticsPayload {
  AccountId: string;
  Days: number;
}

/**
 * Fetches guide analytics data for dashboard
 * @param payload - The request payload containing AccountId and Days
 * @returns Promise<GuideAnalyticsResponse | null>
 */
export const getGuideAnalytics = async (
  payload: GuideAnalyticsPayload
): Promise<GuideAnalyticsResponse | null> => {
  try {
    // Check cache first
    const cachedData = getFromCache(guideCache, payload.AccountId, payload.Days);
    if (cachedData) {
      return cachedData;
    }

    const response = await adminApiService.get<GuideAnalyticsResponse>(
      "/Dashboard/GetGuideAnalytics",
      {
        params: {
          accountId: payload.AccountId,
          days: payload.Days
        }
      }
    );

    // Update cache
    setInCache(guideCache, payload.AccountId, payload.Days, response.data);

    return response.data;
  } catch (error) {
    console.error("Error fetching guide analytics:", error);
    throw error;
  }
};

/**
 * Fetches guide analytics with AccountId from localStorage
 * @param days - Number of days for the analytics (7, 30, 90, or 365)
 * @returns Promise<GuideAnalyticsResponse | null>
 */
export const getGuideAnalyticsWithStoredAccount = async (
  days: number = 7
): Promise<GuideAnalyticsResponse | null> => {
  const accountId = getCurrentAccountId();
  
  if (!accountId) {
    console.error("No AccountId found in localStorage");
    return null;
  }

  const payload: GuideAnalyticsPayload = {
    AccountId: accountId,
    Days: days
  };

  return getGuideAnalytics(payload);
};
