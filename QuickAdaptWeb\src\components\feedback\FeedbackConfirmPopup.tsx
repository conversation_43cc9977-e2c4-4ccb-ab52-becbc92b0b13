import React from 'react';
import { <PERSON>, <PERSON>po<PERSON>, <PERSON>ton, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { erroricon } from "../../assets/icons/icons"; 

// Define the types for the props
interface ConfirmPopupProps {
  onClose: () => void;
  onOk: () => void;
  setRating: any;
  setTextInput: any;
  setFiles: any;
  title: string;
    description: string;
  descriptionTwo: string;
  setError: any;
  setFileError: any;
  button1: string;
  button2: string;
}

const ConfirmPopup: React.FC<ConfirmPopupProps> = ({ onClose, onOk, setRating, setTextInput, setFiles, title, description, descriptionTwo, setError, setFileError, button1, button2 }) => {
  const handleClose = () =>
  {
    setRating(null);
    setTextInput('');
    onClose();
    setFiles([]);
    setError(''); 
    setFileError('');
  }
  return (
    <Dialog
      open
      onClose={(event, reason) => {
        if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
          onClose(); // Only allow closing with the "Cancel" button
        }
      }}
      PaperProps={{
        sx: {
          width: '333px',
          height: '251px',
          borderRadius: '15px',
          padding: '20px',
          textAlign: 'center',
          boxSizing: 'border-box',
          position: 'relative'
        },
      }}
    >
      <Box
        component="img"
        src={erroricon}
        alt="Error Icon"
        sx={{
          width: '44px',
          height: '44px',
          display: 'block',
          margin: '0 auto',
          marginTop: '10px',
        }}
      />
      <DialogTitle sx={{ paddingTop: '10px' }}>
        <Typography
          sx={{
            fontFamily: 'Poppins',
            fontSize: '18px',
            fontWeight: 'bold',
            lineHeight: '30px',
            textAlign: 'center',
          }}
        >
          {title}
        </Typography>
      </DialogTitle>
      <DialogContent sx={{ paddingTop: '12px', overflow: 'hidden' }}>
        <Typography
          sx={{
            fontFamily: 'Poppins',
            fontSize: '14px',
            fontWeight: 200,
            lineHeight: '20px',
            textAlign: 'center',
            whiteSpace: 'nowrap', 
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
        >
          {description}
        </Typography>
          </DialogContent>
          <DialogContent sx={{ paddingTop: '12px', overflow: 'hidden',marginTop:'-10px' }}>
        <Typography
          sx={{
            fontFamily: 'Poppins',
            fontSize: '14px',
            fontWeight: 200,
            lineHeight: '20px',
            textAlign: 'center',
            whiteSpace: 'nowrap', 
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
        >
          {descriptionTwo}
        </Typography>
      </DialogContent>
      <DialogActions sx={{ justifyContent: 'center' }} className='confirm-actions'>
        <Button
          onClick={handleClose}
          sx={{
            width: '106px',
            height: '37px',
            padding: '6px 12px',
            gap: '10px',
            borderRadius: '11px',
            border: '1px solid rgba(95, 158, 160, 1)', 
            fontFamily: 'Poppins',
            fontSize: '16px',
            fontWeight: 200,
            lineHeight: '24px',
            textAlign: 'left',
            color: 'rgba(95, 158, 160, 1)',
              backgroundColor: 'transparent',
              textTransform: 'none', 
          }}
        >
          {button1}
        </Button>
        <Button
          onClick={onOk}
          
          sx={{
            width: '106px',
            height: '37px',
            padding: '6px 12px',
            gap: '4px',
              borderRadius: '11px',
              border: '1px solid rgba(95, 158, 160, 1)',
            fontFamily: 'Poppins',
            fontSize: '16px',
            fontWeight: 200,
            lineHeight: '24px',
            textAlign: 'left',
              color  : 'white',
              backgroundColor: 'rgba(95, 158, 160, 1)',
              textTransform: 'none', 
          }}
        >
          {button2}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ConfirmPopup;
