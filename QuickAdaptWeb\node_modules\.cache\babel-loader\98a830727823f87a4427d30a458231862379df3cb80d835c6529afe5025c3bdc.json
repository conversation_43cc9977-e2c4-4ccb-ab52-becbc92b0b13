{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptWeb\\\\src\\\\components\\\\organization\\\\OrganizationEdit.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { adminUrl } from \"../../services/APIService\";\nimport { Alert, FormControlLabel, Snackbar, Switch } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EditOrganization = props => {\n  _s();\n  const {\n    modelsData,\n    setModelsData,\n    showEditPopup,\n    setShowEditPopup,\n    OrganizationId,\n    sortModel,\n    filters,\n    setLoading,\n    setModels,\n    models,\n    handleClose,\n    skip,\n    top,\n    setTotalcount,\n    updateOrganizationDetails\n  } = props;\n  const [userDetails, setUserDetails] = useState({\n    OrganizationId: \"\",\n    Name: \"\",\n    // TimeZone: \"Asia/Kolkata\", // default value\n    // DateFormat: \"dd-MM-yyyy\",\n    // Logo: \"\",\n    Status: \"\",\n    CreatedDate: \"\",\n    UpdatedDate: \"\",\n    AuthorizationType: \"\",\n    IsActive: \"\",\n    Rtl: false,\n    TimeFormat: \"\",\n    ThemeId: \"\",\n    Type: \"\",\n    OrganizationPlanId: \"\",\n    OrganizationPlan: null,\n    Plan: \"\"\n  });\n  const [errors, setErrors] = useState({\n    Name: \"\",\n    Logo: \"\",\n    TimeZone: \"\",\n    DateFormat: \"\",\n    Type: \"\",\n    Rtl: \"\"\n  });\n  const [initialValues, setInitialValues] = useState({\n    ...userDetails\n  });\n  const [snackbarOpen, setSnackbarOpen] = useState(false);\n  const [snackbarMessage, setSnackbarMessage] = useState(\"\");\n  const [snackbarSeverity, setSnackbarSeverity] = useState(\"success\");\n  const handleTimezoneChange = event => {\n    setUserDetails(prevDetails => ({\n      ...prevDetails,\n      TimeZone: event.target.value\n    }));\n  };\n  const handleFileChange = event => {\n    if (event.target.files && event.target.files[0]) {\n      const file = event.target.files[0];\n      setUserDetails(prevDetails => ({\n        ...prevDetails,\n        Logo: file.name\n      }));\n    }\n  };\n  const handleSnackbarClose = () => {\n    setSnackbarOpen(false);\n  };\n  useEffect(() => {\n    const checkNameUniqueness = async () => {\n      if (userDetails.Name.trim() === \"\") {\n        setErrors(prevErrors => ({\n          ...prevErrors,\n          Name: \"\"\n        }));\n        return;\n      }\n      const isNameUnique = modelsData.every(org => org.Name !== userDetails.Name || org.OrganizationId === userDetails.OrganizationId);\n      if (!isNameUnique) {\n        setErrors(prevErrors => ({\n          ...prevErrors,\n          Name: 'Organization Name already exists'\n        }));\n      } else {\n        setErrors(prevErrors => ({\n          ...prevErrors,\n          Name: \"\"\n        }));\n      }\n    };\n    if (userDetails.Name !== \"\") {\n      checkNameUniqueness();\n    }\n  }, [userDetails.Name, modelsData, models, userDetails.OrganizationId]);\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setUserDetails(prevDetails => ({\n      ...prevDetails,\n      [name]: value\n    }));\n\n    // Perform validation on change\n    validateField(name, value);\n  };\n  const handleBlur = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    validateField(name, value);\n  };\n  const validateField = (name, value) => {\n    let isValid = true;\n    let newErrors = {\n      ...errors\n    };\n    switch (name) {\n      case \"Name\":\n        if (!value) {\n          newErrors.Name = \"Organization Name is required\";\n          isValid = false;\n        } else if (value.length < 5) {\n          newErrors.Name = \"Organization Name must be at least 5 characters\";\n          isValid = false;\n        } else if (value.length > 50) {\n          newErrors.Name = \"Organization Name must be at most 50 characters\";\n          isValid = false;\n        } else if (!/^[a-zA-Z0-9 ]+$/.test(value)) {\n          newErrors.Name = \"Organization Name can only contain letters, numbers, and spaces\";\n          isValid = false;\n        } else if (!checkNameUniqueness(value, userDetails.OrganizationId)) {\n          newErrors.Name = 'Organization Name already exists';\n          isValid = false;\n        } else {\n          newErrors.Name = \"\";\n        }\n        break;\n      case \"Logo\":\n        if (!value) {\n          newErrors.Logo = \"Logo is required\";\n          isValid = false;\n        } else {\n          newErrors.Logo = \"\";\n        }\n        break;\n      case \"TimeZone\":\n        if (!value) {\n          newErrors.TimeZone = \"Timezone is required\";\n          isValid = false;\n        } else {\n          newErrors.TimeZone = \"\";\n        }\n        break;\n      case \"DateFormat\":\n        if (!value) {\n          newErrors.DateFormat = \"Date Format is required\";\n          isValid = false;\n        } else {\n          newErrors.DateFormat = \"\";\n        }\n        break;\n      case \"Type\":\n        if (!value) {\n          newErrors.Type = \"Type is required\";\n          isValid = false;\n        } else {\n          newErrors.Type = \"\";\n        }\n        break;\n      default:\n        break;\n    }\n    setErrors(newErrors);\n    return isValid;\n  };\n  const validateFields = () => {\n    let isValid = true;\n    for (const [name, value] of Object.entries(userDetails)) {\n      if (!validateField(name, value)) {\n        isValid = false;\n      }\n    }\n    return isValid;\n  };\n  const checkNameUniqueness = (name, currentOrgId) => {\n    const isNameUnique = modelsData.every(org => org.Name !== name || org.OrganizationId === currentOrgId);\n    return isNameUnique;\n  };\n  const isFormChanged = () => {\n    return JSON.stringify(userDetails) !== JSON.stringify(initialValues);\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (validateFields()) {\n      try {\n        updateOrganizationDetails(userDetails);\n      } catch (error) {\n        console.error(\"Failed to update organization:\", error);\n        setSnackbarMessage(\"Failed to update organization\");\n        setSnackbarSeverity(\"error\");\n        setSnackbarOpen(true);\n      }\n    } else {\n      setSnackbarMessage(\"Please correct the errors in the form\");\n      setSnackbarSeverity(\"error\");\n      setSnackbarOpen(true);\n    }\n  };\n  useEffect(() => {\n    if (showEditPopup) {\n      fetchOrganizationDetails(OrganizationId);\n    }\n  }, [showEditPopup, OrganizationId]);\n  const fetchOrganizationDetails = async id => {\n    try {\n      const token = localStorage.getItem('access_token');\n      const response = await fetch(`${adminUrl}/Organization/GetOrganizationById?organizationId=${id}`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        throw new Error(\"Network response was not ok\");\n      }\n      const data = await response.json();\n      setUserDetails({\n        OrganizationId: data.OrganizationId,\n        Name: data.Name,\n        // TimeZone: data.TimeZone,\n        // DateFormat: data.DateFormat,\n        // Logo: data.Logo,\n        Status: data.Status,\n        CreatedDate: data.CreatedDate,\n        UpdatedDate: data.UpdatedDate,\n        AuthorizationType: data.AuthorizationType,\n        IsActive: data.IsActive,\n        Rtl: data.RTL,\n        TimeFormat: data.TimeFormat,\n        ThemeId: data.ThemeId,\n        Type: data.Type,\n        OrganizationPlanId: data.OrganizationPlanId,\n        OrganizationPlan: data.OrganizationPlan,\n        Plan: data.Plan\n      });\n      setInitialValues({\n        OrganizationId: data.OrganizationId,\n        Name: data.Name,\n        // TimeZone: data.TimeZone,\n        // DateFormat: data.DateFormat,\n        // Logo: data.Logo,\n        Status: data.Status,\n        CreatedDate: data.CreatedDate,\n        UpdatedDate: data.UpdatedDate,\n        AuthorizationType: data.AuthorizationType,\n        IsActive: data.IsActive,\n        Rtl: data.RTL,\n        TimeFormat: data.TimeFormat,\n        ThemeId: data.ThemeId,\n        Type: data.Type,\n        OrganizationPlanId: data.OrganizationPlanId,\n        OrganizationPlan: data.OrganizationPlan,\n        Plan: data.Plan\n      });\n    } catch (error) {\n      console.error(\"Failed to fetch organization details:\", error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [showEditPopup && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-popup\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Edit Organization\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n          onClick: () => handleClose(),\n          className: \"close-icon\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          x: \"0px\",\n          y: \"0px\",\n          width: \"24\",\n          height: \"24\",\n          viewBox: \"0 0 50 50\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M 7.71875 6.28125 L 6.28125 7.71875 L 23.5625 25 L 6.28125 42.28125 L 7.71875 43.71875 L 25 26.4375 L 42.28125 43.71875 L 43.71875 42.28125 L 26.4375 25 L 43.71875 7.71875 L 42.28125 6.28125 L 25 23.5625 Z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-usrform\",\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-txtfld\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"organizationname\",\n              className: \"qadpt-txtlabel\",\n              children: \"Organization Name*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              className: \"qadpt-txtinp\",\n              type: \"text\",\n              name: \"Name\",\n              value: userDetails.Name,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this), errors.Name && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"error\",\n              children: errors.Name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 32\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-txtfld\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"type\",\n              className: \"qadpt-txtlabel\",\n              children: \"Type*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"type\",\n              name: \"Type\",\n              value: userDetails.Type,\n              onChange: handleChange,\n              className: errors.Type ? \"error-input\" : \"qadpt-txtinp\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Client\",\n                children: \"Client\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Testing\",\n                children: \"Testing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"POC\",\n                children: \"POC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Prospects\",\n                children: \"Prospects\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 15\n            }, this), errors.Type && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"error\",\n              children: errors.Type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-txtfld qadpt-switch\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"qadpt-txtlabel\",\n              children: \"RTL\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              control: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: userDetails.Rtl // Reflects the state correctly\n                ,\n                onChange: e => setUserDetails(prevDetails => ({\n                  ...prevDetails,\n                  Rtl: e.target.checked\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 7\n              }, this),\n              label: \"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-button\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: isFormChanged() ? 'qadpt-enab' : 'qadpt-disab',\n          disabled: !isFormChanged(),\n          onClick: handleSubmit,\n          children: \"Update\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: snackbarOpen,\n      autoHideDuration: 6000,\n      onClose: handleSnackbarClose,\n      anchorOrigin: {\n        vertical: \"top\",\n        horizontal: \"center\"\n      },\n      sx: {\n        zIndex: 10000,\n        marginTop: 4\n      } // Optionally adjust the zIndex if needed\n      ,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleSnackbarClose,\n        severity: snackbarSeverity,\n        sx: {\n          width: \"100%\"\n        },\n        children: snackbarMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 3\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 425,\n      columnNumber: 3\n    }, this), \"    \"]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 295,\n    columnNumber: 5\n  }, this);\n};\n_s(EditOrganization, \"cILT5InPdN3OpPAQUR+DeiybJdY=\");\n_c = EditOrganization;\nexport default EditOrganization;\nvar _c;\n$RefreshReg$(_c, \"EditOrganization\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "adminUrl", "<PERSON><PERSON>", "FormControlLabel", "Snackbar", "Switch", "jsxDEV", "_jsxDEV", "EditOrganization", "props", "_s", "modelsData", "setModelsData", "showEditPopup", "setShowEditPopup", "OrganizationId", "sortModel", "filters", "setLoading", "setModels", "models", "handleClose", "skip", "top", "setTotalcount", "updateOrganizationDetails", "userDetails", "setUserDetails", "Name", "Status", "CreatedDate", "UpdatedDate", "AuthorizationType", "IsActive", "Rtl", "TimeFormat", "ThemeId", "Type", "OrganizationPlanId", "OrganizationPlan", "Plan", "errors", "setErrors", "Logo", "TimeZone", "DateFormat", "initialValues", "setInitialValues", "snackbarOpen", "setSnackbarOpen", "snackbarMessage", "setSnackbarMessage", "snackbarSeverity", "setSnackbarSeverity", "handleTimezoneChange", "event", "prevDetails", "target", "value", "handleFileChange", "files", "file", "name", "handleSnackbarClose", "checkNameUniqueness", "trim", "prevErrors", "isNameUnique", "every", "org", "handleChange", "e", "validateField", "handleBlur", "<PERSON><PERSON><PERSON><PERSON>", "newErrors", "length", "test", "validateFields", "Object", "entries", "currentOrgId", "isFormChanged", "JSON", "stringify", "handleSubmit", "preventDefault", "error", "console", "fetchOrganizationDetails", "id", "token", "localStorage", "getItem", "response", "fetch", "method", "headers", "ok", "Error", "data", "json", "RTL", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "xmlns", "x", "y", "width", "height", "viewBox", "d", "onSubmit", "htmlFor", "type", "onChange", "control", "checked", "label", "disabled", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "sx", "zIndex", "marginTop", "severity", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/components/organization/OrganizationEdit.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { fetchOrganizations, getOrganizations, updateOrganization } from \"../../services/OrganizationService\";\r\nimport { adminUrl } from \"../../services/APIService\";\r\nimport { timezones } from \"../../timezones\";\r\nimport { Alert, FormControlLabel, Snackbar, Switch } from '@mui/material';\r\n\r\nconst EditOrganization = (props: any) => {\r\n  const {\r\n    modelsData,\r\n    setModelsData,\r\n    showEditPopup,\r\n    setShowEditPopup,\r\n    OrganizationId,\r\n    sortModel,\r\n    filters,\r\n    setLoading,\r\n    setModels,\r\n    models,\r\n    handleClose,\r\n    skip,\r\n    top,\r\n    setTotalcount,\r\n    updateOrganizationDetails\r\n  } = props;\r\n\r\n  const [userDetails, setUserDetails] = useState({\r\n    OrganizationId: \"\",\r\n    Name: \"\",\r\n    // TimeZone: \"Asia/Kolkata\", // default value\r\n    // DateFormat: \"dd-MM-yyyy\",\r\n    // Logo: \"\",\r\n    Status: \"\",\r\n    CreatedDate: \"\",\r\n    UpdatedDate: \"\",\r\n    AuthorizationType: \"\",\r\n    IsActive: \"\",\r\n    Rtl: false,\r\n    TimeFormat: \"\",\r\n    ThemeId: \"\",\r\n    Type: \"\",\r\n    OrganizationPlanId: \"\",\r\n    OrganizationPlan: null,\r\n    Plan: \"\",\r\n  });\r\n\r\n  const [errors, setErrors] = useState({\r\n    Name: \"\",\r\n    Logo: \"\",\r\n    TimeZone: \"\",\r\n    DateFormat: \"\",\r\n    Type: \"\",\r\n    Rtl: \"\"\r\n  });\r\n  const [initialValues, setInitialValues] = useState({ ...userDetails });\r\n\r\n  const [snackbarOpen, setSnackbarOpen] = useState(false);\r\n  const [snackbarMessage, setSnackbarMessage] = useState(\"\");\r\n  const [snackbarSeverity, setSnackbarSeverity] = useState<\"success\" | \"error\">(\"success\");\r\n\r\n  const handleTimezoneChange = (event: any) => {\r\n    setUserDetails(prevDetails => ({ ...prevDetails, TimeZone: event.target.value }));\r\n  };\r\n\r\n  const handleFileChange = (event: any) => {\r\n    if (event.target.files && event.target.files[0]) {\r\n      const file = event.target.files[0];\r\n      setUserDetails(prevDetails => ({ ...prevDetails, Logo: file.name }));\r\n    }\r\n  };\r\n\r\n  const handleSnackbarClose = () => {\r\n    setSnackbarOpen(false);\r\n  };\r\n\r\n  useEffect(() => {\r\n    const checkNameUniqueness = async () => {\r\n      if (userDetails.Name.trim() === \"\") {\r\n        setErrors(prevErrors => ({\r\n          ...prevErrors,\r\n          Name: \"\"\r\n        }));\r\n        return;\r\n      }\r\n\r\n      const isNameUnique = modelsData.every((org: { Name: string; OrganizationId: string }) => \r\n        org.Name !== userDetails.Name || org.OrganizationId === userDetails.OrganizationId\r\n      );\r\n\r\n      if (!isNameUnique) {\r\n        setErrors(prevErrors => ({\r\n          ...prevErrors,\r\n          Name: 'Organization Name already exists'\r\n        }));\r\n      } else {\r\n        setErrors(prevErrors => ({\r\n          ...prevErrors,\r\n          Name: \"\"\r\n        }));\r\n      }\r\n    };\r\n\r\n    if (userDetails.Name !== \"\") {\r\n      checkNameUniqueness();\r\n    }\r\n  }, [userDetails.Name, modelsData, models, userDetails.OrganizationId]);\r\n\r\n  const handleChange = (e: any) => {\r\n    const { name, value } = e.target;\r\n    \r\n    setUserDetails(prevDetails => ({\r\n      ...prevDetails,\r\n      [name]: value,\r\n    }));\r\n\r\n    // Perform validation on change\r\n    validateField(name, value);\r\n  };\r\n\r\n  const handleBlur = (e: any) => {\r\n    const { name, value } = e.target;\r\n    validateField(name, value);\r\n  };\r\n\r\n  const validateField = (name: string, value: any) => {\r\n    let isValid = true;\r\n    let newErrors: any = { ...errors };\r\n\r\n    switch (name) {\r\n      case \"Name\":\r\n        if (!value) {\r\n          newErrors.Name = \"Organization Name is required\";\r\n          isValid = false;\r\n        } else if (value.length < 5) {\r\n          newErrors.Name = \"Organization Name must be at least 5 characters\";\r\n          isValid = false;\r\n        } else if (value.length > 50) {\r\n          newErrors.Name = \"Organization Name must be at most 50 characters\";\r\n          isValid = false;\r\n        } else if (!/^[a-zA-Z0-9 ]+$/.test(value)) {\r\n          newErrors.Name = \"Organization Name can only contain letters, numbers, and spaces\";\r\n          isValid = false;\r\n        } else if (!checkNameUniqueness(value, userDetails.OrganizationId)) {\r\n          newErrors.Name = 'Organization Name already exists';\r\n          isValid = false;\r\n        } else {\r\n          newErrors.Name = \"\";\r\n        }\r\n        break;\r\n      case \"Logo\":\r\n        if (!value) {\r\n          newErrors.Logo = \"Logo is required\";\r\n          isValid = false;\r\n        } else {\r\n          newErrors.Logo = \"\";\r\n        }\r\n        break;\r\n      case \"TimeZone\":\r\n        if (!value) {\r\n          newErrors.TimeZone = \"Timezone is required\";\r\n          isValid = false;\r\n        } else {\r\n          newErrors.TimeZone = \"\";\r\n        }\r\n        break;\r\n      case \"DateFormat\":\r\n        if (!value) {\r\n          newErrors.DateFormat = \"Date Format is required\";\r\n          isValid = false;\r\n        } else {\r\n          newErrors.DateFormat = \"\";\r\n        }\r\n        break;\r\n      case \"Type\":\r\n        if (!value) {\r\n          newErrors.Type = \"Type is required\";\r\n          isValid = false;\r\n        } else {\r\n          newErrors.Type = \"\";\r\n        }\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n\r\n    setErrors(newErrors);\r\n    return isValid;\r\n  };\r\n\r\n  const validateFields = () => {\r\n    let isValid = true;\r\n\r\n    for (const [name, value] of Object.entries(userDetails)) {\r\n      if (!validateField(name, value)) {\r\n        isValid = false;\r\n      }\r\n    }\r\n\r\n    return isValid;\r\n  };\r\n\r\n  const checkNameUniqueness = (name: string, currentOrgId: string) => {\r\n    const isNameUnique = modelsData.every((org: { Name: string; OrganizationId: string }) => \r\n      org.Name !== name || org.OrganizationId === currentOrgId\r\n    );\r\n    return isNameUnique;\r\n  };\r\n\r\n  const isFormChanged = () => {\r\n    return JSON.stringify(userDetails) !== JSON.stringify(initialValues);\r\n  };\r\n\r\n  const handleSubmit = async (e: any) => {\r\n    e.preventDefault();\r\n\r\n    if (validateFields()) {\r\n      try {\r\n        updateOrganizationDetails(userDetails);\r\n      } catch (error) {\r\n        console.error(\"Failed to update organization:\", error);\r\n        setSnackbarMessage(\"Failed to update organization\");\r\n        setSnackbarSeverity(\"error\");\r\n        setSnackbarOpen(true);\r\n      }\r\n    } else {\r\n      setSnackbarMessage(\"Please correct the errors in the form\");\r\n      setSnackbarSeverity(\"error\");\r\n      setSnackbarOpen(true);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (showEditPopup) {\r\n      fetchOrganizationDetails(OrganizationId);\r\n    }\r\n  }, [showEditPopup, OrganizationId]);\r\n\r\n  const fetchOrganizationDetails = async (id: any) => {\r\n    try {\r\n      const token = localStorage.getItem('access_token'); \r\n      const response = await fetch(`${adminUrl}/Organization/GetOrganizationById?organizationId=${id}`, {\r\n        method: 'GET',\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`, \r\n          'Content-Type': 'application/json' \r\n        }\r\n      });\r\n      if (!response.ok) {\r\n        throw new Error(\"Network response was not ok\");\r\n      }\r\n      const data = await response.json();\r\n      setUserDetails({\r\n        OrganizationId: data.OrganizationId,\r\n        Name: data.Name,\r\n        // TimeZone: data.TimeZone,\r\n        // DateFormat: data.DateFormat,\r\n        // Logo: data.Logo,\r\n        Status: data.Status,\r\n        CreatedDate: data.CreatedDate,\r\n        UpdatedDate: data.UpdatedDate,\r\n        AuthorizationType: data.AuthorizationType,\r\n        IsActive: data.IsActive,\r\n        Rtl: data.RTL,\r\n        TimeFormat: data.TimeFormat,\r\n        ThemeId: data.ThemeId,\r\n        Type: data.Type,\r\n        OrganizationPlanId: data.OrganizationPlanId,\r\n        OrganizationPlan: data.OrganizationPlan,\r\n        Plan: data.Plan\r\n      });\r\n      setInitialValues({\r\n        OrganizationId: data.OrganizationId,\r\n        Name: data.Name,\r\n        // TimeZone: data.TimeZone,\r\n        // DateFormat: data.DateFormat,\r\n        // Logo: data.Logo,\r\n        Status: data.Status,\r\n        CreatedDate: data.CreatedDate,\r\n        UpdatedDate: data.UpdatedDate,\r\n        AuthorizationType: data.AuthorizationType,\r\n        IsActive: data.IsActive,\r\n        Rtl: data.RTL,\r\n        TimeFormat: data.TimeFormat,\r\n        ThemeId: data.ThemeId,\r\n        Type: data.Type,\r\n        OrganizationPlanId: data.OrganizationPlanId,\r\n        OrganizationPlan: data.OrganizationPlan,\r\n        Plan: data.Plan\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Failed to fetch organization details:\", error);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div>\r\n    {showEditPopup && (\r\n      <div className=\"user-popup\">\r\n        <div className=\"qadpt-header\">\r\n          <span>Edit Organization</span>\r\n          <svg\r\n            onClick={() => handleClose()}\r\n            className=\"close-icon\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            x=\"0px\"\r\n            y=\"0px\"\r\n            width=\"24\"\r\n            height=\"24\"\r\n            viewBox=\"0 0 50 50\"\r\n          >\r\n            <path d=\"M 7.71875 6.28125 L 6.28125 7.71875 L 23.5625 25 L 6.28125 42.28125 L 7.71875 43.71875 L 25 26.4375 L 42.28125 43.71875 L 43.71875 42.28125 L 26.4375 25 L 43.71875 7.71875 L 42.28125 6.28125 L 25 23.5625 Z\"></path>\r\n          </svg>\r\n        </div>\r\n        <div className=\"qadpt-usrform\">\r\n          <form onSubmit={handleSubmit}>\r\n            <div className=\"qadpt-txtfld\">\r\n              <label htmlFor=\"organizationname\" className=\"qadpt-txtlabel\">Organization Name*</label>\r\n              <input\r\n                className=\"qadpt-txtinp\"\r\n                type=\"text\"\r\n                name=\"Name\"\r\n                value={userDetails.Name}\r\n                onChange={handleChange}\r\n              />\r\n               {errors.Name && <div className=\"error\">{errors.Name}</div>}\r\n            </div>\r\n    \r\n            {/* <div className=\"qadpt-txtfld\">\r\n              <label htmlFor=\"logo\" className=\"qadpt-txtlabel\">Logo*</label>\r\n              <input\r\n                className=\"qadpt-txtinp\"\r\n                type=\"file\"\r\n                name=\"logo\"\r\n                accept=\"image/*\"\r\n                onChange={handleFileChange}\r\n              />\r\n              {errors.Logo && <div className=\"error\">{errors.Logo}</div>}\r\n            </div> */}\r\n    \r\n            {/* <div className=\"qadpt-txtfld\">\r\n              <label htmlFor=\"timezone\" className=\"qadpt-txtlabel\">Timezone*</label>\r\n              <select\r\n                 value={userDetails.TimeZone}\r\n                onChange={handleTimezoneChange}\r\n                className=\"qadpt-txtinp\"\r\n              >\r\n                {timezones.map((timezone) => (\r\n                  <option key={timezone.Id} value={timezone.Id}>\r\n                    {timezone.DisplayName}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n              {errors.TimeZone && <span className=\"error\">{errors.TimeZone}</span>}\r\n            </div> */}\r\n    \r\n            {/* <div className=\"qadpt-txtfld\">\r\n              <label htmlFor=\"dateFormat\" className=\"qadpt-txtlabel\">Date Format*</label>\r\n              <select\r\n                id=\"dateFormat\"\r\n                name=\"DateFormat\"\r\n                value={userDetails.DateFormat}\r\n                onChange={handleChange}\r\n                className={errors.DateFormat ? \"error-input\" : \"qadpt-txtinp\"}\r\n              >\r\n                <option value=\"dd-MM-yyyy\">dd-MM-yyyy</option>\r\n                <option value=\"MM-dd-yyyy\">MM-dd-yyyy</option>\r\n                <option value=\"yyyy-MM-dd\">yyyy-MM-dd</option>\r\n              </select>\r\n              {errors.DateFormat && <span className=\"error\">{errors.DateFormat}</span>}            </div> */}\r\n    \r\n            <div className=\"qadpt-txtfld\">\r\n              <label htmlFor=\"type\" className=\"qadpt-txtlabel\">Type*</label>\r\n              <select\r\n                id=\"type\"\r\n                name=\"Type\"\r\n                value={userDetails.Type}\r\n                onChange={handleChange}\r\n                className={errors.Type ? \"error-input\" : \"qadpt-txtinp\"}\r\n              >\r\n                <option value=\"Client\">Client</option>\r\n                <option value=\"Testing\">Testing</option>\r\n                <option value=\"POC\">POC</option>\r\n                <option value=\"Prospects\">Prospects</option>\r\n              </select>\r\n                {errors.Type && <span className=\"error\">{errors.Type}</span>}\r\n              </div>\r\n    \r\n            <div className=\"qadpt-txtfld qadpt-switch\">\r\n              <span className=\"qadpt-txtlabel\">RTL</span>\r\n             \r\n              <FormControlLabel\r\n\t\t\t\t\tcontrol={\r\n\r\n\t\t\t\t\t\t<Switch\r\n\t\t\t\t\t\t\t\t\r\n      checked={userDetails.Rtl} // Reflects the state correctly\r\n      onChange={(e) => setUserDetails(prevDetails => ({\r\n        ...prevDetails,\r\n        Rtl: e.target.checked\r\n      }))}\r\n    />\r\n    }label=\"\"\r\n                />\r\n             \r\n\r\n\r\n\r\n\r\n\r\n            </div>\r\n          </form>\r\n        </div>\r\n        <div className=\"qadpt-button\">\r\n        <button \r\n              type=\"submit\" \r\n            className={isFormChanged() ? 'qadpt-enab' : 'qadpt-disab'}\r\n            disabled={!isFormChanged()}\r\n\t\tonClick={handleSubmit}\r\n            >\r\n              Update\r\n            </button>\r\n            </div>\r\n      </div>\r\n  \r\n    )}\r\n  <Snackbar\r\n  open={snackbarOpen}\r\n  autoHideDuration={6000}\r\n  onClose={handleSnackbarClose}\r\n  anchorOrigin={{ vertical: \"top\", horizontal: \"center\" }}\r\n  sx={{ zIndex: 10000 ,marginTop:4}} // Optionally adjust the zIndex if needed\r\n>\r\n  <Alert\r\n      onClose={handleSnackbarClose}\r\n      severity={snackbarSeverity}\r\n      sx={{ width: \"100%\" }}\r\n  >\r\n      {snackbarMessage}\r\n  </Alert>\r\n</Snackbar>    </div>\r\n  );\r\n};\r\n\r\nexport default EditOrganization;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAElD,SAASC,QAAQ,QAAQ,2BAA2B;AAEpD,SAASC,KAAK,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1E,MAAMC,gBAAgB,GAAIC,KAAU,IAAK;EAAAC,EAAA;EACvC,MAAM;IACJC,UAAU;IACVC,aAAa;IACbC,aAAa;IACbC,gBAAgB;IAChBC,cAAc;IACdC,SAAS;IACTC,OAAO;IACPC,UAAU;IACVC,SAAS;IACTC,MAAM;IACNC,WAAW;IACXC,IAAI;IACJC,GAAG;IACHC,aAAa;IACbC;EACF,CAAC,GAAGhB,KAAK;EAET,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC;IAC7CgB,cAAc,EAAE,EAAE;IAClBa,IAAI,EAAE,EAAE;IACR;IACA;IACA;IACAC,MAAM,EAAE,EAAE;IACVC,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,EAAE;IACfC,iBAAiB,EAAE,EAAE;IACrBC,QAAQ,EAAE,EAAE;IACZC,GAAG,EAAE,KAAK;IACVC,UAAU,EAAE,EAAE;IACdC,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE,EAAE;IACRC,kBAAkB,EAAE,EAAE;IACtBC,gBAAgB,EAAE,IAAI;IACtBC,IAAI,EAAE;EACR,CAAC,CAAC;EAEF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG3C,QAAQ,CAAC;IACnC6B,IAAI,EAAE,EAAE;IACRe,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,EAAE;IACdR,IAAI,EAAE,EAAE;IACRH,GAAG,EAAE;EACP,CAAC,CAAC;EACF,MAAM,CAACY,aAAa,EAAEC,gBAAgB,CAAC,GAAGhD,QAAQ,CAAC;IAAE,GAAG2B;EAAY,CAAC,CAAC;EAEtE,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmD,eAAe,EAAEC,kBAAkB,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACqD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtD,QAAQ,CAAsB,SAAS,CAAC;EAExF,MAAMuD,oBAAoB,GAAIC,KAAU,IAAK;IAC3C5B,cAAc,CAAC6B,WAAW,KAAK;MAAE,GAAGA,WAAW;MAAEZ,QAAQ,EAAEW,KAAK,CAACE,MAAM,CAACC;IAAM,CAAC,CAAC,CAAC;EACnF,CAAC;EAED,MAAMC,gBAAgB,GAAIJ,KAAU,IAAK;IACvC,IAAIA,KAAK,CAACE,MAAM,CAACG,KAAK,IAAIL,KAAK,CAACE,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC,EAAE;MAC/C,MAAMC,IAAI,GAAGN,KAAK,CAACE,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC;MAClCjC,cAAc,CAAC6B,WAAW,KAAK;QAAE,GAAGA,WAAW;QAAEb,IAAI,EAAEkB,IAAI,CAACC;MAAK,CAAC,CAAC,CAAC;IACtE;EACF,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChCd,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAEDjD,SAAS,CAAC,MAAM;IACd,MAAMgE,mBAAmB,GAAG,MAAAA,CAAA,KAAY;MACtC,IAAItC,WAAW,CAACE,IAAI,CAACqC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAClCvB,SAAS,CAACwB,UAAU,KAAK;UACvB,GAAGA,UAAU;UACbtC,IAAI,EAAE;QACR,CAAC,CAAC,CAAC;QACH;MACF;MAEA,MAAMuC,YAAY,GAAGxD,UAAU,CAACyD,KAAK,CAAEC,GAA6C,IAClFA,GAAG,CAACzC,IAAI,KAAKF,WAAW,CAACE,IAAI,IAAIyC,GAAG,CAACtD,cAAc,KAAKW,WAAW,CAACX,cACtE,CAAC;MAED,IAAI,CAACoD,YAAY,EAAE;QACjBzB,SAAS,CAACwB,UAAU,KAAK;UACvB,GAAGA,UAAU;UACbtC,IAAI,EAAE;QACR,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLc,SAAS,CAACwB,UAAU,KAAK;UACvB,GAAGA,UAAU;UACbtC,IAAI,EAAE;QACR,CAAC,CAAC,CAAC;MACL;IACF,CAAC;IAED,IAAIF,WAAW,CAACE,IAAI,KAAK,EAAE,EAAE;MAC3BoC,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAACtC,WAAW,CAACE,IAAI,EAAEjB,UAAU,EAAES,MAAM,EAAEM,WAAW,CAACX,cAAc,CAAC,CAAC;EAEtE,MAAMuD,YAAY,GAAIC,CAAM,IAAK;IAC/B,MAAM;MAAET,IAAI;MAAEJ;IAAM,CAAC,GAAGa,CAAC,CAACd,MAAM;IAEhC9B,cAAc,CAAC6B,WAAW,KAAK;MAC7B,GAAGA,WAAW;MACd,CAACM,IAAI,GAAGJ;IACV,CAAC,CAAC,CAAC;;IAEH;IACAc,aAAa,CAACV,IAAI,EAAEJ,KAAK,CAAC;EAC5B,CAAC;EAED,MAAMe,UAAU,GAAIF,CAAM,IAAK;IAC7B,MAAM;MAAET,IAAI;MAAEJ;IAAM,CAAC,GAAGa,CAAC,CAACd,MAAM;IAChCe,aAAa,CAACV,IAAI,EAAEJ,KAAK,CAAC;EAC5B,CAAC;EAED,MAAMc,aAAa,GAAGA,CAACV,IAAY,EAAEJ,KAAU,KAAK;IAClD,IAAIgB,OAAO,GAAG,IAAI;IAClB,IAAIC,SAAc,GAAG;MAAE,GAAGlC;IAAO,CAAC;IAElC,QAAQqB,IAAI;MACV,KAAK,MAAM;QACT,IAAI,CAACJ,KAAK,EAAE;UACViB,SAAS,CAAC/C,IAAI,GAAG,+BAA+B;UAChD8C,OAAO,GAAG,KAAK;QACjB,CAAC,MAAM,IAAIhB,KAAK,CAACkB,MAAM,GAAG,CAAC,EAAE;UAC3BD,SAAS,CAAC/C,IAAI,GAAG,iDAAiD;UAClE8C,OAAO,GAAG,KAAK;QACjB,CAAC,MAAM,IAAIhB,KAAK,CAACkB,MAAM,GAAG,EAAE,EAAE;UAC5BD,SAAS,CAAC/C,IAAI,GAAG,iDAAiD;UAClE8C,OAAO,GAAG,KAAK;QACjB,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAACG,IAAI,CAACnB,KAAK,CAAC,EAAE;UACzCiB,SAAS,CAAC/C,IAAI,GAAG,iEAAiE;UAClF8C,OAAO,GAAG,KAAK;QACjB,CAAC,MAAM,IAAI,CAACV,mBAAmB,CAACN,KAAK,EAAEhC,WAAW,CAACX,cAAc,CAAC,EAAE;UAClE4D,SAAS,CAAC/C,IAAI,GAAG,kCAAkC;UACnD8C,OAAO,GAAG,KAAK;QACjB,CAAC,MAAM;UACLC,SAAS,CAAC/C,IAAI,GAAG,EAAE;QACrB;QACA;MACF,KAAK,MAAM;QACT,IAAI,CAAC8B,KAAK,EAAE;UACViB,SAAS,CAAChC,IAAI,GAAG,kBAAkB;UACnC+B,OAAO,GAAG,KAAK;QACjB,CAAC,MAAM;UACLC,SAAS,CAAChC,IAAI,GAAG,EAAE;QACrB;QACA;MACF,KAAK,UAAU;QACb,IAAI,CAACe,KAAK,EAAE;UACViB,SAAS,CAAC/B,QAAQ,GAAG,sBAAsB;UAC3C8B,OAAO,GAAG,KAAK;QACjB,CAAC,MAAM;UACLC,SAAS,CAAC/B,QAAQ,GAAG,EAAE;QACzB;QACA;MACF,KAAK,YAAY;QACf,IAAI,CAACc,KAAK,EAAE;UACViB,SAAS,CAAC9B,UAAU,GAAG,yBAAyB;UAChD6B,OAAO,GAAG,KAAK;QACjB,CAAC,MAAM;UACLC,SAAS,CAAC9B,UAAU,GAAG,EAAE;QAC3B;QACA;MACF,KAAK,MAAM;QACT,IAAI,CAACa,KAAK,EAAE;UACViB,SAAS,CAACtC,IAAI,GAAG,kBAAkB;UACnCqC,OAAO,GAAG,KAAK;QACjB,CAAC,MAAM;UACLC,SAAS,CAACtC,IAAI,GAAG,EAAE;QACrB;QACA;MACF;QACE;IACJ;IAEAK,SAAS,CAACiC,SAAS,CAAC;IACpB,OAAOD,OAAO;EAChB,CAAC;EAED,MAAMI,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIJ,OAAO,GAAG,IAAI;IAElB,KAAK,MAAM,CAACZ,IAAI,EAAEJ,KAAK,CAAC,IAAIqB,MAAM,CAACC,OAAO,CAACtD,WAAW,CAAC,EAAE;MACvD,IAAI,CAAC8C,aAAa,CAACV,IAAI,EAAEJ,KAAK,CAAC,EAAE;QAC/BgB,OAAO,GAAG,KAAK;MACjB;IACF;IAEA,OAAOA,OAAO;EAChB,CAAC;EAED,MAAMV,mBAAmB,GAAGA,CAACF,IAAY,EAAEmB,YAAoB,KAAK;IAClE,MAAMd,YAAY,GAAGxD,UAAU,CAACyD,KAAK,CAAEC,GAA6C,IAClFA,GAAG,CAACzC,IAAI,KAAKkC,IAAI,IAAIO,GAAG,CAACtD,cAAc,KAAKkE,YAC9C,CAAC;IACD,OAAOd,YAAY;EACrB,CAAC;EAED,MAAMe,aAAa,GAAGA,CAAA,KAAM;IAC1B,OAAOC,IAAI,CAACC,SAAS,CAAC1D,WAAW,CAAC,KAAKyD,IAAI,CAACC,SAAS,CAACtC,aAAa,CAAC;EACtE,CAAC;EAED,MAAMuC,YAAY,GAAG,MAAOd,CAAM,IAAK;IACrCA,CAAC,CAACe,cAAc,CAAC,CAAC;IAElB,IAAIR,cAAc,CAAC,CAAC,EAAE;MACpB,IAAI;QACFrD,yBAAyB,CAACC,WAAW,CAAC;MACxC,CAAC,CAAC,OAAO6D,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtDpC,kBAAkB,CAAC,+BAA+B,CAAC;QACnDE,mBAAmB,CAAC,OAAO,CAAC;QAC5BJ,eAAe,CAAC,IAAI,CAAC;MACvB;IACF,CAAC,MAAM;MACLE,kBAAkB,CAAC,uCAAuC,CAAC;MAC3DE,mBAAmB,CAAC,OAAO,CAAC;MAC5BJ,eAAe,CAAC,IAAI,CAAC;IACvB;EACF,CAAC;EAEDjD,SAAS,CAAC,MAAM;IACd,IAAIa,aAAa,EAAE;MACjB4E,wBAAwB,CAAC1E,cAAc,CAAC;IAC1C;EACF,CAAC,EAAE,CAACF,aAAa,EAAEE,cAAc,CAAC,CAAC;EAEnC,MAAM0E,wBAAwB,GAAG,MAAOC,EAAO,IAAK;IAClD,IAAI;MACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;MAClD,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9F,QAAQ,oDAAoDyF,EAAE,EAAE,EAAE;QAChGM,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUN,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,IAAI,CAACG,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;MAChD;MACA,MAAMC,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;MAClC1E,cAAc,CAAC;QACbZ,cAAc,EAAEqF,IAAI,CAACrF,cAAc;QACnCa,IAAI,EAAEwE,IAAI,CAACxE,IAAI;QACf;QACA;QACA;QACAC,MAAM,EAAEuE,IAAI,CAACvE,MAAM;QACnBC,WAAW,EAAEsE,IAAI,CAACtE,WAAW;QAC7BC,WAAW,EAAEqE,IAAI,CAACrE,WAAW;QAC7BC,iBAAiB,EAAEoE,IAAI,CAACpE,iBAAiB;QACzCC,QAAQ,EAAEmE,IAAI,CAACnE,QAAQ;QACvBC,GAAG,EAAEkE,IAAI,CAACE,GAAG;QACbnE,UAAU,EAAEiE,IAAI,CAACjE,UAAU;QAC3BC,OAAO,EAAEgE,IAAI,CAAChE,OAAO;QACrBC,IAAI,EAAE+D,IAAI,CAAC/D,IAAI;QACfC,kBAAkB,EAAE8D,IAAI,CAAC9D,kBAAkB;QAC3CC,gBAAgB,EAAE6D,IAAI,CAAC7D,gBAAgB;QACvCC,IAAI,EAAE4D,IAAI,CAAC5D;MACb,CAAC,CAAC;MACFO,gBAAgB,CAAC;QACfhC,cAAc,EAAEqF,IAAI,CAACrF,cAAc;QACnCa,IAAI,EAAEwE,IAAI,CAACxE,IAAI;QACf;QACA;QACA;QACAC,MAAM,EAAEuE,IAAI,CAACvE,MAAM;QACnBC,WAAW,EAAEsE,IAAI,CAACtE,WAAW;QAC7BC,WAAW,EAAEqE,IAAI,CAACrE,WAAW;QAC7BC,iBAAiB,EAAEoE,IAAI,CAACpE,iBAAiB;QACzCC,QAAQ,EAAEmE,IAAI,CAACnE,QAAQ;QACvBC,GAAG,EAAEkE,IAAI,CAACE,GAAG;QACbnE,UAAU,EAAEiE,IAAI,CAACjE,UAAU;QAC3BC,OAAO,EAAEgE,IAAI,CAAChE,OAAO;QACrBC,IAAI,EAAE+D,IAAI,CAAC/D,IAAI;QACfC,kBAAkB,EAAE8D,IAAI,CAAC9D,kBAAkB;QAC3CC,gBAAgB,EAAE6D,IAAI,CAAC7D,gBAAgB;QACvCC,IAAI,EAAE4D,IAAI,CAAC5D;MACb,CAAC,CAAC;IACJ,CAAC,CAAC,OAAO+C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IAC/D;EACF,CAAC;EAED,oBACEhF,OAAA;IAAAgG,QAAA,GACC1F,aAAa,iBACZN,OAAA;MAAKiG,SAAS,EAAC,YAAY;MAAAD,QAAA,gBACzBhG,OAAA;QAAKiG,SAAS,EAAC,cAAc;QAAAD,QAAA,gBAC3BhG,OAAA;UAAAgG,QAAA,EAAM;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9BrG,OAAA;UACEsG,OAAO,EAAEA,CAAA,KAAMxF,WAAW,CAAC,CAAE;UAC7BmF,SAAS,EAAC,YAAY;UACtBM,KAAK,EAAC,4BAA4B;UAClCC,CAAC,EAAC,KAAK;UACPC,CAAC,EAAC,KAAK;UACPC,KAAK,EAAC,IAAI;UACVC,MAAM,EAAC,IAAI;UACXC,OAAO,EAAC,WAAW;UAAAZ,QAAA,eAEnBhG,OAAA;YAAM6G,CAAC,EAAC;UAA+M;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5N,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNrG,OAAA;QAAKiG,SAAS,EAAC,eAAe;QAAAD,QAAA,eAC5BhG,OAAA;UAAM8G,QAAQ,EAAEhC,YAAa;UAAAkB,QAAA,gBAC3BhG,OAAA;YAAKiG,SAAS,EAAC,cAAc;YAAAD,QAAA,gBAC3BhG,OAAA;cAAO+G,OAAO,EAAC,kBAAkB;cAACd,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvFrG,OAAA;cACEiG,SAAS,EAAC,cAAc;cACxBe,IAAI,EAAC,MAAM;cACXzD,IAAI,EAAC,MAAM;cACXJ,KAAK,EAAEhC,WAAW,CAACE,IAAK;cACxB4F,QAAQ,EAAElD;YAAa;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,EACAnE,MAAM,CAACb,IAAI,iBAAIrB,OAAA;cAAKiG,SAAS,EAAC,OAAO;cAAAD,QAAA,EAAE9D,MAAM,CAACb;YAAI;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eA6CNrG,OAAA;YAAKiG,SAAS,EAAC,cAAc;YAAAD,QAAA,gBAC3BhG,OAAA;cAAO+G,OAAO,EAAC,MAAM;cAACd,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9DrG,OAAA;cACEmF,EAAE,EAAC,MAAM;cACT5B,IAAI,EAAC,MAAM;cACXJ,KAAK,EAAEhC,WAAW,CAACW,IAAK;cACxBmF,QAAQ,EAAElD,YAAa;cACvBkC,SAAS,EAAE/D,MAAM,CAACJ,IAAI,GAAG,aAAa,GAAG,cAAe;cAAAkE,QAAA,gBAExDhG,OAAA;gBAAQmD,KAAK,EAAC,QAAQ;gBAAA6C,QAAA,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtCrG,OAAA;gBAAQmD,KAAK,EAAC,SAAS;gBAAA6C,QAAA,EAAC;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCrG,OAAA;gBAAQmD,KAAK,EAAC,KAAK;gBAAA6C,QAAA,EAAC;cAAG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChCrG,OAAA;gBAAQmD,KAAK,EAAC,WAAW;gBAAA6C,QAAA,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,EACNnE,MAAM,CAACJ,IAAI,iBAAI9B,OAAA;cAAMiG,SAAS,EAAC,OAAO;cAAAD,QAAA,EAAE9D,MAAM,CAACJ;YAAI;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eAERrG,OAAA;YAAKiG,SAAS,EAAC,2BAA2B;YAAAD,QAAA,gBACxChG,OAAA;cAAMiG,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAE3CrG,OAAA,CAACJ,gBAAgB;cAC1BsH,OAAO,eAENlH,OAAA,CAACF,MAAM;gBAEPqH,OAAO,EAAEhG,WAAW,CAACQ,GAAI,CAAC;gBAAA;gBAC1BsF,QAAQ,EAAGjD,CAAC,IAAK5C,cAAc,CAAC6B,WAAW,KAAK;kBAC9C,GAAGA,WAAW;kBACdtB,GAAG,EAAEqC,CAAC,CAACd,MAAM,CAACiE;gBAChB,CAAC,CAAC;cAAE;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CACA;cAAAe,KAAK,EAAC;YAAE;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrG,OAAA;QAAKiG,SAAS,EAAC,cAAc;QAAAD,QAAA,eAC7BhG,OAAA;UACMgH,IAAI,EAAC,QAAQ;UACff,SAAS,EAAEtB,aAAa,CAAC,CAAC,GAAG,YAAY,GAAG,aAAc;UAC1D0C,QAAQ,EAAE,CAAC1C,aAAa,CAAC,CAAE;UACrC2B,OAAO,EAAExB,YAAa;UAAAkB,QAAA,EACX;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAEN,eACHrG,OAAA,CAACH,QAAQ;MACTyH,IAAI,EAAE7E,YAAa;MACnB8E,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEhE,mBAAoB;MAC7BiE,YAAY,EAAE;QAAEC,QAAQ,EAAE,KAAK;QAAEC,UAAU,EAAE;MAAS,CAAE;MACxDC,EAAE,EAAE;QAAEC,MAAM,EAAE,KAAK;QAAEC,SAAS,EAAC;MAAC,CAAE,CAAC;MAAA;MAAA9B,QAAA,eAEnChG,OAAA,CAACL,KAAK;QACF6H,OAAO,EAAEhE,mBAAoB;QAC7BuE,QAAQ,EAAElF,gBAAiB;QAC3B+E,EAAE,EAAE;UAAElB,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,EAErBrD;MAAe;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,QAAI;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AAErB,CAAC;AAAClG,EAAA,CAlbIF,gBAAgB;AAAA+H,EAAA,GAAhB/H,gBAAgB;AAobtB,eAAeA,gBAAgB;AAAC,IAAA+H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}