{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptWeb\\\\src\\\\components\\\\training\\\\Training.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Button, Container, TextField, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, IconButton, Tooltip, Alert } from '@mui/material';\nimport { DataGrid } from '@mui/x-data-grid';\nimport { useTranslation } from 'react-i18next';\nimport { useAuth } from '../auth/AuthProvider';\nimport { isSidebarOpen, subscribe } from \"../adminMenu/sidemenustate\";\nimport { useContext } from 'react';\nimport { AccountContext } from '../account/AccountContext';\nimport { useSnackbar } from '../../SnackbarContext';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport CloudUploadIcon from '@mui/icons-material/CloudUpload';\nimport { getAllTrainingDocuments, uploadTrainingDocument, DeleteKnowledgeBase, updateTrainingDocument, downloadTrainingDocument, replaceTrainingDocumentFile } from '../../services/TrainingService';\nimport { DocumentTypes, PriorityLevels } from '../../models/Training';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Training = () => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    userDetails\n  } = useAuth();\n  const {\n    accountId\n  } = useContext(AccountContext);\n  const {\n    openSnackbar\n  } = useSnackbar();\n  const [sidebarOpen, setSidebarOpen] = useState(isSidebarOpen());\n  const [models, setModels] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [totalCount, setTotalCount] = useState(0);\n  const [paginationModel, setPaginationModel] = useState({\n    page: 0,\n    pageSize: 15\n  });\n  const [dialogOpen, setDialogOpen] = useState(false);\n  const [editMode, setEditMode] = useState(false);\n  const [selectedDocument, setSelectedDocument] = useState(null);\n  const [uploadFile, setUploadFile] = useState(null);\n  const [replaceFile, setReplaceFile] = useState(null);\n  const [formData, setFormData] = useState({\n    title: '',\n    documentType: '',\n    priority: 1\n  });\n  const [filters, setFilters] = useState([]);\n  const [orderByFields, setOrderByFields] = useState('CreatedDate desc');\n  const {\n    roles\n  } = useContext(AccountContext);\n  useEffect(() => {\n    const unsubscribe = subscribe(setSidebarOpen);\n    return () => unsubscribe();\n  }, []);\n  useEffect(() => {\n    if (userDetails !== null && userDetails !== void 0 && userDetails.OrganizationId) {\n      fetchTrainingDocuments();\n    }\n  }, [userDetails === null || userDetails === void 0 ? void 0 : userDetails.OrganizationId, paginationModel, accountId]);\n  const fetchTrainingDocuments = async () => {\n    if (!(userDetails !== null && userDetails !== void 0 && userDetails.OrganizationId)) return;\n    const skip = paginationModel.page * paginationModel.pageSize;\n    const top = paginationModel.pageSize;\n    await getAllTrainingDocuments(setModels, setLoading, userDetails.OrganizationId, skip, top, setTotalCount, orderByFields, filters, accountId);\n  };\n  const handleFileChange = event => {\n    var _event$target$files;\n    const file = (_event$target$files = event.target.files) === null || _event$target$files === void 0 ? void 0 : _event$target$files[0];\n    if (file) {\n      setUploadFile(file);\n    }\n  };\n  const handleReplaceFileChange = event => {\n    var _event$target$files2;\n    const file = (_event$target$files2 = event.target.files) === null || _event$target$files2 === void 0 ? void 0 : _event$target$files2[0];\n    if (file) {\n      setReplaceFile(file);\n    }\n  };\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleUpload = async () => {\n    if (!uploadFile || !formData.title || !formData.documentType || !(userDetails !== null && userDetails !== void 0 && userDetails.OrganizationId) || !accountId) {\n      openSnackbar(translate('Please fill all required fields and select a file'), 'error');\n      return;\n    }\n    try {\n      await uploadTrainingDocument(uploadFile, formData.title, formData.documentType, formData.priority, userDetails.OrganizationId, userDetails.UserId || '', accountId);\n      openSnackbar(translate('Document uploaded successfully'), 'success');\n      setDialogOpen(false);\n      resetForm();\n      fetchTrainingDocuments();\n    } catch (error) {\n      var _err$response, _err$response$data, _err$response2, _err$response2$data;\n      const err = error;\n      const errorMessage = (err === null || err === void 0 ? void 0 : (_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.ErrorMessage) || (// backend-defined error message\n      err === null || err === void 0 ? void 0 : (_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.message) || (// common fallback\n      err === null || err === void 0 ? void 0 : err.message) ||\n      // JS/network error\n      'An unexpected error occurred while uploading the document.';\n      openSnackbar(translate(errorMessage), 'error');\n    }\n  };\n  const handleEdit = document => {\n    setSelectedDocument(document);\n\n    // Debug: Log the document type to understand the format\n    console.log('Document Type from API:', document.DocumentType);\n    console.log('Available Document Types:', DocumentTypes.map(dt => dt.value));\n\n    // Map the document type to the correct form value\n    const documentTypeValue = mapDocumentTypeToFormValue(document.DocumentType);\n    console.log('Mapped Document Type:', documentTypeValue);\n\n    // Show warning if document type couldn't be mapped to a known type\n    if (document.DocumentType && !DocumentTypes.some(dt => dt.value === documentTypeValue)) {\n      console.warn(`Document type \"${document.DocumentType}\" doesn't match any predefined types. Using original value.`);\n    }\n    setFormData({\n      title: document.Title,\n      documentType: documentTypeValue,\n      priority: document.Priority\n    });\n    setEditMode(true);\n    setDialogOpen(true);\n  };\n  const handleUpdate = async () => {\n    if (!selectedDocument || !formData.title || !formData.documentType || !(userDetails !== null && userDetails !== void 0 && userDetails.OrganizationId) || !accountId) {\n      openSnackbar(translate('Please fill all required fields'), 'error');\n      return;\n    }\n    try {\n      // If a replacement file is selected, use the file replacement API\n      if (replaceFile) {\n        await replaceTrainingDocumentFile(selectedDocument.Id, replaceFile, formData.title, formData.documentType, formData.priority, userDetails.OrganizationId, userDetails.UserId || '', accountId);\n      } else {\n        // Otherwise, just update the metadata\n        await updateTrainingDocument(selectedDocument.Id, formData.title, formData.documentType, formData.priority, userDetails.OrganizationId, userDetails.UserId || '');\n        setDialogOpen(false);\n        resetForm();\n        fetchTrainingDocuments();\n      }\n    } catch (error) {\n      openSnackbar(translate('Failed to update document'), 'error');\n    }\n  };\n  const handleDelete = async documentId => {\n    if (!(userDetails !== null && userDetails !== void 0 && userDetails.OrganizationId)) return;\n    if (window.confirm('Are you sure you want to delete this document?')) {\n      try {\n        await DeleteKnowledgeBase(documentId);\n        openSnackbar(translate('Document deleted successfully'), 'success');\n        fetchTrainingDocuments();\n      } catch (error) {\n        openSnackbar(translate('Failed to delete document'), 'error');\n      }\n    }\n  };\n  const handleDownload = async documentId => {\n    if (!(userDetails !== null && userDetails !== void 0 && userDetails.OrganizationId)) return;\n    try {\n      await downloadTrainingDocument(documentId, userDetails.OrganizationId);\n    } catch (error) {\n      openSnackbar(translate('Failed to download document'), 'error');\n    }\n  };\n  const resetForm = () => {\n    setFormData({\n      title: '',\n      documentType: '',\n      priority: 1\n    });\n    setUploadFile(null);\n    setReplaceFile(null);\n    setSelectedDocument(null);\n    setEditMode(false);\n  };\n\n  // Validate if the current document type is valid\n  const isValidDocumentType = docType => {\n    return DocumentTypes.some(dt => dt.value === docType) || docType === '';\n  };\n  const openUploadDialog = () => {\n    resetForm();\n    setDialogOpen(true);\n  };\n  const getPriorityLabel = priority => {\n    const priorityLevel = PriorityLevels.find(p => p.value === priority);\n    return priorityLevel ? priorityLevel.label : 'Unknown';\n  };\n  const getPriorityColor = priority => {\n    switch (priority) {\n      case 1:\n        return 'info';\n      case 2:\n        return 'primary';\n      case 3:\n        return 'warning';\n      case 4:\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const getDocumentTypeLabel = type => {\n    const docType = DocumentTypes.find(dt => dt.value === type);\n    return docType ? docType.label : type;\n  };\n\n  // Utility function to map API document type to form value\n  const mapDocumentTypeToFormValue = apiDocumentType => {\n    if (!apiDocumentType) return '';\n\n    // Try exact match first\n    const exactMatch = DocumentTypes.find(dt => dt.value === apiDocumentType);\n    if (exactMatch) return exactMatch.value;\n\n    // Try case-insensitive match on value\n    const valueMatch = DocumentTypes.find(dt => dt.value.toLowerCase() === apiDocumentType.toLowerCase());\n    if (valueMatch) return valueMatch.value;\n\n    // Try case-insensitive match on label\n    const labelMatch = DocumentTypes.find(dt => dt.label.toLowerCase() === apiDocumentType.toLowerCase());\n    if (labelMatch) return labelMatch.value;\n\n    // Return original value if no match found\n    return apiDocumentType;\n  };\n  const formatFileSize = bytes => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n  const columns = [{\n    field: 'Name',\n    headerName: translate('File Name'),\n    // width: 300,\n    resizable: false,\n    sortable: true\n  }, {\n    field: 'Title',\n    headerName: translate('Title'),\n    // width: 200,\n    resizable: false,\n    sortable: true\n  }, {\n    field: 'DocumentType',\n    headerName: translate('Document Type'),\n    // width: 180,\n    sortable: true,\n    resizable: false,\n    renderCell: params => getDocumentTypeLabel(params.value)\n  }, {\n    field: 'EmbeddingStatus',\n    headerName: translate('Training Status'),\n    // width: 180,\n    sortable: true,\n    resizable: false,\n    renderCell: params => getDocumentTypeLabel(params.value)\n  },\n  // {\n  //     field: 'Priority',\n  //     headerName: translate('Priority'),\n  //     width: 120,\n  //     sortable: true,\n  //     renderCell: (params) => (\n  //         <Chip\n  //             label={getPriorityLabel(params.value)}\n  //             color={getPriorityColor(params.value)}\n  //             size=\"small\"\n  //         />\n  //     )\n  // },\n  // {\n  //     field: 'FileSize',\n  //     headerName: translate('File Size'),\n  //     width: 120,\n  //     sortable: true,\n  //     renderCell: (params) => formatFileSize(params.value || 0)\n  // },\n  {\n    field: 'CreatedDate',\n    resizable: false,\n    headerName: translate('Upload Date'),\n    // width: 150,\n    sortable: true,\n    renderCell: params => new Date(params.value).toLocaleDateString()\n  }, {\n    field: 'actions',\n    headerName: translate('Actions'),\n    // width: 150,\n    sortable: false,\n    resizable: false,\n    renderCell: params => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Delete\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          onClick: () => handleDelete(params.row.Id),\n          children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 17\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-midpart setng-box\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-content-block\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-head\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-title-sec\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-title\",\n              children: translate('Training')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-description\",\n              children: translate('Manage knowledge base documents')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-right-part\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: openUploadDialog,\n              className: \"qadpt-memberButton\",\n              disabled: ![\"Account Admin\", \"Editor\"].some(role => roles.includes(role)),\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fal fa-add-plus\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: translate('Upload Document')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: \"30px\"\n          },\n          children: /*#__PURE__*/_jsxDEV(DataGrid, {\n            className: \"qadpt-setting-grd\",\n            rows: models,\n            columns: columns,\n            pagination: true,\n            paginationMode: \"server\",\n            rowCount: totalCount,\n            paginationModel: paginationModel,\n            onPaginationModelChange: setPaginationModel,\n            loading: loading,\n            pageSizeOptions: [15, 25, 50],\n            getRowId: row => row.Id,\n            disableRowSelectionOnClick: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 415,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: dialogOpen,\n      onClose: () => setDialogOpen(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      className: \"qadpt-trainpop\",\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: editMode ? translate('Edit Document') : translate('Upload Document')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 455,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: 2,\n            mt: 1\n          },\n          className: \"qadpt-uplddoc\",\n          children: [!editMode && /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            component: \"label\",\n            startIcon: /*#__PURE__*/_jsxDEV(CloudUploadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 44\n            }, this),\n            fullWidth: true,\n            children: [uploadFile ? uploadFile.name : translate('Select File'), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"file\",\n              hidden: true,\n              onChange: handleFileChange,\n              accept: \".pdf,.doc,.docx,.txt,.md\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 29\n          }, this), editMode && /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2,\n                p: 2,\n                bgcolor: 'grey.50',\n                borderRadius: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 1,\n                  mb: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 37\n              }, this), (selectedDocument === null || selectedDocument === void 0 ? void 0 : selectedDocument.FileSize) && /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  color: 'text.secondary',\n                  fontSize: '0.875rem'\n                },\n                children: [translate('Size'), \": \", formatFileSize(selectedDocument.FileSize)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              component: \"label\",\n              startIcon: /*#__PURE__*/_jsxDEV(CloudUploadIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 48\n              }, this),\n              fullWidth: true,\n              color: replaceFile ? \"success\" : \"primary\",\n              children: [replaceFile ? `${translate('New File Selected')}: ${replaceFile.name}` : translate('Change File (Optional)'), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                hidden: true,\n                onChange: handleReplaceFileChange,\n                accept: \".pdf,.doc,.docx,.txt,.md\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 500,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 33\n            }, this), replaceFile && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 1,\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Alert, {\n                severity: \"info\",\n                sx: {\n                  flexGrow: 1\n                },\n                children: translate('The current file will be replaced with the new file')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                size: \"small\",\n                onClick: () => setReplaceFile(null),\n                color: \"error\",\n                variant: \"outlined\",\n                children: translate('Keep Current File')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: translate('Title'),\n            value: formData.title,\n            onChange: e => handleFormChange('title', e.target.value),\n            fullWidth: true,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: translate('Document Type')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: formData.documentType || '',\n              onChange: e => handleFormChange('documentType', e.target.value),\n              label: translate('Document Type'),\n              displayEmpty: false,\n              children: [DocumentTypes.map(type => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: type.value,\n                children: type.label\n              }, type.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 37\n              }, this)), formData.documentType && !DocumentTypes.some(dt => dt.value === formData.documentType) && /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: formData.documentType,\n                children: [formData.documentType, \" (Custom)\"]\n              }, formData.documentType, true, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: translate('Priority')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: formData.priority,\n              onChange: e => handleFormChange('priority', e.target.value),\n              label: translate('Priority'),\n              children: PriorityLevels.map(level => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: level.value,\n                children: level.label\n              }, level.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 565,\n                columnNumber: 37\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 458,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setDialogOpen(false),\n          children: translate('Cancel')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 574,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: editMode ? handleUpdate : handleUpload,\n          variant: \"contained\",\n          children: editMode ? translate('Update') : translate('Upload')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 577,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 573,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 454,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 414,\n    columnNumber: 9\n  }, this);\n};\n_s(Training, \"NcmPJoZ/CahnMAsSt98OjZxAI7c=\", false, function () {\n  return [useTranslation, useAuth, useSnackbar];\n});\n_c = Training;\nexport default Training;\nvar _c;\n$RefreshReg$(_c, \"Training\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "<PERSON><PERSON>", "Container", "TextField", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "InputLabel", "Select", "MenuItem", "IconButton", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "DataGrid", "useTranslation", "useAuth", "isSidebarOpen", "subscribe", "useContext", "AccountContext", "useSnackbar", "DeleteIcon", "CloudUploadIcon", "getAllTrainingDocuments", "uploadTrainingDocument", "DeleteKnowledgeBase", "updateTrainingDocument", "downloadTrainingDocument", "replaceTrainingDocumentFile", "DocumentTypes", "PriorityLevels", "jsxDEV", "_jsxDEV", "Training", "_s", "t", "translate", "userDetails", "accountId", "openSnackbar", "sidebarOpen", "setSidebarOpen", "models", "setModels", "loading", "setLoading", "totalCount", "setTotalCount", "paginationModel", "setPaginationModel", "page", "pageSize", "dialogOpen", "setDialogOpen", "editMode", "setEditMode", "selectedDocument", "setSelectedDocument", "uploadFile", "setUploadFile", "replaceFile", "setReplaceFile", "formData", "setFormData", "title", "documentType", "priority", "filters", "setFilters", "order<PERSON><PERSON><PERSON><PERSON>s", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roles", "unsubscribe", "OrganizationId", "fetchTrainingDocuments", "skip", "top", "handleFileChange", "event", "_event$target$files", "file", "target", "files", "handleReplaceFileChange", "_event$target$files2", "handleFormChange", "field", "value", "prev", "handleUpload", "UserId", "resetForm", "error", "_err$response", "_err$response$data", "_err$response2", "_err$response2$data", "err", "errorMessage", "response", "data", "ErrorMessage", "message", "handleEdit", "document", "console", "log", "DocumentType", "map", "dt", "documentTypeValue", "mapDocumentTypeToFormValue", "some", "warn", "Title", "Priority", "handleUpdate", "Id", "handleDelete", "documentId", "window", "confirm", "handleDownload", "isValidDocumentType", "docType", "openUploadDialog", "getPriorityLabel", "priorityLevel", "find", "p", "label", "getPriorityColor", "getDocumentTypeLabel", "type", "apiDocumentType", "exactMatch", "valueMatch", "toLowerCase", "labelMatch", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "parseFloat", "pow", "toFixed", "columns", "headerName", "resizable", "sortable", "renderCell", "params", "Date", "toLocaleDateString", "children", "size", "onClick", "row", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "disabled", "role", "includes", "style", "marginTop", "rows", "pagination", "paginationMode", "rowCount", "onPaginationModelChange", "pageSizeOptions", "getRowId", "disableRowSelectionOnClick", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "sx", "display", "flexDirection", "gap", "mt", "variant", "component", "startIcon", "name", "hidden", "onChange", "accept", "mb", "bgcolor", "borderRadius", "alignItems", "FileSize", "color", "fontSize", "severity", "flexGrow", "e", "required", "displayEmpty", "level", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/components/training/Training.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport {\r\n    Box,\r\n    Button,\r\n    Container,\r\n    TextField,\r\n    Dialog,\r\n    DialogTitle,\r\n    DialogContent,\r\n    DialogActions,\r\n    FormControl,\r\n    InputLabel,\r\n    Select,\r\n    MenuItem,\r\n    IconButton,\r\n    Tooltip,\r\n    Alert\r\n} from '@mui/material';\r\nimport { DataGrid, GridColDef, GridPaginationModel } from '@mui/x-data-grid';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { useAuth } from '../auth/AuthProvider';\r\nimport { isSidebarOpen, subscribe } from \"../adminMenu/sidemenustate\";\r\nimport { useContext } from 'react';\r\nimport { AccountContext } from '../account/AccountContext';\r\nimport { useSnackbar } from '../../SnackbarContext';\r\nimport DeleteIcon from '@mui/icons-material/Delete';\r\nimport CloudUploadIcon from '@mui/icons-material/CloudUpload';\r\nimport {\r\n    getAllTrainingDocuments,\r\n    uploadTrainingDocument,\r\n    DeleteKnowledgeBase,\r\n    updateTrainingDocument,\r\n    downloadTrainingDocument,\r\n    replaceTrainingDocumentFile\r\n} from '../../services/TrainingService';\r\nimport { TrainingDocument, DocumentTypes, PriorityLevels } from '../../models/Training';\r\nimport { AxiosError } from 'axios';\r\n\r\ninterface TrainingProps {}\r\n\r\nconst Training: React.FC<TrainingProps> = () => {\r\n    const { t: translate } = useTranslation();\r\n    const { userDetails } = useAuth();\r\n    const { accountId } = useContext(AccountContext);\r\n    const { openSnackbar } = useSnackbar();\r\n    const [sidebarOpen, setSidebarOpen] = useState(isSidebarOpen());\r\n    const [models, setModels] = useState<TrainingDocument[]>([]);\r\n    const [loading, setLoading] = useState(false);\r\n    const [totalCount, setTotalCount] = useState(0);\r\n    const [paginationModel, setPaginationModel] = useState<GridPaginationModel>({\r\n        page: 0,\r\n        pageSize: 15,\r\n    });\r\n    const [dialogOpen, setDialogOpen] = useState(false);\r\n    const [editMode, setEditMode] = useState(false);\r\n    const [selectedDocument, setSelectedDocument] = useState<TrainingDocument | null>(null);\r\n    const [uploadFile, setUploadFile] = useState<File | null>(null);\r\n    const [replaceFile, setReplaceFile] = useState<File | null>(null);\r\n    const [formData, setFormData] = useState({\r\n        title: '',\r\n        documentType: '',\r\n        priority: 1\r\n    });\r\n    const [filters, setFilters] = useState<any[]>([]);\r\n    const [orderByFields, setOrderByFields] = useState('CreatedDate desc');\r\n    const { roles } = useContext(AccountContext);\r\n\r\n    useEffect(() => {\r\n        const unsubscribe = subscribe(setSidebarOpen);\r\n        return () => unsubscribe();\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        if (userDetails?.OrganizationId) {\r\n            fetchTrainingDocuments();\r\n        }\r\n    }, [userDetails?.OrganizationId, paginationModel, accountId]);\r\n\r\n    const fetchTrainingDocuments = async () => {\r\n        if (!userDetails?.OrganizationId) return;\r\n\r\n        const skip = paginationModel.page * paginationModel.pageSize;\r\n        const top = paginationModel.pageSize;\r\n\r\n        await getAllTrainingDocuments(\r\n            setModels,\r\n            setLoading,\r\n            userDetails.OrganizationId,\r\n            skip,\r\n            top,\r\n            setTotalCount,\r\n            orderByFields,\r\n            filters,\r\n            accountId\r\n        );\r\n    };\r\n\r\n    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n        const file = event.target.files?.[0];\r\n        if (file) {\r\n            setUploadFile(file);\r\n        }\r\n    };\r\n\r\n    const handleReplaceFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n        const file = event.target.files?.[0];\r\n        if (file) {\r\n            setReplaceFile(file);\r\n        }\r\n    };\r\n\r\n    const handleFormChange = (field: string, value: any) => {\r\n        setFormData(prev => ({\r\n            ...prev,\r\n            [field]: value\r\n        }));\r\n    };\r\n\r\n    const handleUpload = async () => {\r\n        if (!uploadFile || !formData.title || !formData.documentType || !userDetails?.OrganizationId || !accountId) {\r\n            openSnackbar(translate('Please fill all required fields and select a file'), 'error');\r\n            return;\r\n        }\r\n\r\n        try {\r\n            await uploadTrainingDocument(\r\n                uploadFile,\r\n                formData.title,\r\n                formData.documentType,\r\n                formData.priority,\r\n                userDetails.OrganizationId,\r\n                userDetails.UserId || '',\r\n                accountId\r\n            );\r\n\r\n            openSnackbar(translate('Document uploaded successfully'), 'success');\r\n            setDialogOpen(false);\r\n            resetForm();\r\n            fetchTrainingDocuments();\r\n        } catch (error) {\r\n            const err = error as AxiosError<{ ErrorMessage?: string; message?: string }>;\r\n\r\n            const errorMessage =\r\n            err?.response?.data?.ErrorMessage || // backend-defined error message\r\n            err?.response?.data?.message ||     // common fallback\r\n            err?.message ||                     // JS/network error\r\n            'An unexpected error occurred while uploading the document.';\r\n        \r\n          openSnackbar(translate(errorMessage), 'error');        }\r\n    };\r\n\r\n    const handleEdit = (document: TrainingDocument) => {\r\n        setSelectedDocument(document);\r\n\r\n        // Debug: Log the document type to understand the format\r\n        console.log('Document Type from API:', document.DocumentType);\r\n        console.log('Available Document Types:', DocumentTypes.map(dt => dt.value));\r\n\r\n        // Map the document type to the correct form value\r\n        const documentTypeValue = mapDocumentTypeToFormValue(document.DocumentType);\r\n\r\n        console.log('Mapped Document Type:', documentTypeValue);\r\n\r\n        // Show warning if document type couldn't be mapped to a known type\r\n        if (document.DocumentType && !DocumentTypes.some(dt => dt.value === documentTypeValue)) {\r\n            console.warn(`Document type \"${document.DocumentType}\" doesn't match any predefined types. Using original value.`);\r\n        }\r\n\r\n        setFormData({\r\n            title: document.Title,\r\n            documentType: documentTypeValue,\r\n            priority: document.Priority\r\n        });\r\n        setEditMode(true);\r\n        setDialogOpen(true);\r\n    };\r\n\r\n    const handleUpdate = async () => {\r\n        if (!selectedDocument || !formData.title || !formData.documentType || !userDetails?.OrganizationId || !accountId) {\r\n            openSnackbar(translate('Please fill all required fields'), 'error');\r\n            return;\r\n        }\r\n\r\n        try {\r\n            // If a replacement file is selected, use the file replacement API\r\n            if (replaceFile) {\r\n                await replaceTrainingDocumentFile(\r\n                    selectedDocument.Id,\r\n                    replaceFile,\r\n                    formData.title,\r\n                    formData.documentType,\r\n                    formData.priority,\r\n                    userDetails.OrganizationId,\r\n                    userDetails.UserId || '',\r\n                    accountId\r\n                );\r\n\r\n            } else {\r\n                // Otherwise, just update the metadata\r\n                await updateTrainingDocument(\r\n                    selectedDocument.Id,\r\n                    formData.title,\r\n                    formData.documentType,\r\n                    formData.priority,\r\n                    userDetails.OrganizationId,\r\n                    userDetails.UserId || ''\r\n                );\r\n\r\n                setDialogOpen(false);\r\n                resetForm();\r\n                fetchTrainingDocuments();\r\n            }\r\n        } catch (error) {\r\n            openSnackbar(translate('Failed to update document'), 'error');\r\n        }\r\n    };\r\n\r\n    const handleDelete = async (documentId: string) => {\r\n        if (!userDetails?.OrganizationId) return;\r\n\r\n        if (window.confirm('Are you sure you want to delete this document?')) {\r\n            try {\r\n                await DeleteKnowledgeBase(documentId);\r\n                openSnackbar(translate('Document deleted successfully'), 'success');\r\n                fetchTrainingDocuments();\r\n            } catch (error) {\r\n                openSnackbar(translate('Failed to delete document'), 'error');\r\n            }\r\n        }\r\n    };\r\n\r\n    const handleDownload = async (documentId: string) => {\r\n        if (!userDetails?.OrganizationId) return;\r\n\r\n        try {\r\n            await downloadTrainingDocument(documentId, userDetails.OrganizationId);\r\n        } catch (error) {\r\n            openSnackbar(translate('Failed to download document'), 'error');\r\n        }\r\n    };\r\n\r\n    const resetForm = () => {\r\n        setFormData({\r\n            title: '',\r\n            documentType: '',\r\n            priority: 1\r\n        });\r\n        setUploadFile(null);\r\n        setReplaceFile(null);\r\n        setSelectedDocument(null);\r\n        setEditMode(false);\r\n    };\r\n\r\n    // Validate if the current document type is valid\r\n    const isValidDocumentType = (docType: string): boolean => {\r\n        return DocumentTypes.some(dt => dt.value === docType) || docType === '';\r\n    };\r\n\r\n    const openUploadDialog = () => {\r\n        resetForm();\r\n        setDialogOpen(true);\r\n    };\r\n\r\n    const getPriorityLabel = (priority: number) => {\r\n        const priorityLevel = PriorityLevels.find(p => p.value === priority);\r\n        return priorityLevel ? priorityLevel.label : 'Unknown';\r\n    };\r\n\r\n    const getPriorityColor = (priority: number): \"default\" | \"primary\" | \"secondary\" | \"error\" | \"info\" | \"success\" | \"warning\" => {\r\n        switch (priority) {\r\n            case 1: return 'info';\r\n            case 2: return 'primary';\r\n            case 3: return 'warning';\r\n            case 4: return 'error';\r\n            default: return 'default';\r\n        }\r\n    };\r\n\r\n    const getDocumentTypeLabel = (type: string) => {\r\n        const docType = DocumentTypes.find(dt => dt.value === type);\r\n        return docType ? docType.label : type;\r\n    };\r\n\r\n    // Utility function to map API document type to form value\r\n    const mapDocumentTypeToFormValue = (apiDocumentType: string): string => {\r\n        if (!apiDocumentType) return '';\r\n\r\n        // Try exact match first\r\n        const exactMatch = DocumentTypes.find(dt => dt.value === apiDocumentType);\r\n        if (exactMatch) return exactMatch.value;\r\n\r\n        // Try case-insensitive match on value\r\n        const valueMatch = DocumentTypes.find(dt =>\r\n            dt.value.toLowerCase() === apiDocumentType.toLowerCase()\r\n        );\r\n        if (valueMatch) return valueMatch.value;\r\n\r\n        // Try case-insensitive match on label\r\n        const labelMatch = DocumentTypes.find(dt =>\r\n            dt.label.toLowerCase() === apiDocumentType.toLowerCase()\r\n        );\r\n        if (labelMatch) return labelMatch.value;\r\n\r\n        // Return original value if no match found\r\n        return apiDocumentType;\r\n    };\r\n\r\n    const formatFileSize = (bytes: number) => {\r\n        if (bytes === 0) return '0 Bytes';\r\n        const k = 1024;\r\n        const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n    };\r\n\r\n    const columns: GridColDef[] = [\r\n        {\r\n            field: 'Name',\r\n            headerName: translate('File Name'),\r\n            // width: 300,\r\n            resizable: false,\r\n            sortable: true\r\n        },\r\n        {\r\n            field: 'Title',\r\n            headerName: translate('Title'),\r\n            // width: 200,\r\n            resizable: false,\r\n            sortable: true\r\n        },\r\n        {\r\n            field: 'DocumentType',\r\n            headerName: translate('Document Type'),\r\n            // width: 180,\r\n            sortable: true,\r\n            resizable: false,\r\n            renderCell: (params) => getDocumentTypeLabel(params.value)\r\n        },\r\n        {\r\n            field: 'EmbeddingStatus',\r\n            headerName: translate('Training Status'),\r\n            // width: 180,\r\n            sortable: true,\r\n            resizable: false,\r\n            renderCell: (params) => getDocumentTypeLabel(params.value)\r\n        },\r\n\r\n        // {\r\n        //     field: 'Priority',\r\n        //     headerName: translate('Priority'),\r\n        //     width: 120,\r\n        //     sortable: true,\r\n        //     renderCell: (params) => (\r\n        //         <Chip\r\n        //             label={getPriorityLabel(params.value)}\r\n        //             color={getPriorityColor(params.value)}\r\n        //             size=\"small\"\r\n        //         />\r\n        //     )\r\n        // },\r\n        // {\r\n        //     field: 'FileSize',\r\n        //     headerName: translate('File Size'),\r\n        //     width: 120,\r\n        //     sortable: true,\r\n        //     renderCell: (params) => formatFileSize(params.value || 0)\r\n        // },\r\n        {\r\n            field: 'CreatedDate',\r\n            resizable: false,\r\n            headerName: translate('Upload Date'),\r\n            // width: 150,\r\n            sortable: true,\r\n            renderCell: (params) => new Date(params.value).toLocaleDateString()\r\n        },\r\n        {\r\n            field: 'actions',\r\n            headerName: translate('Actions'),\r\n            // width: 150,\r\n            sortable: false,\r\n            resizable: false,\r\n            renderCell: (params) => (\r\n                <div>\r\n                    {/* <Tooltip title=\"Download\">\r\n                        <IconButton\r\n                            size=\"small\"\r\n                            onClick={() => handleDownload(params.row.Id)}\r\n                        >\r\n                            <DownloadIcon />\r\n                        </IconButton>\r\n                    </Tooltip> */}\r\n                    {/* <Tooltip title=\"Edit\">\r\n                        <IconButton\r\n                            size=\"small\"\r\n                            onClick={() => handleEdit(params.row)}\r\n                        >\r\n                            <EditIcon />\r\n                        </IconButton>\r\n                    </Tooltip> */}\r\n                    <Tooltip title=\"Delete\">\r\n                        <IconButton\r\n                            size=\"small\"\r\n                            onClick={() => handleDelete(params.row.Id)}\r\n                        >\r\n                            <DeleteIcon />\r\n                        </IconButton>\r\n                    </Tooltip>\r\n                </div>\r\n            )\r\n        }\r\n    ];\r\n\r\n    return (\r\n        <Container>\r\n            <div className=\"qadpt-midpart setng-box\">\r\n                <div className=\"qadpt-content-block\">\r\n                    <div className=\"qadpt-head\">\r\n                        <div className=\"qadpt-title-sec\">\r\n                            <div className=\"qadpt-title\">{translate('Training')}</div>\r\n                            <div className=\"qadpt-description\">{translate('Manage knowledge base documents')}</div>\r\n                        </div>\r\n                        <div className=\"qadpt-right-part\">\r\n                            <button\r\n                                onClick={openUploadDialog}\r\n                                className=\"qadpt-memberButton\"\r\n                                disabled={ ![\"Account Admin\",\"Editor\"].some(role => roles.includes(role)) }\r\n                            >\r\n                                <i className=\"fal fa-add-plus\"></i>\r\n                                <span>{translate('Upload Document')}</span>\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div style={{marginTop:\"30px\"}}>\r\n                        <DataGrid\r\n                            className=\"qadpt-setting-grd\"\r\n\r\n                            rows={models}\r\n                            columns={columns}\r\n                            pagination\r\n                            paginationMode=\"server\"\r\n                            rowCount={totalCount}\r\n                            paginationModel={paginationModel}\r\n                            onPaginationModelChange={setPaginationModel}\r\n                            loading={loading}\r\n                            pageSizeOptions={[15, 25, 50]}\r\n                            getRowId={(row) => row.Id}\r\n                            disableRowSelectionOnClick\r\n                        />\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth=\"sm\" fullWidth className='qadpt-trainpop'>\r\n                <DialogTitle>\r\n                    {editMode ? translate('Edit Document') : translate('Upload Document')}\r\n                </DialogTitle>\r\n                <DialogContent>\r\n                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }} className=\"qadpt-uplddoc\">\r\n                        {!editMode && (\r\n                            <Button\r\n                                variant=\"outlined\"\r\n                                component=\"label\"\r\n                                startIcon={<CloudUploadIcon />}\r\n                                fullWidth\r\n                            >\r\n                                {uploadFile ? uploadFile.name : translate('Select File')}\r\n                                <input\r\n                                    type=\"file\"\r\n                                    hidden\r\n                                    onChange={handleFileChange}\r\n                                    accept=\".pdf,.doc,.docx,.txt,.md\"\r\n                                />\r\n                            </Button>\r\n                        )}\r\n\r\n                        {editMode && (\r\n                            <Box>\r\n                                {/* Show current file information */}\r\n                                <Box sx={{ mb: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>\r\n                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>\r\n\r\n                                    </Box>\r\n                                    {selectedDocument?.FileSize && (\r\n                                        <Box sx={{ color: 'text.secondary', fontSize: '0.875rem' }}>\r\n                                            {translate('Size')}: {formatFileSize(selectedDocument.FileSize)}\r\n                                        </Box>\r\n                                    )}\r\n                                </Box>\r\n\r\n                                {/* File replacement option */}\r\n                                <Button\r\n                                    variant=\"outlined\"\r\n                                    component=\"label\"\r\n                                    startIcon={<CloudUploadIcon />}\r\n                                    fullWidth\r\n                                    color={replaceFile ? \"success\" : \"primary\"}\r\n                                >\r\n                                    {replaceFile ? `${translate('New File Selected')}: ${replaceFile.name}` : translate('Change File (Optional)')}\r\n                                    <input\r\n                                        type=\"file\"\r\n                                        hidden\r\n                                        onChange={handleReplaceFileChange}\r\n                                        accept=\".pdf,.doc,.docx,.txt,.md\"\r\n                                    />\r\n                                </Button>\r\n\r\n                                {replaceFile && (\r\n                                    <Box sx={{ mt: 1, display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                                        <Alert severity=\"info\" sx={{ flexGrow: 1 }}>\r\n                                            {translate('The current file will be replaced with the new file')}\r\n                                        </Alert>\r\n                                        <Button\r\n                                            size=\"small\"\r\n                                            onClick={() => setReplaceFile(null)}\r\n                                            color=\"error\"\r\n                                            variant=\"outlined\"\r\n                                        >\r\n                                            {translate('Keep Current File')}\r\n                                        </Button>\r\n                                    </Box>\r\n                                )}\r\n                            </Box>\r\n                        )}\r\n\r\n                        <TextField\r\n                            label={translate('Title')}\r\n                            value={formData.title}\r\n                            onChange={(e) => handleFormChange('title', e.target.value)}\r\n                            fullWidth\r\n                            required\r\n                        />\r\n\r\n                        <FormControl fullWidth required>\r\n                            <InputLabel>{translate('Document Type')}</InputLabel>\r\n                            <Select\r\n                                value={formData.documentType || ''}\r\n                                onChange={(e) => handleFormChange('documentType', e.target.value)}\r\n                                label={translate('Document Type')}\r\n                                displayEmpty={false}\r\n                            >\r\n                                {DocumentTypes.map((type) => (\r\n                                    <MenuItem key={type.value} value={type.value}>\r\n                                        {type.label}\r\n                                    </MenuItem>\r\n                                ))}\r\n                                {/* Add fallback option if current value doesn't match any predefined types */}\r\n                                {formData.documentType &&\r\n                                 !DocumentTypes.some(dt => dt.value === formData.documentType) && (\r\n                                    <MenuItem key={formData.documentType} value={formData.documentType}>\r\n                                        {formData.documentType} (Custom)\r\n                                    </MenuItem>\r\n                                )}\r\n                            </Select>\r\n                        </FormControl>\r\n\r\n                        <FormControl fullWidth>\r\n                            <InputLabel>{translate('Priority')}</InputLabel>\r\n                            <Select\r\n                                value={formData.priority}\r\n                                onChange={(e) => handleFormChange('priority', e.target.value)}\r\n                                label={translate('Priority')}\r\n                            >\r\n                                {PriorityLevels.map((level) => (\r\n                                    <MenuItem key={level.value} value={level.value}>\r\n                                        {level.label}\r\n                                    </MenuItem>\r\n                                ))}\r\n                            </Select>\r\n                        </FormControl>\r\n                    </Box>\r\n                </DialogContent>\r\n                <DialogActions>\r\n                    <Button onClick={() => setDialogOpen(false)}>\r\n                        {translate('Cancel')}\r\n                    </Button>\r\n                    <Button\r\n                        onClick={editMode ? handleUpdate : handleUpload}\r\n                        variant=\"contained\"\r\n                    >\r\n                        {editMode ? translate('Update') : translate('Upload')}\r\n                    </Button>\r\n                </DialogActions>\r\n            </Dialog>\r\n        </Container>\r\n    );\r\n};\r\n\r\nexport default Training;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACIC,GAAG,EACHC,MAAM,EACNC,SAAS,EACTC,SAAS,EACTC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,UAAU,EACVC,OAAO,EACPC,KAAK,QACF,eAAe;AACtB,SAASC,QAAQ,QAAyC,kBAAkB;AAC5E,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,aAAa,EAAEC,SAAS,QAAQ,4BAA4B;AACrE,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,WAAW,QAAQ,uBAAuB;AACnD,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,SACIC,uBAAuB,EACvBC,sBAAsB,EACtBC,mBAAmB,EACnBC,sBAAsB,EACtBC,wBAAwB,EACxBC,2BAA2B,QACxB,gCAAgC;AACvC,SAA2BC,aAAa,EAAEC,cAAc,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAKxF,MAAMC,QAAiC,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5C,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGtB,cAAc,CAAC,CAAC;EACzC,MAAM;IAAEuB;EAAY,CAAC,GAAGtB,OAAO,CAAC,CAAC;EACjC,MAAM;IAAEuB;EAAU,CAAC,GAAGpB,UAAU,CAACC,cAAc,CAAC;EAChD,MAAM;IAAEoB;EAAa,CAAC,GAAGnB,WAAW,CAAC,CAAC;EACtC,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAACoB,aAAa,CAAC,CAAC,CAAC;EAC/D,MAAM,CAAC0B,MAAM,EAAEC,SAAS,CAAC,GAAG/C,QAAQ,CAAqB,EAAE,CAAC;EAC5D,MAAM,CAACgD,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkD,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACoD,eAAe,EAAEC,kBAAkB,CAAC,GAAGrD,QAAQ,CAAsB;IACxEsD,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC0D,QAAQ,EAAEC,WAAW,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC4D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7D,QAAQ,CAA0B,IAAI,CAAC;EACvF,MAAM,CAAC8D,UAAU,EAAEC,aAAa,CAAC,GAAG/D,QAAQ,CAAc,IAAI,CAAC;EAC/D,MAAM,CAACgE,WAAW,EAAEC,cAAc,CAAC,GAAGjE,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAACkE,QAAQ,EAAEC,WAAW,CAAC,GAAGnE,QAAQ,CAAC;IACrCoE,KAAK,EAAE,EAAE;IACTC,YAAY,EAAE,EAAE;IAChBC,QAAQ,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxE,QAAQ,CAAQ,EAAE,CAAC;EACjD,MAAM,CAACyE,aAAa,EAAEC,gBAAgB,CAAC,GAAG1E,QAAQ,CAAC,kBAAkB,CAAC;EACtE,MAAM;IAAE2E;EAAM,CAAC,GAAGrD,UAAU,CAACC,cAAc,CAAC;EAE5CtB,SAAS,CAAC,MAAM;IACZ,MAAM2E,WAAW,GAAGvD,SAAS,CAACwB,cAAc,CAAC;IAC7C,OAAO,MAAM+B,WAAW,CAAC,CAAC;EAC9B,CAAC,EAAE,EAAE,CAAC;EAEN3E,SAAS,CAAC,MAAM;IACZ,IAAIwC,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEoC,cAAc,EAAE;MAC7BC,sBAAsB,CAAC,CAAC;IAC5B;EACJ,CAAC,EAAE,CAACrC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEoC,cAAc,EAAEzB,eAAe,EAAEV,SAAS,CAAC,CAAC;EAE7D,MAAMoC,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,EAACrC,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEoC,cAAc,GAAE;IAElC,MAAME,IAAI,GAAG3B,eAAe,CAACE,IAAI,GAAGF,eAAe,CAACG,QAAQ;IAC5D,MAAMyB,GAAG,GAAG5B,eAAe,CAACG,QAAQ;IAEpC,MAAM5B,uBAAuB,CACzBoB,SAAS,EACTE,UAAU,EACVR,WAAW,CAACoC,cAAc,EAC1BE,IAAI,EACJC,GAAG,EACH7B,aAAa,EACbsB,aAAa,EACbF,OAAO,EACP7B,SACJ,CAAC;EACL,CAAC;EAED,MAAMuC,gBAAgB,GAAIC,KAA0C,IAAK;IAAA,IAAAC,mBAAA;IACrE,MAAMC,IAAI,IAAAD,mBAAA,GAAGD,KAAK,CAACG,MAAM,CAACC,KAAK,cAAAH,mBAAA,uBAAlBA,mBAAA,CAAqB,CAAC,CAAC;IACpC,IAAIC,IAAI,EAAE;MACNrB,aAAa,CAACqB,IAAI,CAAC;IACvB;EACJ,CAAC;EAED,MAAMG,uBAAuB,GAAIL,KAA0C,IAAK;IAAA,IAAAM,oBAAA;IAC5E,MAAMJ,IAAI,IAAAI,oBAAA,GAAGN,KAAK,CAACG,MAAM,CAACC,KAAK,cAAAE,oBAAA,uBAAlBA,oBAAA,CAAqB,CAAC,CAAC;IACpC,IAAIJ,IAAI,EAAE;MACNnB,cAAc,CAACmB,IAAI,CAAC;IACxB;EACJ,CAAC;EAED,MAAMK,gBAAgB,GAAGA,CAACC,KAAa,EAAEC,KAAU,KAAK;IACpDxB,WAAW,CAACyB,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,CAACF,KAAK,GAAGC;IACb,CAAC,CAAC,CAAC;EACP,CAAC;EAED,MAAME,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAAC/B,UAAU,IAAI,CAACI,QAAQ,CAACE,KAAK,IAAI,CAACF,QAAQ,CAACG,YAAY,IAAI,EAAC5B,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEoC,cAAc,KAAI,CAACnC,SAAS,EAAE;MACxGC,YAAY,CAACH,SAAS,CAAC,mDAAmD,CAAC,EAAE,OAAO,CAAC;MACrF;IACJ;IAEA,IAAI;MACA,MAAMZ,sBAAsB,CACxBkC,UAAU,EACVI,QAAQ,CAACE,KAAK,EACdF,QAAQ,CAACG,YAAY,EACrBH,QAAQ,CAACI,QAAQ,EACjB7B,WAAW,CAACoC,cAAc,EAC1BpC,WAAW,CAACqD,MAAM,IAAI,EAAE,EACxBpD,SACJ,CAAC;MAEDC,YAAY,CAACH,SAAS,CAAC,gCAAgC,CAAC,EAAE,SAAS,CAAC;MACpEiB,aAAa,CAAC,KAAK,CAAC;MACpBsC,SAAS,CAAC,CAAC;MACXjB,sBAAsB,CAAC,CAAC;IAC5B,CAAC,CAAC,OAAOkB,KAAK,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA,EAAAC,cAAA,EAAAC,mBAAA;MACZ,MAAMC,GAAG,GAAGL,KAAgE;MAE5E,MAAMM,YAAY,GAClB,CAAAD,GAAG,aAAHA,GAAG,wBAAAJ,aAAA,GAAHI,GAAG,CAAEE,QAAQ,cAAAN,aAAA,wBAAAC,kBAAA,GAAbD,aAAA,CAAeO,IAAI,cAAAN,kBAAA,uBAAnBA,kBAAA,CAAqBO,YAAY,MAAI;MACrCJ,GAAG,aAAHA,GAAG,wBAAAF,cAAA,GAAHE,GAAG,CAAEE,QAAQ,cAAAJ,cAAA,wBAAAC,mBAAA,GAAbD,cAAA,CAAeK,IAAI,cAAAJ,mBAAA,uBAAnBA,mBAAA,CAAqBM,OAAO,MAAQ;MACpCL,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEK,OAAO;MAAwB;MACpC,4DAA4D;MAE9D/D,YAAY,CAACH,SAAS,CAAC8D,YAAY,CAAC,EAAE,OAAO,CAAC;IAAS;EAC7D,CAAC;EAED,MAAMK,UAAU,GAAIC,QAA0B,IAAK;IAC/C/C,mBAAmB,CAAC+C,QAAQ,CAAC;;IAE7B;IACAC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEF,QAAQ,CAACG,YAAY,CAAC;IAC7DF,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE7E,aAAa,CAAC+E,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACtB,KAAK,CAAC,CAAC;;IAE3E;IACA,MAAMuB,iBAAiB,GAAGC,0BAA0B,CAACP,QAAQ,CAACG,YAAY,CAAC;IAE3EF,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEI,iBAAiB,CAAC;;IAEvD;IACA,IAAIN,QAAQ,CAACG,YAAY,IAAI,CAAC9E,aAAa,CAACmF,IAAI,CAACH,EAAE,IAAIA,EAAE,CAACtB,KAAK,KAAKuB,iBAAiB,CAAC,EAAE;MACpFL,OAAO,CAACQ,IAAI,CAAC,kBAAkBT,QAAQ,CAACG,YAAY,6DAA6D,CAAC;IACtH;IAEA5C,WAAW,CAAC;MACRC,KAAK,EAAEwC,QAAQ,CAACU,KAAK;MACrBjD,YAAY,EAAE6C,iBAAiB;MAC/B5C,QAAQ,EAAEsC,QAAQ,CAACW;IACvB,CAAC,CAAC;IACF5D,WAAW,CAAC,IAAI,CAAC;IACjBF,aAAa,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM+D,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAAC5D,gBAAgB,IAAI,CAACM,QAAQ,CAACE,KAAK,IAAI,CAACF,QAAQ,CAACG,YAAY,IAAI,EAAC5B,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEoC,cAAc,KAAI,CAACnC,SAAS,EAAE;MAC9GC,YAAY,CAACH,SAAS,CAAC,iCAAiC,CAAC,EAAE,OAAO,CAAC;MACnE;IACJ;IAEA,IAAI;MACA;MACA,IAAIwB,WAAW,EAAE;QACb,MAAMhC,2BAA2B,CAC7B4B,gBAAgB,CAAC6D,EAAE,EACnBzD,WAAW,EACXE,QAAQ,CAACE,KAAK,EACdF,QAAQ,CAACG,YAAY,EACrBH,QAAQ,CAACI,QAAQ,EACjB7B,WAAW,CAACoC,cAAc,EAC1BpC,WAAW,CAACqD,MAAM,IAAI,EAAE,EACxBpD,SACJ,CAAC;MAEL,CAAC,MAAM;QACH;QACA,MAAMZ,sBAAsB,CACxB8B,gBAAgB,CAAC6D,EAAE,EACnBvD,QAAQ,CAACE,KAAK,EACdF,QAAQ,CAACG,YAAY,EACrBH,QAAQ,CAACI,QAAQ,EACjB7B,WAAW,CAACoC,cAAc,EAC1BpC,WAAW,CAACqD,MAAM,IAAI,EAC1B,CAAC;QAEDrC,aAAa,CAAC,KAAK,CAAC;QACpBsC,SAAS,CAAC,CAAC;QACXjB,sBAAsB,CAAC,CAAC;MAC5B;IACJ,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACZrD,YAAY,CAACH,SAAS,CAAC,2BAA2B,CAAC,EAAE,OAAO,CAAC;IACjE;EACJ,CAAC;EAED,MAAMkF,YAAY,GAAG,MAAOC,UAAkB,IAAK;IAC/C,IAAI,EAAClF,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEoC,cAAc,GAAE;IAElC,IAAI+C,MAAM,CAACC,OAAO,CAAC,gDAAgD,CAAC,EAAE;MAClE,IAAI;QACA,MAAMhG,mBAAmB,CAAC8F,UAAU,CAAC;QACrChF,YAAY,CAACH,SAAS,CAAC,+BAA+B,CAAC,EAAE,SAAS,CAAC;QACnEsC,sBAAsB,CAAC,CAAC;MAC5B,CAAC,CAAC,OAAOkB,KAAK,EAAE;QACZrD,YAAY,CAACH,SAAS,CAAC,2BAA2B,CAAC,EAAE,OAAO,CAAC;MACjE;IACJ;EACJ,CAAC;EAED,MAAMsF,cAAc,GAAG,MAAOH,UAAkB,IAAK;IACjD,IAAI,EAAClF,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEoC,cAAc,GAAE;IAElC,IAAI;MACA,MAAM9C,wBAAwB,CAAC4F,UAAU,EAAElF,WAAW,CAACoC,cAAc,CAAC;IAC1E,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACZrD,YAAY,CAACH,SAAS,CAAC,6BAA6B,CAAC,EAAE,OAAO,CAAC;IACnE;EACJ,CAAC;EAED,MAAMuD,SAAS,GAAGA,CAAA,KAAM;IACpB5B,WAAW,CAAC;MACRC,KAAK,EAAE,EAAE;MACTC,YAAY,EAAE,EAAE;MAChBC,QAAQ,EAAE;IACd,CAAC,CAAC;IACFP,aAAa,CAAC,IAAI,CAAC;IACnBE,cAAc,CAAC,IAAI,CAAC;IACpBJ,mBAAmB,CAAC,IAAI,CAAC;IACzBF,WAAW,CAAC,KAAK,CAAC;EACtB,CAAC;;EAED;EACA,MAAMoE,mBAAmB,GAAIC,OAAe,IAAc;IACtD,OAAO/F,aAAa,CAACmF,IAAI,CAACH,EAAE,IAAIA,EAAE,CAACtB,KAAK,KAAKqC,OAAO,CAAC,IAAIA,OAAO,KAAK,EAAE;EAC3E,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC3BlC,SAAS,CAAC,CAAC;IACXtC,aAAa,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMyE,gBAAgB,GAAI5D,QAAgB,IAAK;IAC3C,MAAM6D,aAAa,GAAGjG,cAAc,CAACkG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1C,KAAK,KAAKrB,QAAQ,CAAC;IACpE,OAAO6D,aAAa,GAAGA,aAAa,CAACG,KAAK,GAAG,SAAS;EAC1D,CAAC;EAED,MAAMC,gBAAgB,GAAIjE,QAAgB,IAAqF;IAC3H,QAAQA,QAAQ;MACZ,KAAK,CAAC;QAAE,OAAO,MAAM;MACrB,KAAK,CAAC;QAAE,OAAO,SAAS;MACxB,KAAK,CAAC;QAAE,OAAO,SAAS;MACxB,KAAK,CAAC;QAAE,OAAO,OAAO;MACtB;QAAS,OAAO,SAAS;IAC7B;EACJ,CAAC;EAED,MAAMkE,oBAAoB,GAAIC,IAAY,IAAK;IAC3C,MAAMT,OAAO,GAAG/F,aAAa,CAACmG,IAAI,CAACnB,EAAE,IAAIA,EAAE,CAACtB,KAAK,KAAK8C,IAAI,CAAC;IAC3D,OAAOT,OAAO,GAAGA,OAAO,CAACM,KAAK,GAAGG,IAAI;EACzC,CAAC;;EAED;EACA,MAAMtB,0BAA0B,GAAIuB,eAAuB,IAAa;IACpE,IAAI,CAACA,eAAe,EAAE,OAAO,EAAE;;IAE/B;IACA,MAAMC,UAAU,GAAG1G,aAAa,CAACmG,IAAI,CAACnB,EAAE,IAAIA,EAAE,CAACtB,KAAK,KAAK+C,eAAe,CAAC;IACzE,IAAIC,UAAU,EAAE,OAAOA,UAAU,CAAChD,KAAK;;IAEvC;IACA,MAAMiD,UAAU,GAAG3G,aAAa,CAACmG,IAAI,CAACnB,EAAE,IACpCA,EAAE,CAACtB,KAAK,CAACkD,WAAW,CAAC,CAAC,KAAKH,eAAe,CAACG,WAAW,CAAC,CAC3D,CAAC;IACD,IAAID,UAAU,EAAE,OAAOA,UAAU,CAACjD,KAAK;;IAEvC;IACA,MAAMmD,UAAU,GAAG7G,aAAa,CAACmG,IAAI,CAACnB,EAAE,IACpCA,EAAE,CAACqB,KAAK,CAACO,WAAW,CAAC,CAAC,KAAKH,eAAe,CAACG,WAAW,CAAC,CAC3D,CAAC;IACD,IAAIC,UAAU,EAAE,OAAOA,UAAU,CAACnD,KAAK;;IAEvC;IACA,OAAO+C,eAAe;EAC1B,CAAC;EAED,MAAMK,cAAc,GAAIC,KAAa,IAAK;IACtC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACtC,GAAG,CAACkC,KAAK,CAAC,GAAGI,IAAI,CAACtC,GAAG,CAACmC,CAAC,CAAC,CAAC;IACnD,OAAOK,UAAU,CAAC,CAACN,KAAK,GAAGI,IAAI,CAACG,GAAG,CAACN,CAAC,EAAEE,CAAC,CAAC,EAAEK,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGN,KAAK,CAACC,CAAC,CAAC;EAC3E,CAAC;EAED,MAAMM,OAAqB,GAAG,CAC1B;IACI/D,KAAK,EAAE,MAAM;IACbgE,UAAU,EAAElH,SAAS,CAAC,WAAW,CAAC;IAClC;IACAmH,SAAS,EAAE,KAAK;IAChBC,QAAQ,EAAE;EACd,CAAC,EACD;IACIlE,KAAK,EAAE,OAAO;IACdgE,UAAU,EAAElH,SAAS,CAAC,OAAO,CAAC;IAC9B;IACAmH,SAAS,EAAE,KAAK;IAChBC,QAAQ,EAAE;EACd,CAAC,EACD;IACIlE,KAAK,EAAE,cAAc;IACrBgE,UAAU,EAAElH,SAAS,CAAC,eAAe,CAAC;IACtC;IACAoH,QAAQ,EAAE,IAAI;IACdD,SAAS,EAAE,KAAK;IAChBE,UAAU,EAAGC,MAAM,IAAKtB,oBAAoB,CAACsB,MAAM,CAACnE,KAAK;EAC7D,CAAC,EACD;IACID,KAAK,EAAE,iBAAiB;IACxBgE,UAAU,EAAElH,SAAS,CAAC,iBAAiB,CAAC;IACxC;IACAoH,QAAQ,EAAE,IAAI;IACdD,SAAS,EAAE,KAAK;IAChBE,UAAU,EAAGC,MAAM,IAAKtB,oBAAoB,CAACsB,MAAM,CAACnE,KAAK;EAC7D,CAAC;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IACID,KAAK,EAAE,aAAa;IACpBiE,SAAS,EAAE,KAAK;IAChBD,UAAU,EAAElH,SAAS,CAAC,aAAa,CAAC;IACpC;IACAoH,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAGC,MAAM,IAAK,IAAIC,IAAI,CAACD,MAAM,CAACnE,KAAK,CAAC,CAACqE,kBAAkB,CAAC;EACtE,CAAC,EACD;IACItE,KAAK,EAAE,SAAS;IAChBgE,UAAU,EAAElH,SAAS,CAAC,SAAS,CAAC;IAChC;IACAoH,QAAQ,EAAE,KAAK;IACfD,SAAS,EAAE,KAAK;IAChBE,UAAU,EAAGC,MAAM,iBACf1H,OAAA;MAAA6H,QAAA,eAiBI7H,OAAA,CAACrB,OAAO;QAACqD,KAAK,EAAC,QAAQ;QAAA6F,QAAA,eACnB7H,OAAA,CAACtB,UAAU;UACPoJ,IAAI,EAAC,OAAO;UACZC,OAAO,EAAEA,CAAA,KAAMzC,YAAY,CAACoC,MAAM,CAACM,GAAG,CAAC3C,EAAE,CAAE;UAAAwC,QAAA,eAE3C7H,OAAA,CAACX,UAAU;YAAA4I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT;EAEb,CAAC,CACJ;EAED,oBACIpI,OAAA,CAAChC,SAAS;IAAA6J,QAAA,gBACN7H,OAAA;MAAKqI,SAAS,EAAC,yBAAyB;MAAAR,QAAA,eACpC7H,OAAA;QAAKqI,SAAS,EAAC,qBAAqB;QAAAR,QAAA,gBAChC7H,OAAA;UAAKqI,SAAS,EAAC,YAAY;UAAAR,QAAA,gBACvB7H,OAAA;YAAKqI,SAAS,EAAC,iBAAiB;YAAAR,QAAA,gBAC5B7H,OAAA;cAAKqI,SAAS,EAAC,aAAa;cAAAR,QAAA,EAAEzH,SAAS,CAAC,UAAU;YAAC;cAAA6H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1DpI,OAAA;cAAKqI,SAAS,EAAC,mBAAmB;cAAAR,QAAA,EAAEzH,SAAS,CAAC,iCAAiC;YAAC;cAAA6H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtF,CAAC,eACNpI,OAAA;YAAKqI,SAAS,EAAC,kBAAkB;YAAAR,QAAA,eAC7B7H,OAAA;cACI+H,OAAO,EAAElC,gBAAiB;cAC1BwC,SAAS,EAAC,oBAAoB;cAC9BC,QAAQ,EAAG,CAAC,CAAC,eAAe,EAAC,QAAQ,CAAC,CAACtD,IAAI,CAACuD,IAAI,IAAIhG,KAAK,CAACiG,QAAQ,CAACD,IAAI,CAAC,CAAG;cAAAV,QAAA,gBAE3E7H,OAAA;gBAAGqI,SAAS,EAAC;cAAiB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnCpI,OAAA;gBAAA6H,QAAA,EAAOzH,SAAS,CAAC,iBAAiB;cAAC;gBAAA6H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENpI,OAAA;UAAKyI,KAAK,EAAE;YAACC,SAAS,EAAC;UAAM,CAAE;UAAAb,QAAA,eAC3B7H,OAAA,CAACnB,QAAQ;YACLwJ,SAAS,EAAC,mBAAmB;YAE7BM,IAAI,EAAEjI,MAAO;YACb2G,OAAO,EAAEA,OAAQ;YACjBuB,UAAU;YACVC,cAAc,EAAC,QAAQ;YACvBC,QAAQ,EAAEhI,UAAW;YACrBE,eAAe,EAAEA,eAAgB;YACjC+H,uBAAuB,EAAE9H,kBAAmB;YAC5CL,OAAO,EAAEA,OAAQ;YACjBoI,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;YAC9BC,QAAQ,EAAGjB,GAAG,IAAKA,GAAG,CAAC3C,EAAG;YAC1B6D,0BAA0B;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENpI,OAAA,CAAC9B,MAAM;MAACiL,IAAI,EAAE/H,UAAW;MAACgI,OAAO,EAAEA,CAAA,KAAM/H,aAAa,CAAC,KAAK,CAAE;MAACgI,QAAQ,EAAC,IAAI;MAACC,SAAS;MAACjB,SAAS,EAAC,gBAAgB;MAAAR,QAAA,gBAC7G7H,OAAA,CAAC7B,WAAW;QAAA0J,QAAA,EACPvG,QAAQ,GAAGlB,SAAS,CAAC,eAAe,CAAC,GAAGA,SAAS,CAAC,iBAAiB;MAAC;QAAA6H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eACdpI,OAAA,CAAC5B,aAAa;QAAAyJ,QAAA,eACV7H,OAAA,CAAClC,GAAG;UAACyL,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,aAAa,EAAE,QAAQ;YAAEC,GAAG,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAE,CAAE;UAACtB,SAAS,EAAC,eAAe;UAAAR,QAAA,GAC1F,CAACvG,QAAQ,iBACNtB,OAAA,CAACjC,MAAM;YACH6L,OAAO,EAAC,UAAU;YAClBC,SAAS,EAAC,OAAO;YACjBC,SAAS,eAAE9J,OAAA,CAACV,eAAe;cAAA2I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC/BkB,SAAS;YAAAzB,QAAA,GAERnG,UAAU,GAAGA,UAAU,CAACqI,IAAI,GAAG3J,SAAS,CAAC,aAAa,CAAC,eACxDJ,OAAA;cACIqG,IAAI,EAAC,MAAM;cACX2D,MAAM;cACNC,QAAQ,EAAEpH,gBAAiB;cAC3BqH,MAAM,EAAC;YAA0B;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACX,EAEA9G,QAAQ,iBACLtB,OAAA,CAAClC,GAAG;YAAA+J,QAAA,gBAEA7H,OAAA,CAAClC,GAAG;cAACyL,EAAE,EAAE;gBAAEY,EAAE,EAAE,CAAC;gBAAElE,CAAC,EAAE,CAAC;gBAAEmE,OAAO,EAAE,SAAS;gBAAEC,YAAY,EAAE;cAAE,CAAE;cAAAxC,QAAA,gBAC1D7H,OAAA,CAAClC,GAAG;gBAACyL,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEc,UAAU,EAAE,QAAQ;kBAAEZ,GAAG,EAAE,CAAC;kBAAES,EAAE,EAAE;gBAAE;cAAE;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAE7D,CAAC,EACL,CAAA5G,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE+I,QAAQ,kBACvBvK,OAAA,CAAClC,GAAG;gBAACyL,EAAE,EAAE;kBAAEiB,KAAK,EAAE,gBAAgB;kBAAEC,QAAQ,EAAE;gBAAW,CAAE;gBAAA5C,QAAA,GACtDzH,SAAS,CAAC,MAAM,CAAC,EAAC,IAAE,EAACuG,cAAc,CAACnF,gBAAgB,CAAC+I,QAAQ,CAAC;cAAA;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CACR;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAGNpI,OAAA,CAACjC,MAAM;cACH6L,OAAO,EAAC,UAAU;cAClBC,SAAS,EAAC,OAAO;cACjBC,SAAS,eAAE9J,OAAA,CAACV,eAAe;gBAAA2I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC/BkB,SAAS;cACTkB,KAAK,EAAE5I,WAAW,GAAG,SAAS,GAAG,SAAU;cAAAiG,QAAA,GAE1CjG,WAAW,GAAG,GAAGxB,SAAS,CAAC,mBAAmB,CAAC,KAAKwB,WAAW,CAACmI,IAAI,EAAE,GAAG3J,SAAS,CAAC,wBAAwB,CAAC,eAC7GJ,OAAA;gBACIqG,IAAI,EAAC,MAAM;gBACX2D,MAAM;gBACNC,QAAQ,EAAE9G,uBAAwB;gBAClC+G,MAAM,EAAC;cAA0B;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAERxG,WAAW,iBACR5B,OAAA,CAAClC,GAAG;cAACyL,EAAE,EAAE;gBAAEI,EAAE,EAAE,CAAC;gBAAEH,OAAO,EAAE,MAAM;gBAAEc,UAAU,EAAE,QAAQ;gBAAEZ,GAAG,EAAE;cAAE,CAAE;cAAA7B,QAAA,gBAC9D7H,OAAA,CAACpB,KAAK;gBAAC8L,QAAQ,EAAC,MAAM;gBAACnB,EAAE,EAAE;kBAAEoB,QAAQ,EAAE;gBAAE,CAAE;gBAAA9C,QAAA,EACtCzH,SAAS,CAAC,qDAAqD;cAAC;gBAAA6H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACRpI,OAAA,CAACjC,MAAM;gBACH+J,IAAI,EAAC,OAAO;gBACZC,OAAO,EAAEA,CAAA,KAAMlG,cAAc,CAAC,IAAI,CAAE;gBACpC2I,KAAK,EAAC,OAAO;gBACbZ,OAAO,EAAC,UAAU;gBAAA/B,QAAA,EAEjBzH,SAAS,CAAC,mBAAmB;cAAC;gBAAA6H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CACR,eAEDpI,OAAA,CAAC/B,SAAS;YACNiI,KAAK,EAAE9F,SAAS,CAAC,OAAO,CAAE;YAC1BmD,KAAK,EAAEzB,QAAQ,CAACE,KAAM;YACtBiI,QAAQ,EAAGW,CAAC,IAAKvH,gBAAgB,CAAC,OAAO,EAAEuH,CAAC,CAAC3H,MAAM,CAACM,KAAK,CAAE;YAC3D+F,SAAS;YACTuB,QAAQ;UAAA;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAEFpI,OAAA,CAAC1B,WAAW;YAACgL,SAAS;YAACuB,QAAQ;YAAAhD,QAAA,gBAC3B7H,OAAA,CAACzB,UAAU;cAAAsJ,QAAA,EAAEzH,SAAS,CAAC,eAAe;YAAC;cAAA6H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACrDpI,OAAA,CAACxB,MAAM;cACH+E,KAAK,EAAEzB,QAAQ,CAACG,YAAY,IAAI,EAAG;cACnCgI,QAAQ,EAAGW,CAAC,IAAKvH,gBAAgB,CAAC,cAAc,EAAEuH,CAAC,CAAC3H,MAAM,CAACM,KAAK,CAAE;cAClE2C,KAAK,EAAE9F,SAAS,CAAC,eAAe,CAAE;cAClC0K,YAAY,EAAE,KAAM;cAAAjD,QAAA,GAEnBhI,aAAa,CAAC+E,GAAG,CAAEyB,IAAI,iBACpBrG,OAAA,CAACvB,QAAQ;gBAAkB8E,KAAK,EAAE8C,IAAI,CAAC9C,KAAM;gBAAAsE,QAAA,EACxCxB,IAAI,CAACH;cAAK,GADAG,IAAI,CAAC9C,KAAK;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEf,CACb,CAAC,EAEDtG,QAAQ,CAACG,YAAY,IACrB,CAACpC,aAAa,CAACmF,IAAI,CAACH,EAAE,IAAIA,EAAE,CAACtB,KAAK,KAAKzB,QAAQ,CAACG,YAAY,CAAC,iBAC1DjC,OAAA,CAACvB,QAAQ;gBAA6B8E,KAAK,EAAEzB,QAAQ,CAACG,YAAa;gBAAA4F,QAAA,GAC9D/F,QAAQ,CAACG,YAAY,EAAC,WAC3B;cAAA,GAFeH,QAAQ,CAACG,YAAY;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAE1B,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEdpI,OAAA,CAAC1B,WAAW;YAACgL,SAAS;YAAAzB,QAAA,gBAClB7H,OAAA,CAACzB,UAAU;cAAAsJ,QAAA,EAAEzH,SAAS,CAAC,UAAU;YAAC;cAAA6H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAChDpI,OAAA,CAACxB,MAAM;cACH+E,KAAK,EAAEzB,QAAQ,CAACI,QAAS;cACzB+H,QAAQ,EAAGW,CAAC,IAAKvH,gBAAgB,CAAC,UAAU,EAAEuH,CAAC,CAAC3H,MAAM,CAACM,KAAK,CAAE;cAC9D2C,KAAK,EAAE9F,SAAS,CAAC,UAAU,CAAE;cAAAyH,QAAA,EAE5B/H,cAAc,CAAC8E,GAAG,CAAEmG,KAAK,iBACtB/K,OAAA,CAACvB,QAAQ;gBAAmB8E,KAAK,EAAEwH,KAAK,CAACxH,KAAM;gBAAAsE,QAAA,EAC1CkD,KAAK,CAAC7E;cAAK,GADD6E,KAAK,CAACxH,KAAK;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhB,CACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAChBpI,OAAA,CAAC3B,aAAa;QAAAwJ,QAAA,gBACV7H,OAAA,CAACjC,MAAM;UAACgK,OAAO,EAAEA,CAAA,KAAM1G,aAAa,CAAC,KAAK,CAAE;UAAAwG,QAAA,EACvCzH,SAAS,CAAC,QAAQ;QAAC;UAAA6H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACTpI,OAAA,CAACjC,MAAM;UACHgK,OAAO,EAAEzG,QAAQ,GAAG8D,YAAY,GAAG3B,YAAa;UAChDmG,OAAO,EAAC,WAAW;UAAA/B,QAAA,EAElBvG,QAAQ,GAAGlB,SAAS,CAAC,QAAQ,CAAC,GAAGA,SAAS,CAAC,QAAQ;QAAC;UAAA6H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEpB,CAAC;AAAClI,EAAA,CAliBID,QAAiC;EAAA,QACVnB,cAAc,EACfC,OAAO,EAENK,WAAW;AAAA;AAAA4L,EAAA,GAJlC/K,QAAiC;AAoiBvC,eAAeA,QAAQ;AAAC,IAAA+K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}