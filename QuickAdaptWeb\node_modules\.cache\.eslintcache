[{"E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\index.tsx": "1", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\reportWebVitals.ts": "2", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\App.tsx": "3", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\auth\\AuthProvider.tsx": "4", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\SnackbarContext.tsx": "5", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\ExtensionContext.tsx": "6", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\RtlContext.tsx": "7", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\layout\\Layout.tsx": "8", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\ExpirelinkService.tsx": "9", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\routing\\Routings.tsx": "10", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\APIService.tsx": "11", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\account\\AccountContext.tsx": "12", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\auth\\UseAuth.tsx": "13", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\account\\AccountList.tsx": "14", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\Callback.tsx": "15", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\routing\\ProtectedRoute.tsx": "16", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\account\\AccountCreate.tsx": "17", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\RegistrationPage\\RegistrationPage.tsx": "18", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\adminMenu\\AdminMenu.tsx": "19", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\DomainSettings.tsx": "20", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\NotificationSettings.tsx": "21", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\ThemeSettings.tsx": "22", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\RightSettings.tsx": "23", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\BillingSettings.tsx": "24", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\UnInstall.tsx": "25", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\InstallSettings.tsx": "26", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\Settings.tsx": "27", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\TeamSettings.tsx": "28", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\guide\\Builder.tsx": "29", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\guide\\GuideList.tsx": "30", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\AdminList.tsx": "31", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\AdminPage.tsx": "32", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\OrganizationList.tsx": "33", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\audience\\Audience.tsx": "34", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\tours\\Tours.tsx": "35", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\banners\\Banners.tsx": "36", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\announcements\\Announcements.tsx": "37", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\tooltips\\Tooltips.tsx": "38", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\hotspots\\Hotspots.tsx": "39", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\checklists\\Checklists.tsx": "40", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\surveys\\Survey.tsx": "41", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\dashboard\\Dashboard.tsx": "42", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\agents\\ScriptHistory.tsx": "43", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\agents\\Scripts.tsx": "44", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\agents\\ScriptHistoryViewer.tsx": "45", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\agents\\Agentslist.tsx": "46", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\webappsettingspage\\WebAppSettings.tsx": "47", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\login\\Expiredlink.tsx": "48", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\login\\Superadminloginpage.tsx": "49", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\login\\ResetPassword.tsx": "50", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\login\\Forgotpassword.tsx": "51", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\login\\login.tsx": "52", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\auth\\OidcConfig.ts": "53", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\OrganizationService.ts": "54", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\AccountService.tsx": "55", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\adminMenu\\sidemenustate.tsx": "56", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\MultilingualService.ts": "57", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\account\\AccountsColumnMenu.tsx": "58", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\account\\AccountEdit.tsx": "59", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\Signup.tsx": "60", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\pagewrapper.tsx": "61", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\ProfileSettings.tsx": "62", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\adminMenu\\sideMenu.tsx": "63", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\AlertSettings.tsx": "64", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\fileManagement\\FileList.tsx": "65", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\auditLog\\SuperAdminAuditLogList.tsx": "66", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\auditLog\\AuditLogList.tsx": "67", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\training\\Training.tsx": "68", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserList.tsx": "69", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\Home.tsx": "70", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\GuideService.tsx": "71", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\UserRoleService.ts": "72", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\Filterpopup.tsx": "73", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\guide\\GuideEdit.tsx": "74", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\guide\\GuideCreate.tsx": "75", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\guide\\GuideTable.tsx": "76", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\UserService.ts": "77", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\Grid.tsx": "78", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\AddAdmin.tsx": "79", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\timezones.tsx": "80", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\multilingual\\LanguageContext.tsx": "81", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\multilingual\\Multilingual.tsx": "82", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\feedback\\ShareFeedbackPopup.tsx": "83", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\roles\\RolePopup.tsx": "84", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\OrganizationEdit.tsx": "85", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\TimeZoneConversion.tsx": "86", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\OrganizationCustomColumnMenu.tsx": "87", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\multilingual\\i18n.tsx": "88", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\roles\\RoleDeletePopup.tsx": "89", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\CreateNewGuidePopup.tsx": "90", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\ExtensionRequiredPopup.tsx": "91", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\assets\\icons\\icons.tsx": "92", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\CloneGuidePopup.tsx": "93", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\utils\\openGuideInBuilder.ts": "94", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\ScriptService.ts": "95", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\SystemPromtServices.tsx": "96", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\ForgotPasswordService.tsx": "97", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\LoginService.tsx": "98", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\ResetpasswordService.tsx": "99", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\adminMenu\\changepassword.tsx": "100", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\account\\AccountCustomColumnMenuUserItem.tsx": "101", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\ProfileSettingPageService.tsx": "102", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\Popup.tsx": "103", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\FileManagementService.tsx": "104", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\AuditLogServices.tsx": "105", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\TrainingService.tsx": "106", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserCreate.tsx": "107", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserEdit.tsx": "108", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserPasswordReset.tsx": "109", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserCustomColumnMenu.tsx": "110", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\orgData.js": "111", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\models\\Training.ts": "112", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserUnblock.tsx": "113", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserEnable.tsx": "114", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\Userdisable.tsx": "115", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserBlock.tsx": "116", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\FeedbackService.tsx": "117", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\feedback\\FeedbackConfirmPopup.tsx": "118", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\OrganizationcustomcolumnMenuItem.tsx": "119", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserCustomColumnMenuUserItem.tsx": "120", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\adminMenu\\logoutpopup.tsx": "121", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\dashboard\\AnalyticsDashboard.tsx": "122", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\EditSubscription.tsx": "123", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\Card.tsx": "124", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\ModernButton.tsx": "125", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\ModernDataGrid.tsx": "126", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\dashboard\\ModernDashboard.tsx": "127", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\dashboard\\ChartPlaceholder.tsx": "128", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\dashboard\\AnalyticsTab.tsx": "129", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\dashboard\\AIPerformanceTab.tsx": "130", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\dashboard\\OverviewTab.tsx": "131", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\CircularIndeterminate.tsx": "132", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\routing\\NavigationPromptMultilingual.tsx": "133", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\routing\\usePrompt.ts": "134", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\ImageUploadSection.tsx": "135", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\DashboardService.tsx": "136"}, {"size": 789, "mtime": *************, "results": "137", "hashOfConfig": "138"}, {"size": 440, "mtime": *************, "results": "139", "hashOfConfig": "138"}, {"size": 10723, "mtime": *************, "results": "140", "hashOfConfig": "138"}, {"size": 7540, "mtime": *************, "results": "141", "hashOfConfig": "138"}, {"size": 2488, "mtime": *************, "results": "142", "hashOfConfig": "138"}, {"size": 3194, "mtime": *************, "results": "143", "hashOfConfig": "138"}, {"size": 730, "mtime": *************, "results": "144", "hashOfConfig": "138"}, {"size": 526, "mtime": *************, "results": "145", "hashOfConfig": "138"}, {"size": 872, "mtime": *************, "results": "146", "hashOfConfig": "138"}, {"size": 11574, "mtime": *************, "results": "147", "hashOfConfig": "138"}, {"size": 4588, "mtime": *************, "results": "148", "hashOfConfig": "138"}, {"size": 793, "mtime": *************, "results": "149", "hashOfConfig": "138"}, {"size": 193, "mtime": 1742548446242, "results": "150", "hashOfConfig": "138"}, {"size": 21571, "mtime": *************, "results": "151", "hashOfConfig": "138"}, {"size": 925, "mtime": *************, "results": "152", "hashOfConfig": "138"}, {"size": 3537, "mtime": *************, "results": "153", "hashOfConfig": "138"}, {"size": 13558, "mtime": 1754384420584, "results": "154", "hashOfConfig": "138"}, {"size": 22209, "mtime": 1742548446227, "results": "155", "hashOfConfig": "138"}, {"size": 42286, "mtime": *************, "results": "156", "hashOfConfig": "138"}, {"size": 999, "mtime": 1742548499244, "results": "157", "hashOfConfig": "138"}, {"size": 191, "mtime": 1742548446273, "results": "158", "hashOfConfig": "138"}, {"size": 153, "mtime": 1742548446273, "results": "159", "hashOfConfig": "138"}, {"size": 5719, "mtime": 1752831950345, "results": "160", "hashOfConfig": "138"}, {"size": 1269, "mtime": 1742548499244, "results": "161", "hashOfConfig": "138"}, {"size": 3373, "mtime": 1752131906083, "results": "162", "hashOfConfig": "138"}, {"size": 6369, "mtime": 1753765611862, "results": "163", "hashOfConfig": "138"}, {"size": 8494, "mtime": 1754372915693, "results": "164", "hashOfConfig": "138"}, {"size": 42005, "mtime": 1753765611862, "results": "165", "hashOfConfig": "138"}, {"size": 3575, "mtime": 1753765611846, "results": "166", "hashOfConfig": "138"}, {"size": 6466, "mtime": 1742548446258, "results": "167", "hashOfConfig": "138"}, {"size": 11554, "mtime": 1742548499213, "results": "168", "hashOfConfig": "138"}, {"size": 21150, "mtime": 1753765611846, "results": "169", "hashOfConfig": "138"}, {"size": 34232, "mtime": 1754372915693, "results": "170", "hashOfConfig": "138"}, {"size": 643, "mtime": 1742548499166, "results": "171", "hashOfConfig": "138"}, {"size": 17113, "mtime": 1753765611877, "results": "172", "hashOfConfig": "138"}, {"size": 18389, "mtime": 1753765611830, "results": "173", "hashOfConfig": "138"}, {"size": 17310, "mtime": *************, "results": "174", "hashOfConfig": "138"}, {"size": 16211, "mtime": 1753765611877, "results": "175", "hashOfConfig": "138"}, {"size": 16172, "mtime": 1753765611846, "results": "176", "hashOfConfig": "138"}, {"size": 17308, "mtime": 1753765611830, "results": "177", "hashOfConfig": "138"}, {"size": 623, "mtime": 1742548499260, "results": "178", "hashOfConfig": "138"}, {"size": 520, "mtime": 1752148863346, "results": "179", "hashOfConfig": "138"}, {"size": 13015, "mtime": 1753765611815, "results": "180", "hashOfConfig": "138"}, {"size": 22796, "mtime": 1753765789900, "results": "181", "hashOfConfig": "138"}, {"size": 9065, "mtime": 1753765611815, "results": "182", "hashOfConfig": "138"}, {"size": 21147, "mtime": 1753796850139, "results": "183", "hashOfConfig": "138"}, {"size": 71793, "mtime": *************, "results": "184", "hashOfConfig": "138"}, {"size": 1063, "mtime": *************, "results": "185", "hashOfConfig": "138"}, {"size": 10328, "mtime": 1754372915693, "results": "186", "hashOfConfig": "138"}, {"size": 15776, "mtime": 1754372915693, "results": "187", "hashOfConfig": "138"}, {"size": 12764, "mtime": *************, "results": "188", "hashOfConfig": "138"}, {"size": 23545, "mtime": 1754372915693, "results": "189", "hashOfConfig": "138"}, {"size": 746, "mtime": 1742548499166, "results": "190", "hashOfConfig": "138"}, {"size": 7835, "mtime": *************, "results": "191", "hashOfConfig": "138"}, {"size": 10794, "mtime": *************, "results": "192", "hashOfConfig": "138"}, {"size": 435, "mtime": 1742548446242, "results": "193", "hashOfConfig": "138"}, {"size": 6051, "mtime": 1753765611909, "results": "194", "hashOfConfig": "138"}, {"size": 1501, "mtime": 1742548499150, "results": "195", "hashOfConfig": "138"}, {"size": 16479, "mtime": 1754384420584, "results": "196", "hashOfConfig": "138"}, {"size": 1027, "mtime": 1742548446317, "results": "197", "hashOfConfig": "138"}, {"size": 559, "mtime": 1742548499213, "results": "198", "hashOfConfig": "138"}, {"size": 15765, "mtime": 1753765611862, "results": "199", "hashOfConfig": "138"}, {"size": 15153, "mtime": 1753765722756, "results": "200", "hashOfConfig": "138"}, {"size": 1260, "mtime": 1742548499244, "results": "201", "hashOfConfig": "138"}, {"size": 27417, "mtime": 1754384420600, "results": "202", "hashOfConfig": "138"}, {"size": 24480, "mtime": 1753765611815, "results": "203", "hashOfConfig": "138"}, {"size": 18333, "mtime": 1753765611815, "results": "204", "hashOfConfig": "138"}, {"size": 23712, "mtime": *************, "results": "205", "hashOfConfig": "138"}, {"size": 50891, "mtime": 1753796815481, "results": "206", "hashOfConfig": "138"}, {"size": 4616, "mtime": *************, "results": "207", "hashOfConfig": "138"}, {"size": 9230, "mtime": 1753340131768, "results": "208", "hashOfConfig": "138"}, {"size": 3786, "mtime": 1753765611909, "results": "209", "hashOfConfig": "138"}, {"size": 9015, "mtime": 1754372915693, "results": "210", "hashOfConfig": "138"}, {"size": 7028, "mtime": 1742548446258, "results": "211", "hashOfConfig": "138"}, {"size": 18452, "mtime": 1753765611846, "results": "212", "hashOfConfig": "138"}, {"size": 9869, "mtime": 1742548446258, "results": "213", "hashOfConfig": "138"}, {"size": 12025, "mtime": 1753765611909, "results": "214", "hashOfConfig": "138"}, {"size": 4881, "mtime": 1742548446242, "results": "215", "hashOfConfig": "138"}, {"size": 6872, "mtime": 1742548499213, "results": "216", "hashOfConfig": "138"}, {"size": 405227, "mtime": 1742548446323, "results": "217", "hashOfConfig": "138"}, {"size": 1020, "mtime": *************, "results": "218", "hashOfConfig": "138"}, {"size": 46328, "mtime": 1753765611846, "results": "219", "hashOfConfig": "138"}, {"size": 14002, "mtime": 1753765611830, "results": "220", "hashOfConfig": "138"}, {"size": 10823, "mtime": 1753765611862, "results": "221", "hashOfConfig": "138"}, {"size": 13679, "mtime": 1754372915693, "results": "222", "hashOfConfig": "138"}, {"size": 883, "mtime": 1742548499182, "results": "223", "hashOfConfig": "138"}, {"size": 2421, "mtime": 1742548499213, "results": "224", "hashOfConfig": "138"}, {"size": 336, "mtime": *************, "results": "225", "hashOfConfig": "138"}, {"size": 2150, "mtime": 1753765611862, "results": "226", "hashOfConfig": "138"}, {"size": 10084, "mtime": 1753765611830, "results": "227", "hashOfConfig": "138"}, {"size": 2801, "mtime": 1753765611830, "results": "228", "hashOfConfig": "138"}, {"size": 8497, "mtime": *************, "results": "229", "hashOfConfig": "138"}, {"size": 4766, "mtime": 1753765611830, "results": "230", "hashOfConfig": "138"}, {"size": 508, "mtime": 1753765611924, "results": "231", "hashOfConfig": "138"}, {"size": 2499, "mtime": 1747915360725, "results": "232", "hashOfConfig": "138"}, {"size": 5667, "mtime": 1748948053337, "results": "233", "hashOfConfig": "138"}, {"size": 1266, "mtime": *************, "results": "234", "hashOfConfig": "138"}, {"size": 1248, "mtime": *************, "results": "235", "hashOfConfig": "138"}, {"size": 1566, "mtime": 1742548499307, "results": "236", "hashOfConfig": "138"}, {"size": 12593, "mtime": 1753765611799, "results": "237", "hashOfConfig": "138"}, {"size": 5478, "mtime": 1753765611799, "results": "238", "hashOfConfig": "138"}, {"size": 1971, "mtime": 1742548499307, "results": "239", "hashOfConfig": "138"}, {"size": 2561, "mtime": 1742548446242, "results": "240", "hashOfConfig": "138"}, {"size": 3447, "mtime": 1753796815481, "results": "241", "hashOfConfig": "138"}, {"size": 2343, "mtime": *************, "results": "242", "hashOfConfig": "138"}, {"size": 6070, "mtime": 1748943304967, "results": "243", "hashOfConfig": "138"}, {"size": 23464, "mtime": 1753765611877, "results": "244", "hashOfConfig": "138"}, {"size": 19304, "mtime": 1753765611877, "results": "245", "hashOfConfig": "138"}, {"size": 6298, "mtime": *************, "results": "246", "hashOfConfig": "138"}, {"size": 1743, "mtime": 1742548499260, "results": "247", "hashOfConfig": "138"}, {"size": 103561, "mtime": 1742548446273, "results": "248", "hashOfConfig": "138"}, {"size": 1275, "mtime": 1748436448770, "results": "249", "hashOfConfig": "138"}, {"size": 3325, "mtime": 1753765611893, "results": "250", "hashOfConfig": "138"}, {"size": 3377, "mtime": 1753765611877, "results": "251", "hashOfConfig": "138"}, {"size": 3485, "mtime": 1753765611893, "results": "252", "hashOfConfig": "138"}, {"size": 2755, "mtime": 1753765611877, "results": "253", "hashOfConfig": "138"}, {"size": 767, "mtime": *************, "results": "254", "hashOfConfig": "138"}, {"size": 4371, "mtime": 1752831948941, "results": "255", "hashOfConfig": "138"}, {"size": 15272, "mtime": 1754372915693, "results": "256", "hashOfConfig": "138"}, {"size": 6105, "mtime": 1742548499260, "results": "257", "hashOfConfig": "138"}, {"size": 3528, "mtime": 1742548499166, "results": "258", "hashOfConfig": "138"}, {"size": 27045, "mtime": 1753077436320, "results": "259", "hashOfConfig": "138"}, {"size": 7038, "mtime": 1752831950128, "results": "260", "hashOfConfig": "138"}, {"size": 2502, "mtime": 1752844714979, "results": "261", "hashOfConfig": "138"}, {"size": 4346, "mtime": 1752844736208, "results": "262", "hashOfConfig": "138"}, {"size": 4594, "mtime": 1752845769016, "results": "263", "hashOfConfig": "138"}, {"size": 77766, "mtime": 1754387073533, "results": "264", "hashOfConfig": "138"}, {"size": 2910, "mtime": 1753072658211, "results": "265", "hashOfConfig": "138"}, {"size": 38272, "mtime": 1754285884674, "results": "266", "hashOfConfig": "138"}, {"size": 39034, "mtime": 1753679432013, "results": "267", "hashOfConfig": "138"}, {"size": 59647, "mtime": 1754285884674, "results": "268", "hashOfConfig": "138"}, {"size": 301, "mtime": 1753765611830, "results": "269", "hashOfConfig": "138"}, {"size": 1117, "mtime": 1753765611893, "results": "270", "hashOfConfig": "138"}, {"size": 4992, "mtime": 1753765611909, "results": "271", "hashOfConfig": "138"}, {"size": 9501, "mtime": 1754384420584, "results": "272", "hashOfConfig": "138"}, {"size": 6228, "mtime": 1754285884674, "results": "273", "hashOfConfig": "138"}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1vegnqt", {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 31, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 52, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 40, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 80, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 34, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 38, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 28, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 71, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 28, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 37, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 38, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 37, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 92, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 38, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 28, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\index.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\reportWebVitals.ts", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\App.tsx", ["682", "683", "684", "685", "686", "687", "688", "689", "690"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\auth\\AuthProvider.tsx", ["691", "692", "693", "694", "695", "696"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\SnackbarContext.tsx", ["697", "698", "699", "700", "701", "702", "703", "704", "705"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\ExtensionContext.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\RtlContext.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\layout\\Layout.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\ExpirelinkService.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\routing\\Routings.tsx", ["706", "707", "708", "709", "710", "711", "712", "713", "714", "715", "716"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\APIService.tsx", ["717", "718"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\account\\AccountContext.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\auth\\UseAuth.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\account\\AccountList.tsx", ["719", "720", "721", "722", "723", "724", "725", "726", "727", "728", "729", "730", "731", "732", "733", "734", "735", "736", "737", "738", "739", "740", "741", "742", "743", "744", "745", "746", "747", "748", "749"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\Callback.tsx", ["750", "751", "752", "753", "754", "755"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\routing\\ProtectedRoute.tsx", ["756", "757", "758", "759", "760", "761", "762", "763"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\account\\AccountCreate.tsx", ["764", "765", "766", "767", "768", "769", "770", "771", "772", "773", "774", "775", "776", "777", "778", "779", "780", "781", "782", "783", "784"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\RegistrationPage\\RegistrationPage.tsx", ["785", "786"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\adminMenu\\AdminMenu.tsx", ["787", "788", "789", "790", "791", "792", "793", "794", "795", "796", "797", "798", "799", "800", "801", "802", "803", "804", "805", "806", "807", "808", "809", "810", "811", "812", "813", "814", "815", "816", "817", "818", "819", "820", "821", "822", "823", "824", "825", "826", "827", "828", "829", "830", "831", "832", "833", "834", "835", "836", "837", "838"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\DomainSettings.tsx", ["839", "840"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\NotificationSettings.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\ThemeSettings.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\RightSettings.tsx", ["841", "842"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\BillingSettings.tsx", ["843", "844"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\UnInstall.tsx", ["845", "846", "847", "848", "849", "850"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\InstallSettings.tsx", ["851", "852", "853", "854", "855", "856", "857", "858", "859"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\Settings.tsx", ["860", "861", "862", "863", "864", "865", "866", "867", "868", "869", "870", "871", "872", "873", "874"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\TeamSettings.tsx", ["875", "876", "877", "878", "879", "880", "881", "882", "883", "884", "885", "886", "887", "888", "889", "890", "891", "892", "893", "894", "895", "896", "897", "898", "899", "900", "901", "902", "903", "904", "905", "906", "907", "908", "909", "910", "911", "912", "913", "914"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\guide\\Builder.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\guide\\GuideList.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\AdminList.tsx", ["915", "916", "917", "918"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\AdminPage.tsx", ["919", "920", "921", "922", "923", "924", "925", "926", "927", "928", "929", "930", "931"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\OrganizationList.tsx", ["932", "933", "934", "935", "936", "937", "938", "939", "940", "941", "942", "943", "944", "945", "946", "947", "948", "949", "950", "951", "952", "953", "954", "955", "956", "957", "958", "959", "960", "961", "962", "963", "964", "965", "966", "967", "968", "969", "970", "971", "972", "973", "974", "975", "976", "977", "978", "979", "980", "981", "982", "983", "984", "985", "986", "987", "988", "989", "990", "991", "992", "993", "994", "995", "996", "997", "998", "999", "1000", "1001", "1002", "1003", "1004", "1005", "1006", "1007", "1008", "1009", "1010", "1011"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\audience\\Audience.tsx", ["1012", "1013"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\tours\\Tours.tsx", ["1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026", "1027", "1028", "1029", "1030", "1031", "1032", "1033", "1034", "1035", "1036", "1037", "1038", "1039", "1040", "1041", "1042", "1043", "1044", "1045", "1046", "1047"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\banners\\Banners.tsx", ["1048", "1049", "1050", "1051", "1052", "1053", "1054", "1055", "1056", "1057", "1058", "1059", "1060", "1061", "1062", "1063", "1064", "1065", "1066", "1067", "1068", "1069", "1070", "1071", "1072", "1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080", "1081", "1082", "1083", "1084", "1085"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\announcements\\Announcements.tsx", ["1086", "1087", "1088", "1089", "1090", "1091", "1092", "1093", "1094", "1095", "1096", "1097", "1098", "1099", "1100", "1101", "1102"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\tooltips\\Tooltips.tsx", ["1103", "1104", "1105", "1106", "1107", "1108", "1109", "1110", "1111", "1112", "1113", "1114", "1115", "1116", "1117", "1118", "1119", "1120", "1121", "1122", "1123", "1124", "1125", "1126", "1127", "1128", "1129", "1130", "1131"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\hotspots\\Hotspots.tsx", ["1132", "1133", "1134", "1135", "1136", "1137", "1138", "1139", "1140", "1141", "1142", "1143", "1144", "1145", "1146", "1147", "1148", "1149", "1150", "1151", "1152", "1153", "1154", "1155", "1156", "1157", "1158", "1159"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\checklists\\Checklists.tsx", ["1160", "1161", "1162", "1163", "1164", "1165", "1166", "1167", "1168", "1169", "1170", "1171", "1172", "1173", "1174", "1175", "1176"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\surveys\\Survey.tsx", ["1177"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\dashboard\\Dashboard.tsx", ["1178"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\agents\\ScriptHistory.tsx", ["1179", "1180", "1181", "1182", "1183"], ["1184"], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\agents\\Scripts.tsx", [], ["1185"], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\agents\\ScriptHistoryViewer.tsx", ["1186", "1187"], ["1188"], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\agents\\Agentslist.tsx", ["1189", "1190", "1191", "1192", "1193", "1194", "1195", "1196", "1197", "1198", "1199", "1200", "1201", "1202", "1203", "1204", "1205", "1206", "1207", "1208", "1209", "1210", "1211", "1212", "1213", "1214", "1215", "1216", "1217", "1218", "1219", "1220", "1221", "1222", "1223", "1224", "1225", "1226", "1227", "1228", "1229", "1230", "1231", "1232", "1233", "1234", "1235", "1236", "1237", "1238", "1239", "1240", "1241", "1242", "1243", "1244", "1245", "1246", "1247", "1248", "1249", "1250", "1251", "1252", "1253", "1254", "1255", "1256", "1257", "1258", "1259"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\webappsettingspage\\WebAppSettings.tsx", ["1260", "1261", "1262", "1263", "1264", "1265", "1266", "1267", "1268", "1269", "1270", "1271", "1272", "1273", "1274", "1275", "1276", "1277", "1278", "1279", "1280", "1281", "1282", "1283", "1284", "1285", "1286", "1287", "1288"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\login\\Expiredlink.tsx", ["1289", "1290"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\login\\Superadminloginpage.tsx", ["1291", "1292", "1293", "1294", "1295", "1296", "1297", "1298", "1299", "1300", "1301", "1302", "1303", "1304", "1305", "1306", "1307", "1308", "1309", "1310", "1311", "1312", "1313", "1314", "1315", "1316", "1317", "1318"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\login\\ResetPassword.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\login\\Forgotpassword.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\login\\login.tsx", ["1319", "1320", "1321", "1322", "1323", "1324", "1325", "1326", "1327", "1328", "1329", "1330", "1331", "1332", "1333", "1334", "1335", "1336", "1337", "1338", "1339", "1340", "1341"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\auth\\OidcConfig.ts", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\OrganizationService.ts", ["1342", "1343", "1344", "1345", "1346"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\AccountService.tsx", ["1347"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\adminMenu\\sidemenustate.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\MultilingualService.ts", ["1348", "1349"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\account\\AccountsColumnMenu.tsx", ["1350"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\account\\AccountEdit.tsx", ["1351", "1352", "1353", "1354", "1355", "1356", "1357", "1358", "1359", "1360", "1361", "1362", "1363", "1364", "1365", "1366"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\Signup.tsx", ["1367", "1368"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\pagewrapper.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\ProfileSettings.tsx", ["1369", "1370", "1371", "1372", "1373", "1374", "1375", "1376", "1377", "1378", "1379", "1380", "1381", "1382", "1383", "1384", "1385", "1386", "1387", "1388", "1389", "1390", "1391", "1392", "1393", "1394", "1395"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\adminMenu\\sideMenu.tsx", ["1396", "1397", "1398", "1399", "1400", "1401", "1402", "1403", "1404", "1405", "1406", "1407", "1408", "1409", "1410", "1411", "1412"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\AlertSettings.tsx", ["1413", "1414"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\fileManagement\\FileList.tsx", ["1415", "1416", "1417", "1418", "1419", "1420", "1421", "1422", "1423", "1424", "1425", "1426", "1427", "1428", "1429", "1430", "1431", "1432", "1433", "1434", "1435", "1436", "1437", "1438", "1439", "1440", "1441", "1442", "1443", "1444", "1445", "1446", "1447", "1448", "1449", "1450", "1451"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\auditLog\\SuperAdminAuditLogList.tsx", ["1452", "1453", "1454", "1455", "1456", "1457", "1458", "1459", "1460", "1461", "1462", "1463", "1464", "1465", "1466", "1467", "1468", "1469", "1470", "1471", "1472", "1473", "1474", "1475", "1476", "1477", "1478", "1479", "1480", "1481", "1482", "1483", "1484", "1485", "1486", "1487", "1488", "1489"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\auditLog\\AuditLogList.tsx", ["1490", "1491", "1492", "1493", "1494", "1495", "1496", "1497", "1498", "1499", "1500", "1501", "1502", "1503", "1504", "1505", "1506", "1507", "1508", "1509", "1510", "1511", "1512", "1513", "1514", "1515", "1516", "1517", "1518", "1519", "1520", "1521", "1522", "1523", "1524", "1525", "1526"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\training\\Training.tsx", ["1527", "1528", "1529", "1530", "1531", "1532", "1533", "1534", "1535"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserList.tsx", ["1536", "1537", "1538", "1539", "1540", "1541", "1542", "1543", "1544", "1545", "1546", "1547", "1548", "1549", "1550", "1551", "1552", "1553", "1554", "1555", "1556", "1557", "1558", "1559", "1560", "1561", "1562", "1563", "1564", "1565", "1566", "1567", "1568", "1569", "1570", "1571", "1572", "1573", "1574", "1575", "1576", "1577", "1578", "1579", "1580", "1581", "1582", "1583", "1584", "1585", "1586", "1587", "1588", "1589", "1590", "1591", "1592", "1593", "1594", "1595", "1596", "1597", "1598", "1599", "1600", "1601", "1602", "1603", "1604", "1605", "1606", "1607", "1608", "1609", "1610", "1611", "1612", "1613", "1614", "1615", "1616", "1617", "1618", "1619", "1620", "1621", "1622", "1623", "1624", "1625", "1626", "1627"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\Home.tsx", ["1628", "1629", "1630", "1631", "1632", "1633", "1634", "1635", "1636", "1637", "1638", "1639", "1640", "1641", "1642", "1643", "1644", "1645", "1646", "1647", "1648", "1649"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\GuideService.tsx", ["1650", "1651"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\UserRoleService.ts", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\Filterpopup.tsx", ["1652"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\guide\\GuideEdit.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\guide\\GuideCreate.tsx", ["1653", "1654"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\guide\\GuideTable.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\UserService.ts", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\Grid.tsx", ["1655", "1656", "1657", "1658", "1659", "1660", "1661", "1662", "1663", "1664", "1665"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\AddAdmin.tsx", ["1666", "1667", "1668", "1669", "1670", "1671", "1672", "1673", "1674"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\timezones.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\multilingual\\LanguageContext.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\multilingual\\Multilingual.tsx", ["1675", "1676"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\feedback\\ShareFeedbackPopup.tsx", ["1677", "1678", "1679", "1680", "1681", "1682", "1683", "1684", "1685", "1686", "1687", "1688", "1689"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\roles\\RolePopup.tsx", ["1690", "1691", "1692", "1693", "1694", "1695", "1696", "1697", "1698", "1699", "1700", "1701", "1702", "1703", "1704", "1705", "1706", "1707", "1708", "1709", "1710", "1711", "1712", "1713", "1714", "1715", "1716", "1717", "1718", "1719", "1720", "1721", "1722", "1723", "1724", "1725", "1726", "1727"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\OrganizationEdit.tsx", ["1728", "1729", "1730", "1731", "1732", "1733", "1734", "1735", "1736", "1737", "1738", "1739", "1740", "1741", "1742", "1743"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\TimeZoneConversion.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\OrganizationCustomColumnMenu.tsx", ["1744", "1745", "1746", "1747", "1748", "1749"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\multilingual\\i18n.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\roles\\RoleDeletePopup.tsx", ["1750"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\CreateNewGuidePopup.tsx", ["1751", "1752", "1753", "1754", "1755", "1756", "1757", "1758", "1759", "1760", "1761"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\ExtensionRequiredPopup.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\assets\\icons\\icons.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\CloneGuidePopup.tsx", ["1762", "1763"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\utils\\openGuideInBuilder.ts", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\ScriptService.ts", ["1764"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\SystemPromtServices.tsx", ["1765"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\ForgotPasswordService.tsx", ["1766", "1767"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\LoginService.tsx", ["1768", "1769", "1770"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\ResetpasswordService.tsx", ["1771", "1772"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\adminMenu\\changepassword.tsx", ["1773", "1774"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\account\\AccountCustomColumnMenuUserItem.tsx", ["1775"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\ProfileSettingPageService.tsx", ["1776", "1777", "1778"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\Popup.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\FileManagementService.tsx", ["1779", "1780"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\AuditLogServices.tsx", ["1781", "1782"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\TrainingService.tsx", ["1783", "1784", "1785", "1786"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserCreate.tsx", ["1787", "1788", "1789", "1790", "1791", "1792", "1793", "1794", "1795", "1796", "1797", "1798", "1799", "1800", "1801", "1802", "1803", "1804", "1805", "1806", "1807", "1808", "1809", "1810", "1811", "1812"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserEdit.tsx", ["1813", "1814", "1815", "1816", "1817", "1818", "1819", "1820", "1821", "1822", "1823", "1824", "1825", "1826", "1827", "1828", "1829", "1830", "1831", "1832", "1833", "1834"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserPasswordReset.tsx", ["1835", "1836", "1837", "1838", "1839", "1840", "1841", "1842", "1843", "1844", "1845", "1846", "1847", "1848", "1849", "1850", "1851", "1852", "1853", "1854", "1855", "1856", "1857", "1858", "1859", "1860", "1861", "1862"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserCustomColumnMenu.tsx", ["1863"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\orgData.js", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\models\\Training.ts", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserUnblock.tsx", ["1864", "1865", "1866", "1867", "1868", "1869", "1870", "1871", "1872", "1873", "1874", "1875", "1876", "1877", "1878", "1879", "1880", "1881", "1882", "1883", "1884", "1885", "1886", "1887", "1888", "1889"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserEnable.tsx", ["1890", "1891", "1892", "1893", "1894", "1895", "1896", "1897", "1898", "1899", "1900", "1901", "1902", "1903", "1904", "1905", "1906", "1907", "1908", "1909", "1910", "1911", "1912", "1913", "1914", "1915"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\Userdisable.tsx", ["1916", "1917", "1918", "1919", "1920", "1921", "1922", "1923", "1924", "1925", "1926", "1927", "1928", "1929", "1930", "1931", "1932", "1933", "1934", "1935", "1936", "1937", "1938", "1939", "1940", "1941"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserBlock.tsx", ["1942", "1943", "1944", "1945", "1946", "1947", "1948", "1949", "1950", "1951", "1952", "1953", "1954", "1955", "1956", "1957", "1958", "1959", "1960", "1961", "1962", "1963", "1964", "1965", "1966", "1967"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\FeedbackService.tsx", ["1968"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\feedback\\FeedbackConfirmPopup.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\OrganizationcustomcolumnMenuItem.tsx", ["1969", "1970", "1971", "1972"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserCustomColumnMenuUserItem.tsx", ["1973"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\adminMenu\\logoutpopup.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\dashboard\\AnalyticsDashboard.tsx", ["1974"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\EditSubscription.tsx", ["1975"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\Card.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\ModernButton.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\ModernDataGrid.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\dashboard\\ModernDashboard.tsx", ["1976", "1977"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\dashboard\\ChartPlaceholder.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\dashboard\\AnalyticsTab.tsx", ["1978", "1979", "1980"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\dashboard\\AIPerformanceTab.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\dashboard\\OverviewTab.tsx", ["1981", "1982", "1983"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\CircularIndeterminate.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\routing\\NavigationPromptMultilingual.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\routing\\usePrompt.ts", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\ImageUploadSection.tsx", ["1984", "1985", "1986", "1987"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\DashboardService.tsx", [], [], {"ruleId": "1988", "severity": 1, "message": "1989", "line": 1, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 31}, {"ruleId": "1988", "severity": 1, "message": "1992", "line": 2, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 33}, {"ruleId": "1988", "severity": 1, "message": "1993", "line": 11, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 11, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "1994", "line": 16, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 16, "endColumn": 15}, {"ruleId": "1988", "severity": 1, "message": "1995", "line": 27, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 27, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "1996", "line": 27, "column": 19, "nodeType": "1990", "messageId": "1991", "endLine": 27, "endColumn": 28}, {"ruleId": "1988", "severity": 1, "message": "1997", "line": 29, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 29, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "1998", "line": 32, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 32, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "1999", "line": 32, "column": 28, "nodeType": "1990", "messageId": "1991", "endLine": 32, "endColumn": 46}, {"ruleId": "1988", "severity": 1, "message": "2000", "line": 2, "column": 16, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2001", "line": 8, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 8, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2002", "line": 10, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 10, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2003", "line": 11, "column": 26, "nodeType": "1990", "messageId": "1991", "endLine": 11, "endColumn": 38}, {"ruleId": "1988", "severity": 1, "message": "2004", "line": 60, "column": 21, "nodeType": "1990", "messageId": "1991", "endLine": 60, "endColumn": 33}, {"ruleId": "2005", "severity": 1, "message": "2006", "line": 121, "column": 6, "nodeType": "2007", "endLine": 121, "endColumn": 17, "suggestions": "2008"}, {"ruleId": "1988", "severity": 1, "message": "2009", "line": 3, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 3, "endColumn": 31}, {"ruleId": "1988", "severity": 1, "message": "2010", "line": 4, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 32}, {"ruleId": "1988", "severity": 1, "message": "2011", "line": 5, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2012", "line": 6, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 6, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2013", "line": 7, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 7, "endColumn": 33}, {"ruleId": "1988", "severity": 1, "message": "2014", "line": 8, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 8, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2015", "line": 9, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2016", "line": 10, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 10, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2017", "line": 31, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 31, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2018", "line": 3, "column": 20, "nodeType": "1990", "messageId": "1991", "endLine": 3, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2019", "line": 4, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2020", "line": 4, "column": 23, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 34}, {"ruleId": "1988", "severity": 1, "message": "2021", "line": 5, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 12}, {"ruleId": "1988", "severity": 1, "message": "2022", "line": 7, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 7, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2023", "line": 9, "column": 21, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2024", "line": 15, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 15, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2025", "line": 41, "column": 7, "nodeType": "1990", "messageId": "1991", "endLine": 41, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2026", "line": 62, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 62, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2027", "line": 62, "column": 16, "nodeType": "1990", "messageId": "1991", "endLine": 62, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2028", "line": 63, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 63, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2029", "line": 5, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 15}, {"ruleId": "1988", "severity": 1, "message": "2030", "line": 81, "column": 13, "nodeType": "1990", "messageId": "1991", "endLine": 81, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2031", "line": 5, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2032", "line": 14, "column": 34, "nodeType": "1990", "messageId": "1991", "endLine": 14, "endColumn": 50}, {"ruleId": "1988", "severity": 1, "message": "2033", "line": 16, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 16, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2034", "line": 17, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 17, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2035", "line": 20, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 20, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2036", "line": 21, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 21, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2037", "line": 26, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 26, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2038", "line": 27, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 27, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2039", "line": 33, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 33, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2040", "line": 38, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 38, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2041", "line": 39, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 39, "endColumn": 15}, {"ruleId": "1988", "severity": 1, "message": "2042", "line": 46, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 46, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2043", "line": 68, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 68, "endColumn": 15}, {"ruleId": "1988", "severity": 1, "message": "2044", "line": 74, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 74, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2045", "line": 74, "column": 19, "nodeType": "1990", "messageId": "1991", "endLine": 74, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2046", "line": 78, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 78, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2047", "line": 80, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 80, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2048", "line": 85, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 85, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2049", "line": 90, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 90, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2050", "line": 112, "column": 17, "nodeType": "1990", "messageId": "1991", "endLine": 112, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2051", "line": 115, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 115, "endColumn": 29}, {"ruleId": "1988", "severity": 1, "message": "2052", "line": 115, "column": 31, "nodeType": "1990", "messageId": "1991", "endLine": 115, "endColumn": 54}, {"ruleId": "2005", "severity": 1, "message": "2053", "line": 142, "column": 5, "nodeType": "2007", "endLine": 142, "endColumn": 22, "suggestions": "2054"}, {"ruleId": "1988", "severity": 1, "message": "2055", "line": 166, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 166, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2056", "line": 167, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 167, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2057", "line": 226, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 226, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2058", "line": 228, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 228, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2059", "line": 279, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 279, "endColumn": 36}, {"ruleId": "1988", "severity": 1, "message": "2060", "line": 290, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 290, "endColumn": 33}, {"ruleId": "1988", "severity": 1, "message": "2061", "line": 367, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 367, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2062", "line": 370, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 370, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2063", "line": 2, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2064", "line": 4, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2065", "line": 5, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2066", "line": 6, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 6, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2026", "line": 12, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 12, "endColumn": 15}, {"ruleId": "2005", "severity": 1, "message": "2067", "line": 22, "column": 6, "nodeType": "2007", "endLine": 22, "endColumn": 8, "suggestions": "2068"}, {"ruleId": "1988", "severity": 1, "message": "2069", "line": 6, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 6, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2070", "line": 8, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 8, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2071", "line": 17, "column": 24, "nodeType": "1990", "messageId": "1991", "endLine": 17, "endColumn": 37}, {"ruleId": "2005", "severity": 1, "message": "2072", "line": 52, "column": 5, "nodeType": "2007", "endLine": 52, "endColumn": 7, "suggestions": "2073"}, {"ruleId": "2074", "severity": 1, "message": "2075", "line": 55, "column": 50, "nodeType": "2076", "messageId": "2077", "endLine": 55, "endColumn": 52}, {"ruleId": "2074", "severity": 1, "message": "2075", "line": 55, "column": 88, "nodeType": "2076", "messageId": "2077", "endLine": 55, "endColumn": 90}, {"ruleId": "2078", "severity": 1, "message": "2079", "line": 83, "column": 79, "nodeType": "2080", "messageId": "2081", "endLine": 83, "endColumn": 81}, {"ruleId": "2078", "severity": 1, "message": "2079", "line": 85, "column": 81, "nodeType": "2080", "messageId": "2081", "endLine": 85, "endColumn": 83}, {"ruleId": "1988", "severity": 1, "message": "2082", "line": 1, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 36}, {"ruleId": "1988", "severity": 1, "message": "2083", "line": 5, "column": 21, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2084", "line": 5, "column": 29, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 37}, {"ruleId": "1988", "severity": 1, "message": "2085", "line": 5, "column": 47, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 57}, {"ruleId": "1988", "severity": 1, "message": "2086", "line": 5, "column": 95, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 101}, {"ruleId": "1988", "severity": 1, "message": "2032", "line": 5, "column": 103, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 119}, {"ruleId": "1988", "severity": 1, "message": "2087", "line": 5, "column": 121, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 126}, {"ruleId": "1988", "severity": 1, "message": "2088", "line": 5, "column": 128, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 138}, {"ruleId": "1988", "severity": 1, "message": "2089", "line": 6, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 6, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "1995", "line": 32, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 32, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2090", "line": 33, "column": 25, "nodeType": "1990", "messageId": "1991", "endLine": 33, "endColumn": 42}, {"ruleId": "1988", "severity": 1, "message": "2091", "line": 34, "column": 15, "nodeType": "1990", "messageId": "1991", "endLine": 34, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2092", "line": 35, "column": 14, "nodeType": "1990", "messageId": "1991", "endLine": 35, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2093", "line": 36, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 36, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2094", "line": 43, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 43, "endColumn": 15}, {"ruleId": "1988", "severity": 1, "message": "2095", "line": 69, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 69, "endColumn": 31}, {"ruleId": "1988", "severity": 1, "message": "2096", "line": 73, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 73, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2097", "line": 77, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 77, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2098", "line": 80, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 80, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2099", "line": 259, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 259, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2100", "line": 267, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 267, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2101", "line": 34, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 34, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2102", "line": 35, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 35, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2103", "line": 5, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2104", "line": 6, "column": 39, "nodeType": "1990", "messageId": "1991", "endLine": 6, "endColumn": 45}, {"ruleId": "1988", "severity": 1, "message": "2105", "line": 10, "column": 21, "nodeType": "1990", "messageId": "1991", "endLine": 10, "endColumn": 31}, {"ruleId": "1988", "severity": 1, "message": "2106", "line": 11, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 11, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2107", "line": 12, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 12, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2108", "line": 13, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 13, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2109", "line": 14, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 14, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2085", "line": 15, "column": 51, "nodeType": "1990", "messageId": "1991", "endLine": 15, "endColumn": 61}, {"ruleId": "1988", "severity": 1, "message": "2110", "line": 15, "column": 73, "nodeType": "1990", "messageId": "1991", "endLine": 15, "endColumn": 77}, {"ruleId": "1988", "severity": 1, "message": "2111", "line": 17, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 17, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2112", "line": 18, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 18, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2113", "line": 24, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 24, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2114", "line": 26, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 26, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2115", "line": 27, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 27, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2116", "line": 28, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 28, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2117", "line": 29, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 29, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2118", "line": 31, "column": 28, "nodeType": "1990", "messageId": "1991", "endLine": 31, "endColumn": 36}, {"ruleId": "1988", "severity": 1, "message": "2119", "line": 36, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 36, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2120", "line": 43, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 43, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2002", "line": 52, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 52, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2121", "line": 56, "column": 43, "nodeType": "1990", "messageId": "1991", "endLine": 56, "endColumn": 50}, {"ruleId": "1988", "severity": 1, "message": "2047", "line": 112, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 112, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2026", "line": 117, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 117, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2122", "line": 125, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 125, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2123", "line": 125, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 125, "endColumn": 46}, {"ruleId": "1988", "severity": 1, "message": "2124", "line": 128, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 128, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2125", "line": 131, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 131, "endColumn": 23}, {"ruleId": "2078", "severity": 1, "message": "2126", "line": 194, "column": 55, "nodeType": "2080", "messageId": "2081", "endLine": 194, "endColumn": 57}, {"ruleId": "2078", "severity": 1, "message": "2079", "line": 202, "column": 40, "nodeType": "2080", "messageId": "2081", "endLine": 202, "endColumn": 42}, {"ruleId": "2005", "severity": 1, "message": "2127", "line": 267, "column": 5, "nodeType": "2007", "endLine": 267, "endColumn": 16, "suggestions": "2128"}, {"ruleId": "1988", "severity": 1, "message": "2129", "line": 314, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 314, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2130", "line": 373, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 373, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2131", "line": 389, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 389, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2132", "line": 395, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 395, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2133", "line": 401, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 401, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2134", "line": 407, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 407, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2135", "line": 413, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 413, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2136", "line": 419, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 419, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2137", "line": 425, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 425, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2138", "line": 431, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 431, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2139", "line": 437, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 437, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2140", "line": 471, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 471, "endColumn": 29}, {"ruleId": "2078", "severity": 1, "message": "2126", "line": 485, "column": 56, "nodeType": "2080", "messageId": "2081", "endLine": 485, "endColumn": 58}, {"ruleId": "2005", "severity": 1, "message": "2141", "line": 489, "column": 5, "nodeType": "2007", "endLine": 489, "endColumn": 15, "suggestions": "2142"}, {"ruleId": "1988", "severity": 1, "message": "2143", "line": 494, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 494, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2144", "line": 504, "column": 13, "nodeType": "1990", "messageId": "1991", "endLine": 504, "endColumn": 33}, {"ruleId": "1988", "severity": 1, "message": "2145", "line": 515, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 515, "endColumn": 31}, {"ruleId": "1988", "severity": 1, "message": "2146", "line": 532, "column": 13, "nodeType": "1990", "messageId": "1991", "endLine": 532, "endColumn": 19}, {"ruleId": "2147", "severity": 1, "message": "2148", "line": 582, "column": 8, "nodeType": "2149", "endLine": 585, "endColumn": 9}, {"ruleId": "2147", "severity": 1, "message": "2148", "line": 588, "column": 8, "nodeType": "2149", "endLine": 591, "endColumn": 9}, {"ruleId": "2147", "severity": 1, "message": "2148", "line": 595, "column": 8, "nodeType": "2149", "endLine": 598, "endColumn": 9}, {"ruleId": "2150", "severity": 1, "message": "2151", "line": 958, "column": 9, "nodeType": "2149", "endLine": 958, "endColumn": 32}, {"ruleId": "1988", "severity": 1, "message": "2047", "line": 11, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 11, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2152", "line": 11, "column": 23, "nodeType": "1990", "messageId": "1991", "endLine": 11, "endColumn": 37}, {"ruleId": "1988", "severity": 1, "message": "2047", "line": 9, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 21}, {"ruleId": "2153", "severity": 1, "message": "2154", "line": 51, "column": 9, "nodeType": "2149", "endLine": 57, "endColumn": 11}, {"ruleId": "1988", "severity": 1, "message": "2047", "line": 11, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 11, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2152", "line": 11, "column": 23, "nodeType": "1990", "messageId": "1991", "endLine": 11, "endColumn": 37}, {"ruleId": "1988", "severity": 1, "message": "2155", "line": 2, "column": 26, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 36}, {"ruleId": "1988", "severity": 1, "message": "2156", "line": 2, "column": 38, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 47}, {"ruleId": "1988", "severity": 1, "message": "2157", "line": 2, "column": 49, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 55}, {"ruleId": "1988", "severity": 1, "message": "2158", "line": 2, "column": 57, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 61}, {"ruleId": "1988", "severity": 1, "message": "2085", "line": 2, "column": 63, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 73}, {"ruleId": "1988", "severity": 1, "message": "2159", "line": 2, "column": 75, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 89}, {"ruleId": "1988", "severity": 1, "message": "2160", "line": 3, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 3, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2161", "line": 3, "column": 16, "nodeType": "1990", "messageId": "1991", "endLine": 3, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2162", "line": 4, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2063", "line": 9, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2047", "line": 14, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 14, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2152", "line": 14, "column": 23, "nodeType": "1990", "messageId": "1991", "endLine": 14, "endColumn": 37}, {"ruleId": "1988", "severity": 1, "message": "2027", "line": 18, "column": 20, "nodeType": "1990", "messageId": "1991", "endLine": 18, "endColumn": 31}, {"ruleId": "1988", "severity": 1, "message": "2163", "line": 27, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 27, "endColumn": 14}, {"ruleId": "2005", "severity": 1, "message": "2164", "line": 75, "column": 6, "nodeType": "2007", "endLine": 75, "endColumn": 28, "suggestions": "2165"}, {"ruleId": "1988", "severity": 1, "message": "2166", "line": 3, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 3, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2167", "line": 9, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 12}, {"ruleId": "1988", "severity": 1, "message": "2168", "line": 14, "column": 7, "nodeType": "1990", "messageId": "1991", "endLine": 14, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2169", "line": 27, "column": 7, "nodeType": "1990", "messageId": "1991", "endLine": 27, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2170", "line": 32, "column": 7, "nodeType": "1990", "messageId": "1991", "endLine": 32, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2171", "line": 39, "column": 7, "nodeType": "1990", "messageId": "1991", "endLine": 39, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2172", "line": 43, "column": 7, "nodeType": "1990", "messageId": "1991", "endLine": 43, "endColumn": 28}, {"ruleId": "1988", "severity": 1, "message": "2173", "line": 63, "column": 7, "nodeType": "1990", "messageId": "1991", "endLine": 63, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2174", "line": 73, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 73, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "1995", "line": 74, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 74, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2175", "line": 76, "column": 19, "nodeType": "1990", "messageId": "1991", "endLine": 76, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2026", "line": 79, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 79, "endColumn": 13}, {"ruleId": "2005", "severity": 1, "message": "2072", "line": 110, "column": 5, "nodeType": "2007", "endLine": 110, "endColumn": 7, "suggestions": "2176"}, {"ruleId": "1988", "severity": 1, "message": "2177", "line": 114, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 114, "endColumn": 21}, {"ruleId": "2078", "severity": 1, "message": "2126", "line": 175, "column": 93, "nodeType": "2080", "messageId": "2081", "endLine": 175, "endColumn": 95}, {"ruleId": "1988", "severity": 1, "message": "2156", "line": 4, "column": 18, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2085", "line": 4, "column": 72, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 82}, {"ruleId": "1988", "severity": 1, "message": "2178", "line": 4, "column": 142, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 154}, {"ruleId": "1988", "severity": 1, "message": "2179", "line": 4, "column": 173, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 179}, {"ruleId": "1988", "severity": 1, "message": "2029", "line": 4, "column": 189, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 194}, {"ruleId": "1988", "severity": 1, "message": "2180", "line": 5, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2181", "line": 6, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 6, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2182", "line": 7, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 7, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2183", "line": 8, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 8, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2184", "line": 10, "column": 53, "nodeType": "1990", "messageId": "1991", "endLine": 10, "endColumn": 72}, {"ruleId": "1988", "severity": 1, "message": "2038", "line": 11, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 11, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2185", "line": 12, "column": 122, "nodeType": "1990", "messageId": "1991", "endLine": 12, "endColumn": 136}, {"ruleId": "1988", "severity": 1, "message": "2186", "line": 13, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 13, "endColumn": 28}, {"ruleId": "1988", "severity": 1, "message": "2155", "line": 19, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 19, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2187", "line": 24, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 24, "endColumn": 29}, {"ruleId": "1988", "severity": 1, "message": "2188", "line": 27, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 27, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2189", "line": 32, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 32, "endColumn": 28}, {"ruleId": "1988", "severity": 1, "message": "2190", "line": 42, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 42, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2191", "line": 42, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 42, "endColumn": 43}, {"ruleId": "1988", "severity": 1, "message": "2192", "line": 52, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 52, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2193", "line": 63, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 63, "endColumn": 28}, {"ruleId": "1988", "severity": 1, "message": "2194", "line": 64, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 64, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2195", "line": 65, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 65, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2196", "line": 104, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 104, "endColumn": 27}, {"ruleId": "2005", "severity": 1, "message": "2197", "line": 123, "column": 8, "nodeType": "2007", "endLine": 123, "endColumn": 32, "suggestions": "2198"}, {"ruleId": "2005", "severity": 1, "message": "2197", "line": 149, "column": 8, "nodeType": "2007", "endLine": 149, "endColumn": 24, "suggestions": "2199"}, {"ruleId": "2005", "severity": 1, "message": "2200", "line": 174, "column": 8, "nodeType": "2007", "endLine": 174, "endColumn": 10, "suggestions": "2201"}, {"ruleId": "1988", "severity": 1, "message": "2202", "line": 196, "column": 23, "nodeType": "1990", "messageId": "1991", "endLine": 196, "endColumn": 38}, {"ruleId": "1988", "severity": 1, "message": "2203", "line": 230, "column": 23, "nodeType": "1990", "messageId": "1991", "endLine": 230, "endColumn": 33}, {"ruleId": "1988", "severity": 1, "message": "2202", "line": 240, "column": 23, "nodeType": "1990", "messageId": "1991", "endLine": 240, "endColumn": 38}, {"ruleId": "2005", "severity": 1, "message": "2204", "line": 275, "column": 8, "nodeType": "2007", "endLine": 275, "endColumn": 42, "suggestions": "2205"}, {"ruleId": "1988", "severity": 1, "message": "2206", "line": 355, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 355, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2207", "line": 368, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 368, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2208", "line": 369, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 369, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2209", "line": 370, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 370, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2202", "line": 490, "column": 23, "nodeType": "1990", "messageId": "1991", "endLine": 490, "endColumn": 38}, {"ruleId": "1988", "severity": 1, "message": "2202", "line": 545, "column": 23, "nodeType": "1990", "messageId": "1991", "endLine": 545, "endColumn": 38}, {"ruleId": "1988", "severity": 1, "message": "2210", "line": 576, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 576, "endColumn": 23}, {"ruleId": "2005", "severity": 1, "message": "2211", "line": 592, "column": 12, "nodeType": "2007", "endLine": 592, "endColumn": 44, "suggestions": "2212"}, {"ruleId": "1988", "severity": 1, "message": "2213", "line": 618, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 618, "endColumn": 41}, {"ruleId": "1988", "severity": 1, "message": "2214", "line": 18, "column": 29, "nodeType": "1990", "messageId": "1991", "endLine": 18, "endColumn": 48}, {"ruleId": "1988", "severity": 1, "message": "2215", "line": 32, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 32, "endColumn": 15}, {"ruleId": "1988", "severity": 1, "message": "2216", "line": 171, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 171, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2217", "line": 241, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 241, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2019", "line": 4, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2218", "line": 6, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 6, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2160", "line": 9, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2219", "line": 22, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 22, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2124", "line": 23, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 23, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2220", "line": 35, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 35, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2221", "line": 36, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 36, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2222", "line": 37, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 37, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2223", "line": 37, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 37, "endColumn": 45}, {"ruleId": "1988", "severity": 1, "message": "2224", "line": 41, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 41, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2225", "line": 42, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 42, "endColumn": 25}, {"ruleId": "2078", "severity": 1, "message": "2126", "line": 131, "column": 26, "nodeType": "2080", "messageId": "2081", "endLine": 131, "endColumn": 28}, {"ruleId": "2078", "severity": 1, "message": "2126", "line": 208, "column": 24, "nodeType": "2080", "messageId": "2081", "endLine": 208, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2226", "line": 2, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2227", "line": 4, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2228", "line": 5, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2229", "line": 7, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 7, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2155", "line": 9, "column": 20, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2031", "line": 15, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 15, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2157", "line": 20, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 20, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2230", "line": 20, "column": 18, "nodeType": "1990", "messageId": "1991", "endLine": 20, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2084", "line": 20, "column": 24, "nodeType": "1990", "messageId": "1991", "endLine": 20, "endColumn": 32}, {"ruleId": "1988", "severity": 1, "message": "2231", "line": 20, "column": 73, "nodeType": "1990", "messageId": "1991", "endLine": 20, "endColumn": 82}, {"ruleId": "1988", "severity": 1, "message": "2083", "line": 20, "column": 84, "nodeType": "1990", "messageId": "1991", "endLine": 20, "endColumn": 90}, {"ruleId": "1988", "severity": 1, "message": "2232", "line": 20, "column": 92, "nodeType": "1990", "messageId": "1991", "endLine": 20, "endColumn": 95}, {"ruleId": "1988", "severity": 1, "message": "2033", "line": 22, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 22, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2034", "line": 23, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 23, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2233", "line": 24, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 24, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2037", "line": 26, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 26, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2035", "line": 27, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 27, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2002", "line": 28, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 28, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2234", "line": 34, "column": 114, "nodeType": "1990", "messageId": "1991", "endLine": 34, "endColumn": 134}, {"ruleId": "1988", "severity": 1, "message": "2235", "line": 36, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 36, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2236", "line": 37, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 37, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2237", "line": 38, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 38, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2042", "line": 49, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 49, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2238", "line": 55, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 55, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2239", "line": 55, "column": 22, "nodeType": "1990", "messageId": "1991", "endLine": 55, "endColumn": 36}, {"ruleId": "1988", "severity": 1, "message": "2240", "line": 56, "column": 21, "nodeType": "1990", "messageId": "1991", "endLine": 56, "endColumn": 34}, {"ruleId": "1988", "severity": 1, "message": "2241", "line": 57, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 57, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2242", "line": 57, "column": 21, "nodeType": "1990", "messageId": "1991", "endLine": 57, "endColumn": 34}, {"ruleId": "1988", "severity": 1, "message": "2243", "line": 60, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 60, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2044", "line": 66, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 66, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2215", "line": 68, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 68, "endColumn": 15}, {"ruleId": "1988", "severity": 1, "message": "2244", "line": 68, "column": 17, "nodeType": "1990", "messageId": "1991", "endLine": 68, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2245", "line": 70, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 70, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2246", "line": 70, "column": 19, "nodeType": "1990", "messageId": "1991", "endLine": 70, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2247", "line": 71, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 71, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2248", "line": 71, "column": 24, "nodeType": "1990", "messageId": "1991", "endLine": 71, "endColumn": 40}, {"ruleId": "1988", "severity": 1, "message": "2249", "line": 72, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 72, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2250", "line": 72, "column": 16, "nodeType": "1990", "messageId": "1991", "endLine": 72, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2251", "line": 82, "column": 24, "nodeType": "1990", "messageId": "1991", "endLine": 82, "endColumn": 40}, {"ruleId": "1988", "severity": 1, "message": "2252", "line": 86, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 86, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2253", "line": 86, "column": 15, "nodeType": "1990", "messageId": "1991", "endLine": 86, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2254", "line": 87, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 87, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2255", "line": 89, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 89, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2256", "line": 89, "column": 24, "nodeType": "1990", "messageId": "1991", "endLine": 89, "endColumn": 40}, {"ruleId": "1988", "severity": 1, "message": "2257", "line": 90, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 90, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2258", "line": 91, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 91, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2259", "line": 107, "column": 24, "nodeType": "1990", "messageId": "1991", "endLine": 107, "endColumn": 40}, {"ruleId": "1988", "severity": 1, "message": "2260", "line": 109, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 109, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2261", "line": 116, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 116, "endColumn": 28}, {"ruleId": "1988", "severity": 1, "message": "2262", "line": 122, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 122, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2263", "line": 123, "column": 21, "nodeType": "1990", "messageId": "1991", "endLine": 123, "endColumn": 34}, {"ruleId": "1988", "severity": 1, "message": "2264", "line": 124, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 124, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2265", "line": 124, "column": 19, "nodeType": "1990", "messageId": "1991", "endLine": 124, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2266", "line": 127, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 127, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2267", "line": 130, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 130, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2268", "line": 152, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 152, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2269", "line": 217, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 217, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2270", "line": 283, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 283, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2271", "line": 285, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 285, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2057", "line": 288, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 288, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2058", "line": 290, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 290, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2272", "line": 292, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 292, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2273", "line": 296, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 296, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2274", "line": 296, "column": 24, "nodeType": "1990", "messageId": "1991", "endLine": 296, "endColumn": 40}, {"ruleId": "1988", "severity": 1, "message": "2275", "line": 298, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 298, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2276", "line": 298, "column": 22, "nodeType": "1990", "messageId": "1991", "endLine": 298, "endColumn": 36}, {"ruleId": "2005", "severity": 1, "message": "2277", "line": 323, "column": 5, "nodeType": "2007", "endLine": 323, "endColumn": 42, "suggestions": "2278"}, {"ruleId": "2005", "severity": 1, "message": "2279", "line": 342, "column": 4, "nodeType": "2007", "endLine": 342, "endColumn": 15, "suggestions": "2280"}, {"ruleId": "1988", "severity": 1, "message": "2281", "line": 367, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 367, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2222", "line": 387, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 387, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2059", "line": 402, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 402, "endColumn": 36}, {"ruleId": "1988", "severity": 1, "message": "2044", "line": 403, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 403, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2282", "line": 405, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 405, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2060", "line": 413, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 413, "endColumn": 33}, {"ruleId": "2005", "severity": 1, "message": "2283", "line": 546, "column": 5, "nodeType": "2007", "endLine": 546, "endColumn": 7, "suggestions": "2284"}, {"ruleId": "1988", "severity": 1, "message": "2285", "line": 588, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 588, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2286", "line": 596, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 596, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2287", "line": 696, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 696, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2288", "line": 703, "column": 24, "nodeType": "1990", "messageId": "1991", "endLine": 703, "endColumn": 40}, {"ruleId": "1988", "severity": 1, "message": "2289", "line": 705, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 705, "endColumn": 29}, {"ruleId": "1988", "severity": 1, "message": "2290", "line": 3, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 3, "endColumn": 40}, {"ruleId": "1988", "severity": 1, "message": "2152", "line": 11, "column": 23, "nodeType": "1990", "messageId": "1991", "endLine": 11, "endColumn": 37}, {"ruleId": "1988", "severity": 1, "message": "2157", "line": 2, "column": 32, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 38}, {"ruleId": "1988", "severity": 1, "message": "2179", "line": 2, "column": 40, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 46}, {"ruleId": "1988", "severity": 1, "message": "2183", "line": 2, "column": 48, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 61}, {"ruleId": "1988", "severity": 1, "message": "2180", "line": 2, "column": 63, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 76}, {"ruleId": "1988", "severity": 1, "message": "2181", "line": 2, "column": 78, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 89}, {"ruleId": "1988", "severity": 1, "message": "2182", "line": 2, "column": 114, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 131}, {"ruleId": "1988", "severity": 1, "message": "2291", "line": 5, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2015", "line": 6, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 6, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2292", "line": 7, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 7, "endColumn": 33}, {"ruleId": "1988", "severity": 1, "message": "2293", "line": 10, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 10, "endColumn": 33}, {"ruleId": "1988", "severity": 1, "message": "2294", "line": 17, "column": 25, "nodeType": "1990", "messageId": "1991", "endLine": 17, "endColumn": 34}, {"ruleId": "1988", "severity": 1, "message": "2295", "line": 23, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 23, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2047", "line": 56, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 56, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2152", "line": 56, "column": 23, "nodeType": "1990", "messageId": "1991", "endLine": 56, "endColumn": 37}, {"ruleId": "1988", "severity": 1, "message": "2296", "line": 57, "column": 14, "nodeType": "1990", "messageId": "1991", "endLine": 57, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "1995", "line": 58, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 58, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2297", "line": 59, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 59, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2090", "line": 59, "column": 26, "nodeType": "1990", "messageId": "1991", "endLine": 59, "endColumn": 43}, {"ruleId": "1988", "severity": 1, "message": "2093", "line": 60, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 60, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2298", "line": 60, "column": 22, "nodeType": "1990", "messageId": "1991", "endLine": 60, "endColumn": 35}, {"ruleId": "1988", "severity": 1, "message": "2299", "line": 61, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 61, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2251", "line": 61, "column": 25, "nodeType": "1990", "messageId": "1991", "endLine": 61, "endColumn": 41}, {"ruleId": "1988", "severity": 1, "message": "2300", "line": 62, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 62, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2301", "line": 62, "column": 19, "nodeType": "1990", "messageId": "1991", "endLine": 62, "endColumn": 29}, {"ruleId": "1988", "severity": 1, "message": "2302", "line": 63, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 63, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2091", "line": 63, "column": 16, "nodeType": "1990", "messageId": "1991", "endLine": 63, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2303", "line": 64, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 64, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2092", "line": 64, "column": 15, "nodeType": "1990", "messageId": "1991", "endLine": 64, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2304", "line": 72, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 72, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2305", "line": 72, "column": 19, "nodeType": "1990", "messageId": "1991", "endLine": 72, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2306", "line": 93, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 93, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2124", "line": 94, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 94, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2307", "line": 94, "column": 19, "nodeType": "1990", "messageId": "1991", "endLine": 94, "endColumn": 29}, {"ruleId": "2005", "severity": 1, "message": "2308", "line": 171, "column": 6, "nodeType": "2007", "endLine": 171, "endColumn": 44, "suggestions": "2309"}, {"ruleId": "1988", "severity": 1, "message": "2157", "line": 2, "column": 32, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 38}, {"ruleId": "1988", "severity": 1, "message": "2179", "line": 2, "column": 40, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 46}, {"ruleId": "1988", "severity": 1, "message": "2183", "line": 2, "column": 48, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 61}, {"ruleId": "1988", "severity": 1, "message": "2180", "line": 2, "column": 63, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 76}, {"ruleId": "1988", "severity": 1, "message": "2181", "line": 2, "column": 78, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 89}, {"ruleId": "1988", "severity": 1, "message": "2182", "line": 2, "column": 114, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 131}, {"ruleId": "1988", "severity": 1, "message": "2291", "line": 5, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2015", "line": 6, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 6, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2292", "line": 7, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 7, "endColumn": 33}, {"ruleId": "1988", "severity": 1, "message": "2293", "line": 11, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 11, "endColumn": 33}, {"ruleId": "1988", "severity": 1, "message": "2294", "line": 18, "column": 25, "nodeType": "1990", "messageId": "1991", "endLine": 18, "endColumn": 34}, {"ruleId": "1988", "severity": 1, "message": "2295", "line": 25, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 25, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2047", "line": 57, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 57, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2152", "line": 57, "column": 23, "nodeType": "1990", "messageId": "1991", "endLine": 57, "endColumn": 37}, {"ruleId": "1988", "severity": 1, "message": "2310", "line": 59, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 59, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2311", "line": 59, "column": 21, "nodeType": "1990", "messageId": "1991", "endLine": 59, "endColumn": 33}, {"ruleId": "1988", "severity": 1, "message": "2296", "line": 60, "column": 14, "nodeType": "1990", "messageId": "1991", "endLine": 60, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "1995", "line": 61, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 61, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2297", "line": 62, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 62, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2090", "line": 62, "column": 26, "nodeType": "1990", "messageId": "1991", "endLine": 62, "endColumn": 43}, {"ruleId": "1988", "severity": 1, "message": "2093", "line": 63, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 63, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2298", "line": 63, "column": 22, "nodeType": "1990", "messageId": "1991", "endLine": 63, "endColumn": 35}, {"ruleId": "1988", "severity": 1, "message": "2299", "line": 64, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 64, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2251", "line": 64, "column": 25, "nodeType": "1990", "messageId": "1991", "endLine": 64, "endColumn": 41}, {"ruleId": "1988", "severity": 1, "message": "2300", "line": 65, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 65, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2301", "line": 65, "column": 19, "nodeType": "1990", "messageId": "1991", "endLine": 65, "endColumn": 29}, {"ruleId": "1988", "severity": 1, "message": "2302", "line": 66, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 66, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2091", "line": 66, "column": 16, "nodeType": "1990", "messageId": "1991", "endLine": 66, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2303", "line": 67, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 67, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2092", "line": 67, "column": 15, "nodeType": "1990", "messageId": "1991", "endLine": 67, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2312", "line": 75, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 75, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2313", "line": 75, "column": 21, "nodeType": "1990", "messageId": "1991", "endLine": 75, "endColumn": 34}, {"ruleId": "1988", "severity": 1, "message": "2306", "line": 96, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 96, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2124", "line": 97, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 97, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2307", "line": 97, "column": 19, "nodeType": "1990", "messageId": "1991", "endLine": 97, "endColumn": 29}, {"ruleId": "2005", "severity": 1, "message": "2314", "line": 102, "column": 6, "nodeType": "2007", "endLine": 102, "endColumn": 19, "suggestions": "2315"}, {"ruleId": "1988", "severity": 1, "message": "2316", "line": 158, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 158, "endColumn": 22}, {"ruleId": "2005", "severity": 1, "message": "2314", "line": 177, "column": 6, "nodeType": "2007", "endLine": 177, "endColumn": 44, "suggestions": "2317"}, {"ruleId": "1988", "severity": 1, "message": "2157", "line": 2, "column": 32, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 38}, {"ruleId": "1988", "severity": 1, "message": "2179", "line": 2, "column": 40, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 46}, {"ruleId": "1988", "severity": 1, "message": "2183", "line": 2, "column": 48, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 61}, {"ruleId": "1988", "severity": 1, "message": "2180", "line": 2, "column": 63, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 76}, {"ruleId": "1988", "severity": 1, "message": "2181", "line": 2, "column": 78, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 89}, {"ruleId": "1988", "severity": 1, "message": "2182", "line": 2, "column": 114, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 131}, {"ruleId": "1988", "severity": 1, "message": "2291", "line": 5, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2015", "line": 6, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 6, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2292", "line": 7, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 7, "endColumn": 33}, {"ruleId": "1988", "severity": 1, "message": "2293", "line": 9, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 33}, {"ruleId": "1988", "severity": 1, "message": "2295", "line": 23, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 23, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2296", "line": 65, "column": 16, "nodeType": "1990", "messageId": "1991", "endLine": 65, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2318", "line": 66, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 66, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "1995", "line": 69, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 69, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2306", "line": 70, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 70, "endColumn": 23}, {"ruleId": "2005", "severity": 1, "message": "2319", "line": 81, "column": 4, "nodeType": "2007", "endLine": 81, "endColumn": 17, "suggestions": "2320"}, {"ruleId": "2005", "severity": 1, "message": "2319", "line": 141, "column": 6, "nodeType": "2007", "endLine": 141, "endColumn": 44, "suggestions": "2321"}, {"ruleId": "1988", "severity": 1, "message": "2157", "line": 2, "column": 32, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 38}, {"ruleId": "1988", "severity": 1, "message": "2179", "line": 2, "column": 40, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 46}, {"ruleId": "1988", "severity": 1, "message": "2183", "line": 2, "column": 48, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 61}, {"ruleId": "1988", "severity": 1, "message": "2180", "line": 2, "column": 63, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 76}, {"ruleId": "1988", "severity": 1, "message": "2181", "line": 2, "column": 78, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 89}, {"ruleId": "1988", "severity": 1, "message": "2182", "line": 2, "column": 114, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 131}, {"ruleId": "1988", "severity": 1, "message": "2291", "line": 5, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2015", "line": 6, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 6, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2292", "line": 7, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 7, "endColumn": 33}, {"ruleId": "1988", "severity": 1, "message": "2293", "line": 9, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 33}, {"ruleId": "2322", "severity": 1, "message": "2323", "line": 27, "column": 11, "nodeType": "1990", "messageId": "2324", "endLine": 27, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2325", "line": 45, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 45, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2296", "line": 49, "column": 14, "nodeType": "1990", "messageId": "1991", "endLine": 49, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2326", "line": 51, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 51, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2327", "line": 51, "column": 23, "nodeType": "1990", "messageId": "1991", "endLine": 51, "endColumn": 37}, {"ruleId": "1988", "severity": 1, "message": "2299", "line": 52, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 52, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2251", "line": 52, "column": 25, "nodeType": "1990", "messageId": "1991", "endLine": 52, "endColumn": 41}, {"ruleId": "1988", "severity": 1, "message": "2300", "line": 53, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 53, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2301", "line": 53, "column": 19, "nodeType": "1990", "messageId": "1991", "endLine": 53, "endColumn": 29}, {"ruleId": "1988", "severity": 1, "message": "2328", "line": 54, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 54, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2329", "line": 54, "column": 20, "nodeType": "1990", "messageId": "1991", "endLine": 54, "endColumn": 31}, {"ruleId": "1988", "severity": 1, "message": "2124", "line": 67, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 67, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2307", "line": 67, "column": 19, "nodeType": "1990", "messageId": "1991", "endLine": 67, "endColumn": 29}, {"ruleId": "1988", "severity": 1, "message": "2306", "line": 71, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 71, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2302", "line": 72, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 72, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2091", "line": 72, "column": 16, "nodeType": "1990", "messageId": "1991", "endLine": 72, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2303", "line": 73, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 73, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2092", "line": 73, "column": 15, "nodeType": "1990", "messageId": "1991", "endLine": 73, "endColumn": 21}, {"ruleId": "2005", "severity": 1, "message": "2330", "line": 156, "column": 6, "nodeType": "2007", "endLine": 156, "endColumn": 45, "suggestions": "2331"}, {"ruleId": "1988", "severity": 1, "message": "2157", "line": 2, "column": 32, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 38}, {"ruleId": "1988", "severity": 1, "message": "2179", "line": 2, "column": 40, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 46}, {"ruleId": "1988", "severity": 1, "message": "2183", "line": 2, "column": 48, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 61}, {"ruleId": "1988", "severity": 1, "message": "2180", "line": 2, "column": 63, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 76}, {"ruleId": "1988", "severity": 1, "message": "2181", "line": 2, "column": 78, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 89}, {"ruleId": "1988", "severity": 1, "message": "2182", "line": 2, "column": 114, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 131}, {"ruleId": "1988", "severity": 1, "message": "2291", "line": 5, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2015", "line": 6, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 6, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2292", "line": 7, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 7, "endColumn": 33}, {"ruleId": "1988", "severity": 1, "message": "2293", "line": 9, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 33}, {"ruleId": "1988", "severity": 1, "message": "2325", "line": 43, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 43, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2296", "line": 47, "column": 14, "nodeType": "1990", "messageId": "1991", "endLine": 47, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2332", "line": 49, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 49, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2333", "line": 49, "column": 23, "nodeType": "1990", "messageId": "1991", "endLine": 49, "endColumn": 37}, {"ruleId": "1988", "severity": 1, "message": "2299", "line": 50, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 50, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2251", "line": 50, "column": 25, "nodeType": "1990", "messageId": "1991", "endLine": 50, "endColumn": 41}, {"ruleId": "1988", "severity": 1, "message": "2300", "line": 51, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 51, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2301", "line": 51, "column": 19, "nodeType": "1990", "messageId": "1991", "endLine": 51, "endColumn": 29}, {"ruleId": "1988", "severity": 1, "message": "2328", "line": 52, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 52, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2329", "line": 52, "column": 20, "nodeType": "1990", "messageId": "1991", "endLine": 52, "endColumn": 31}, {"ruleId": "1988", "severity": 1, "message": "2124", "line": 65, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 65, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2307", "line": 65, "column": 19, "nodeType": "1990", "messageId": "1991", "endLine": 65, "endColumn": 29}, {"ruleId": "1988", "severity": 1, "message": "2306", "line": 69, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 69, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2302", "line": 70, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 70, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2091", "line": 70, "column": 16, "nodeType": "1990", "messageId": "1991", "endLine": 70, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2303", "line": 71, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 71, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2092", "line": 71, "column": 15, "nodeType": "1990", "messageId": "1991", "endLine": 71, "endColumn": 21}, {"ruleId": "2005", "severity": 1, "message": "2334", "line": 152, "column": 6, "nodeType": "2007", "endLine": 152, "endColumn": 45, "suggestions": "2335"}, {"ruleId": "1988", "severity": 1, "message": "2157", "line": 2, "column": 32, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 38}, {"ruleId": "1988", "severity": 1, "message": "2179", "line": 2, "column": 40, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 46}, {"ruleId": "1988", "severity": 1, "message": "2183", "line": 2, "column": 48, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 61}, {"ruleId": "1988", "severity": 1, "message": "2180", "line": 2, "column": 63, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 76}, {"ruleId": "1988", "severity": 1, "message": "2181", "line": 2, "column": 78, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 89}, {"ruleId": "1988", "severity": 1, "message": "2182", "line": 2, "column": 114, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 131}, {"ruleId": "1988", "severity": 1, "message": "2291", "line": 5, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2015", "line": 6, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 6, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2292", "line": 7, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 7, "endColumn": 33}, {"ruleId": "1988", "severity": 1, "message": "2293", "line": 9, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 33}, {"ruleId": "1988", "severity": 1, "message": "2295", "line": 23, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 23, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2296", "line": 65, "column": 16, "nodeType": "1990", "messageId": "1991", "endLine": 65, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2318", "line": 66, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 66, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "1995", "line": 69, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 69, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2306", "line": 70, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 70, "endColumn": 23}, {"ruleId": "2005", "severity": 1, "message": "2336", "line": 81, "column": 4, "nodeType": "2007", "endLine": 81, "endColumn": 17, "suggestions": "2337"}, {"ruleId": "2005", "severity": 1, "message": "2336", "line": 141, "column": 6, "nodeType": "2007", "endLine": 141, "endColumn": 44, "suggestions": "2338"}, {"ruleId": "1988", "severity": 1, "message": "2152", "line": 10, "column": 23, "nodeType": "1990", "messageId": "1991", "endLine": 10, "endColumn": 37}, {"ruleId": "1988", "severity": 1, "message": "2152", "line": 9, "column": 23, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 37}, {"ruleId": "1988", "severity": 1, "message": "2339", "line": 9, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2340", "line": 29, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 29, "endColumn": 31}, {"ruleId": "1988", "severity": 1, "message": "2341", "line": 30, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 30, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2342", "line": 151, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 151, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2343", "line": 174, "column": 15, "nodeType": "1990", "messageId": "1991", "endLine": 174, "endColumn": 28}, {"ruleId": "2005", "severity": 1, "message": "2344", "line": 71, "column": 6, "nodeType": "2007", "endLine": 71, "endColumn": 34, "suggestions": "2345", "suppressions": "2346"}, {"ruleId": "2005", "severity": 1, "message": "2347", "line": 204, "column": 6, "nodeType": "2007", "endLine": 204, "endColumn": 17, "suggestions": "2348", "suppressions": "2349"}, {"ruleId": "1988", "severity": 1, "message": "2350", "line": 14, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 14, "endColumn": 7}, {"ruleId": "1988", "severity": 1, "message": "2351", "line": 19, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 19, "endColumn": 17}, {"ruleId": "2005", "severity": 1, "message": "2352", "line": 54, "column": 6, "nodeType": "2007", "endLine": 54, "endColumn": 19, "suggestions": "2353", "suppressions": "2354"}, {"ruleId": "1988", "severity": 1, "message": "2031", "line": 5, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2355", "line": 10, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 10, "endColumn": 10}, {"ruleId": "1988", "severity": 1, "message": "2184", "line": 12, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 12, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2032", "line": 16, "column": 34, "nodeType": "1990", "messageId": "1991", "endLine": 16, "endColumn": 50}, {"ruleId": "1988", "severity": 1, "message": "2156", "line": 16, "column": 86, "nodeType": "1990", "messageId": "1991", "endLine": 16, "endColumn": 95}, {"ruleId": "1988", "severity": 1, "message": "2356", "line": 16, "column": 117, "nodeType": "1990", "messageId": "1991", "endLine": 16, "endColumn": 128}, {"ruleId": "1988", "severity": 1, "message": "2357", "line": 19, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 19, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2033", "line": 20, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 20, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2034", "line": 21, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 21, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2035", "line": 24, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 24, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2036", "line": 25, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 25, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2358", "line": 26, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 26, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2188", "line": 31, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 31, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2359", "line": 32, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 32, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2360", "line": 32, "column": 26, "nodeType": "1990", "messageId": "1991", "endLine": 32, "endColumn": 51}, {"ruleId": "1988", "severity": 1, "message": "2361", "line": 32, "column": 53, "nodeType": "1990", "messageId": "1991", "endLine": 32, "endColumn": 68}, {"ruleId": "1988", "severity": 1, "message": "2037", "line": 35, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 35, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2038", "line": 36, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 36, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2362", "line": 37, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 37, "endColumn": 31}, {"ruleId": "1988", "severity": 1, "message": "2229", "line": 39, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 39, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2039", "line": 42, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 42, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2159", "line": 43, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 43, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2363", "line": 44, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 44, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2364", "line": 45, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 45, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2041", "line": 48, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 48, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2365", "line": 51, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 51, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2366", "line": 52, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 52, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2367", "line": 53, "column": 15, "nodeType": "1990", "messageId": "1991", "endLine": 53, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2368", "line": 55, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 55, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2369", "line": 56, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 56, "endColumn": 15}, {"ruleId": "1988", "severity": 1, "message": "1995", "line": 146, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 146, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2044", "line": 152, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 152, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2045", "line": 152, "column": 19, "nodeType": "1990", "messageId": "1991", "endLine": 152, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2124", "line": 153, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 153, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2370", "line": 154, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 154, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2371", "line": 154, "column": 24, "nodeType": "1990", "messageId": "1991", "endLine": 154, "endColumn": 40}, {"ruleId": "1988", "severity": 1, "message": "2372", "line": 155, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 155, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2373", "line": 155, "column": 24, "nodeType": "1990", "messageId": "1991", "endLine": 155, "endColumn": 40}, {"ruleId": "1988", "severity": 1, "message": "2046", "line": 156, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 156, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2374", "line": 156, "column": 24, "nodeType": "1990", "messageId": "1991", "endLine": 156, "endColumn": 40}, {"ruleId": "1988", "severity": 1, "message": "2222", "line": 157, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 157, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2223", "line": 157, "column": 26, "nodeType": "1990", "messageId": "1991", "endLine": 157, "endColumn": 44}, {"ruleId": "1988", "severity": 1, "message": "2047", "line": 158, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 158, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2375", "line": 160, "column": 26, "nodeType": "1990", "messageId": "1991", "endLine": 160, "endColumn": 44}, {"ruleId": "1988", "severity": 1, "message": "2376", "line": 161, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 161, "endColumn": 46}, {"ruleId": "1988", "severity": 1, "message": "2048", "line": 163, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 163, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2377", "line": 164, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 164, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2093", "line": 167, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 167, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2049", "line": 168, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 168, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2378", "line": 172, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 172, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2301", "line": 173, "column": 18, "nodeType": "1990", "messageId": "1991", "endLine": 173, "endColumn": 28}, {"ruleId": "1988", "severity": 1, "message": "2379", "line": 174, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 174, "endColumn": 29}, {"ruleId": "1988", "severity": 1, "message": "2380", "line": 183, "column": 26, "nodeType": "1990", "messageId": "1991", "endLine": 183, "endColumn": 44}, {"ruleId": "1988", "severity": 1, "message": "2381", "line": 192, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 192, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2382", "line": 199, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 199, "endColumn": 15}, {"ruleId": "1988", "severity": 1, "message": "2383", "line": 200, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 200, "endColumn": 19}, {"ruleId": "2005", "severity": 1, "message": "2384", "line": 261, "column": 5, "nodeType": "2007", "endLine": 261, "endColumn": 32, "suggestions": "2385"}, {"ruleId": "1988", "severity": 1, "message": "2343", "line": 302, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 302, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2386", "line": 375, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 375, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2387", "line": 376, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 376, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2057", "line": 401, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 401, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2058", "line": 403, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 403, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2059", "line": 406, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 406, "endColumn": 36}, {"ruleId": "1988", "severity": 1, "message": "2060", "line": 418, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 418, "endColumn": 33}, {"ruleId": "1988", "severity": 1, "message": "2096", "line": 459, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 459, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2206", "line": 462, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 462, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2388", "line": 466, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 466, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2389", "line": 575, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 575, "endColumn": 23}, {"ruleId": "2078", "severity": 1, "message": "2126", "line": 617, "column": 36, "nodeType": "2080", "messageId": "2081", "endLine": 617, "endColumn": 38}, {"ruleId": "2078", "severity": 1, "message": "2126", "line": 617, "column": 62, "nodeType": "2080", "messageId": "2081", "endLine": 617, "endColumn": 64}, {"ruleId": "2078", "severity": 1, "message": "2126", "line": 619, "column": 32, "nodeType": "2080", "messageId": "2081", "endLine": 619, "endColumn": 34}, {"ruleId": "1988", "severity": 1, "message": "2390", "line": 3, "column": 92, "nodeType": "1990", "messageId": "1991", "endLine": 3, "endColumn": 102}, {"ruleId": "1988", "severity": 1, "message": "2391", "line": 3, "column": 123, "nodeType": "1990", "messageId": "1991", "endLine": 3, "endColumn": 130}, {"ruleId": "1988", "severity": 1, "message": "2392", "line": 6, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 6, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2393", "line": 15, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 15, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2394", "line": 18, "column": 17, "nodeType": "1990", "messageId": "1991", "endLine": 18, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2395", "line": 20, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 20, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2033", "line": 23, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 23, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2396", "line": 25, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 25, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2041", "line": 26, "column": 20, "nodeType": "1990", "messageId": "1991", "endLine": 26, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2397", "line": 27, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 27, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2398", "line": 140, "column": 28, "nodeType": "1990", "messageId": "1991", "endLine": 140, "endColumn": 38}, {"ruleId": "1988", "severity": 1, "message": "2399", "line": 164, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 164, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2400", "line": 164, "column": 18, "nodeType": "1990", "messageId": "1991", "endLine": 164, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2401", "line": 165, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 165, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2402", "line": 165, "column": 26, "nodeType": "1990", "messageId": "1991", "endLine": 165, "endColumn": 41}, {"ruleId": "1988", "severity": 1, "message": "2403", "line": 182, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 182, "endColumn": 29}, {"ruleId": "1988", "severity": 1, "message": "2404", "line": 182, "column": 31, "nodeType": "1990", "messageId": "1991", "endLine": 182, "endColumn": 51}, {"ruleId": "1988", "severity": 1, "message": "2405", "line": 183, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 183, "endColumn": 29}, {"ruleId": "1988", "severity": 1, "message": "2406", "line": 183, "column": 31, "nodeType": "1990", "messageId": "1991", "endLine": 183, "endColumn": 51}, {"ruleId": "1988", "severity": 1, "message": "2407", "line": 184, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 184, "endColumn": 31}, {"ruleId": "1988", "severity": 1, "message": "2408", "line": 184, "column": 33, "nodeType": "1990", "messageId": "1991", "endLine": 184, "endColumn": 55}, {"ruleId": "1988", "severity": 1, "message": "2409", "line": 202, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 202, "endColumn": 24}, {"ruleId": "2005", "severity": 1, "message": "2410", "line": 246, "column": 8, "nodeType": "2007", "endLine": 246, "endColumn": 37, "suggestions": "2411"}, {"ruleId": "1988", "severity": 1, "message": "2412", "line": 324, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 324, "endColumn": 26}, {"ruleId": "2005", "severity": 1, "message": "2413", "line": 346, "column": 8, "nodeType": "2007", "endLine": 346, "endColumn": 18, "suggestions": "2414"}, {"ruleId": "2005", "severity": 1, "message": "2415", "line": 401, "column": 8, "nodeType": "2007", "endLine": 401, "endColumn": 24, "suggestions": "2416"}, {"ruleId": "2074", "severity": 1, "message": "2417", "line": 611, "column": 27, "nodeType": "2076", "messageId": "2077", "endLine": 611, "endColumn": 29}, {"ruleId": "2074", "severity": 1, "message": "2417", "line": 611, "column": 42, "nodeType": "2076", "messageId": "2077", "endLine": 611, "endColumn": 44}, {"ruleId": "2005", "severity": 1, "message": "2418", "line": 706, "column": 4, "nodeType": "2007", "endLine": 706, "endColumn": 11, "suggestions": "2419"}, {"ruleId": "1988", "severity": 1, "message": "2420", "line": 2, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2421", "line": 8, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 8, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2082", "line": 1, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 36}, {"ruleId": "1988", "severity": 1, "message": "2422", "line": 3, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 3, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2423", "line": 5, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2424", "line": 6, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 6, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2425", "line": 7, "column": 26, "nodeType": "1990", "messageId": "1991", "endLine": 7, "endColumn": 41}, {"ruleId": "1988", "severity": 1, "message": "2426", "line": 10, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 10, "endColumn": 28}, {"ruleId": "1988", "severity": 1, "message": "2120", "line": 11, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 11, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2065", "line": 13, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 13, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2427", "line": 14, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 14, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2187", "line": 15, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 15, "endColumn": 29}, {"ruleId": "1988", "severity": 1, "message": "2000", "line": 18, "column": 25, "nodeType": "1990", "messageId": "1991", "endLine": 18, "endColumn": 36}, {"ruleId": "1988", "severity": 1, "message": "2428", "line": 23, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 23, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2026", "line": 25, "column": 13, "nodeType": "1990", "messageId": "1991", "endLine": 25, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "1995", "line": 25, "column": 18, "nodeType": "1990", "messageId": "1991", "endLine": 25, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "1996", "line": 25, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 25, "endColumn": 36}, {"ruleId": "1988", "severity": 1, "message": "2429", "line": 26, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 26, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2430", "line": 27, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 27, "endColumn": 31}, {"ruleId": "1988", "severity": 1, "message": "2249", "line": 31, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 31, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2431", "line": 31, "column": 19, "nodeType": "1990", "messageId": "1991", "endLine": 31, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2432", "line": 33, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 33, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2433", "line": 33, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 33, "endColumn": 43}, {"ruleId": "1988", "severity": 1, "message": "2099", "line": 34, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 34, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2434", "line": 34, "column": 22, "nodeType": "1990", "messageId": "1991", "endLine": 34, "endColumn": 33}, {"ruleId": "1988", "severity": 1, "message": "2435", "line": 35, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 35, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2436", "line": 35, "column": 21, "nodeType": "1990", "messageId": "1991", "endLine": 35, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2437", "line": 36, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 36, "endColumn": 31}, {"ruleId": "1988", "severity": 1, "message": "2438", "line": 36, "column": 33, "nodeType": "1990", "messageId": "1991", "endLine": 36, "endColumn": 55}, {"ruleId": "1988", "severity": 1, "message": "1998", "line": 37, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 37, "endColumn": 28}, {"ruleId": "1988", "severity": 1, "message": "2422", "line": 3, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 3, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2423", "line": 5, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2425", "line": 7, "column": 26, "nodeType": "1990", "messageId": "1991", "endLine": 7, "endColumn": 41}, {"ruleId": "1988", "severity": 1, "message": "2426", "line": 10, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 10, "endColumn": 28}, {"ruleId": "1988", "severity": 1, "message": "2427", "line": 13, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 13, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2000", "line": 16, "column": 25, "nodeType": "1990", "messageId": "1991", "endLine": 16, "endColumn": 36}, {"ruleId": "1988", "severity": 1, "message": "2439", "line": 24, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 24, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2440", "line": 28, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 28, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2430", "line": 29, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 29, "endColumn": 31}, {"ruleId": "1988", "severity": 1, "message": "2441", "line": 50, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 50, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2249", "line": 78, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 78, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2431", "line": 78, "column": 19, "nodeType": "1990", "messageId": "1991", "endLine": 78, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2432", "line": 80, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 80, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2099", "line": 81, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 81, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2435", "line": 82, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 82, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2436", "line": 82, "column": 21, "nodeType": "1990", "messageId": "1991", "endLine": 82, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2437", "line": 83, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 83, "endColumn": 31}, {"ruleId": "1988", "severity": 1, "message": "2438", "line": 83, "column": 33, "nodeType": "1990", "messageId": "1991", "endLine": 83, "endColumn": 55}, {"ruleId": "1988", "severity": 1, "message": "1998", "line": 84, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 84, "endColumn": 28}, {"ruleId": "1988", "severity": 1, "message": "1996", "line": 88, "column": 22, "nodeType": "1990", "messageId": "1991", "endLine": 88, "endColumn": 31}, {"ruleId": "2005", "severity": 1, "message": "2067", "line": 115, "column": 4, "nodeType": "2007", "endLine": 115, "endColumn": 47, "suggestions": "2442"}, {"ruleId": "2005", "severity": 1, "message": "2443", "line": 297, "column": 8, "nodeType": "2007", "endLine": 297, "endColumn": 21, "suggestions": "2444"}, {"ruleId": "2005", "severity": 1, "message": "2445", "line": 360, "column": 26, "nodeType": "2446", "endLine": 360, "endColumn": 49}, {"ruleId": "1988", "severity": 1, "message": "2447", "line": 2, "column": 25, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 39}, {"ruleId": "1988", "severity": 1, "message": "2448", "line": 4, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2020", "line": 5, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2449", "line": 10, "column": 7, "nodeType": "1990", "messageId": "1991", "endLine": 10, "endColumn": 15}, {"ruleId": "1988", "severity": 1, "message": "2450", "line": 11, "column": 7, "nodeType": "1990", "messageId": "1991", "endLine": 11, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2449", "line": 19, "column": 7, "nodeType": "1990", "messageId": "1991", "endLine": 19, "endColumn": 15}, {"ruleId": "1988", "severity": 1, "message": "2002", "line": 1, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2449", "line": 3, "column": 7, "nodeType": "1990", "messageId": "1991", "endLine": 3, "endColumn": 15}, {"ruleId": "1988", "severity": 1, "message": "2451", "line": 2, "column": 26, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 48}, {"ruleId": "1988", "severity": 1, "message": "2087", "line": 7, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 7, "endColumn": 8}, {"ruleId": "1988", "severity": 1, "message": "2088", "line": 8, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 8, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2032", "line": 9, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2452", "line": 36, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 36, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2453", "line": 37, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 37, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2454", "line": 38, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 38, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2455", "line": 45, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 45, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2456", "line": 45, "column": 18, "nodeType": "1990", "messageId": "1991", "endLine": 45, "endColumn": 28}, {"ruleId": "2005", "severity": 1, "message": "2457", "line": 94, "column": 5, "nodeType": "2007", "endLine": 94, "endColumn": 20, "suggestions": "2458"}, {"ruleId": "2005", "severity": 1, "message": "2164", "line": 127, "column": 5, "nodeType": "2007", "endLine": 127, "endColumn": 21, "suggestions": "2459"}, {"ruleId": "2005", "severity": 1, "message": "2072", "line": 153, "column": 5, "nodeType": "2007", "endLine": 153, "endColumn": 7, "suggestions": "2460"}, {"ruleId": "1988", "severity": 1, "message": "2461", "line": 195, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 195, "endColumn": 38}, {"ruleId": "1988", "severity": 1, "message": "2099", "line": 197, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 197, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2098", "line": 202, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 202, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2462", "line": 250, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 250, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2463", "line": 361, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 361, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2070", "line": 4, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2464", "line": 4, "column": 40, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 48}, {"ruleId": "1988", "severity": 1, "message": "2465", "line": 8, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 8, "endColumn": 12}, {"ruleId": "1988", "severity": 1, "message": "2159", "line": 8, "column": 57, "nodeType": "1990", "messageId": "1991", "endLine": 8, "endColumn": 71}, {"ruleId": "1988", "severity": 1, "message": "2088", "line": 9, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2032", "line": 9, "column": 15, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 31}, {"ruleId": "1988", "severity": 1, "message": "2087", "line": 9, "column": 33, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 38}, {"ruleId": "1988", "severity": 1, "message": "2356", "line": 9, "column": 40, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 51}, {"ruleId": "1988", "severity": 1, "message": "2466", "line": 9, "column": 53, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 62}, {"ruleId": "1988", "severity": 1, "message": "2104", "line": 9, "column": 64, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 70}, {"ruleId": "1988", "severity": 1, "message": "2231", "line": 10, "column": 14, "nodeType": "1990", "messageId": "1991", "endLine": 10, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2086", "line": 10, "column": 25, "nodeType": "1990", "messageId": "1991", "endLine": 10, "endColumn": 31}, {"ruleId": "1988", "severity": 1, "message": "2083", "line": 10, "column": 43, "nodeType": "1990", "messageId": "1991", "endLine": 10, "endColumn": 49}, {"ruleId": "1988", "severity": 1, "message": "2467", "line": 13, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 13, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2468", "line": 14, "column": 25, "nodeType": "1990", "messageId": "1991", "endLine": 14, "endColumn": 38}, {"ruleId": "1988", "severity": 1, "message": "2019", "line": 15, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 15, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2426", "line": 20, "column": 26, "nodeType": "1990", "messageId": "1991", "endLine": 20, "endColumn": 44}, {"ruleId": "1988", "severity": 1, "message": "2427", "line": 20, "column": 46, "nodeType": "1990", "messageId": "1991", "endLine": 20, "endColumn": 57}, {"ruleId": "1988", "severity": 1, "message": "2365", "line": 22, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 22, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2469", "line": 26, "column": 7, "nodeType": "1990", "messageId": "1991", "endLine": 26, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2470", "line": 31, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 31, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2471", "line": 32, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 32, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2472", "line": 32, "column": 24, "nodeType": "1990", "messageId": "1991", "endLine": 32, "endColumn": 39}, {"ruleId": "1988", "severity": 1, "message": "1995", "line": 33, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 33, "endColumn": 18}, {"ruleId": "2005", "severity": 1, "message": "2473", "line": 94, "column": 3, "nodeType": "2007", "endLine": 94, "endColumn": 24, "suggestions": "2474"}, {"ruleId": "2005", "severity": 1, "message": "2072", "line": 104, "column": 5, "nodeType": "2007", "endLine": 104, "endColumn": 34, "suggestions": "2475"}, {"ruleId": "1988", "severity": 1, "message": "2476", "line": 105, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 105, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2437", "line": 117, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 117, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2477", "line": 258, "column": 7, "nodeType": "1990", "messageId": "1991", "endLine": 258, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2290", "line": 1, "column": 38, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 51}, {"ruleId": "1988", "severity": 1, "message": "2478", "line": 8, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 8, "endColumn": 10}, {"ruleId": "1988", "severity": 1, "message": "2479", "line": 14, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 14, "endColumn": 11}, {"ruleId": "1988", "severity": 1, "message": "2480", "line": 16, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 16, "endColumn": 9}, {"ruleId": "1988", "severity": 1, "message": "2481", "line": 19, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 19, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2085", "line": 24, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 24, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2086", "line": 25, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 25, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2116", "line": 27, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 27, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2391", "line": 33, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 33, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2482", "line": 33, "column": 30, "nodeType": "1990", "messageId": "1991", "endLine": 33, "endColumn": 40}, {"ruleId": "1988", "severity": 1, "message": "2483", "line": 109, "column": 28, "nodeType": "1990", "messageId": "1991", "endLine": 109, "endColumn": 47}, {"ruleId": "1988", "severity": 1, "message": "1995", "line": 110, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 110, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2026", "line": 112, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 112, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2175", "line": 115, "column": 20, "nodeType": "1990", "messageId": "1991", "endLine": 115, "endColumn": 31}, {"ruleId": "2005", "severity": 1, "message": "2072", "line": 147, "column": 6, "nodeType": "2007", "endLine": 147, "endColumn": 8, "suggestions": "2484"}, {"ruleId": "2078", "severity": 1, "message": "2126", "line": 230, "column": 41, "nodeType": "2080", "messageId": "2081", "endLine": 230, "endColumn": 43}, {"ruleId": "1988", "severity": 1, "message": "2143", "line": 241, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 241, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2047", "line": 10, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 10, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2152", "line": 10, "column": 23, "nodeType": "1990", "messageId": "1991", "endLine": 10, "endColumn": 37}, {"ruleId": "1988", "severity": 1, "message": "2485", "line": 23, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 23, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2029", "line": 24, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 24, "endColumn": 10}, {"ruleId": "1988", "severity": 1, "message": "2486", "line": 26, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 26, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2110", "line": 33, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 33, "endColumn": 9}, {"ruleId": "1988", "severity": 1, "message": "2487", "line": 39, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 39, "endColumn": 12}, {"ruleId": "1988", "severity": 1, "message": "2035", "line": 45, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 45, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2488", "line": 48, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 48, "endColumn": 15}, {"ruleId": "1988", "severity": 1, "message": "2489", "line": 50, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 50, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2490", "line": 51, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 51, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2491", "line": 52, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 52, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2492", "line": 53, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 53, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2493", "line": 61, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 61, "endColumn": 15}, {"ruleId": "2322", "severity": 1, "message": "2494", "line": 68, "column": 11, "nodeType": "1990", "messageId": "2324", "endLine": 68, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2046", "line": 78, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 78, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2495", "line": 79, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 79, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2221", "line": 86, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 86, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2222", "line": 87, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 87, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2496", "line": 88, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 88, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2497", "line": 88, "column": 25, "nodeType": "1990", "messageId": "1991", "endLine": 88, "endColumn": 39}, {"ruleId": "1988", "severity": 1, "message": "2452", "line": 89, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 89, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2498", "line": 89, "column": 26, "nodeType": "1990", "messageId": "1991", "endLine": 89, "endColumn": 41}, {"ruleId": "1988", "severity": 1, "message": "2453", "line": 90, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 90, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2375", "line": 90, "column": 29, "nodeType": "1990", "messageId": "1991", "endLine": 90, "endColumn": 47}, {"ruleId": "1988", "severity": 1, "message": "2454", "line": 91, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 91, "endColumn": 28}, {"ruleId": "1988", "severity": 1, "message": "2376", "line": 91, "column": 30, "nodeType": "1990", "messageId": "1991", "endLine": 91, "endColumn": 49}, {"ruleId": "1988", "severity": 1, "message": "2499", "line": 97, "column": 19, "nodeType": "1990", "messageId": "1991", "endLine": 97, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2300", "line": 98, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 98, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2301", "line": 98, "column": 21, "nodeType": "1990", "messageId": "1991", "endLine": 98, "endColumn": 31}, {"ruleId": "2005", "severity": 1, "message": "2500", "line": 147, "column": 8, "nodeType": "2007", "endLine": 147, "endColumn": 44, "suggestions": "2501"}, {"ruleId": "1988", "severity": 1, "message": "2096", "line": 149, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 149, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2502", "line": 215, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 215, "endColumn": 32}, {"ruleId": "1988", "severity": 1, "message": "2503", "line": 260, "column": 7, "nodeType": "1990", "messageId": "1991", "endLine": 260, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2059", "line": 271, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 271, "endColumn": 34}, {"ruleId": "1988", "severity": 1, "message": "2504", "line": 325, "column": 19, "nodeType": "1990", "messageId": "1991", "endLine": 325, "endColumn": 29}, {"ruleId": "2078", "severity": 1, "message": "2126", "line": 562, "column": 51, "nodeType": "2080", "messageId": "2081", "endLine": 562, "endColumn": 53}, {"ruleId": "2505", "severity": 1, "message": "2506", "line": 573, "column": 49, "nodeType": "2149", "endLine": 573, "endColumn": 95}, {"ruleId": "2505", "severity": 1, "message": "2506", "line": 647, "column": 45, "nodeType": "2149", "endLine": 647, "endColumn": 129}, {"ruleId": "1988", "severity": 1, "message": "2507", "line": 9, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2085", "line": 17, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 17, "endColumn": 12}, {"ruleId": "1988", "severity": 1, "message": "2508", "line": 20, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 20, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2509", "line": 22, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 22, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2510", "line": 24, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 24, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2511", "line": 26, "column": 32, "nodeType": "1990", "messageId": "1991", "endLine": 26, "endColumn": 60}, {"ruleId": "1988", "severity": 1, "message": "2512", "line": 27, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 27, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2364", "line": 29, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 29, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2232", "line": 33, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 33, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2513", "line": 102, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 102, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2514", "line": 102, "column": 23, "nodeType": "1990", "messageId": "1991", "endLine": 102, "endColumn": 38}, {"ruleId": "1988", "severity": 1, "message": "2515", "line": 104, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 104, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2516", "line": 104, "column": 25, "nodeType": "1990", "messageId": "1991", "endLine": 104, "endColumn": 42}, {"ruleId": "1988", "severity": 1, "message": "2517", "line": 106, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 106, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2518", "line": 106, "column": 16, "nodeType": "1990", "messageId": "1991", "endLine": 106, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2519", "line": 107, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 107, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2520", "line": 107, "column": 16, "nodeType": "1990", "messageId": "1991", "endLine": 107, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2521", "line": 115, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 115, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2522", "line": 116, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 116, "endColumn": 28}, {"ruleId": "1988", "severity": 1, "message": "2523", "line": 117, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 117, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2524", "line": 119, "column": 22, "nodeType": "1990", "messageId": "1991", "endLine": 119, "endColumn": 36}, {"ruleId": "1988", "severity": 1, "message": "2525", "line": 121, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 121, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2526", "line": 121, "column": 20, "nodeType": "1990", "messageId": "1991", "endLine": 121, "endColumn": 32}, {"ruleId": "1988", "severity": 1, "message": "2527", "line": 122, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 122, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2528", "line": 122, "column": 26, "nodeType": "1990", "messageId": "1991", "endLine": 122, "endColumn": 44}, {"ruleId": "1988", "severity": 1, "message": "2095", "line": 123, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 123, "endColumn": 31}, {"ruleId": "1988", "severity": 1, "message": "2529", "line": 123, "column": 33, "nodeType": "1990", "messageId": "1991", "endLine": 123, "endColumn": 58}, {"ruleId": "1988", "severity": 1, "message": "2249", "line": 124, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 124, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2530", "line": 125, "column": 23, "nodeType": "1990", "messageId": "1991", "endLine": 125, "endColumn": 38}, {"ruleId": "1988", "severity": 1, "message": "2531", "line": 126, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 126, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2251", "line": 127, "column": 24, "nodeType": "1990", "messageId": "1991", "endLine": 127, "endColumn": 40}, {"ruleId": "1988", "severity": 1, "message": "2301", "line": 128, "column": 18, "nodeType": "1990", "messageId": "1991", "endLine": 128, "endColumn": 28}, {"ruleId": "1988", "severity": 1, "message": "2047", "line": 132, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 132, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2302", "line": 138, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 138, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2303", "line": 139, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 139, "endColumn": 15}, {"ruleId": "2005", "severity": 1, "message": "2532", "line": 228, "column": 7, "nodeType": "2007", "endLine": 228, "endColumn": 23, "suggestions": "2533"}, {"ruleId": "2005", "severity": 1, "message": "2534", "line": 255, "column": 6, "nodeType": "2007", "endLine": 255, "endColumn": 23, "suggestions": "2535"}, {"ruleId": "1988", "severity": 1, "message": "2059", "line": 437, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 437, "endColumn": 36}, {"ruleId": "1988", "severity": 1, "message": "2508", "line": 19, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 19, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2509", "line": 21, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 21, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2511", "line": 24, "column": 32, "nodeType": "1990", "messageId": "1991", "endLine": 24, "endColumn": 60}, {"ruleId": "1988", "severity": 1, "message": "2512", "line": 25, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 25, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2232", "line": 27, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 27, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2085", "line": 27, "column": 14, "nodeType": "1990", "messageId": "1991", "endLine": 27, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2364", "line": 28, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 28, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2430", "line": 32, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 32, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2515", "line": 118, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 118, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2516", "line": 118, "column": 25, "nodeType": "1990", "messageId": "1991", "endLine": 118, "endColumn": 42}, {"ruleId": "1988", "severity": 1, "message": "2527", "line": 119, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 119, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2528", "line": 119, "column": 26, "nodeType": "1990", "messageId": "1991", "endLine": 119, "endColumn": 44}, {"ruleId": "1988", "severity": 1, "message": "2273", "line": 121, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 121, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2219", "line": 122, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 122, "endColumn": 15}, {"ruleId": "1988", "severity": 1, "message": "2531", "line": 123, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 123, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2302", "line": 124, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 124, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2303", "line": 125, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 125, "endColumn": 12}, {"ruleId": "1988", "severity": 1, "message": "2251", "line": 126, "column": 24, "nodeType": "1990", "messageId": "1991", "endLine": 126, "endColumn": 40}, {"ruleId": "1988", "severity": 1, "message": "2301", "line": 127, "column": 18, "nodeType": "1990", "messageId": "1991", "endLine": 127, "endColumn": 28}, {"ruleId": "1988", "severity": 1, "message": "2536", "line": 128, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 128, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2537", "line": 128, "column": 26, "nodeType": "1990", "messageId": "1991", "endLine": 128, "endColumn": 44}, {"ruleId": "1988", "severity": 1, "message": "2049", "line": 129, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 129, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2240", "line": 129, "column": 21, "nodeType": "1990", "messageId": "1991", "endLine": 129, "endColumn": 34}, {"ruleId": "1988", "severity": 1, "message": "2519", "line": 130, "column": 13, "nodeType": "1990", "messageId": "1991", "endLine": 130, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2520", "line": 130, "column": 20, "nodeType": "1990", "messageId": "1991", "endLine": 130, "endColumn": 28}, {"ruleId": "1988", "severity": 1, "message": "2524", "line": 137, "column": 22, "nodeType": "1990", "messageId": "1991", "endLine": 137, "endColumn": 36}, {"ruleId": "1988", "severity": 1, "message": "2525", "line": 139, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 139, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2526", "line": 139, "column": 20, "nodeType": "1990", "messageId": "1991", "endLine": 139, "endColumn": 32}, {"ruleId": "1988", "severity": 1, "message": "1995", "line": 140, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 140, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2538", "line": 141, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 141, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2175", "line": 141, "column": 19, "nodeType": "1990", "messageId": "1991", "endLine": 141, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2047", "line": 143, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 143, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2539", "line": 163, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 163, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2540", "line": 163, "column": 23, "nodeType": "1990", "messageId": "1991", "endLine": 163, "endColumn": 38}, {"ruleId": "2005", "severity": 1, "message": "2541", "line": 185, "column": 7, "nodeType": "2007", "endLine": 185, "endColumn": 23, "suggestions": "2542"}, {"ruleId": "2005", "severity": 1, "message": "2541", "line": 248, "column": 4, "nodeType": "2007", "endLine": 248, "endColumn": 37, "suggestions": "2543"}, {"ruleId": "1988", "severity": 1, "message": "2059", "line": 386, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 386, "endColumn": 36}, {"ruleId": "1988", "severity": 1, "message": "2047", "line": 46, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 46, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2301", "line": 64, "column": 21, "nodeType": "1990", "messageId": "1991", "endLine": 64, "endColumn": 31}, {"ruleId": "1988", "severity": 1, "message": "2251", "line": 65, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 65, "endColumn": 43}, {"ruleId": "2005", "severity": 1, "message": "2544", "line": 77, "column": 8, "nodeType": "2007", "endLine": 77, "endColumn": 65, "suggestions": "2545"}, {"ruleId": "1988", "severity": 1, "message": "2503", "line": 152, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 152, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2546", "line": 232, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 232, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2547", "line": 255, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 255, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2548", "line": 264, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 264, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2549", "line": 269, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 269, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2290", "line": 1, "column": 38, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 51}, {"ruleId": "1988", "severity": 1, "message": "2031", "line": 4, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2550", "line": 12, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 12, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2551", "line": 19, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 19, "endColumn": 9}, {"ruleId": "1988", "severity": 1, "message": "2155", "line": 20, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 20, "endColumn": 12}, {"ruleId": "1988", "severity": 1, "message": "2086", "line": 22, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 22, "endColumn": 8}, {"ruleId": "1988", "severity": 1, "message": "2485", "line": 25, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 25, "endColumn": 10}, {"ruleId": "1988", "severity": 1, "message": "2029", "line": 26, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 26, "endColumn": 7}, {"ruleId": "1988", "severity": 1, "message": "2391", "line": 27, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 27, "endColumn": 9}, {"ruleId": "1988", "severity": 1, "message": "2552", "line": 31, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 31, "endColumn": 29}, {"ruleId": "1988", "severity": 1, "message": "2553", "line": 32, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 32, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2033", "line": 34, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 34, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2036", "line": 36, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 36, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2554", "line": 38, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 38, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2555", "line": 39, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 39, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2035", "line": 40, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 40, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2556", "line": 41, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 41, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2002", "line": 42, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 42, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2557", "line": 43, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 43, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2558", "line": 49, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 49, "endColumn": 33}, {"ruleId": "1988", "severity": 1, "message": "2559", "line": 55, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 55, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2560", "line": 56, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 56, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2561", "line": 56, "column": 26, "nodeType": "1990", "messageId": "1991", "endLine": 56, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2562", "line": 56, "column": 32, "nodeType": "1990", "messageId": "1991", "endLine": 56, "endColumn": 43}, {"ruleId": "1988", "severity": 1, "message": "2563", "line": 56, "column": 45, "nodeType": "1990", "messageId": "1991", "endLine": 56, "endColumn": 53}, {"ruleId": "1988", "severity": 1, "message": "2218", "line": 59, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 59, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2564", "line": 60, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 60, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2565", "line": 64, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 64, "endColumn": 11}, {"ruleId": "1988", "severity": 1, "message": "2566", "line": 65, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 65, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2567", "line": 66, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 66, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2568", "line": 67, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 67, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2569", "line": 69, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 69, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2106", "line": 70, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 70, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2570", "line": 73, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 73, "endColumn": 15}, {"ruleId": "1988", "severity": 1, "message": "2571", "line": 80, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 80, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2042", "line": 104, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 104, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2572", "line": 117, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 117, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2573", "line": 118, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 118, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2574", "line": 119, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 119, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2124", "line": 124, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 124, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2575", "line": 127, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 127, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2576", "line": 127, "column": 22, "nodeType": "1990", "messageId": "1991", "endLine": 127, "endColumn": 36}, {"ruleId": "1988", "severity": 1, "message": "2249", "line": 128, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 128, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2250", "line": 128, "column": 16, "nodeType": "1990", "messageId": "1991", "endLine": 128, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2047", "line": 129, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 129, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2577", "line": 131, "column": 19, "nodeType": "1990", "messageId": "1991", "endLine": 131, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2578", "line": 132, "column": 19, "nodeType": "1990", "messageId": "1991", "endLine": 132, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2579", "line": 133, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 133, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2452", "line": 134, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 134, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2453", "line": 135, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 135, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2375", "line": 135, "column": 26, "nodeType": "1990", "messageId": "1991", "endLine": 135, "endColumn": 44}, {"ruleId": "1988", "severity": 1, "message": "2454", "line": 136, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 136, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2376", "line": 136, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 136, "endColumn": 46}, {"ruleId": "1988", "severity": 1, "message": "1995", "line": 137, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 137, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2580", "line": 141, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 141, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2581", "line": 165, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 165, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2582", "line": 165, "column": 22, "nodeType": "1990", "messageId": "1991", "endLine": 165, "endColumn": 36}, {"ruleId": "1988", "severity": 1, "message": "2583", "line": 169, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 169, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2584", "line": 169, "column": 21, "nodeType": "1990", "messageId": "1991", "endLine": 169, "endColumn": 34}, {"ruleId": "1988", "severity": 1, "message": "2050", "line": 179, "column": 17, "nodeType": "1990", "messageId": "1991", "endLine": 179, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2585", "line": 233, "column": 6, "nodeType": "1990", "messageId": "1991", "endLine": 233, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2586", "line": 234, "column": 6, "nodeType": "1990", "messageId": "1991", "endLine": 234, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2587", "line": 243, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 243, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2588", "line": 250, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 250, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2589", "line": 257, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 257, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2463", "line": 264, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 264, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2590", "line": 284, "column": 25, "nodeType": "1990", "messageId": "1991", "endLine": 284, "endColumn": 42}, {"ruleId": "1988", "severity": 1, "message": "2591", "line": 286, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 286, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2592", "line": 286, "column": 18, "nodeType": "1990", "messageId": "1991", "endLine": 286, "endColumn": 28}, {"ruleId": "1988", "severity": 1, "message": "2593", "line": 287, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 287, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2594", "line": 287, "column": 19, "nodeType": "1990", "messageId": "1991", "endLine": 287, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2595", "line": 288, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 288, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2265", "line": 288, "column": 19, "nodeType": "1990", "messageId": "1991", "endLine": 288, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2596", "line": 333, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 333, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2268", "line": 394, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 394, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2264", "line": 460, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 460, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2057", "line": 1112, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 1112, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2058", "line": 1113, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 1113, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2597", "line": 1121, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 1121, "endColumn": 17}, {"ruleId": "2005", "severity": 1, "message": "2598", "line": 1142, "column": 5, "nodeType": "2007", "endLine": 1142, "endColumn": 21, "suggestions": "2599"}, {"ruleId": "1988", "severity": 1, "message": "2600", "line": 1150, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 1150, "endColumn": 29}, {"ruleId": "1988", "severity": 1, "message": "2601", "line": 1158, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 1158, "endColumn": 33}, {"ruleId": "1988", "severity": 1, "message": "2216", "line": 1176, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 1176, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2059", "line": 1185, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 1185, "endColumn": 36}, {"ruleId": "1988", "severity": 1, "message": "2060", "line": 1196, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 1196, "endColumn": 33}, {"ruleId": "1988", "severity": 1, "message": "2602", "line": 1245, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 1245, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2603", "line": 1252, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 1252, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2097", "line": 1256, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 1256, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2604", "line": 1265, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 1265, "endColumn": 45}, {"ruleId": "1988", "severity": 1, "message": "2605", "line": 1266, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 1266, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2606", "line": 1268, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 1268, "endColumn": 28}, {"ruleId": "1988", "severity": 1, "message": "2281", "line": 1270, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 1270, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2294", "line": 3, "column": 25, "nodeType": "1990", "messageId": "1991", "endLine": 3, "endColumn": 34}, {"ruleId": "1988", "severity": 1, "message": "2120", "line": 4, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2187", "line": 5, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 29}, {"ruleId": "1988", "severity": 1, "message": "2063", "line": 10, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 10, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "1998", "line": 23, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 23, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2607", "line": 23, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 23, "endColumn": 41}, {"ruleId": "1988", "severity": 1, "message": "2437", "line": 24, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 24, "endColumn": 28}, {"ruleId": "1988", "severity": 1, "message": "2438", "line": 24, "column": 30, "nodeType": "1990", "messageId": "1991", "endLine": 24, "endColumn": 52}, {"ruleId": "1988", "severity": 1, "message": "2047", "line": 25, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 25, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2152", "line": 25, "column": 22, "nodeType": "1990", "messageId": "1991", "endLine": 25, "endColumn": 36}, {"ruleId": "1988", "severity": 1, "message": "2608", "line": 26, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 26, "endColumn": 15}, {"ruleId": "1988", "severity": 1, "message": "2609", "line": 26, "column": 17, "nodeType": "1990", "messageId": "1991", "endLine": 26, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2306", "line": 27, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 27, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2432", "line": 28, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 28, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2433", "line": 28, "column": 24, "nodeType": "1990", "messageId": "1991", "endLine": 28, "endColumn": 40}, {"ruleId": "1988", "severity": 1, "message": "2610", "line": 29, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 29, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2611", "line": 36, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 36, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2612", "line": 64, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 64, "endColumn": 27}, {"ruleId": "2078", "severity": 1, "message": "2079", "line": 66, "column": 30, "nodeType": "2080", "messageId": "2081", "endLine": 66, "endColumn": 32}, {"ruleId": "2078", "severity": 1, "message": "2079", "line": 75, "column": 38, "nodeType": "2080", "messageId": "2081", "endLine": 75, "endColumn": 40}, {"ruleId": "2078", "severity": 1, "message": "2079", "line": 88, "column": 36, "nodeType": "2080", "messageId": "2081", "endLine": 88, "endColumn": 38}, {"ruleId": "2005", "severity": 1, "message": "2613", "line": 97, "column": 5, "nodeType": "2007", "endLine": 97, "endColumn": 18, "suggestions": "2614"}, {"ruleId": "1988", "severity": 1, "message": "2002", "line": 1, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2615", "line": 3, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 3, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2047", "line": 35, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 35, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2616", "line": 76, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 76, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2617", "line": 78, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 78, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2618", "line": 2, "column": 20, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 31}, {"ruleId": "1988", "severity": 1, "message": "2083", "line": 3, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 3, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2084", "line": 4, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2085", "line": 5, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2619", "line": 6, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 6, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2620", "line": 7, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 7, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2621", "line": 11, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 11, "endColumn": 32}, {"ruleId": "1988", "severity": 1, "message": "2622", "line": 52, "column": 6, "nodeType": "1990", "messageId": "1991", "endLine": 52, "endColumn": 15}, {"ruleId": "1988", "severity": 1, "message": "2623", "line": 54, "column": 6, "nodeType": "1990", "messageId": "1991", "endLine": 54, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2605", "line": 80, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 80, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2606", "line": 88, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 88, "endColumn": 28}, {"ruleId": "1988", "severity": 1, "message": "2002", "line": 3, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 3, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2624", "line": 4, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2625", "line": 10, "column": 52, "nodeType": "1990", "messageId": "1991", "endLine": 10, "endColumn": 72}, {"ruleId": "1988", "severity": 1, "message": "2215", "line": 11, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 11, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2244", "line": 11, "column": 20, "nodeType": "1990", "messageId": "1991", "endLine": 11, "endColumn": 29}, {"ruleId": "1988", "severity": 1, "message": "2249", "line": 12, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 12, "endColumn": 15}, {"ruleId": "1988", "severity": 1, "message": "2250", "line": 12, "column": 17, "nodeType": "1990", "messageId": "1991", "endLine": 12, "endColumn": 25}, {"ruleId": "2005", "severity": 1, "message": "2626", "line": 44, "column": 5, "nodeType": "2007", "endLine": 44, "endColumn": 20, "suggestions": "2627"}, {"ruleId": "1988", "severity": 1, "message": "2628", "line": 77, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 77, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2629", "line": 18, "column": 59, "nodeType": "1990", "messageId": "1991", "endLine": 18, "endColumn": 68}, {"ruleId": "1988", "severity": 1, "message": "2630", "line": 72, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 72, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2180", "line": 1, "column": 38, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 51}, {"ruleId": "1988", "severity": 1, "message": "2631", "line": 1, "column": 114, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 126}, {"ruleId": "1988", "severity": 1, "message": "2632", "line": 7, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 7, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2002", "line": 9, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2452", "line": 42, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 42, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2453", "line": 43, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 43, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2375", "line": 43, "column": 26, "nodeType": "1990", "messageId": "1991", "endLine": 43, "endColumn": 44}, {"ruleId": "1988", "severity": 1, "message": "2454", "line": 44, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 44, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2376", "line": 44, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 44, "endColumn": 46}, {"ruleId": "1988", "severity": 1, "message": "2633", "line": 52, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 52, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2463", "line": 53, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 53, "endColumn": 28}, {"ruleId": "1988", "severity": 1, "message": "2634", "line": 80, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 80, "endColumn": 32}, {"ruleId": "2005", "severity": 1, "message": "2635", "line": 175, "column": 6, "nodeType": "2007", "endLine": 175, "endColumn": 26, "suggestions": "2636"}, {"ruleId": "1988", "severity": 1, "message": "2637", "line": 1, "column": 38, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 49}, {"ruleId": "1988", "severity": 1, "message": "2638", "line": 2, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 29}, {"ruleId": "1988", "severity": 1, "message": "2639", "line": 3, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 3, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2640", "line": 3, "column": 32, "nodeType": "1990", "messageId": "1991", "endLine": 3, "endColumn": 49}, {"ruleId": "1988", "severity": 1, "message": "2070", "line": 4, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2464", "line": 4, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 35}, {"ruleId": "1988", "severity": 1, "message": "2156", "line": 5, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2085", "line": 5, "column": 37, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 47}, {"ruleId": "1988", "severity": 1, "message": "2485", "line": 5, "column": 49, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 57}, {"ruleId": "1988", "severity": 1, "message": "2029", "line": 5, "column": 59, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 64}, {"ruleId": "1988", "severity": 1, "message": "2179", "line": 5, "column": 97, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 103}, {"ruleId": "1988", "severity": 1, "message": "2181", "line": 5, "column": 105, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 116}, {"ruleId": "1988", "severity": 1, "message": "2180", "line": 5, "column": 118, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 131}, {"ruleId": "1988", "severity": 1, "message": "2089", "line": 6, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 6, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2229", "line": 8, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 8, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2641", "line": 10, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 10, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2642", "line": 17, "column": 6, "nodeType": "1990", "messageId": "1991", "endLine": 17, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2222", "line": 21, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 21, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2223", "line": 22, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 22, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2221", "line": 32, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 32, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2373", "line": 33, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 33, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2370", "line": 34, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 34, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2643", "line": 35, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 35, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2644", "line": 36, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 36, "endColumn": 12}, {"ruleId": "1988", "severity": 1, "message": "2307", "line": 37, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 37, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2298", "line": 38, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 38, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2645", "line": 39, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 39, "endColumn": 15}, {"ruleId": "1988", "severity": 1, "message": "2300", "line": 40, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 40, "endColumn": 10}, {"ruleId": "1988", "severity": 1, "message": "2301", "line": 41, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 41, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2452", "line": 45, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 45, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2453", "line": 46, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 46, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2375", "line": 46, "column": 26, "nodeType": "1990", "messageId": "1991", "endLine": 46, "endColumn": 44}, {"ruleId": "1988", "severity": 1, "message": "2454", "line": 47, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 47, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2376", "line": 47, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 47, "endColumn": 46}, {"ruleId": "1988", "severity": 1, "message": "1995", "line": 48, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 48, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2090", "line": 49, "column": 25, "nodeType": "1990", "messageId": "1991", "endLine": 49, "endColumn": 42}, {"ruleId": "1988", "severity": 1, "message": "2463", "line": 52, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 52, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2213", "line": 91, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 91, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2646", "line": 2, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 28}, {"ruleId": "1988", "severity": 1, "message": "2647", "line": 2, "column": 30, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 46}, {"ruleId": "1988", "severity": 1, "message": "2648", "line": 2, "column": 48, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 66}, {"ruleId": "1988", "severity": 1, "message": "2236", "line": 4, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2649", "line": 10, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 10, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2373", "line": 12, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 12, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2378", "line": 14, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 14, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2300", "line": 15, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 15, "endColumn": 12}, {"ruleId": "1988", "severity": 1, "message": "2307", "line": 16, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 16, "endColumn": 15}, {"ruleId": "1988", "severity": 1, "message": "2644", "line": 17, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 17, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2302", "line": 20, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 20, "endColumn": 9}, {"ruleId": "1988", "severity": 1, "message": "2303", "line": 21, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 21, "endColumn": 8}, {"ruleId": "1988", "severity": 1, "message": "2298", "line": 22, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 22, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2261", "line": 60, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 60, "endColumn": 29}, {"ruleId": "1988", "severity": 1, "message": "2286", "line": 64, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 64, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2650", "line": 119, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 119, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2651", "line": 1, "column": 17, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2082", "line": 1, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 36}, {"ruleId": "1988", "severity": 1, "message": "2451", "line": 2, "column": 26, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 48}, {"ruleId": "1988", "severity": 1, "message": "2646", "line": 3, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 3, "endColumn": 28}, {"ruleId": "1988", "severity": 1, "message": "2652", "line": 5, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 28}, {"ruleId": "1988", "severity": 1, "message": "2653", "line": 6, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 6, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2157", "line": 1, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2485", "line": 2, "column": 29, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 37}, {"ruleId": "1988", "severity": 1, "message": "2029", "line": 2, "column": 39, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 44}, {"ruleId": "1988", "severity": 1, "message": "2155", "line": 2, "column": 52, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 62}, {"ruleId": "1988", "severity": 1, "message": "2311", "line": 23, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 23, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2307", "line": 24, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 24, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2298", "line": 27, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 27, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2452", "line": 42, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 42, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2454", "line": 43, "column": 12, "nodeType": "1990", "messageId": "1991", "endLine": 43, "endColumn": 28}, {"ruleId": "1988", "severity": 1, "message": "2376", "line": 43, "column": 30, "nodeType": "1990", "messageId": "1991", "endLine": 43, "endColumn": 49}, {"ruleId": "1988", "severity": 1, "message": "2654", "line": 134, "column": 29, "nodeType": "1990", "messageId": "1991", "endLine": 134, "endColumn": 32}, {"ruleId": "1988", "severity": 1, "message": "2463", "line": 156, "column": 11, "nodeType": "1990", "messageId": "1991", "endLine": 156, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2155", "line": 2, "column": 92, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 102}, {"ruleId": "1988", "severity": 1, "message": "2655", "line": 75, "column": 19, "nodeType": "1990", "messageId": "1991", "endLine": 75, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2070", "line": 1, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2070", "line": 1, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2002", "line": 1, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2656", "line": 3, "column": 7, "nodeType": "1990", "messageId": "1991", "endLine": 3, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2365", "line": 1, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2070", "line": 2, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2657", "line": 2, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 40}, {"ruleId": "1988", "severity": 1, "message": "2658", "line": 1, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2121", "line": 2, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 34}, {"ruleId": "1988", "severity": 1, "message": "2422", "line": 9, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2423", "line": 10, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 10, "endColumn": 21}, {"ruleId": "2005", "severity": 1, "message": "2659", "line": 48, "column": 5, "nodeType": "2007", "endLine": 48, "endColumn": 14, "suggestions": "2660"}, {"ruleId": "1988", "severity": 1, "message": "2661", "line": 3, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 3, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2002", "line": 4, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2662", "line": 24, "column": 7, "nodeType": "1990", "messageId": "1991", "endLine": 24, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2070", "line": 1, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2464", "line": 1, "column": 43, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 51}, {"ruleId": "1988", "severity": 1, "message": "2512", "line": 3, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 3, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2447", "line": 4, "column": 25, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 39}, {"ruleId": "1988", "severity": 1, "message": "2070", "line": 1, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2663", "line": 2, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2664", "line": 2, "column": 28, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 50}, {"ruleId": "1988", "severity": 1, "message": "2002", "line": 3, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 3, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2365", "line": 7, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 7, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2665", "line": 9, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2427", "line": 9, "column": 26, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 37}, {"ruleId": "1988", "severity": 1, "message": "2666", "line": 10, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 10, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2641", "line": 12, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 12, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2570", "line": 13, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 13, "endColumn": 15}, {"ruleId": "1988", "severity": 1, "message": "2469", "line": 55, "column": 7, "nodeType": "1990", "messageId": "1991", "endLine": 55, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "1995", "line": 69, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 69, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2667", "line": 98, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 98, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2668", "line": 99, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 99, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2669", "line": 99, "column": 20, "nodeType": "1990", "messageId": "1991", "endLine": 99, "endColumn": 32}, {"ruleId": "1988", "severity": 1, "message": "2452", "line": 112, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 112, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2453", "line": 114, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 114, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2375", "line": 114, "column": 26, "nodeType": "1990", "messageId": "1991", "endLine": 114, "endColumn": 44}, {"ruleId": "1988", "severity": 1, "message": "2454", "line": 115, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 115, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2376", "line": 115, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 115, "endColumn": 46}, {"ruleId": "1988", "severity": 1, "message": "2670", "line": 117, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 117, "endColumn": 38}, {"ruleId": "1988", "severity": 1, "message": "2671", "line": 126, "column": 17, "nodeType": "1990", "messageId": "1991", "endLine": 126, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2672", "line": 127, "column": 25, "nodeType": "1990", "messageId": "1991", "endLine": 127, "endColumn": 42}, {"ruleId": "1988", "severity": 1, "message": "2673", "line": 128, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 128, "endColumn": 18}, {"ruleId": "2078", "severity": 1, "message": "2126", "line": 173, "column": 22, "nodeType": "2080", "messageId": "2081", "endLine": 173, "endColumn": 24}, {"ruleId": "1988", "severity": 1, "message": "2100", "line": 282, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 282, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2674", "line": 344, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 344, "endColumn": 31}, {"ruleId": "1988", "severity": 1, "message": "2455", "line": 345, "column": 7, "nodeType": "1990", "messageId": "1991", "endLine": 345, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2675", "line": 346, "column": 7, "nodeType": "1990", "messageId": "1991", "endLine": 346, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2463", "line": 373, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 373, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2676", "line": 1, "column": 51, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 61}, {"ruleId": "1988", "severity": 1, "message": "2083", "line": 5, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 8}, {"ruleId": "1988", "severity": 1, "message": "2084", "line": 6, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 6, "endColumn": 10}, {"ruleId": "1988", "severity": 1, "message": "2390", "line": 8, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 8, "endColumn": 12}, {"ruleId": "1988", "severity": 1, "message": "2677", "line": 9, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2485", "line": 11, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 11, "endColumn": 10}, {"ruleId": "1988", "severity": 1, "message": "2029", "line": 12, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 12, "endColumn": 7}, {"ruleId": "1988", "severity": 1, "message": "2427", "line": 22, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 22, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2464", "line": 23, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 23, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2625", "line": 51, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 51, "endColumn": 23}, {"ruleId": "1988", "severity": 1, "message": "2678", "line": 54, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 54, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "1995", "line": 62, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 62, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2679", "line": 64, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 64, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2455", "line": 110, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 110, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2456", "line": 110, "column": 18, "nodeType": "1990", "messageId": "1991", "endLine": 110, "endColumn": 28}, {"ruleId": "1988", "severity": 1, "message": "2680", "line": 161, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 161, "endColumn": 12}, {"ruleId": "2005", "severity": 1, "message": "2681", "line": 176, "column": 5, "nodeType": "2007", "endLine": 176, "endColumn": 20, "suggestions": "2682"}, {"ruleId": "1988", "severity": 1, "message": "2602", "line": 269, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 269, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2667", "line": 286, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 286, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2683", "line": 287, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 287, "endColumn": 15}, {"ruleId": "1988", "severity": 1, "message": "2671", "line": 287, "column": 17, "nodeType": "1990", "messageId": "1991", "endLine": 287, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2672", "line": 288, "column": 25, "nodeType": "1990", "messageId": "1991", "endLine": 288, "endColumn": 42}, {"ruleId": "1988", "severity": 1, "message": "2082", "line": 1, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 36}, {"ruleId": "1988", "severity": 1, "message": "2676", "line": 1, "column": 51, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 61}, {"ruleId": "1988", "severity": 1, "message": "2684", "line": 1, "column": 63, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 72}, {"ruleId": "1988", "severity": 1, "message": "2157", "line": 4, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 8}, {"ruleId": "1988", "severity": 1, "message": "2083", "line": 5, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 8}, {"ruleId": "1988", "severity": 1, "message": "2084", "line": 6, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 6, "endColumn": 10}, {"ruleId": "1988", "severity": 1, "message": "2356", "line": 7, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 7, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2390", "line": 8, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 8, "endColumn": 12}, {"ruleId": "1988", "severity": 1, "message": "2677", "line": 9, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2485", "line": 11, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 11, "endColumn": 10}, {"ruleId": "1988", "severity": 1, "message": "2029", "line": 12, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 12, "endColumn": 7}, {"ruleId": "1988", "severity": 1, "message": "2232", "line": 13, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 13, "endColumn": 5}, {"ruleId": "1988", "severity": 1, "message": "2685", "line": 15, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 15, "endColumn": 27}, {"ruleId": "1988", "severity": 1, "message": "2425", "line": 15, "column": 48, "nodeType": "1990", "messageId": "1991", "endLine": 15, "endColumn": 63}, {"ruleId": "1988", "severity": 1, "message": "2464", "line": 16, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 16, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2365", "line": 22, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 22, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2229", "line": 23, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 23, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2678", "line": 36, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 36, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2644", "line": 37, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 37, "endColumn": 12}, {"ruleId": "1988", "severity": 1, "message": "2302", "line": 39, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 39, "endColumn": 7}, {"ruleId": "1988", "severity": 1, "message": "2303", "line": 40, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 40, "endColumn": 6}, {"ruleId": "1988", "severity": 1, "message": "2297", "line": 41, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 41, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2298", "line": 42, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 42, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2027", "line": 46, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 46, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2607", "line": 46, "column": 22, "nodeType": "1990", "messageId": "1991", "endLine": 46, "endColumn": 36}, {"ruleId": "1988", "severity": 1, "message": "2680", "line": 83, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 83, "endColumn": 12}, {"ruleId": "1988", "severity": 1, "message": "2686", "line": 90, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 90, "endColumn": 26}, {"ruleId": "1988", "severity": 1, "message": "2602", "line": 99, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 99, "endColumn": 20}, {"ruleId": "1988", "severity": 1, "message": "2451", "line": 2, "column": 26, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 48}, {"ruleId": "1988", "severity": 1, "message": "2082", "line": 1, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 36}, {"ruleId": "1988", "severity": 1, "message": "2637", "line": 1, "column": 38, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 49}, {"ruleId": "1988", "severity": 1, "message": "2676", "line": 1, "column": 51, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 61}, {"ruleId": "1988", "severity": 1, "message": "2684", "line": 1, "column": 63, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 72}, {"ruleId": "1988", "severity": 1, "message": "2156", "line": 3, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 3, "endColumn": 11}, {"ruleId": "1988", "severity": 1, "message": "2157", "line": 4, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 8}, {"ruleId": "1988", "severity": 1, "message": "2083", "line": 5, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 8}, {"ruleId": "1988", "severity": 1, "message": "2084", "line": 6, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 6, "endColumn": 10}, {"ruleId": "1988", "severity": 1, "message": "2356", "line": 7, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 7, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2390", "line": 8, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 8, "endColumn": 12}, {"ruleId": "1988", "severity": 1, "message": "2677", "line": 9, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2687", "line": 10, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 10, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2485", "line": 11, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 11, "endColumn": 10}, {"ruleId": "1988", "severity": 1, "message": "2029", "line": 12, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 12, "endColumn": 7}, {"ruleId": "1988", "severity": 1, "message": "2232", "line": 13, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 13, "endColumn": 5}, {"ruleId": "1988", "severity": 1, "message": "2464", "line": 16, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 16, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2085", "line": 18, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 18, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2159", "line": 19, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 19, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2423", "line": 20, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 20, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2422", "line": 21, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 21, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2365", "line": 22, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 22, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2229", "line": 24, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 24, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2642", "line": 32, "column": 6, "nodeType": "1990", "messageId": "1991", "endLine": 32, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2678", "line": 39, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 39, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "1995", "line": 50, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 50, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2090", "line": 52, "column": 25, "nodeType": "1990", "messageId": "1991", "endLine": 52, "endColumn": 42}, {"ruleId": "1988", "severity": 1, "message": "2082", "line": 1, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 36}, {"ruleId": "1988", "severity": 1, "message": "2637", "line": 1, "column": 38, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 49}, {"ruleId": "1988", "severity": 1, "message": "2676", "line": 1, "column": 51, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 61}, {"ruleId": "1988", "severity": 1, "message": "2684", "line": 1, "column": 63, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 72}, {"ruleId": "1988", "severity": 1, "message": "2156", "line": 3, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 3, "endColumn": 11}, {"ruleId": "1988", "severity": 1, "message": "2157", "line": 4, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 8}, {"ruleId": "1988", "severity": 1, "message": "2083", "line": 5, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 8}, {"ruleId": "1988", "severity": 1, "message": "2084", "line": 6, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 6, "endColumn": 10}, {"ruleId": "1988", "severity": 1, "message": "2356", "line": 7, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 7, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2390", "line": 8, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 8, "endColumn": 12}, {"ruleId": "1988", "severity": 1, "message": "2677", "line": 9, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2687", "line": 10, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 10, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2485", "line": 11, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 11, "endColumn": 10}, {"ruleId": "1988", "severity": 1, "message": "2029", "line": 12, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 12, "endColumn": 7}, {"ruleId": "1988", "severity": 1, "message": "2232", "line": 13, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 13, "endColumn": 5}, {"ruleId": "1988", "severity": 1, "message": "2464", "line": 16, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 16, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2085", "line": 18, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 18, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2159", "line": 19, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 19, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2423", "line": 20, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 20, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2422", "line": 21, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 21, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2365", "line": 22, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 22, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2229", "line": 24, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 24, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2642", "line": 32, "column": 6, "nodeType": "1990", "messageId": "1991", "endLine": 32, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2678", "line": 39, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 39, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "1995", "line": 50, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 50, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2090", "line": 52, "column": 25, "nodeType": "1990", "messageId": "1991", "endLine": 52, "endColumn": 42}, {"ruleId": "1988", "severity": 1, "message": "2082", "line": 1, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 36}, {"ruleId": "1988", "severity": 1, "message": "2637", "line": 1, "column": 38, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 49}, {"ruleId": "1988", "severity": 1, "message": "2676", "line": 1, "column": 51, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 61}, {"ruleId": "1988", "severity": 1, "message": "2684", "line": 1, "column": 63, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 72}, {"ruleId": "1988", "severity": 1, "message": "2156", "line": 3, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 3, "endColumn": 11}, {"ruleId": "1988", "severity": 1, "message": "2157", "line": 4, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 8}, {"ruleId": "1988", "severity": 1, "message": "2083", "line": 5, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 8}, {"ruleId": "1988", "severity": 1, "message": "2084", "line": 6, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 6, "endColumn": 10}, {"ruleId": "1988", "severity": 1, "message": "2356", "line": 7, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 7, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2390", "line": 8, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 8, "endColumn": 12}, {"ruleId": "1988", "severity": 1, "message": "2677", "line": 9, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2687", "line": 10, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 10, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2485", "line": 11, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 11, "endColumn": 10}, {"ruleId": "1988", "severity": 1, "message": "2029", "line": 12, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 12, "endColumn": 7}, {"ruleId": "1988", "severity": 1, "message": "2232", "line": 13, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 13, "endColumn": 5}, {"ruleId": "1988", "severity": 1, "message": "2464", "line": 16, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 16, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2085", "line": 18, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 18, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2159", "line": 19, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 19, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2423", "line": 20, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 20, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2422", "line": 21, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 21, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2365", "line": 22, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 22, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2229", "line": 24, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 24, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2642", "line": 32, "column": 6, "nodeType": "1990", "messageId": "1991", "endLine": 32, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2678", "line": 40, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 40, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "1995", "line": 51, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 51, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2090", "line": 53, "column": 25, "nodeType": "1990", "messageId": "1991", "endLine": 53, "endColumn": 42}, {"ruleId": "1988", "severity": 1, "message": "2082", "line": 1, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 36}, {"ruleId": "1988", "severity": 1, "message": "2637", "line": 1, "column": 38, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 49}, {"ruleId": "1988", "severity": 1, "message": "2676", "line": 1, "column": 51, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 61}, {"ruleId": "1988", "severity": 1, "message": "2684", "line": 1, "column": 63, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 72}, {"ruleId": "1988", "severity": 1, "message": "2156", "line": 3, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 3, "endColumn": 11}, {"ruleId": "1988", "severity": 1, "message": "2157", "line": 4, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 4, "endColumn": 8}, {"ruleId": "1988", "severity": 1, "message": "2083", "line": 5, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 5, "endColumn": 8}, {"ruleId": "1988", "severity": 1, "message": "2084", "line": 6, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 6, "endColumn": 10}, {"ruleId": "1988", "severity": 1, "message": "2356", "line": 7, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 7, "endColumn": 13}, {"ruleId": "1988", "severity": 1, "message": "2390", "line": 8, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 8, "endColumn": 12}, {"ruleId": "1988", "severity": 1, "message": "2677", "line": 9, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 16}, {"ruleId": "1988", "severity": 1, "message": "2687", "line": 10, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 10, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2485", "line": 11, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 11, "endColumn": 10}, {"ruleId": "1988", "severity": 1, "message": "2029", "line": 12, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 12, "endColumn": 7}, {"ruleId": "1988", "severity": 1, "message": "2232", "line": 13, "column": 2, "nodeType": "1990", "messageId": "1991", "endLine": 13, "endColumn": 5}, {"ruleId": "1988", "severity": 1, "message": "2464", "line": 16, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 16, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2085", "line": 18, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 18, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2159", "line": 19, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 19, "endColumn": 22}, {"ruleId": "1988", "severity": 1, "message": "2423", "line": 20, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 20, "endColumn": 21}, {"ruleId": "1988", "severity": 1, "message": "2422", "line": 21, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 21, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2365", "line": 22, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 22, "endColumn": 19}, {"ruleId": "1988", "severity": 1, "message": "2229", "line": 24, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 24, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2642", "line": 32, "column": 6, "nodeType": "1990", "messageId": "1991", "endLine": 32, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2678", "line": 39, "column": 3, "nodeType": "1990", "messageId": "1991", "endLine": 39, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "1995", "line": 50, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 50, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2090", "line": 52, "column": 25, "nodeType": "1990", "messageId": "1991", "endLine": 52, "endColumn": 42}, {"ruleId": "1988", "severity": 1, "message": "2464", "line": 3, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 3, "endColumn": 35}, {"ruleId": "1988", "severity": 1, "message": "2229", "line": 9, "column": 8, "nodeType": "1990", "messageId": "1991", "endLine": 9, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2688", "line": 60, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 60, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2689", "line": 60, "column": 20, "nodeType": "1990", "messageId": "1991", "endLine": 60, "endColumn": 31}, {"ruleId": "2005", "severity": 1, "message": "2690", "line": 95, "column": 6, "nodeType": "2007", "endLine": 95, "endColumn": 40, "suggestions": "2691"}, {"ruleId": "2005", "severity": 1, "message": "2692", "line": 59, "column": 5, "nodeType": "2007", "endLine": 59, "endColumn": 14, "suggestions": "2693"}, {"ruleId": "1988", "severity": 1, "message": "2694", "line": 122, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 122, "endColumn": 20}, {"ruleId": "2078", "severity": 1, "message": "2126", "line": 124, "column": 28, "nodeType": "2080", "messageId": "2081", "endLine": 124, "endColumn": 30}, {"ruleId": "1988", "severity": 1, "message": "2695", "line": 1, "column": 27, "nodeType": "1990", "messageId": "1991", "endLine": 1, "endColumn": 35}, {"ruleId": "1988", "severity": 1, "message": "2339", "line": 2, "column": 38, "nodeType": "1990", "messageId": "1991", "endLine": 2, "endColumn": 54}, {"ruleId": "1988", "severity": 1, "message": "2124", "line": 17, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 17, "endColumn": 17}, {"ruleId": "1988", "severity": 1, "message": "2696", "line": 18, "column": 10, "nodeType": "1990", "messageId": "1991", "endLine": 18, "endColumn": 15}, {"ruleId": "2005", "severity": 1, "message": "2697", "line": 251, "column": 6, "nodeType": "2007", "endLine": 251, "endColumn": 8, "suggestions": "2698"}, {"ruleId": "1988", "severity": 1, "message": "2699", "line": 30, "column": 7, "nodeType": "1990", "messageId": "1991", "endLine": 30, "endColumn": 18}, {"ruleId": "1988", "severity": 1, "message": "2700", "line": 95, "column": 7, "nodeType": "1990", "messageId": "1991", "endLine": 95, "endColumn": 44}, {"ruleId": "1988", "severity": 1, "message": "2701", "line": 545, "column": 9, "nodeType": "1990", "messageId": "1991", "endLine": 545, "endColumn": 25}, {"ruleId": "1988", "severity": 1, "message": "2702", "line": 6, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 6, "endColumn": 14}, {"ruleId": "1988", "severity": 1, "message": "2703", "line": 7, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 7, "endColumn": 10}, {"ruleId": "1988", "severity": 1, "message": "2704", "line": 8, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 8, "endColumn": 15}, {"ruleId": "1988", "severity": 1, "message": "2705", "line": 10, "column": 5, "nodeType": "1990", "messageId": "1991", "endLine": 10, "endColumn": 16}, "@typescript-eslint/no-unused-vars", "'lazy' is defined but never used.", "Identifier", "unusedVar", "'Router' is defined but never used.", "'useParams' is defined but never used.", "'Cookies' is defined but never used.", "'signOut' is assigned a value but never used.", "'loggedOut' is assigned a value but never used.", "'isResetLinkValid' is assigned a value but never used.", "'loginUserDetails' is assigned a value but never used.", "'setLoginUserDetail' is assigned a value but never used.", "'UserManager' is defined but never used.", "'Content' is defined but never used.", "'axios' is defined but never used.", "'getUserRoles' is defined but never used.", "'decodedToken' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'location.pathname', 'noLayoutRoutes', and 'signOut'. Either include them or remove the dependency array.", "ArrayExpression", ["2706"], "'CelebrationOutlinedIcon' is defined but never used.", "'ErrorOutlineOutlinedIcon' is defined but never used.", "'LockOpenIcon' is defined but never used.", "'NoAccountsOutlinedIcon' is defined but never used.", "'AccountCircleOutlinedIcon' is defined but never used.", "'KeyOutlinedIcon' is defined but never used.", "'CopyAllOutlinedIcon' is defined but never used.", "'LockOutlinedIcon' is defined but never used.", "'snackbarIcon' is assigned a value but never used.", "'Outlet' is defined but never used.", "'useLocation' is defined but never used.", "'useNavigate' is defined but never used.", "'Home' is defined but never used.", "'ProfileSettings' is defined but never used.", "'Domain' is defined but never used.", "'CodeInstall' is defined but never used.", "'Teamsetting' is assigned a value but never used.", "'user' is assigned a value but never used.", "'userDetails' is assigned a value but never used.", "'passwordLogId' is assigned a value but never used.", "'Alert' is defined but never used.", "'isOnLoginPage' is assigned a value but never used.", "'GridRenderCellParams' is defined but never used.", "'FormControlLabel' is defined but never used.", "'DeleteIcon' is defined but never used.", "'MailIcon' is defined but never used.", "'CustomGrid' is defined but never used.", "'MarkEmailReadIcon' is defined but never used.", "'organizationsList' is defined but never used.", "'CustomColumnMenu' is defined but never used.", "'FilterPopup' is defined but never used.", "'AddBoxIcon' is defined but never used.", "'Delete' is defined but never used.", "'CustomDataGridProps' is defined but never used.", "'email' is defined but never used.", "'anchorEl' is assigned a value but never used.", "'setAnchorEl' is assigned a value but never used.", "'emailiddelete' is assigned a value but never used.", "'sidebarOpen' is assigned a value but never used.", "'storedOrganization' is assigned a value but never used.", "'searchText' is assigned a value but never used.", "'setErrors' is assigned a value but never used.", "'AccountDeleteDetails' is assigned a value but never used.", "'setAccountDeleteDetails' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'FetchAccounts'. Either include it or remove the dependency array.", ["2707"], "'email' is assigned a value but never used.", "'accountIdNew' is assigned a value but never used.", "'onPageChange' is assigned a value but never used.", "'onPageSizeChange' is assigned a value but never used.", "'CustomToolbar' is assigned a value but never used.", "'handleDownloadExcelClick' is assigned a value but never used.", "'filteredColumnNames' is assigned a value but never used.", "'handleApplyFilters' is assigned a value but never used.", "'Navigate' is defined but never used.", "'Login' is defined but never used.", "'jwt_decode' is defined but never used.", "'LoginUserInfo' is defined but never used.", "React Hook useEffect has a missing dependency: 'navigate'. Either include it or remove the dependency array.", ["2708"], "'logout' is defined but never used.", "'adminApiService' is defined but never used.", "'setUserDetail' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'userDetails'. Either include it or remove the dependency array.", ["2709"], "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "'useEffect' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'IconButton' is defined but never used.", "'Switch' is defined but never used.", "'Radio' is defined but never used.", "'RadioGroup' is defined but never used.", "'CloseIcon' is defined but never used.", "'setOrganizationId' is assigned a value but never used.", "'setskip' is assigned a value but never used.", "'settop' is assigned a value but never used.", "'totalcount' is assigned a value but never used.", "'isRtl' is assigned a value but never used.", "'selectedOrganizationId' is assigned a value but never used.", "'openPopup' is assigned a value but never used.", "'handleSubmit' is assigned a value but never used.", "'alphanumericRegex' is assigned a value but never used.", "'response' is assigned a value but never used.", "'handleSelectChange' is assigned a value but never used.", "'errorMessage' is assigned a value but never used.", "'successMessage' is assigned a value but never used.", "'SAinitialsData' is defined but never used.", "'Avatar' is defined but never used.", "'quickadopt' is defined but never used.", "'AccountCircleIcon' is defined but never used.", "'LogoutIcon' is defined but never used.", "'LocalActivityIcon' is defined but never used.", "'Popup' is defined but never used.", "'Grid' is defined but never used.", "'LanguageIcon' is defined but never used.", "'translateText' is defined but never used.", "'AccountSettings' is defined but never used.", "'setSidebarOpen' is defined but never used.", "'MenuOpenIcon' is defined but never used.", "'MenuIcon' is defined but never used.", "'Sidebar' is defined but never used.", "'settings' is defined but never used.", "'LogoutPopup' is defined but never used.", "'userManager' is defined but never used.", "'userUrl' is defined but never used.", "'translatedLabels' is assigned a value but never used.", "'setTranslatedLabels' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'ORGANIZATION_ID' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "React Hook useEffect has missing dependencies: 'toLanguage' and 'userDetails?.Language'. Either include them or remove the dependency array. Outer scope values like 'accountId' aren't valid dependencies because mutating them doesn't re-render the component.", ["2710"], "'labelsNew' is assigned a value but never used.", "'handleHomeClick' is assigned a value but never used.", "'handleOrgClick' is assigned a value but never used.", "'handleAccountClick' is assigned a value but never used.", "'handleGuideClick' is assigned a value but never used.", "'handleAccSeetingsClick' is assigned a value but never used.", "'handleTeamClick' is assigned a value but never used.", "'handleThemesClick' is assigned a value but never used.", "'handleBillingClick' is assigned a value but never used.", "'handleInstallClick' is assigned a value but never used.", "'handlenotifyClick' is assigned a value but never used.", "'handleQuickAdoptClick' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'setAccountId', 'setRoles', 'userRoles', and 'userType'. Either include them or remove the dependency array.", ["2711"], "'toggleSidebar' is assigned a value but never used.", "'isAutoLoginCompleted' is assigned a value but never used.", "'hasAutoLoginUserData' is assigned a value but never used.", "'orgRtl' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'setSidebarOpen' is assigned a value but never used.", "jsx-a11y/role-supports-aria-props", "The attribute aria-pressed is not supported by the role checkbox. This role is implicit on the element input.", "'Typography' is defined but never used.", "'TextField' is defined but never used.", "'Button' is defined but never used.", "'Link' is defined but never used.", "'InputAdornment' is defined but never used.", "'blue' is defined but never used.", "'blueGrey' is defined but never used.", "'TextareaAutosize' is defined but never used.", "'orgId' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'translate'. Either include it or remove the dependency array.", ["2712"], "'isSidebarOpen' is defined but never used.", "'Card' is defined but never used.", "'ModernSidebar' is assigned a value but never used.", "'SidebarHeader' is assigned a value but never used.", "'SidebarTitle' is assigned a value but never used.", "'ModernList' is assigned a value but never used.", "'ModernListItemWrapper' is assigned a value but never used.", "'ModernListItemText' is assigned a value but never used.", "'isHidden' is assigned a value but never used.", "'setUserType' is assigned a value but never used.", ["2713"], "'settingsItems' is assigned a value but never used.", "'Autocomplete' is defined but never used.", "'Dialog' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContentText' is defined but never used.", "'DialogActions' is defined but never used.", "'GridColumnMenuProps' is defined but never used.", "'deleteUserRole' is defined but never used.", "'GetAllAccountsList' is defined but never used.", "'getOrganizationById' is defined but never used.", "'EditAccount' is defined but never used.", "'ActionDialogProps' is defined but never used.", "'selectedRoles' is assigned a value but never used.", "'setSelectedRoles' is assigned a value but never used.", "'timeZone' is assigned a value but never used.", "'isDialogReadOnly' is assigned a value but never used.", "'dialogMode' is assigned a value but never used.", "'currentRow' is assigned a value but never used.", "'roleNameToIdMap' is assigned a value but never used.", "React Hook useEffect has an unnecessary dependency: 'OrganizationId'. Either exclude it or remove the dependency array. Outer scope values like 'OrganizationId' aren't valid dependencies because mutating them doesn't re-render the component.", ["2714"], ["2715"], "React Hook useEffect has missing dependencies: 'paginationModel.page' and 'paginationModel.pageSize'. Either include them or remove the dependency array.", ["2716"], "'roleIdToNameMap' is assigned a value but never used.", "'limitcount' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUserRoles'. Either include it or remove the dependency array.", ["2717"], "'handleSearch' is assigned a value but never used.", "'uniqueUserNames' is assigned a value but never used.", "'uniqueEmails' is assigned a value but never used.", "'uniqueRoles' is assigned a value but never used.", "'ActionDialog' is assigned a value but never used.", "React Hook useEffect has unnecessary dependencies: 'selectedAccount' and 'selectedEmail'. Either exclude them or remove the dependency array. Outer scope values like 'selectedAccount' aren't valid dependencies because mutating them doesn't re-render the component.", ["2718"], "'duplicateRoles' is assigned a value but never used.", "'SubmitCreatenewUser' is defined but never used.", "'inputs' is assigned a value but never used.", "'openEditPopup' is assigned a value but never used.", "'handleReset' is assigned a value but never used.", "'SubmitCreateUser' is defined but never used.", "'models' is assigned a value but never used.", "'showPopup' is assigned a value but never used.", "'showEditPopup' is assigned a value but never used.", "'showDeletePopup' is assigned a value but never used.", "'setShowDeletePopup' is assigned a value but never used.", "'containerHeight' is assigned a value but never used.", "'buttonMarginTop' is assigned a value but never used.", "'GetAppTwoToneIcon' is defined but never used.", "'format' is defined but never used.", "'moment' is defined but never used.", "'styles' is defined but never used.", "'Menu' is defined but never used.", "'FormGroup' is defined but never used.", "'Box' is defined but never used.", "'SaveAltIcon' is defined but never used.", "'getOrganizationsData' is defined but never used.", "'AnyAaaaRecord' is defined but never used.", "'timezones' is defined but never used.", "'fetchUserDataFromApi' is defined but never used.", "'menuVisible' is assigned a value but never used.", "'setMenuVisible' is assigned a value but never used.", "'setSearchText' is assigned a value but never used.", "'gridHeight' is assigned a value but never used.", "'setGridHeight' is assigned a value but never used.", "'activePlan' is assigned a value but never used.", "'setInputs' is assigned a value but never used.", "'activeId' is assigned a value but never used.", "'setActiveId' is assigned a value but never used.", "'filteredUsers' is assigned a value but never used.", "'setFilteredUsers' is assigned a value but never used.", "'users' is assigned a value but never used.", "'setUsers' is assigned a value but never used.", "'setOrderByFields' is assigned a value but never used.", "'logo' is assigned a value but never used.", "'setLogo' is assigned a value but never used.", "'logoUrl' is assigned a value but never used.", "'timezoneError' is assigned a value but never used.", "'setTimezoneError' is assigned a value but never used.", "'dateFormatError' is assigned a value but never used.", "'currentDate' is assigned a value but never used.", "'setPlanTypeError' is assigned a value but never used.", "'createddate' is assigned a value but never used.", "'handleTimezoneChange' is assigned a value but never used.", "'pendingSwitchState' is assigned a value but never used.", "'setCheckedOne' is assigned a value but never used.", "'IsActive' is assigned a value but never used.", "'setIsActive' is assigned a value but never used.", "'handleMailClick' is assigned a value but never used.", "'handleDeleteClick' is assigned a value but never used.", "'handleClose' is assigned a value but never used.", "'handleChangeDate' is assigned a value but never used.", "'selectedValue' is assigned a value but never used.", "'handleRadioChange' is assigned a value but never used.", "'planEditClick' is assigned a value but never used.", "'organizations' is assigned a value but never used.", "'setOrganizations' is assigned a value but never used.", "'filterModel' is assigned a value but never used.", "'setFilterModel' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'isPaginationResetDone'. Either include it or remove the dependency array.", ["2719"], "React Hook useEffect has missing dependencies: 'filters', 'paginationModel.page', and 'paginationModel.pageSize'. Either include them or remove the dependency array.", ["2720"], "'filteredRows' is assigned a value but never used.", "'handleExportMenuClick' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'dateFormat'. Either include it or remove the dependency array.", ["2721"], "'toBase64' is assigned a value but never used.", "'handleFileChange' is assigned a value but never used.", "'validateLogo' is assigned a value but never used.", "'setColumnMenuApi' is assigned a value but never used.", "'handleColumnMenuClose' is assigned a value but never used.", "'useTransition' is defined but never used.", "'EditOutlinedIcon' is defined but never used.", "'DeleteOutlineOutlinedIcon' is defined but never used.", "'settingsiconAnnouncements' is defined but never used.", "'subscribe' is defined but never used.", "'Settings' is defined but never used.", "'setName' is assigned a value but never used.", "'OrganizationId' is assigned a value but never used.", "'setTotalcount' is assigned a value but never used.", "'orderByFields' is assigned a value but never used.", "'filters' is assigned a value but never used.", "'setFilters' is assigned a value but never used.", "'skip' is assigned a value but never used.", "'top' is assigned a value but never used.", "'tourName' is assigned a value but never used.", "'setTourName' is assigned a value but never used.", "'organizationId' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTours'. Either include it or remove the dependency array.", ["2722"], "'tourslist' is assigned a value but never used.", "'setTourslist' is assigned a value but never used.", "'bannerName' is assigned a value but never used.", "'setBannerName' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchBanners'. Either include it or remove the dependency array.", ["2723"], "'handleKeyDown' is assigned a value but never used.", ["2724"], "'userInfo' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAnnouncements'. Either include it or remove the dependency array.", ["2725"], ["2726"], "@typescript-eslint/no-redeclare", "'Tooltip' is already defined.", "redeclared", "'statusss' is assigned a value but never used.", "'TooltipsNew' is assigned a value but never used.", "'setTooltipsNew' is assigned a value but never used.", "'filterss' is assigned a value but never used.", "'setFilterss' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTooltips'. Either include it or remove the dependency array.", ["2727"], "'HotspotsNew' is assigned a value but never used.", "'setHotspotsNew' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchHotspots'. Either include it or remove the dependency array.", ["2728"], "React Hook useEffect has a missing dependency: 'fetchChecklists'. Either include it or remove the dependency array.", ["2729"], ["2730"], "'CircularProgress' is defined but never used.", "'BorderColorOutlinedIcon' is defined but never used.", "'formatDateTime' is defined but never used.", "'formatDate' is assigned a value but never used.", "'truncatedText' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchHistoryData'. Either include it or remove the dependency array.", ["2731"], ["2732"], "React Hook useEffect has a missing dependency: 'loadSystemPrompt'. Either include it or remove the dependency array.", ["2733"], ["2734"], "'Chip' is defined but never used.", "'useAuth' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadHistoricalPrompt'. Either include it or remove the dependency array.", ["2735"], ["2736"], "'DataGrid' is defined but never used.", "'FormControl' is defined but never used.", "'AssignmentOutlinedIcon' is defined but never used.", "'VisibilityIcon' is defined but never used.", "'GetAllAccounts' is defined but never used.", "'fetchDeleteAccountDetails' is defined but never used.", "'GetAccountsList' is defined but never used.", "'AccountCustomColumnMenu' is defined but never used.", "'SearchIcon' is defined but never used.", "'ClearIcon' is defined but never used.", "'JSEncrypt' is defined but never used.", "'ConstructionOutlined' is defined but never used.", "'openaikey' is defined but never used.", "'keyLoader' is defined but never used.", "'width' is defined but never used.", "'accountidedit' is assigned a value but never used.", "'setAccountIdEdit' is assigned a value but never used.", "'showeditPopup' is assigned a value but never used.", "'setShowEditPopup' is assigned a value but never used.", "'setemailiddelete' is assigned a value but never used.", "'setSnackbarMessage' is assigned a value but never used.", "'setSnackbarSeverity' is assigned a value but never used.", "'Organizationid' is assigned a value but never used.", "'sortModel' is assigned a value but never used.", "'handleSortModelChange' is assigned a value but never used.", "'setPaginationModel' is assigned a value but never used.", "'openAiError' is assigned a value but never used.", "'roles' is assigned a value but never used.", "'loadingKey' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'FetchAccountById' and 'FetchSystemPrompts'. Either include them or remove the dependency array. Outer scope values like 'accountId' aren't valid dependencies because mutating them doesn't re-render the component.", ["2737"], "'emailId' is assigned a value but never used.", "'accountid' is assigned a value but never used.", "'globalhandleSearch' is assigned a value but never used.", "'openOpenAiInput' is assigned a value but never used.", "'InputLabel' is defined but never used.", "'Divider' is defined but never used.", "'UnPublishIcon' is defined but never used.", "'ShareIcon' is defined but never used.", "'RadioProps' is defined but never used.", "'SavePageTargets' is defined but never used.", "'ConfirmationDialog' is defined but never used.", "'getAllGuides' is defined but never used.", "'setGuideId' is assigned a value but never used.", "'open' is assigned a value but never used.", "'setOpen' is assigned a value but never used.", "'initialGuide' is assigned a value but never used.", "'setInitialGuide' is assigned a value but never used.", "'hasUnsavedChanges' is assigned a value but never used.", "'setHasUnsavedChanges' is assigned a value but never used.", "'customPublishDate' is assigned a value but never used.", "'setCustomPublishDate' is assigned a value but never used.", "'customUnPublishDate' is assigned a value but never used.", "'setCustomUnPublishDate' is assigned a value but never used.", "'CustomDivider' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'location.pathname' and 'navigate'. Either include them or remove the dependency array.", ["2738"], "'handleDrawClick' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'guide?.GuideDetails.TargetUrl'. Either include it or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setTriggers' needs the current value of 'guide.GuideDetails.TargetUrl'.", ["2739"], "React Hook useEffect has a missing dependency: 'guide?.GuideDetails?.Frequency'. Either include it or remove the dependency array.", ["2740"], "Unexpected mix of '||' and '&&'. Use parentheses to clarify the intended order of operations.", "React Hook useEffect has missing dependencies: 'HandlePublishToggle' and 'currentGuideId'. Either include them or remove the dependency array.", ["2741"], "'linkexpirationimage' is defined but never used.", "'onHandleClick' is assigned a value but never used.", "'Visibility' is defined but never used.", "'VisibilityOff' is defined but never used.", "'superAdminLogin' is defined but never used.", "'encryptPassword' is defined but never used.", "'GetUserDetailsById' is defined but never used.", "'getAllUsers' is defined but never used.", "'userDetails' is defined but never used.", "'UserId' is defined but never used.", "'OrganizationId' is defined but never used.", "'setUser' is assigned a value but never used.", "'loginUserInfo' is assigned a value but never used.", "'setLoginUserInfo' is assigned a value but never used.", "'setresponse' is assigned a value but never used.", "'userIds' is assigned a value but never used.", "'setuserId' is assigned a value but never used.", "'organizationDetails' is assigned a value but never used.", "'setOrganizationDetails' is assigned a value but never used.", "'SAinitialsData' is assigned a value but never used.", "'UserId' is assigned a value but never used.", "'safeLocalStorageClear' is assigned a value but never used.", ["2742"], "React Hook useEffect has an unnecessary dependency: 'userDetails'. Either exclude it or remove the dependency array. Outer scope values like 'userDetails' aren't valid dependencies because mutating them doesn't re-render the component.", ["2743"], "Assignments to the 'UserId' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "MemberExpression", "'userApiService' is defined but never used.", "'AnySoaRecord' is defined but never used.", "'adminUrl' is assigned a value but never used.", "'userUrl' is assigned a value but never used.", "'GridColumnMenuHideItem' is defined but never used.", "'snackbarOpen' is assigned a value but never used.", "'snackbarMessage' is assigned a value but never used.", "'snackbarSeverity' is assigned a value but never used.", "'isValid' is assigned a value but never used.", "'setIsValid' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'accountidedit' and 'fetchAccountDetails'. Either include them or remove the dependency array.", ["2744"], ["2745"], ["2746"], "'handleOrganizationDropdownOpen' is assigned a value but never used.", "'handleSwitchChange' is assigned a value but never used.", "'handleSnackbarClose' is assigned a value but never used.", "'adminUrl' is defined but never used.", "'Container' is defined but never used.", "'FormLabel' is defined but never used.", "'CameraAlt' is defined but never used.", "'GetUserDetail' is defined but never used.", "'publicKey' is assigned a value but never used.", "'contactEditable' is assigned a value but never used.", "'showPassword' is assigned a value but never used.", "'setShowPassword' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentProfileData' and 'updatedProfileData'. Either include them or remove the dependency array. You can also do a functional update 'setCurrentProfileData(c => ...)' if you only need 'currentProfileData' in the 'setCurrentProfileData' call.", ["2747"], ["2748"], "'selectedDate' is assigned a value but never used.", "'getYesterdayDate' is assigned a value but never used.", "'Banners' is defined but never used.", "'Auditlog' is defined but never used.", "'Cursor' is defined but never used.", "'useTheme' is defined but never used.", "'MuiTooltip' is defined but never used.", "'setTranslatedTitles' is assigned a value but never used.", ["2749"], "'Snackbar' is defined but never used.", "'CardContent' is defined but never used.", "'Opacity' is defined but never used.", "'AddIcon' is defined but never used.", "'height' is defined but never used.", "'inherits' is defined but never used.", "'LinkIcon' is defined but never used.", "'UpgradeIcon' is defined but never used.", "'uploadfile' is defined but never used.", "'FileUpload' is already defined.", "'useridedit' is assigned a value but never used.", "'fileUploads' is assigned a value but never used.", "'setFileUploads' is assigned a value but never used.", "'setSnackbarOpen' is assigned a value but never used.", "'setLimit' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["2750"], "'columns' is assigned a value but never used.", "'handleEdit' is assigned a value but never used.", "'filesArray' is assigned a value but never used.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "'GridPaginationModel' is defined but never used.", "'DatePicker' is defined but never used.", "'AdapterDateFns' is defined but never used.", "'getOrganization' is defined but never used.", "'GetAuditLogsByOrganizationId' is defined but never used.", "'SearchParams' is defined but never used.", "'totalRecords' is assigned a value but never used.", "'setTotalRecords' is assigned a value but never used.", "'referenceTypes' is assigned a value but never used.", "'setReferenceTypes' is assigned a value but never used.", "'orgid' is assigned a value but never used.", "'setOrgId' is assigned a value but never used.", "'types' is assigned a value but never used.", "'setTypes' is assigned a value but never used.", "'organization' is assigned a value but never used.", "'defaultOrganization' is assigned a value but never used.", "'orgname' is assigned a value but never used.", "'setCreatedUser' is assigned a value but never used.", "'eventType' is assigned a value but never used.", "'setEventType' is assigned a value but never used.", "'allAuditLogData' is assigned a value but never used.", "'setAllAuditLogData' is assigned a value but never used.", "'setSelectedOrganizationId' is assigned a value but never used.", "'setSelectedUser' is assigned a value but never used.", "'isClearing' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchFilteredData' and 'selectedUser'. Either include them or remove the dependency array.", ["2751"], "React Hook useEffect has missing dependencies: 'fetchFilteredData', 'organizationId', and 'selectedUser'. Either include them or remove the dependency array.", ["2752"], "'selectedOptions' is assigned a value but never used.", "'setSelectedOptions' is assigned a value but never used.", "'userType' is assigned a value but never used.", "'allAuditLogs' is assigned a value but never used.", "'setAllAuditLogs' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchFilteredData'. Either include it or remove the dependency array.", ["2753"], ["2754"], "React Hook useEffect has a missing dependency: 'fetchTrainingDocuments'. Either include it or remove the dependency array.", ["2755"], "'handleDownload' is assigned a value but never used.", "'isValidDocumentType' is assigned a value but never used.", "'getPriorityLabel' is assigned a value but never used.", "'getPriorityColor' is assigned a value but never used.", "'GridColumnsManagement' is defined but never used.", "'Popover' is defined but never used.", "'ToggleOffOutlinedIcon' is defined but never used.", "'ToggleOnRoundedIcon' is defined but never used.", "'loader' is defined but never used.", "'userData' is defined but never used.", "'PersonOffSharpIcon' is defined but never used.", "'MarkEmailRead' is defined but never used.", "'PersonOutlineOutlinedIcon' is defined but never used.", "'DraftsIcon' is defined but never used.", "'Unblockaccount' is defined but never used.", "'Mail' is defined but never used.", "'Keyvertical' is defined but never used.", "'Lockopen' is defined but never used.", "'Submitdisableuser' is defined but never used.", "'BlockUser' is defined but never used.", "'UnblockUser' is defined but never used.", "'deactivateUser' is defined but never used.", "'activateUser' is defined but never used.", "'NoAccountsIcon' is defined but never used.", "'error' is defined but never used.", "'Search' is defined but never used.", "'isfltr' is assigned a value but never used.", "'glblsrch' is assigned a value but never used.", "'fieldName' is defined but never used.", "'useridreset' is assigned a value but never used.", "'setUserIdReset' is assigned a value but never used.", "'setLastName' is assigned a value but never used.", "'setUserName' is assigned a value but never used.", "'helperText' is assigned a value but never used.", "'usersEmails' is assigned a value but never used.", "'newpassword' is assigned a value but never used.", "'setNewpassword' is assigned a value but never used.", "'columnname' is assigned a value but never used.", "'setColumnname' is assigned a value but never used.", "'skipss' is defined but never used.", "'topss' is defined but never used.", "'handleLastnameFocus' is assigned a value but never used.", "'handleUsernameFocus' is assigned a value but never used.", "'handleEmailFocus' is assigned a value but never used.", "'setEmailConfirmed' is assigned a value but never used.", "'blocked' is assigned a value but never used.", "'setBlocked' is assigned a value but never used.", "'activate' is assigned a value but never used.", "'setActivate' is assigned a value but never used.", "'isactive' is assigned a value but never used.", "'success' is assigned a value but never used.", "'fetchData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'filters' and 'orderByFields'. Either include them or remove the dependency array.", ["2756"], "'handleNextButtonClick' is assigned a value but never used.", "'handlePreviousButtonClick' is assigned a value but never used.", "'handleChange' is assigned a value but never used.", "'handleSubmituser' is assigned a value but never used.", "'onChange' is assigned a value but never used.", "'handlePageChange' is assigned a value but never used.", "'handlePageSizeChange' is assigned a value but never used.", "'setUserDetails' is assigned a value but never used.", "'userId' is assigned a value but never used.", "'setUserId' is assigned a value but never used.", "'location' is assigned a value but never used.", "'hasAutoLoginParams' is assigned a value but never used.", "'loggedinUserInfo' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'Navigate' and 'signOut'. Either include them or remove the dependency array. Outer scope values like 'userDetails' aren't valid dependencies because mutating them doesn't re-render the component.", ["2757"], "'AnyMxRecord' is defined but never used.", "'stepImage' is assigned a value but never used.", "'guides' is assigned a value but never used.", "'GridToolbar' is defined but never used.", "'ChevronLeftIcon' is defined but never used.", "'ChevronRightIcon' is defined but never used.", "'CustomPaginationProps' is defined but never used.", "'lastValue' is assigned a value but never used.", "'totalrowss' is defined but never used.", "'GridColDef' is defined but never used.", "'fetchUserDataFromApi' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'userId'. Either include it or remove the dependency array.", ["2758"], "'navigate' is assigned a value but never used.", "'getLabels' is defined but never used.", "'dropdownLanguage' is assigned a value but never used.", "'ListItemText' is defined but never used.", "'ConfirmPopup' is defined but never used.", "'openSnackbar' is assigned a value but never used.", "'handleconfirmclosepopup' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'isShareFeedbackPopupTwo'. Either include it or remove the dependency array. Outer scope values like 'ShareFeedbackPopup' aren't valid dependencies because mutating them doesn't re-render the component.", ["2759"], "'ChangeEvent' is defined but never used.", "'getAllOrganizations' is defined but never used.", "'SubmitAccountDetails' is defined but never used.", "'fetchAccountsById' is defined but never used.", "'User' is defined but never used.", "'ErrorFields' is defined but never used.", "'GetAllAccounts' is assigned a value but never used.", "'setModels' is assigned a value but never used.", "'orderByField' is assigned a value but never used.", "'fetchOrganizations' is defined but never used.", "'getOrganizations' is defined but never used.", "'updateOrganization' is defined but never used.", "'setModelsData' is assigned a value but never used.", "'handleBlur' is assigned a value but never used.", "'useState' is defined but never used.", "'ModelTrainingSharp' is defined but never used.", "'Organization' is defined but never used.", "'url' is assigned a value but never used.", "'guideStatus' is assigned a value but never used.", "'API_URL' is assigned a value but never used.", "'idsApiService' is defined but never used.", "'ResetPassword' is defined but never used.", "React Hook useEffect has missing dependencies: 'column' and 'searchText'. Either include them or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setFilteredOptions' needs the current value of 'searchText'.", ["2760"], "'Status' is defined but never used.", "'headers' is assigned a value but never used.", "'TrainingDocument' is defined but never used.", "'TrainingDocumentUpload' is defined but never used.", "'fetchUsersList' is defined but never used.", "'AnyCnameRecord' is defined but never used.", "'apierror' is assigned a value but never used.", "'isTouched' is assigned a value but never used.", "'setIsTouched' is assigned a value but never used.", "'handleTogglePasswordVisibility' is assigned a value but never used.", "'setEmails' is assigned a value but never used.", "'setContactNumbers' is assigned a value but never used.", "'formValid' is assigned a value but never used.", "'newErrors' is assigned a value but never used.", "'errorMessages' is assigned a value but never used.", "'FocusEvent' is defined but never used.", "'FormHelperText' is defined but never used.", "'fetchUsersList' is assigned a value but never used.", "'userDetailss' is assigned a value but never used.", "'age' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchUserDetails' and 'userId'. Either include them or remove the dependency array.", ["2761"], "'emails' is assigned a value but never used.", "'FormEvent' is defined but never used.", "'SubmitUserDetails' is defined but never used.", "'handleGenderChange' is assigned a value but never used.", "'SelectChangeEvent' is defined but never used.", "'allNames' is assigned a value but never used.", "'setAllNames' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'allOrganizationNames'. Either include it or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setFilteredOptions' needs the current value of 'allOrganizationNames'.", ["2762"], "React Hook useEffect has missing dependencies: 'column' and 'searchText'. Either include them or remove the dependency array. If 'setFilteredOptions' needs the current value of 'searchText', you can also switch to useReducer instead of useState and read 'searchText' in the reducer.", ["2763"], "'dummyAgents' is assigned a value but never used.", "'Suspense' is defined but never used.", "'error' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'guideTypes', 'guidesByType', and 'selectedGuideType'. Either include them or remove the dependency array.", ["2764"], "'MetricsGrid' is assigned a value but never used.", "'MetricCard' is assigned a value but never used.", "'analyticsMetrics' is assigned a value but never used.", "'hyperlink' is defined but never used.", "'files' is defined but never used.", "'uploadicon' is defined but never used.", "'galleryicon' is defined but never used.", {"desc": "2765", "fix": "2766"}, {"desc": "2767", "fix": "2768"}, {"desc": "2769", "fix": "2770"}, {"desc": "2771", "fix": "2772"}, {"desc": "2773", "fix": "2774"}, {"desc": "2775", "fix": "2776"}, {"desc": "2777", "fix": "2778"}, {"desc": "2771", "fix": "2779"}, {"desc": "2780", "fix": "2781"}, {"desc": "2782", "fix": "2783"}, {"desc": "2784", "fix": "2785"}, {"desc": "2786", "fix": "2787"}, {"desc": "2782", "fix": "2788"}, {"desc": "2789", "fix": "2790"}, {"desc": "2791", "fix": "2792"}, {"desc": "2793", "fix": "2794"}, {"desc": "2795", "fix": "2796"}, {"desc": "2797", "fix": "2798"}, {"desc": "2799", "fix": "2800"}, {"desc": "2801", "fix": "2802"}, {"desc": "2803", "fix": "2804"}, {"desc": "2805", "fix": "2806"}, {"desc": "2807", "fix": "2808"}, {"desc": "2809", "fix": "2810"}, {"desc": "2811", "fix": "2812"}, {"desc": "2813", "fix": "2814"}, {"kind": "2815", "justification": "2816"}, {"desc": "2817", "fix": "2818"}, {"kind": "2815", "justification": "2816"}, {"desc": "2819", "fix": "2820"}, {"kind": "2815", "justification": "2816"}, {"desc": "2821", "fix": "2822"}, {"desc": "2823", "fix": "2824"}, {"desc": "2825", "fix": "2826"}, {"desc": "2827", "fix": "2828"}, {"desc": "2829", "fix": "2830"}, {"desc": "2831", "fix": "2832"}, {"desc": "2782", "fix": "2833"}, {"desc": "2834", "fix": "2835"}, {"desc": "2836", "fix": "2837"}, {"desc": "2771", "fix": "2838"}, {"desc": "2839", "fix": "2840"}, {"desc": "2841", "fix": "2842"}, {"desc": "2771", "fix": "2843"}, {"desc": "2844", "fix": "2845"}, {"desc": "2846", "fix": "2847"}, {"desc": "2848", "fix": "2849"}, {"desc": "2850", "fix": "2851"}, {"desc": "2852", "fix": "2853"}, {"desc": "2854", "fix": "2855"}, {"desc": "2856", "fix": "2857"}, {"desc": "2858", "fix": "2859"}, {"desc": "2860", "fix": "2861"}, {"desc": "2862", "fix": "2863"}, {"desc": "2864", "fix": "2865"}, {"desc": "2866", "fix": "2867"}, {"desc": "2868", "fix": "2869"}, {"desc": "2864", "fix": "2870"}, {"desc": "2871", "fix": "2872"}, "Update the dependencies array to be: [location.pathname, loggedOut, noLayoutRoutes, signOut]", {"range": "2873", "text": "2874"}, "Update the dependencies array to be: [FetchAccounts, paginationModel]", {"range": "2875", "text": "2876"}, "Update the dependencies array to be: [navigate]", {"range": "2877", "text": "2878"}, "Update the dependencies array to be: [userDetails]", {"range": "2879", "text": "2880"}, "Update the dependencies array to be: [toLanguage, userDetails?.Language]", {"range": "2881", "text": "2882"}, "Update the dependencies array to be: [accounts, setAccountId, setRoles, userRoles, userType]", {"range": "2883", "text": "2884"}, "Update the dependencies array to be: [copied, openSnackbar, translate]", {"range": "2885", "text": "2886"}, {"range": "2887", "text": "2880"}, "Update the dependencies array to be: [userId]", {"range": "2888", "text": "2889"}, "Update the dependencies array to be: []", {"range": "2890", "text": "2891"}, "Update the dependencies array to be: [paginationModel.page, paginationModel.pageSize]", {"range": "2892", "text": "2893"}, "Update the dependencies array to be: [fetchUserRoles, paginationModel, selectedAccount]", {"range": "2894", "text": "2895"}, {"range": "2896", "text": "2891"}, "Update the dependencies array to be: [paginationModel, filters, sortModel, isPaginationResetDone]", {"range": "2897", "text": "2898"}, "Update the dependencies array to be: [filters, paginationModel.page, paginationModel.pageSize, sortModel]", {"range": "2899", "text": "2900"}, "Update the dependencies array to be: [dateFormat]", {"range": "2901", "text": "2902"}, "Update the dependencies array to be: [paginationModel, activeTab, accountId, fetchTours]", {"range": "2903", "text": "2904"}, "Update the dependencies array to be: [fetchBanners, searchQuery]", {"range": "2905", "text": "2906"}, "Update the dependencies array to be: [paginationModel, activeTab, accountId, fetchBanners]", {"range": "2907", "text": "2908"}, "Update the dependencies array to be: [fetchAnnouncements, searchQuery]", {"range": "2909", "text": "2910"}, "Update the dependencies array to be: [paginationModel, activeTab, accountId, fetchAnnouncements]", {"range": "2911", "text": "2912"}, "Update the dependencies array to be: [paginationModel, activeTab, accountId, fetchTooltips]", {"range": "2913", "text": "2914"}, "Update the dependencies array to be: [paginationModel, activeTab, accountId, fetchHotspots]", {"range": "2915", "text": "2916"}, "Update the dependencies array to be: [fetchChecklists, searchQuery]", {"range": "2917", "text": "2918"}, "Update the dependencies array to be: [paginationModel, activeTab, accountId, fetchChecklists]", {"range": "2919", "text": "2920"}, "Update the dependencies array to be: [agentData, fetchHistoryData, paginationModel]", {"range": "2921", "text": "2922"}, "directive", "", "Update the dependencies array to be: [agentData, loadSystemPrompt]", {"range": "2923", "text": "2924"}, "Update the dependencies array to be: [historyItem, loadHistoricalPrompt]", {"range": "2925", "text": "2926"}, "Update the dependencies array to be: [paginationModel, FetchAccountById, FetchSystemPrompts]", {"range": "2927", "text": "2928"}, "Update the dependencies array to be: [currentGuideId, guideStatus, location.pathname, navigate]", {"range": "2929", "text": "2930"}, "Update the dependencies array to be: [guide?.GuideDetails.TargetUrl, triggers]", {"range": "2931", "text": "2932"}, "Update the dependencies array to be: [guide?.GuideDetails?.Frequency, location.state]", {"range": "2933", "text": "2934"}, "Update the dependencies array to be: [HandlePublishToggle, currentGuideId, guide]", {"range": "2935", "text": "2936"}, "Update the dependencies array to be: [user, autoLoginCompleted, location.search, navigate]", {"range": "2937", "text": "2938"}, {"range": "2939", "text": "2891"}, "Update the dependencies array to be: [accountidedit, fetchAccountDetails, showEditPopup]", {"range": "2940", "text": "2941"}, "Update the dependencies array to be: [AccountDetails, translate]", {"range": "2942", "text": "2943"}, {"range": "2944", "text": "2880"}, "Update the dependencies array to be: [currentProfileData, updatedProfileData, userDetails.UserId]", {"range": "2945", "text": "2946"}, "Update the dependencies array to be: [userDetails, userDetails.OrganizationId]", {"range": "2947", "text": "2948"}, {"range": "2949", "text": "2880"}, "Update the dependencies array to be: [showPopup, skip, debouncedSearchTerm, fetchData]", {"range": "2950", "text": "2951"}, "Update the dependencies array to be: [fetchFilteredData, organizationId, selectedUser]", {"range": "2952", "text": "2953"}, "Update the dependencies array to be: [fetchFilteredData, organizationId, paginationModel, selectedUser]", {"range": "2954", "text": "2955"}, "Update the dependencies array to be: [fetchFilteredData, organizationId]", {"range": "2956", "text": "2957"}, "Update the dependencies array to be: [fetchFilteredData, organizationId, paginationModel]", {"range": "2958", "text": "2959"}, "Update the dependencies array to be: [userDetails?.OrganizationId, paginationModel, accountId, fetchTrainingDocuments]", {"range": "2960", "text": "2961"}, "Update the dependencies array to be: [OrganizationId, filters, orderByFields]", {"range": "2962", "text": "2963"}, "Update the dependencies array to be: [Navigate, signOut]", {"range": "2964", "text": "2965"}, "Update the dependencies array to be: [showEditPopup, userId]", {"range": "2966", "text": "2967"}, "Update the dependencies array to be: [isShareFeedbackPopupTwo]", {"range": "2968", "text": "2969"}, "Update the dependencies array to be: [column, options, searchText]", {"range": "2970", "text": "2971"}, "Update the dependencies array to be: [fetchUserDetails, showEditPopup, userId]", {"range": "2972", "text": "2973"}, "Update the dependencies array to be: [options, models, column, filters, allOrganizationNames]", {"range": "2974", "text": "2975"}, {"range": "2976", "text": "2971"}, "Update the dependencies array to be: [guideTypes, guidesByType, selectedGuideType]", {"range": "2977", "text": "2978"}, [5241, 5252], "[location.pathname, loggedOut, noLayoutRoutes, signOut]", [5375, 5392], "[FetchAccounts, paginationModel]", [786, 788], "[navigate]", [1691, 1693], "[userDetails]", [11079, 11090], "[toLanguage, userDetails?.Language]", [16929, 16939], "[accounts, setAccountId, setRoles, userRoles, userType]", [3078, 3100], "[copied, openSnackbar, translate]", [3624, 3626], [5648, 5672], "[userId]", [6650, 6666], "[]", [7670, 7672], "[paginationModel.page, paginationModel.pageSize]", [12003, 12037], "[fetch<PERSON><PERSON><PERSON><PERSON><PERSON>, paginationModel, selectedAccount]", [25502, 25534], [11129, 11166], "[paginationModel, filters, sortModel, isPaginationResetDone]", [11740, 11751], "[filters, paginationModel.page, paginationModel.pageSize, sortModel]", [17513, 17515], "[dateFormat]", [6097, 6135], "[paginationModel, activeTab, accountId, fetchTours]", [4518, 4531], "[fetchBanners, searchQuery]", [6380, 6418], "[paginationModel, activeTab, accountId, fetchBanners]", [3801, 3814], "[fetchAnnouncements, searchQuery]", [5307, 5345], "[paginationModel, activeTab, accountId, fetchAnnouncements]", [5675, 5714], "[paginationModel, activeTab, accountId, fetchTooltips]", [5662, 5701], "[paginationModel, activeTab, accountId, fetchHotspots]", [3768, 3781], "[fetch<PERSON><PERSON><PERSON><PERSON>, searchQuery]", [5256, 5294], "[paginationModel, activeTab, accountId, fetchChecklists]", [2474, 2502], "[agentData, fetchHistoryData, paginationModel]", [6161, 6172], "[agentData, loadSystemPrompt]", [1765, 1778], "[historyItem, loadHistoricalPrompt]", [9833, 9860], "[pagination<PERSON><PERSON><PERSON>, FetchAccountById, FetchSystemPrompts]", [10800, 10829], "[currentGuideId, guideStatus, location.pathname, navigate]", [14935, 14945], "[guide?.GuideDetails.TargetUrl, triggers]", [17312, 17328], "[guide?.GuideDetails?.Frequency, location.state]", [29948, 29955], "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, currentGuideId, guide]", [5016, 5059], "[user, autoLoginCompleted, location.search, navigate]", [12705, 12718], [2616, 2631], "[accountidedit, fetchAccountDetails, showEditPopup]", [3943, 3959], "[Account<PERSON><PERSON><PERSON>, translate]", [4635, 4637], [3580, 3601], "[currentProfileData, updatedProfileData, userDetails.UserId]", [3874, 3903], "[userDetails, userDetails.OrganizationId]", [4883, 4885], [5086, 5122], "[showPopup, skip, debouncedSear<PERSON>Term, fetchData]", [7999, 8015], "[fetchFiltered<PERSON><PERSON>, organizationId, selectedUser]", [8953, 8970], "[fetchFilteredData, organizationId, paginationModel, selectedUser]", [6171, 6187], "[fetchFilteredData, organizationId]", [8309, 8342], "[fetchFilteredData, organizationId, paginationModel]", [2756, 2813], "[userDetails?.OrganizationId, paginationModel, accountId, fetchTrainingDocuments]", [33395, 33411], "[OrganizationId, filters, orderByFields]", [3838, 3851], "[Navigate, signOut]", [1172, 1187], "[showEditPopup, userId]", [6315, 6335], "[isShareFeedbackPopupTwo]", [1465, 1474], "[column, options, searchText]", [5017, 5032], "[fetchUserDetails, showEditPopup, userId]", [3302, 3336], "[options, models, column, filters, allOrganizationNames]", [1602, 1611], [9164, 9166], "[guideTypes, guidesByType, selectedGuideType]"]