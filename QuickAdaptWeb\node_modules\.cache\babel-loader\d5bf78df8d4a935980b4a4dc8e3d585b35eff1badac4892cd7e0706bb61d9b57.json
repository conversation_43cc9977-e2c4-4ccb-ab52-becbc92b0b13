{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptWeb\\\\src\\\\routing\\\\ProtectedRoute.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Navigate, Outlet } from \"react-router-dom\";\nimport { useAuth } from \"../components/auth/AuthProvider\";\nimport { useLocation } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  allowedUserTypes\n}) => {\n  _s();\n  var _location$state, _loginUser$UserType;\n  const location = useLocation();\n  const {\n    user,\n    userDetails,\n    signOut,\n    loggedOut\n  } = useAuth();\n  const [AdminDetails, setUserDetail] = useState(((_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.userDetail) || null);\n  const [loginUserDetails, setLoginUserDetail] = useState();\n  useEffect(() => {\n    const handleStorageChange = event => {\n      if (event.key === 'logout-event') {\n        signOut();\n      }\n    };\n    window.addEventListener('storage', handleStorageChange);\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n    };\n  }, [signOut]);\n  useEffect(() => {\n    const userInfoString = localStorage.getItem(\"userInfo\");\n    if (userInfoString) {\n      try {\n        const userInfo = JSON.parse(userInfoString);\n        if (userInfo['user']) {\n          const parsedUser = JSON.parse(userInfo['user']);\n          if (parsedUser) {\n            setLoginUserDetail(parsedUser);\n          }\n        }\n      } catch (error) {\n        console.error(\"Error parsing userInfo: \", error);\n      }\n    } else if (userDetails) {\n      setLoginUserDetail(userDetails);\n    }\n  }, []);\n  const loginUser = userDetails || loginUserDetails || AdminDetails || null;\n  const userType = typeof loginUser === 'object' && (loginUser === null || loginUser === void 0 ? void 0 : (_loginUser$UserType = loginUser.UserType) === null || _loginUser$UserType === void 0 ? void 0 : _loginUser$UserType.toLowerCase()) || \"\";\n\n  // Check if we're in auto-login flow to avoid premature redirects\n  const isAutoLoginCompleted = sessionStorage.getItem('autoLoginCompleted') === 'true';\n  const urlParams = new URLSearchParams(window.location.search);\n  const hasAutoLoginParams = urlParams.has('token') && urlParams.has('userId') && urlParams.has('orgId');\n  if ((!user || !loginUser) && userType !== \"superadmin\") {\n    // Don't redirect if we're in auto-login flow or auto-login just completed\n    if (isAutoLoginCompleted || hasAutoLoginParams) {\n      console.log('ProtectedRoute - Skipping redirect due to auto-login flow');\n      // If we have loginUser from localStorage but no user from auth context, allow access\n      if (loginUser) {\n        return /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 16\n        }, this);\n      }\n    }\n    if (loggedOut) {\n      signOut();\n      return /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/login\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 14\n      }, this);\n    }\n\n    // Only redirect to login if not in auto-login flow\n    if (!isAutoLoginCompleted && !hasAutoLoginParams) {\n      return /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/login\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 14\n      }, this);\n    }\n  }\n  if (loginUser !== null && userType !== null && userType.toLocaleLowerCase() != \"superadmin\") {\n    if (allowedUserTypes.includes(\"superadmin\") && userType.toLocaleLowerCase() != \"superadmin\") {\n      // If user type is not allowed, redirect to home or an unauthorized page\n\n      return /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/admin/adminlogin\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 14\n      }, this);\n    } else if (!allowedUserTypes.includes(userType)) {\n      // If user type is not allowed, redirect to home or an unauthorized page\n      return /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 14\n      }, this);\n    }\n  }\n  return /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 96,\n    columnNumber: 10\n  }, this);\n};\n_s(ProtectedRoute, \"+RMKxUlFYfVNDqxz+Q5fNBJmrfU=\", false, function () {\n  return [useLocation, useAuth];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Navigate", "Outlet", "useAuth", "useLocation", "jsxDEV", "_jsxDEV", "ProtectedRoute", "allowedUserTypes", "_s", "_location$state", "_loginUser$UserType", "location", "user", "userDetails", "signOut", "loggedOut", "AdminDetails", "setUserDetail", "state", "userDetail", "loginUserDetails", "setLoginUserDetail", "handleStorageChange", "event", "key", "window", "addEventListener", "removeEventListener", "userInfoString", "localStorage", "getItem", "userInfo", "JSON", "parse", "parsedUser", "error", "console", "loginUser", "userType", "UserType", "toLowerCase", "isAutoLoginCompleted", "sessionStorage", "urlParams", "URLSearchParams", "search", "hasAutoLoginParams", "has", "log", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "toLocaleLowerCase", "includes", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/routing/ProtectedRoute.tsx"], "sourcesContent": ["import React,{useEffect,useState} from \"react\";\r\nimport { Navigate, Outlet } from \"react-router-dom\";\r\nimport { useAuth } from \"../components/auth/AuthProvider\";\r\nimport { useLocation } from \"react-router-dom\";\r\nimport { SuperAdmin } from \"../models/Superadminuser\";\r\nimport { logout } from \"../services/AuthService\";\r\nimport { User } from \"../models/User\";\r\nimport { adminApiService } from \"../services/APIService\";\r\n\r\ninterface ProtectedRouteProps {\r\n  allowedUserTypes: string[];\r\n}\r\n\r\nconst ProtectedRoute: React.FC<ProtectedRouteProps> = ({ allowedUserTypes }) => {\r\n  const location = useLocation();\r\n  const { user,userDetails, signOut,loggedOut } = useAuth();\r\n  const [AdminDetails, setUserDetail] = useState<SuperAdmin | null>(location.state?.userDetail || null);\r\n  const [loginUserDetails, setLoginUserDetail] = useState<User|null> ();\r\n\r\n\r\n  useEffect(() => {\r\n    const handleStorageChange = (event: StorageEvent) => {\r\n      if (event.key === 'logout-event') {\r\n        signOut();\r\n      }\r\n    };\r\n    window.addEventListener('storage', handleStorageChange);\r\n    return () => {\r\n      window.removeEventListener('storage', handleStorageChange);\r\n    };\r\n\r\n  }, [signOut]);\r\n  \r\n  useEffect(() => {\r\n\t\tconst userInfoString = localStorage.getItem(\"userInfo\");\t\r\n\t\tif (userInfoString) { \r\n\t\t\ttry {\r\n\t\t\t\tconst userInfo = JSON.parse(userInfoString);\t\r\n\t\t\t\tif (userInfo['user']) {\r\n\t\t\t\t\tconst parsedUser = JSON.parse(userInfo['user']);\r\n\t\t\t\t\tif (parsedUser) {\r\n\t\t\t\t\t\tsetLoginUserDetail(parsedUser);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(\"Error parsing userInfo: \", error);\r\n\t\t\t}\r\n\t\t}\r\n\t\telse if (userDetails) {\r\n\t\t\tsetLoginUserDetail(userDetails);\t\t\t\t\r\n\t\t}\r\n\t}, []);  \r\n  \r\n  const loginUser = userDetails || loginUserDetails || AdminDetails || null;\r\n  const userType = typeof loginUser === 'object' && loginUser?.UserType?.toLowerCase() || \"\";\r\n\r\n  // Check if we're in auto-login flow to avoid premature redirects\r\n  const isAutoLoginCompleted = sessionStorage.getItem('autoLoginCompleted') === 'true';\r\n  const urlParams = new URLSearchParams(window.location.search);\r\n  const hasAutoLoginParams = urlParams.has('token') && urlParams.has('userId') && urlParams.has('orgId');\r\n\r\n  if ((!user || !loginUser) && userType !== \"superadmin\") {\r\n    // Don't redirect if we're in auto-login flow or auto-login just completed\r\n    if (isAutoLoginCompleted || hasAutoLoginParams) {\r\n      console.log('ProtectedRoute - Skipping redirect due to auto-login flow');\r\n      // If we have loginUser from localStorage but no user from auth context, allow access\r\n      if (loginUser) {\r\n        return <Outlet />;\r\n      }\r\n    }\r\n\r\n    if (loggedOut) {\r\n      signOut();\r\n      return <Navigate to=\"/login\" replace />;\r\n    }\r\n\r\n    // Only redirect to login if not in auto-login flow\r\n    if (!isAutoLoginCompleted && !hasAutoLoginParams) {\r\n      return <Navigate to=\"/login\" replace />;\r\n    }\r\n  }\r\n    \r\n  if (loginUser !== null && userType !== null && userType.toLocaleLowerCase() != \"superadmin\") {\r\n    \r\n    if (allowedUserTypes.includes(\"superadmin\") && userType.toLocaleLowerCase() != \"superadmin\" ) {\r\n      // If user type is not allowed, redirect to home or an unauthorized page\r\n      \r\n      return <Navigate to=\"/admin/adminlogin\" replace />;\r\n    }else if (!allowedUserTypes.includes(userType) ) {\r\n      // If user type is not allowed, redirect to home or an unauthorized page\r\n      return <Navigate to=\"/\" replace />;\r\n    }\r\n    }\r\n\r\n\r\n  return <Outlet />;\r\n};\r\n\r\nexport default ProtectedRoute;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAEC,SAAS,EAACC,QAAQ,QAAO,OAAO;AAC9C,SAASC,QAAQ,EAAEC,MAAM,QAAQ,kBAAkB;AACnD,SAASC,OAAO,QAAQ,iCAAiC;AACzD,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAU/C,MAAMC,cAA6C,GAAGA,CAAC;EAAEC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,mBAAA;EAC9E,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES,IAAI;IAACC,WAAW;IAAEC,OAAO;IAACC;EAAU,CAAC,GAAGb,OAAO,CAAC,CAAC;EACzD,MAAM,CAACc,YAAY,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAoB,EAAAU,eAAA,GAAAE,QAAQ,CAACO,KAAK,cAAAT,eAAA,uBAAdA,eAAA,CAAgBU,UAAU,KAAI,IAAI,CAAC;EACrG,MAAM,CAACC,gBAAgB,EAAEC,kBAAkB,CAAC,GAAGtB,QAAQ,CAAa,CAAC;EAGrED,SAAS,CAAC,MAAM;IACd,MAAMwB,mBAAmB,GAAIC,KAAmB,IAAK;MACnD,IAAIA,KAAK,CAACC,GAAG,KAAK,cAAc,EAAE;QAChCV,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IACDW,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,mBAAmB,CAAC;IACvD,OAAO,MAAM;MACXG,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAEL,mBAAmB,CAAC;IAC5D,CAAC;EAEH,CAAC,EAAE,CAACR,OAAO,CAAC,CAAC;EAEbhB,SAAS,CAAC,MAAM;IAChB,MAAM8B,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IACvD,IAAIF,cAAc,EAAE;MACnB,IAAI;QACH,MAAMG,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,cAAc,CAAC;QAC3C,IAAIG,QAAQ,CAAC,MAAM,CAAC,EAAE;UACrB,MAAMG,UAAU,GAAGF,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,MAAM,CAAC,CAAC;UAC/C,IAAIG,UAAU,EAAE;YACfb,kBAAkB,CAACa,UAAU,CAAC;UAC/B;QACD;MACD,CAAC,CAAC,OAAOC,KAAK,EAAE;QACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MACjD;IACD,CAAC,MACI,IAAItB,WAAW,EAAE;MACrBQ,kBAAkB,CAACR,WAAW,CAAC;IAChC;EACD,CAAC,EAAE,EAAE,CAAC;EAEL,MAAMwB,SAAS,GAAGxB,WAAW,IAAIO,gBAAgB,IAAIJ,YAAY,IAAI,IAAI;EACzE,MAAMsB,QAAQ,GAAG,OAAOD,SAAS,KAAK,QAAQ,KAAIA,SAAS,aAATA,SAAS,wBAAA3B,mBAAA,GAAT2B,SAAS,CAAEE,QAAQ,cAAA7B,mBAAA,uBAAnBA,mBAAA,CAAqB8B,WAAW,CAAC,CAAC,KAAI,EAAE;;EAE1F;EACA,MAAMC,oBAAoB,GAAGC,cAAc,CAACZ,OAAO,CAAC,oBAAoB,CAAC,KAAK,MAAM;EACpF,MAAMa,SAAS,GAAG,IAAIC,eAAe,CAACnB,MAAM,CAACd,QAAQ,CAACkC,MAAM,CAAC;EAC7D,MAAMC,kBAAkB,GAAGH,SAAS,CAACI,GAAG,CAAC,OAAO,CAAC,IAAIJ,SAAS,CAACI,GAAG,CAAC,QAAQ,CAAC,IAAIJ,SAAS,CAACI,GAAG,CAAC,OAAO,CAAC;EAEtG,IAAI,CAAC,CAACnC,IAAI,IAAI,CAACyB,SAAS,KAAKC,QAAQ,KAAK,YAAY,EAAE;IACtD;IACA,IAAIG,oBAAoB,IAAIK,kBAAkB,EAAE;MAC9CV,OAAO,CAACY,GAAG,CAAC,2DAA2D,CAAC;MACxE;MACA,IAAIX,SAAS,EAAE;QACb,oBAAOhC,OAAA,CAACJ,MAAM;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACnB;IACF;IAEA,IAAIrC,SAAS,EAAE;MACbD,OAAO,CAAC,CAAC;MACT,oBAAOT,OAAA,CAACL,QAAQ;QAACqD,EAAE,EAAC,QAAQ;QAACC,OAAO;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACzC;;IAEA;IACA,IAAI,CAACX,oBAAoB,IAAI,CAACK,kBAAkB,EAAE;MAChD,oBAAOzC,OAAA,CAACL,QAAQ;QAACqD,EAAE,EAAC,QAAQ;QAACC,OAAO;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACzC;EACF;EAEA,IAAIf,SAAS,KAAK,IAAI,IAAIC,QAAQ,KAAK,IAAI,IAAIA,QAAQ,CAACiB,iBAAiB,CAAC,CAAC,IAAI,YAAY,EAAE;IAE3F,IAAIhD,gBAAgB,CAACiD,QAAQ,CAAC,YAAY,CAAC,IAAIlB,QAAQ,CAACiB,iBAAiB,CAAC,CAAC,IAAI,YAAY,EAAG;MAC5F;;MAEA,oBAAOlD,OAAA,CAACL,QAAQ;QAACqD,EAAE,EAAC,mBAAmB;QAACC,OAAO;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACpD,CAAC,MAAK,IAAI,CAAC7C,gBAAgB,CAACiD,QAAQ,CAAClB,QAAQ,CAAC,EAAG;MAC/C;MACA,oBAAOjC,OAAA,CAACL,QAAQ;QAACqD,EAAE,EAAC,GAAG;QAACC,OAAO;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACpC;EACA;EAGF,oBAAO/C,OAAA,CAACJ,MAAM;IAAAgD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACnB,CAAC;AAAC5C,EAAA,CAnFIF,cAA6C;EAAA,QAChCH,WAAW,EACoBD,OAAO;AAAA;AAAAuD,EAAA,GAFnDnD,cAA6C;AAqFnD,eAAeA,cAAc;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}