{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptWeb\\\\src\\\\components\\\\account\\\\AccountList.tsx\",\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport CreateAccount from \"./AccountCreate\";\nimport { GridToolbarContainer, GridToolbarColumnsButton, GridToolbarFilterButton, GridToolbarDensitySelector, DataGrid } from \"@mui/x-data-grid\";\nimport { Button, Menu, MenuItem, IconButton, Snackbar, Alert, Box, TextField, Container, Tooltip } from \"@mui/material\";\nimport BorderColorOutlinedIcon from \"@mui/icons-material/BorderColorOutlined\";\nimport SaveAltIcon from \"@mui/icons-material/SaveAlt\";\nimport loader from \"../../assets/loader.gif\";\n//import { AccountData } from \"./AccountData\";\nimport EditAccount from \"./AccountEdit\";\nimport { GetAllAccounts, fetchDeleteAccountDetails, GetAccountsList } from \"../../services/AccountService\";\nimport { isSidebarOpen, subscribe } from \"../adminMenu/sidemenustate\";\nimport AccountCustomColumnMenu from \"./AccountsColumnMenu\";\nimport { useSnackbar } from \"../../SnackbarContext\";\nimport styles from \"./AccountStle.module.scss\";\n// import { OrganizationId } from \"../common/Home\";\nimport { OrganizationId } from \"../common/Home\";\nimport InputAdornment from \"@mui/material/InputAdornment\";\nimport SearchIcon from \"@mui/icons-material/Search\";\nimport ClearIcon from \"@mui/icons-material/Clear\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// type InputFields = {\n// \tAccountId: string,\n// \tAccountName: string,\n// \tAccountType: string,\n// \tCreatedBy: string,\n// \tCreatedDate: string,\n// \tOrganizationid: string,\n// \tUpdatedBy: string,\n// \tUpdatedDate: string,\n// \tActive: Boolean,\n// };\nlet email;\nconst AccountList = () => {\n  _s2();\n  var _s = $RefreshSig$();\n  const {\n    t: translate\n  } = useTranslation();\n  const [showPopup, setShowPopup] = useState(false);\n  const [models, setModels] = useState([]);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [accountidedit, setAccountIdEdit] = useState(\"\");\n  const [showeditPopup, setShowEditPopup] = useState(false);\n  const [emailiddelete, setemailiddelete] = useState(\"\");\n  const [showDeletePopup, setShowDeletePopup] = useState(false);\n  const [sidebarOpen, setSidebarOpen] = useState(isSidebarOpen());\n  const [snackbarOpen, setSnackbarOpen] = useState(false);\n  const [snackbarMessage, setSnackbarMessage] = useState(\"\");\n  const [snackbarSeverity, setSnackbarSeverity] = useState(\"success\");\n  const storedOrganizationString = localStorage.getItem(\"organization\");\n  const storedOrganization = storedOrganizationString ? JSON.parse(storedOrganizationString) : null;\n  const Organizationid = OrganizationId;\n  const [skip, setskip] = useState(0);\n  const [top, settop] = useState(0);\n  const [totalcount, setTotalcount] = useState(0);\n  const [searchText, setSearchText] = useState(\"\");\n  const {\n    openSnackbar\n  } = useSnackbar();\n  const [orderByFields, setOrderByFields] = useState(\"\");\n  const [inputs, setInputs] = useState({\n    AccountName: \"\",\n    SearchInput: \"\"\n  });\n  const [sortModel, setSortModel] = useState([]);\n  const [filters, setFilters] = useState([]);\n  const handleSortModelChange = model => {\n    setSortModel(model);\n    const orderByField = model.map(item => item.sort === \"desc\" ? `${item.field} desc` : item.field).join(\", \");\n    setOrderByFields(orderByField);\n    GetAccountsList(setModels, setLoading, Organizationid, skip, top, setTotalcount, orderByField, filters);\n  };\n  const [paginationModel, setPaginationModel] = useState({\n    page: 0,\n    pageSize: 15\n  });\n  const [errors, setErrors] = useState({\n    AccountName: \"\"\n  });\n  const [AccountDeleteDetails, setAccountDeleteDetails] = useState({\n    AccountId: \"\",\n    AccountName: \"\",\n    AccountType: \"\",\n    CreatedBy: \"\",\n    CreatedDate: \"\",\n    Organizationid: \"\",\n    UpdatedBy: \"\",\n    UpdatedDate: \"\",\n    Active: Boolean(true)\n  });\n  const FetchAccounts = () => {\n    setLoading(true);\n    const skipcount = paginationModel.pageSize || 15;\n    const limitcount = paginationModel.page * skipcount;\n    const skips = limitcount;\n    const top = paginationModel.pageSize;\n    setskip(skips);\n    settop(top);\n    GetAccountsList(setModels, setLoading, Organizationid, skips, top, setTotalcount, orderByFields, filters);\n  };\n  useEffect(() => {\n    setLoading(true);\n    //GetAllAccounts(setModels, setLoading);\n    FetchAccounts();\n  }, [paginationModel]);\n  useEffect(() => {\n    // Always set loading to false when models data is received, regardless of whether it's empty or not\n    setLoading(false);\n  }, [models]);\n  useEffect(() => {\n    const unsubscribe = subscribe(setSidebarOpen);\n    return () => unsubscribe();\n  }, []);\n  const handleSnackbarClose = () => {\n    setSnackbarOpen(false);\n  };\n  const handleDeleteClick = (emailId, accountid) => {\n    setShowDeletePopup(true);\n    setemailiddelete(emailId);\n    setAccountIdEdit(accountid);\n  };\n  const handleeditclick = accountid => {\n    setShowEditPopup(true);\n    setAccountIdEdit(accountid);\n  };\n  const [email, setEmail] = useState(\"\");\n  const [accountIdNew, setAccountIdNew] = useState(\"\");\n  const columns = [{\n    field: \"AccountName\",\n    headerName: translate(\"Account Name\"),\n    width: 350,\n    flex: 1,\n    disableColumnMenu: true,\n    resizable: false\n  }, {\n    field: \"CreatedDate\",\n    headerName: translate(\"Created Date\"),\n    width: 350,\n    flex: 1,\n    disableColumnMenu: true,\n    renderCell: params => {\n      const createdDate = params.value ? new Date(params.value) : null;\n      return createdDate ? createdDate.toLocaleDateString('en-GB') : \"\";\n    },\n    resizable: false\n  }, {\n    field: \"actions\",\n    headerName: translate(\"Actions\"),\n    sortable: false,\n    width: 200,\n    flex: 1,\n    disableColumnMenu: true,\n    resizable: false,\n    renderCell: params => {\n      var emailId = params.row.CreatedBy || false;\n      setEmail(emailId);\n      const accountid = params.row.AccountId || false;\n      setAccountIdNew(accountid);\n      return /*#__PURE__*/_jsxDEV(Box, {\n        className: \"qadpt-grd-act\",\n        children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n          arrow: true,\n          title: translate(\"Edit Account\"),\n          children: /*#__PURE__*/_jsxDEV(BorderColorOutlinedIcon, {\n            style: {\n              color: \"#7A7B7D\"\n            },\n            className: styles.Addiconingrid,\n            onClick: () => handleeditclick(accountid)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          arrow: true,\n          title: translate(\"Delete Account\"),\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => handleDeleteClick(emailId, accountid),\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fal fa-trash-alt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 10\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 6\n      }, this);\n    }\n  }];\n  const onPageChange = newPage => {};\n  const onPageSizeChange = newPageSize => {};\n\n  // useEffect(() => {\n  // \tif (showDeletePopup) {\n  // \t\tfetchAccountDetails(accountidedit);\n  // \t}\n  // }, [showDeletePopup]);\n  // const fetchAccountDetails = async (id: any) => {\n  // \ttry {\n  // \t\tconst response = await fetch(`${adminUrl}/Account/GetAccountById?accountId=${id}`);\n  // \t\tif (!response.ok) {\n  // \t\t\tthrow new Error(\"Network response was not ok\");\n  // \t\t}\n  // \t\tconst data = await response.json();\n  // \t\tsetAccountDeleteDetails({\n  // \t\t\tAccountId: data.AccountId,\n  // \t\t\tAccountName: data.AccountName,\n  // \t\t\tAccountType: data.AccountType,\n  // \t\t\tCreatedBy: data.CreatedBy,\n  // \t\t\tCreatedDate: data.CreatedDate,\n  // \t\t\tOrganizationid: data.Organizationid,\n  // \t\t\tUpdatedBy: data.UpdatedBy,\n  // \t\t\tUpdatedDate: data.UpdatedDate,\n  // \t\t\tActive: false,\n  // \t\t});\n  // \t} catch (error) {\n  // \t\tconsole.error(\"Failed to fetch user details:\", error);\n  // \t}\n  // };\n\n  const handleDeleteAccount = () => {\n    fetchDeleteAccountDetails(accountidedit, setLoading, setModels, setShowDeletePopup, Organizationid, skip, top, setTotalcount, setSnackbarMessage, setSnackbarSeverity, setSnackbarOpen, openSnackbar, orderByFields, filters);\n    //DeleteAccountDetails(setLoading, setModels, setShowDeletePopup, AccountDeleteDetails);\n  };\n  const CustomToolbar = () => {\n    _s();\n    const [anchorEl, setAnchorEl] = useState(null);\n    const handleExportMenuClick = event => {\n      setAnchorEl(event.currentTarget);\n    };\n    const handleExportMenuClose = () => {\n      setAnchorEl(null);\n    };\n    const handleDownloadExcelClick = () => {\n      handleExportMenuClose();\n    };\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: \"flex\",\n        alignItems: \"center\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(GridToolbarContainer, {\n        children: [/*#__PURE__*/_jsxDEV(GridToolbarColumnsButton, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(GridToolbarFilterButton, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(GridToolbarDensitySelector, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        \"aria-controls\": \"export-menu\",\n        \"aria-haspopup\": \"true\",\n        onClick: handleExportMenuClick,\n        style: {\n          marginLeft: \"10px\"\n        },\n        startIcon: /*#__PURE__*/_jsxDEV(SaveAltIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 17\n        }, this),\n        children: translate(\"Export\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(Menu, {\n        id: \"export-menu\",\n        anchorEl: anchorEl,\n        keepMounted: true,\n        open: Boolean(anchorEl),\n        onClose: handleExportMenuClose,\n        children: /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            //handleDownloadExcel();\n            handleExportMenuClose();\n          },\n          children: translate(\"Download Excel\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 5\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 4\n    }, this);\n  };\n  _s(CustomToolbar, \"+aMDa7FPcESUyQDF1vq0RSMn4/k=\");\n  const openPopup = () => {\n    setShowPopup(true);\n  };\n  const handleSearch = value => {\n    setSearchText(value.join(\" \"));\n  };\n  const globalhandleSearch = searchValue => {\n    const newFilter = {\n      FieldName: \"AccountName\",\n      ElementType: \"string\",\n      Condition: \"contains\",\n      Value: searchValue,\n      IsCustomField: false\n    };\n    let skips = 0;\n    let top = 0;\n    const skipCount = paginationModel.pageSize || 15;\n    const limitCount = paginationModel.page * skipCount;\n    top = paginationModel.pageSize;\n    skips = limitCount;\n    setskip(skips);\n    settop(top);\n    try {\n      GetAccountsList(setModels, setLoading, Organizationid, skip, top, setTotalcount, orderByFields, [newFilter]);\n    } catch (error) {\n      console.error(\"Failed to fetch user roles:\", error);\n    }\n  };\n  const clearSearch = () => {\n    setInputs({\n      ...inputs,\n      SearchInput: \"\"\n    });\n    GetAccountsList(setModels, setLoading, Organizationid, skip, top, setTotalcount, orderByFields, filters);\n  };\n  const columnNames = columns.map(col => col.headerName);\n  const actionColumns = [\"Actions\", \"Full Name\"];\n  const filteredColumnNames = columnNames.filter(name => name !== undefined && !actionColumns.includes(name)).map(name => name === \"Role Name\" ? name.replace(/\\s+/g, \"\") : name);\n  const handleApplyFilters = async filters => {\n    // if (selectedAccount) {\n    // \tsetFilters(filters);\n    // \tlet skips: number = 0;\n    // \tlet top: number = 0;\n    // \tconst skipcount = paginationModel.pageSize || 15;\n    // \tconst limitcount = paginationModel.page * skipcount;\n    // \tskips = paginationModel.pageSize;\n    // \ttop = limitcount;\n    // \tconst formattedFilters = filters.map((filter: any) => ({\n    // \t\tFieldName: filter.column,\n    // \t\tElementType: \"string\",\n    // \t\tCondition: filter.operator,\n    // \t\tValue: filter.value,\n    // \t\tIsCustomField: false,\n    // \t}));\n    // \tsetskip(skips);\n    // \tsettop(top);\n    // \tconst orderByFields = \"\";\n    // \ttry {\n    // \t\t(\"\");\n    // \t} catch (error) {\n    // \t\tconsole.error(\"Failed to fetch user roles:\", error);\n    // \t}\n    // } else {\n    // \tsetFilters(filters);\n    // \tlet skips: number = 0;\n    // \tlet top: number = 0;\n    // \tconst skipcount = paginationModel.pageSize || 15;\n    // \tconst limitcount = paginationModel.pageSize;\n    // \tskips = paginationModel.page * skipcount;\n    // \ttop = limitcount;\n    // \tconst formattedFilters = filters.map((filter: any) => ({\n    // \t\tFieldName: filter.column,\n    // \t\tElementType: \"string\",\n    // \t\tCondition: filter.operator,\n    // \t\tValue: filter.value,\n    // \t\tIsCustomField: false,\n    // \t}));\n    // \tsetskip(skips);\n    // \tsettop(top);\n    // \tconst orderByFields = \"\";\n    // \ttry {\n    // \t\t(\"\");\n    // \t} catch (error) {\n    // \t\tconsole.error(\"Failed to fetch user roles:\", error);\n    // \t}\n    // }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-midpart setng-box\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-content-block\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-head\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-title-sec\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-title\",\n                children: translate('Account')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-description\",\n                children: translate('View & Manage your accounts here')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 9\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-right-part\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: openPopup,\n                className: \"qadpt-memberButton\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fal fa-add-plus\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 10\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: translate('Create Account')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 10\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: showPopup ? /*#__PURE__*/_jsxDEV(CreateAccount, {\n              setModels: setModels,\n              setLoading: setLoading,\n              showPopup: showPopup,\n              setShowPopup: setShowPopup,\n              orderByField: orderByFields\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 9\n            }, this) : \"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 7\n          }, this), showeditPopup ? /*#__PURE__*/_jsxDEV(EditAccount, {\n            showEditPopup: showeditPopup,\n            setShowEditPopup: setShowEditPopup,\n            accountidedit: accountidedit,\n            GetAllAccounts: GetAllAccounts,\n            setModels: setModels,\n            setLoading: setLoading,\n            Organizationid: Organizationid,\n            skip: skip,\n            top: top,\n            setTotalcount: setTotalcount,\n            orderByField: orderByFields,\n            filters: filters,\n            setFilters: setFilters\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 8\n          }, this) : \"\", /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-content-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-src-flt\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: /*#__PURE__*/_jsxDEV(TextField, {\n                    className: `qadpt-teamsearch ${errors.SearchInput ? \"qadpt-teamsearch-error\" : \"\"}`\n                    // style={{ right: \"-650px\" }}\n                    ,\n                    name: \"SearchInput\",\n                    value: inputs.SearchInput,\n                    onChange: e => {\n                      const newValue = e.target.value;\n                      setInputs({\n                        ...inputs,\n                        SearchInput: newValue\n                      });\n                      if (newValue === \"\") {\n                        clearSearch();\n                        GetAccountsList(setModels, setLoading, Organizationid, skip, top, setTotalcount, orderByFields, filters);\n                      }\n                    },\n                    onKeyDown: e => {\n                      if (e.key === 'Enter') {\n                        globalhandleSearch(inputs.SearchInput);\n                      }\n                    },\n                    placeholder: translate('Search accounts'),\n                    variant: \"outlined\",\n                    error: !!errors.SearchInput\n                    // sx={{\n                    // \t\"& .MuiFormHelperText-root\": {\n                    // \t\tcolor: errors.SearchInput ? \"red\" : \"inherit\",\n                    // \t},\n                    // \t\"& .MuiOutlinedInput-root\": {\n                    // \t\tpadding: \"0px\",\n                    // \t\tborderRadius: \"20px\",\n                    // \t},\n                    // \t\"& .MuiInputBase-input\": {\n                    // \t\theight: \"1em\",\n                    // \t\tpaddingLeft: \"0px\",\n                    // \t},\n                    // \twidth: \"200px\",\n                    // \tmarginBottom: \"8px\",\n                    // }}\n                    ,\n                    InputProps: {\n                      startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                        position: \"start\",\n                        children: /*#__PURE__*/_jsxDEV(IconButton, {\n                          \"aria-label\": \"search\",\n                          onClick: () => globalhandleSearch(inputs.SearchInput),\n                          onMouseDown: event => event.preventDefault(),\n                          children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 532,\n                            columnNumber: 16\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 527,\n                          columnNumber: 15\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 526,\n                        columnNumber: 14\n                      }, this),\n                      endAdornment: inputs.SearchInput && /*#__PURE__*/_jsxDEV(InputAdornment, {\n                        position: \"end\",\n                        children: /*#__PURE__*/_jsxDEV(IconButton, {\n                          \"aria-label\": \"clear\",\n                          onClick: () => {\n                            setInputs({\n                              ...inputs,\n                              SearchInput: \"\"\n                            });\n                            clearSearch();\n                            GetAccountsList(setModels, setLoading, Organizationid, skip, top, setTotalcount, orderByFields, filters);\n                          },\n                          children: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 555,\n                            columnNumber: 16\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 538,\n                          columnNumber: 15\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 537,\n                        columnNumber: 14\n                      }, this)\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 479,\n                    columnNumber: 10\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 10\n                }, this), \" \"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(DataGrid, {\n                className: \"qadpt-account-grd\",\n                rows: models.length > 0 ? models : [] // Display models if available\n                ,\n                columns: columns,\n                pagination: true,\n                paginationMode: \"server\",\n                slots: {\n                  columnMenu: menuProps => menuProps.colDef.field === \"AccountName\" ? /*#__PURE__*/_jsxDEV(AccountCustomColumnMenu, {\n                    column: menuProps.colDef.field,\n                    setModels: setModels,\n                    setLoading: setLoading,\n                    skip: skip,\n                    top: top,\n                    OrganizationId: Organizationid,\n                    setTotalcount: setTotalcount,\n                    orderByFields: orderByFields,\n                    filters: filters,\n                    setFilters: setFilters,\n                    ...menuProps,\n                    options: models.map(model => model.AccountName || \"\"),\n                    onSearch: handleSearch\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 572,\n                    columnNumber: 13\n                  }, this) : null\n                },\n                getRowId: row => row.AccountId,\n                paginationModel: paginationModel,\n                onPaginationModelChange: model => {\n                  setPaginationModel(model);\n                  // FetchAccounts();\n                },\n                rowCount: totalcount,\n                pageSizeOptions: [15, 25, 50, 100],\n                localeText: {\n                  MuiTablePagination: {\n                    labelRowsPerPage: translate('Records Per Page')\n                  }\n                },\n                disableRowSelectionOnClick: true\n                //loading={loading} // To show the DataGrid's built-in loading overlay\n                ,\n                sortModel: sortModel,\n                onSortModelChange: handleSortModelChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 9\n              }, this), loading && models.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"Loaderstyles\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: loader,\n                  alt: \"Spinner\",\n                  className: \"LoaderSpinnerStyles\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 609,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 10\n              }, this), !loading && models.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'center',\n                  alignItems: 'center',\n                  height: '200px',\n                  color: '#666',\n                  fontSize: '16px'\n                },\n                children: \"No rows\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 7\n          }, this), showDeletePopup ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-modal-overlay\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-usrconfirm-popup qadpt-danger\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"qadpt-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fal fa-trash-alt\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 645,\n                    columnNumber: 13\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 635,\n                  columnNumber: 9\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 634,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-popup-title\",\n                children: translate(\"Delete Account\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-warning\",\n                children: translate(\"The Account cannot be restored once it is deleted.\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 649,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-buttons\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setShowDeletePopup(false),\n                  className: \"qadpt-cancel-button\",\n                  children: translate(\"Cancel\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"qadpt-conform-button\",\n                  onClick: handleDeleteAccount,\n                  children: translate(\"Delete\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 659,\n                  columnNumber: 12\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 8\n          }, this) : \"\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n          open: snackbarOpen,\n          autoHideDuration: 4000,\n          onClose: handleSnackbarClose,\n          anchorOrigin: {\n            vertical: \"top\",\n            horizontal: \"right\"\n          },\n          children: /*#__PURE__*/_jsxDEV(Alert, {\n            onClose: handleSnackbarClose,\n            severity: snackbarSeverity,\n            children: translate(snackbarMessage)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 672,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 422,\n      columnNumber: 4\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 421,\n    columnNumber: 3\n  }, this);\n};\n_s2(AccountList, \"aQHNifyc6r4ZrzoUclRHVq6bcPo=\", false, function () {\n  return [useTranslation, useSnackbar];\n});\n_c = AccountList;\nexport default AccountList;\nvar _c;\n$RefreshReg$(_c, \"AccountList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "CreateAccount", "GridToolbarContainer", "GridToolbarColumnsButton", "GridToolbarFilterButton", "GridToolbarDensitySelector", "DataGrid", "<PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "IconButton", "Snackbar", "<PERSON><PERSON>", "Box", "TextField", "Container", "<PERSON><PERSON><PERSON>", "BorderColorOutlinedIcon", "SaveAltIcon", "loader", "EditAccount", "GetAllAccounts", "fetchDeleteAccountDetails", "GetAccountsList", "isSidebarOpen", "subscribe", "AccountCustomColumnMenu", "useSnackbar", "styles", "OrganizationId", "InputAdornment", "SearchIcon", "ClearIcon", "useTranslation", "jsxDEV", "_jsxDEV", "email", "AccountList", "_s2", "_s", "$RefreshSig$", "t", "translate", "showPopup", "setShowPopup", "models", "setModels", "anchorEl", "setAnchorEl", "loading", "setLoading", "accountidedit", "setAccountIdEdit", "showeditPopup", "setShowEditPopup", "emailiddelete", "setemailiddelete", "showDeletePopup", "setShowDeletePopup", "sidebarOpen", "setSidebarOpen", "snackbarOpen", "setSnackbarOpen", "snackbarMessage", "setSnackbarMessage", "snackbarSeverity", "setSnackbarSeverity", "storedOrganizationString", "localStorage", "getItem", "storedOrganization", "JSON", "parse", "Organizationid", "skip", "setskip", "top", "settop", "totalcount", "setTotalcount", "searchText", "setSearchText", "openSnackbar", "order<PERSON><PERSON><PERSON><PERSON>s", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs", "setInputs", "Account<PERSON><PERSON>", "SearchInput", "sortModel", "setSortModel", "filters", "setFilters", "handleSortModelChange", "model", "orderByField", "map", "item", "sort", "field", "join", "paginationModel", "setPaginationModel", "page", "pageSize", "errors", "setErrors", "AccountDeleteDetails", "setAccountDeleteDetails", "AccountId", "AccountType", "CreatedBy", "CreatedDate", "UpdatedBy", "UpdatedDate", "Active", "Boolean", "FetchAccounts", "skipcount", "limitcount", "skips", "unsubscribe", "handleSnackbarClose", "handleDeleteClick", "emailId", "accountid", "handleeditclick", "setEmail", "accountIdNew", "setAccountIdNew", "columns", "headerName", "width", "flex", "disableColumnMenu", "resizable", "renderCell", "params", "createdDate", "value", "Date", "toLocaleDateString", "sortable", "row", "className", "children", "arrow", "title", "style", "color", "Addiconingrid", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onPageChange", "newPage", "onPageSizeChange", "newPageSize", "handleDeleteAccount", "CustomToolbar", "handleExportMenuClick", "event", "currentTarget", "handleExportMenuClose", "handleDownloadExcelClick", "display", "alignItems", "marginLeft", "startIcon", "id", "keepMounted", "open", "onClose", "openPopup", "handleSearch", "globalhandleSearch", "searchValue", "newFilter", "FieldName", "ElementType", "Condition", "Value", "IsCustomField", "skip<PERSON><PERSON>nt", "limitCount", "error", "console", "clearSearch", "columnNames", "col", "actionColumns", "filteredColumnNames", "filter", "name", "undefined", "includes", "replace", "handleApplyFilters", "showEditPopup", "onChange", "e", "newValue", "target", "onKeyDown", "key", "placeholder", "variant", "InputProps", "startAdornment", "position", "onMouseDown", "preventDefault", "endAdornment", "rows", "length", "pagination", "paginationMode", "slots", "columnMenu", "menuProps", "colDef", "column", "options", "onSearch", "getRowId", "onPaginationModelChange", "rowCount", "pageSizeOptions", "localeText", "MuiTablePagination", "labelRowsPerPage", "disableRowSelectionOnClick", "onSortModelChange", "src", "alt", "justifyContent", "height", "fontSize", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/components/account/AccountList.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport CreateAccount from \"./AccountCreate\";\r\nimport {\r\n\tGridColDef,\r\n\tGridRenderCellParams,\r\n\tGridToolbarContainer,\r\n\tGridToolbarColumnsButton,\r\n\tGridToolbarFilterButton,\r\n\tGridToolbarDensitySelector,\r\n\tDataGrid,\r\n\tGridPaginationModel,\r\n\tGridColumnMenuProps,\r\n} from \"@mui/x-data-grid\";\r\nimport { Button, Menu, MenuItem, FormControlLabel, IconButton, Snackbar, Alert, Box, TextField,\tContainer, Tooltip, } from \"@mui/material\";\r\nimport BorderColorOutlinedIcon from \"@mui/icons-material/BorderColorOutlined\";\r\nimport DeleteIcon from \"@mui/icons-material/Delete\";\r\nimport MailIcon from \"@mui/icons-material/Mail\";\r\nimport SaveAltIcon from \"@mui/icons-material/SaveAlt\";\r\nimport loader from \"../../assets/loader.gif\";\r\nimport CustomGrid from \"../common/Grid\";\r\nimport MarkEmailReadIcon from \"@mui/icons-material/MarkEmailRead\";\r\n//import { AccountData } from \"./AccountData\";\r\nimport EditAccount from \"./AccountEdit\";\r\nimport { GetAllAccounts, fetchDeleteAccountDetails, GetAccountsList } from \"../../services/AccountService\";\r\nimport { isSidebarOpen, subscribe } from \"../adminMenu/sidemenustate\";\r\nimport { organizationsList } from \"../organization/orgData\";\r\nimport CustomColumnMenu from \"../CustomColumnMenu\";\r\nimport AccountCustomColumnMenu from \"./AccountsColumnMenu\";\r\nimport { useSnackbar } from \"../../SnackbarContext\";\r\nimport styles from \"./AccountStle.module.scss\";\r\n// import { OrganizationId } from \"../common/Home\";\r\nimport { OrganizationId } from \"../common/Home\";\r\nimport FilterPopup from \"../settings/Filterpopup\";\r\nimport InputAdornment from \"@mui/material/InputAdornment\";\r\nimport SearchIcon from \"@mui/icons-material/Search\";\r\nimport ClearIcon from \"@mui/icons-material/Clear\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport AddBoxIcon from '@mui/icons-material/AddBox';\r\nimport  Delete  from \"../../assets/icons/delete.svg\";\r\n\r\ntype InputFields = {\r\n\tAccountName: string;\r\n\tSearchInput: string;\r\n};\r\ntype ErrorFields = Partial<InputFields>;\r\ninterface CustomDataGridProps extends React.ComponentProps<typeof DataGrid> {\r\n\tcomponents?: {\r\n\t\tcolumnMenu?: React.ComponentType<GridColumnMenuProps>;\r\n\t};\r\n}\r\n\r\ninterface Model {\r\n\tAccountName: string | null;\r\n\tAdminType: string;\r\n\tAccountId: string;\r\n}\r\n// type InputFields = {\r\n// \tAccountId: string,\r\n// \tAccountName: string,\r\n// \tAccountType: string,\r\n// \tCreatedBy: string,\r\n// \tCreatedDate: string,\r\n// \tOrganizationid: string,\r\n// \tUpdatedBy: string,\r\n// \tUpdatedDate: string,\r\n// \tActive: Boolean,\r\n// };\r\nlet email: any;\r\n\r\nconst AccountList = () => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst [showPopup, setShowPopup] = useState(false);\r\n\tconst [models, setModels] = useState<Model[]>([]);\r\n\tconst [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);\r\n\tconst [loading, setLoading] = useState(false);\r\n\tconst [accountidedit, setAccountIdEdit] = useState(\"\");\r\n\tconst [showeditPopup, setShowEditPopup] = useState(false);\r\n\tconst [emailiddelete, setemailiddelete] = useState(\"\");\r\n\tconst [showDeletePopup, setShowDeletePopup] = useState(false);\r\n\tconst [sidebarOpen, setSidebarOpen] = useState(isSidebarOpen());\r\n\tconst [snackbarOpen, setSnackbarOpen] = useState(false);\r\n\tconst [snackbarMessage, setSnackbarMessage] = useState(\"\");\r\n\tconst [snackbarSeverity, setSnackbarSeverity] = useState<\"success\" | \"error\">(\"success\");\r\n\tconst storedOrganizationString = localStorage.getItem(\"organization\");\r\n\tconst storedOrganization = storedOrganizationString ? JSON.parse(storedOrganizationString) : null;\r\n\tconst Organizationid = OrganizationId;\r\n\tconst [skip, setskip] = useState(0);\r\n\tconst [top, settop] = useState(0);\r\n\tconst [totalcount, setTotalcount] = useState(0);\r\n\tconst [searchText, setSearchText] = useState(\"\");\r\n\tconst { openSnackbar } = useSnackbar();\r\n\tconst [orderByFields, setOrderByFields] = useState(\"\");\r\n\tconst [inputs, setInputs] = useState<InputFields>({\r\n\t\tAccountName: \"\",\r\n\t\tSearchInput: \"\",\r\n\t});\r\n\tconst [sortModel, setSortModel] = useState([]);\r\n\tconst [filters, setFilters] = useState([]);\r\n\tconst handleSortModelChange = (model: any) => {\r\n\t\tsetSortModel(model);\r\n\t\tconst orderByField = model\r\n\t\t\t.map((item: any) => (item.sort === \"desc\" ? `${item.field} desc` : item.field))\r\n\t\t\t.join(\", \");\r\n\r\n\t\tsetOrderByFields(orderByField);\r\n\t\tGetAccountsList(setModels, setLoading, Organizationid, skip, top, setTotalcount, orderByField, filters);\r\n\t};\r\n\tconst [paginationModel, setPaginationModel] = useState<GridPaginationModel>({\r\n\t\tpage: 0,\r\n\t\tpageSize: 15,\r\n\t});\r\n\tconst [errors, setErrors] = useState<ErrorFields>({\r\n\t\tAccountName: \"\",\r\n\t});\r\n\tconst [AccountDeleteDetails, setAccountDeleteDetails] = useState({\r\n\t\tAccountId: \"\",\r\n\t\tAccountName: \"\",\r\n\t\tAccountType: \"\",\r\n\t\tCreatedBy: \"\",\r\n\t\tCreatedDate: \"\",\r\n\t\tOrganizationid: \"\",\r\n\t\tUpdatedBy: \"\",\r\n\t\tUpdatedDate: \"\",\r\n\t\tActive: Boolean(true),\r\n\t});\r\n\r\n\tconst FetchAccounts = () => {\r\n\t\tsetLoading(true);\r\n\t\tconst skipcount = paginationModel.pageSize || 15;\r\n\t\tconst limitcount = paginationModel.page * skipcount;\r\n\t\tconst skips = limitcount;\r\n\t\tconst top = paginationModel.pageSize;\r\n\t\tsetskip(skips);\r\n\t\tsettop(top);\r\n\t\tGetAccountsList(setModels, setLoading, Organizationid, skips, top, setTotalcount, orderByFields, filters);\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tsetLoading(true);\r\n\t\t//GetAllAccounts(setModels, setLoading);\r\n\t\tFetchAccounts();\r\n\t}, [paginationModel]);\r\n\r\n\tuseEffect(() => {\r\n\t\t// Always set loading to false when models data is received, regardless of whether it's empty or not\r\n\t\tsetLoading(false);\r\n\t}, [models]);\r\n\r\n\tuseEffect(() => {\r\n\t\tconst unsubscribe = subscribe(setSidebarOpen);\r\n\t\treturn () => unsubscribe();\r\n\t}, []);\r\n\r\n\tconst handleSnackbarClose = () => {\r\n\t\tsetSnackbarOpen(false);\r\n\t};\r\n\tconst handleDeleteClick = (emailId: string, accountid: string) => {\r\n\t\tsetShowDeletePopup(true);\r\n\t\tsetemailiddelete(emailId);\r\n\t\tsetAccountIdEdit(accountid);\r\n\t};\r\n\tconst handleeditclick = (accountid: string) => {\r\n\t\tsetShowEditPopup(true);\r\n\t\tsetAccountIdEdit(accountid);\r\n\t};\r\n\tconst [email, setEmail] = useState<string>(\"\");\r\n\tconst [accountIdNew, setAccountIdNew] = useState<string>(\"\");\r\n\tconst columns: GridColDef[] = [\r\n\r\n\t\t{\r\n\t\t\tfield: \"AccountName\",\r\n\t\t\theaderName: translate(\"Account Name\"),\r\n\t\t\twidth: 350,\r\n\t\t\tflex: 1,\r\n\t\t\tdisableColumnMenu: true,\r\n\t\t\tresizable: false,\r\n\t\t},\r\n\t\t{\r\n\t\t\tfield: \"CreatedDate\",\r\n\t\t\theaderName: translate(\"Created Date\"),\r\n\t\t\twidth: 350,\r\n\t\t\tflex: 1,\r\n\t\t\tdisableColumnMenu: true,\r\n\t\t\trenderCell: (params) => {\r\n\t\t\t\tconst createdDate = params.value ? new Date(params.value) : null;\r\n\t\t\t\t\r\n\t\t\t\treturn createdDate ? createdDate.toLocaleDateString('en-GB') : \"\";\r\n\t\t\t  },\r\n\t\t\tresizable: false,\r\n\t\t},\r\n\r\n\t\t{\r\n\t\t\tfield: \"actions\",\r\n\t\t\theaderName: translate(\"Actions\"),\r\n\t\t\tsortable: false,\r\n\t\t\twidth: 200,\r\n\t\t\tflex: 1,\r\n\t\t\tdisableColumnMenu: true,\r\n\t\t\tresizable: false,\r\n\t\t\trenderCell: (params) => {\r\n\t\t\t\tvar emailId = params.row.CreatedBy || false;\r\n\t\t\t\tsetEmail(emailId);\r\n\t\t\t\tconst accountid = params.row.AccountId || false;\r\n\t\t\t\tsetAccountIdNew(accountid)\r\n\t\t\t\treturn (\r\n\t\t\t\t\t<Box className=\"qadpt-grd-act\">\r\n\t\t\t\t\t<Tooltip arrow title={translate(\"Edit Account\")}> \r\n                        <BorderColorOutlinedIcon style={{ color: \"#7A7B7D\" }}\r\n                            className={styles.Addiconingrid}\r\n                            onClick={() => handleeditclick(accountid)}\r\n                        />\r\n                    </Tooltip>\r\n                    <Tooltip arrow title={translate(\"Delete Account\")}> \r\n\t\t\t\t\t\t\t<IconButton\r\n                            onClick={() => handleDeleteClick(emailId,accountid)}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<i className='fal fa-trash-alt'></i>\r\n\t\t\t\t\t\t\t\t</IconButton>\r\n                    </Tooltip>\r\n                    </Box>\r\n\t\t\t\t\t);\r\n\t\t\t},\r\n\t\t},\r\n\t];\r\n\r\n\tconst onPageChange = (newPage: number) => {\r\n\t};\r\n\tconst onPageSizeChange = (newPageSize: number) => {\r\n\t};\r\n\r\n\t// useEffect(() => {\r\n\t// \tif (showDeletePopup) {\r\n\t// \t\tfetchAccountDetails(accountidedit);\r\n\t// \t}\r\n\t// }, [showDeletePopup]);\r\n\t// const fetchAccountDetails = async (id: any) => {\r\n\t// \ttry {\r\n\t// \t\tconst response = await fetch(`${adminUrl}/Account/GetAccountById?accountId=${id}`);\r\n\t// \t\tif (!response.ok) {\r\n\t// \t\t\tthrow new Error(\"Network response was not ok\");\r\n\t// \t\t}\r\n\t// \t\tconst data = await response.json();\r\n\t// \t\tsetAccountDeleteDetails({\r\n\t// \t\t\tAccountId: data.AccountId,\r\n\t// \t\t\tAccountName: data.AccountName,\r\n\t// \t\t\tAccountType: data.AccountType,\r\n\t// \t\t\tCreatedBy: data.CreatedBy,\r\n\t// \t\t\tCreatedDate: data.CreatedDate,\r\n\t// \t\t\tOrganizationid: data.Organizationid,\r\n\t// \t\t\tUpdatedBy: data.UpdatedBy,\r\n\t// \t\t\tUpdatedDate: data.UpdatedDate,\r\n\t// \t\t\tActive: false,\r\n\t// \t\t});\r\n\t// \t} catch (error) {\r\n\t// \t\tconsole.error(\"Failed to fetch user details:\", error);\r\n\t// \t}\r\n\t// };\r\n\r\n\tconst handleDeleteAccount = () => {\r\n\t\tfetchDeleteAccountDetails(\r\n\t\t\taccountidedit,\r\n\t\t\tsetLoading,\r\n\t\t\tsetModels,\r\n\t\t\tsetShowDeletePopup,\r\n\t\t\tOrganizationid,\r\n\t\t\tskip,\r\n\t\t\ttop,\r\n\t\t\tsetTotalcount,\r\n\t\t\tsetSnackbarMessage,\r\n\t\t\tsetSnackbarSeverity,\r\n\t\t\tsetSnackbarOpen,\r\n\t\t\topenSnackbar,\r\n\t\t\torderByFields,\r\n\t\t\tfilters\r\n\t\t);\r\n\t\t//DeleteAccountDetails(setLoading, setModels, setShowDeletePopup, AccountDeleteDetails);\r\n\t};\r\n\r\n\tconst CustomToolbar: React.FC<any> = () => {\r\n\t\tconst [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);\r\n\r\n\t\tconst handleExportMenuClick = (event: React.MouseEvent<HTMLButtonElement>) => {\r\n\t\t\tsetAnchorEl(event.currentTarget);\r\n\t\t};\r\n\r\n\t\tconst handleExportMenuClose = () => {\r\n\t\t\tsetAnchorEl(null);\r\n\t\t};\r\n\r\n\t\tconst handleDownloadExcelClick = () => {\r\n\t\t\thandleExportMenuClose();\r\n\t\t};\r\n\r\n\t\treturn (\r\n\t\t\t<div style={{ display: \"flex\", alignItems: \"center\" }}>\r\n\t\t\t\t<GridToolbarContainer>\r\n\t\t\t\t\t<GridToolbarColumnsButton />\r\n\t\t\t\t\t<GridToolbarFilterButton />\r\n\t\t\t\t\t<GridToolbarDensitySelector />\r\n\t\t\t\t</GridToolbarContainer>\r\n\t\t\t\t<Button\r\n\t\t\t\t\taria-controls=\"export-menu\"\r\n\t\t\t\t\taria-haspopup=\"true\"\r\n\t\t\t\t\tonClick={handleExportMenuClick}\r\n\t\t\t\t\tstyle={{ marginLeft: \"10px\" }}\r\n\t\t\t\t\tstartIcon={<SaveAltIcon />}\r\n\t\t\t\t>\r\n\t\t\t\t\t{translate(\"Export\")}\r\n\t\t\t\t</Button>\r\n\t\t\t\t<Menu\r\n\t\t\t\t\tid=\"export-menu\"\r\n\t\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\t\tkeepMounted\r\n\t\t\t\t\topen={Boolean(anchorEl)}\r\n\t\t\t\t\tonClose={handleExportMenuClose}\r\n\t\t\t\t>\r\n\t\t\t\t\t<MenuItem\r\n\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t//handleDownloadExcel();\r\n\t\t\t\t\t\t\thandleExportMenuClose();\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Download Excel\")}\r\n\t\t\t\t\t</MenuItem>\r\n\t\t\t\t</Menu>\r\n\t\t\t</div>\r\n\t\t);\r\n\t};\r\n\r\n\r\n\tconst openPopup = () => {\r\n\t\tsetShowPopup(true);\r\n\t};\r\n\tconst handleSearch = (value: string[]) => {\r\n\t\tsetSearchText(value.join(\" \"));\r\n\t};\r\n\r\n\tconst globalhandleSearch = (searchValue: any) => {\r\n\t\tconst newFilter = {\r\n\t\t\tFieldName: \"AccountName\",\r\n\t\t\tElementType: \"string\",\r\n\t\t\tCondition: \"contains\",\r\n\t\t\tValue: searchValue,\r\n\t\t\tIsCustomField: false,\r\n\t\t};\r\n\t\tlet skips = 0;\r\n\t\tlet top = 0;\r\n\t\tconst skipCount = paginationModel.pageSize || 15;\r\n\t\tconst limitCount = paginationModel.page * skipCount;\r\n\t\ttop = paginationModel.pageSize;\r\n\t\tskips = limitCount;\r\n\t\tsetskip(skips);\r\n\t\tsettop(top);\r\n\r\n\t\ttry {\r\n\t\t\tGetAccountsList(setModels, setLoading, Organizationid, skip, top, setTotalcount, orderByFields, [newFilter]);\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error(\"Failed to fetch user roles:\", error);\r\n\t\t}\r\n\t};\r\n\tconst clearSearch = () => {\r\n\t\tsetInputs({ ...inputs, SearchInput: \"\" });\r\n\t\tGetAccountsList(setModels, setLoading, Organizationid, skip, top, setTotalcount, orderByFields, filters);\r\n\t};\r\n\tconst columnNames = columns.map((col) => col.headerName);\r\n\tconst actionColumns = [\"Actions\", \"Full Name\"];\r\n\tconst filteredColumnNames = columnNames\r\n\t\t.filter((name): name is string => name !== undefined && !actionColumns.includes(name))\r\n\t\t.map((name) => (name === \"Role Name\" ? name.replace(/\\s+/g, \"\") : name));\r\n\tconst handleApplyFilters = async (filters: any) => {\r\n\t\t// if (selectedAccount) {\r\n\t\t// \tsetFilters(filters);\r\n\t\t// \tlet skips: number = 0;\r\n\t\t// \tlet top: number = 0;\r\n\t\t// \tconst skipcount = paginationModel.pageSize || 15;\r\n\t\t// \tconst limitcount = paginationModel.page * skipcount;\r\n\t\t// \tskips = paginationModel.pageSize;\r\n\t\t// \ttop = limitcount;\r\n\t\t// \tconst formattedFilters = filters.map((filter: any) => ({\r\n\t\t// \t\tFieldName: filter.column,\r\n\t\t// \t\tElementType: \"string\",\r\n\t\t// \t\tCondition: filter.operator,\r\n\t\t// \t\tValue: filter.value,\r\n\t\t// \t\tIsCustomField: false,\r\n\t\t// \t}));\r\n\t\t// \tsetskip(skips);\r\n\t\t// \tsettop(top);\r\n\t\t// \tconst orderByFields = \"\";\r\n\t\t// \ttry {\r\n\t\t// \t\t(\"\");\r\n\t\t// \t} catch (error) {\r\n\t\t// \t\tconsole.error(\"Failed to fetch user roles:\", error);\r\n\t\t// \t}\r\n\t\t// } else {\r\n\t\t// \tsetFilters(filters);\r\n\t\t// \tlet skips: number = 0;\r\n\t\t// \tlet top: number = 0;\r\n\t\t// \tconst skipcount = paginationModel.pageSize || 15;\r\n\t\t// \tconst limitcount = paginationModel.pageSize;\r\n\t\t// \tskips = paginationModel.page * skipcount;\r\n\t\t// \ttop = limitcount;\r\n\t\t// \tconst formattedFilters = filters.map((filter: any) => ({\r\n\t\t// \t\tFieldName: filter.column,\r\n\t\t// \t\tElementType: \"string\",\r\n\t\t// \t\tCondition: filter.operator,\r\n\t\t// \t\tValue: filter.value,\r\n\t\t// \t\tIsCustomField: false,\r\n\t\t// \t}));\r\n\t\t// \tsetskip(skips);\r\n\t\t// \tsettop(top);\r\n\t\t// \tconst orderByFields = \"\";\r\n\t\t// \ttry {\r\n\t\t// \t\t(\"\");\r\n\t\t// \t} catch (error) {\r\n\t\t// \t\tconsole.error(\"Failed to fetch user roles:\", error);\r\n\t\t// \t}\r\n\t\t// }\r\n\t};\r\n\r\n\treturn (\r\n\t\t<Container>\r\n\t\t\t<div className=\"qadpt-midpart setng-box\">\r\n\t\t\t\t<div className=\"qadpt-content-block\">\r\n\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<div className=\"qadpt-head\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-title-sec\">\r\n\t\t\t\t\t\t\t\t<div className=\"qadpt-title\">{translate('Account')}</div>\r\n\t\t\t\t\t\t\t\t<div className=\"qadpt-description\">{translate('View & Manage your accounts here')}</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-right-part\">\r\n\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\tonClick={openPopup}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-memberButton\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<i className=\"fal fa-add-plus\"></i>\r\n\t\t\t\t\t\t\t\t\t<span>{translate('Create Account')}</span>\r\n\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t{showPopup ? (\r\n\t\t\t\t\t\t\t\t<CreateAccount\r\n\t\t\t\t\t\t\t\t\tsetModels={setModels}\r\n\t\t\t\t\t\t\t\t\tsetLoading={setLoading}\r\n\t\t\t\t\t\t\t\t\tshowPopup={showPopup}\r\n\t\t\t\t\t\t\t\t\tsetShowPopup={setShowPopup}\r\n\t\t\t\t\t\t\t\t\torderByField={orderByFields}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t\"\"\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t{showeditPopup ? (\r\n\t\t\t\t\t\t\t<EditAccount\r\n\t\t\t\t\t\t\t\tshowEditPopup={showeditPopup}\r\n\t\t\t\t\t\t\t\tsetShowEditPopup={setShowEditPopup}\r\n\t\t\t\t\t\t\t\taccountidedit={accountidedit}\r\n\t\t\t\t\t\t\t\tGetAllAccounts={GetAllAccounts}\r\n\t\t\t\t\t\t\t\tsetModels={setModels}\r\n\t\t\t\t\t\t\t\tsetLoading={setLoading}\r\n\t\t\t\t\t\t\t\tOrganizationid={Organizationid}\r\n\t\t\t\t\t\t\t\tskip={skip}\r\n\t\t\t\t\t\t\t\ttop={top}\r\n\t\t\t\t\t\t\t\tsetTotalcount={setTotalcount}\r\n\t\t\t\t\t\t\t\torderByField={orderByFields}\r\n\t\t\t\t\t\t\t\tfilters={filters}\r\n\t\t\t\t\t\t\t\tsetFilters={setFilters}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\"\"\r\n\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<Box className=\"qadpt-content-box\">\r\n\t\t\t\t\t\t\t\t<div className=\"qadpt-src-flt\">\r\n\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tclassName={`qadpt-teamsearch ${errors.SearchInput ? \"qadpt-teamsearch-error\" : \"\"}`}\r\n\t\t\t\t\t\t\t\t\t\t\t// style={{ right: \"-650px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\tname=\"SearchInput\"\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={inputs.SearchInput}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst newValue = e.target.value;\r\n\t\t\t\t\t\t\t\t\t\t\t\tsetInputs({ ...inputs, SearchInput: newValue });\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (newValue === \"\") {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tclearSearch();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tGetAccountsList(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetModels,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetLoading,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tOrganizationid,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tskip,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttop,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetTotalcount,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\torderByFields,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfilters\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tonKeyDown={(e) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (e.key === 'Enter') {\r\n\t\t\t\t\t\t\t\t\t\t\t\t  globalhandleSearch(inputs.SearchInput);\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t  }}\r\n\t\t\t\t\t\t\t\t\t\t\tplaceholder={translate('Search accounts')}\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\terror={!!errors.SearchInput}\r\n\t\t\t\t\t\t\t\t\t\t\t// sx={{\r\n\t\t\t\t\t\t\t\t\t\t\t// \t\"& .MuiFormHelperText-root\": {\r\n\t\t\t\t\t\t\t\t\t\t\t// \t\tcolor: errors.SearchInput ? \"red\" : \"inherit\",\r\n\t\t\t\t\t\t\t\t\t\t\t// \t},\r\n\t\t\t\t\t\t\t\t\t\t\t// \t\"& .MuiOutlinedInput-root\": {\r\n\t\t\t\t\t\t\t\t\t\t\t// \t\tpadding: \"0px\",\r\n\t\t\t\t\t\t\t\t\t\t\t// \t\tborderRadius: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t// \t},\r\n\t\t\t\t\t\t\t\t\t\t\t// \t\"& .MuiInputBase-input\": {\r\n\t\t\t\t\t\t\t\t\t\t\t// \t\theight: \"1em\",\r\n\t\t\t\t\t\t\t\t\t\t\t// \t\tpaddingLeft: \"0px\",\r\n\t\t\t\t\t\t\t\t\t\t\t// \t},\r\n\t\t\t\t\t\t\t\t\t\t\t// \twidth: \"200px\",\r\n\t\t\t\t\t\t\t\t\t\t\t// \tmarginBottom: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t// }}\r\n\t\t\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tstartAdornment: (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<InputAdornment position=\"start\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\taria-label=\"search\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => globalhandleSearch(inputs.SearchInput)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonMouseDown={(event) => event.preventDefault()}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SearchIcon />\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</InputAdornment>\r\n\t\t\t\t\t\t\t\t\t\t\t\t),\r\n\t\t\t\t\t\t\t\t\t\t\t\tendAdornment: inputs.SearchInput && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<InputAdornment position=\"end\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\taria-label=\"clear\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetInputs({ ...inputs, SearchInput: \"\" });\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclearSearch();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tGetAccountsList(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetModels,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetLoading,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tOrganizationid,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tskip,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttop,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetTotalcount,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\torderByFields,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfilters\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<ClearIcon />\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</InputAdornment>\r\n\t\t\t\t\t\t\t\t\t\t\t\t),\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t</div>{\" \"}\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<DataGrid\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-account-grd\"\r\n\t\t\t\t\t\t\t\t\trows={models.length > 0 ? models : []} // Display models if available\r\n\t\t\t\t\t\t\t\t\tcolumns={columns}\r\n\t\t\t\t\t\t\t\t\tpagination\r\n\t\t\t\t\t\t\t\t\tpaginationMode=\"server\"\r\n\t\t\t\t\t\t\t\t\tslots={{\r\n\t\t\t\t\t\t\t\t\t\tcolumnMenu: (menuProps) =>\r\n\t\t\t\t\t\t\t\t\t\t\tmenuProps.colDef.field === \"AccountName\" ? (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<AccountCustomColumnMenu\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcolumn={menuProps.colDef.field}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsetModels={setModels}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsetLoading={setLoading}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tskip={skip}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttop={top}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tOrganizationId={Organizationid}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsetTotalcount={setTotalcount}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\torderByFields={orderByFields}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tfilters={filters}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsetFilters={setFilters}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{...menuProps}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\toptions={models.map((model: any) => model.AccountName || \"\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonSearch={handleSearch}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t) : null,\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\tgetRowId={(row) => row.AccountId}\r\n\t\t\t\t\t\t\t\t\tpaginationModel={paginationModel}\r\n\t\t\t\t\t\t\t\t\tonPaginationModelChange={(model) => {\r\n\t\t\t\t\t\t\t\t\t\tsetPaginationModel(model);\r\n\t\t\t\t\t\t\t\t\t\t// FetchAccounts();\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\trowCount={totalcount}\r\n\t\t\t\t\t\t\t\t\tpageSizeOptions={[15, 25, 50, 100]}\r\n\t\t\t\t\t\t\t\t\tlocaleText={{\r\n\t\t\t\t\t\t\t\t\t\tMuiTablePagination: {\r\n\t\t\t\t\t\t\t\t\t\t\tlabelRowsPerPage: translate('Records Per Page'),\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\tdisableRowSelectionOnClick={true}\r\n\t\t\t\t\t\t\t\t\t//loading={loading} // To show the DataGrid's built-in loading overlay\r\n\t\t\t\t\t\t\t\t\tsortModel={sortModel}\r\n\t\t\t\t\t\t\t\t\tonSortModelChange={handleSortModelChange}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{loading && models.length === 0 && (\r\n\t\t\t\t\t\t\t\t\t<div className=\"Loaderstyles\">\r\n\t\t\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\t\t\tsrc={loader}\r\n\t\t\t\t\t\t\t\t\t\t\talt=\"Spinner\"\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"LoaderSpinnerStyles\"\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t{!loading && models.length === 0 && (\r\n\t\t\t\t\t\t\t\t\t<div style={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: 'flex',\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent: 'center',\r\n\t\t\t\t\t\t\t\t\t\talignItems: 'center',\r\n\t\t\t\t\t\t\t\t\t\theight: '200px',\r\n\t\t\t\t\t\t\t\t\t\tcolor: '#666',\r\n\t\t\t\t\t\t\t\t\t\tfontSize: '16px'\r\n\t\t\t\t\t\t\t\t\t}}>\r\n\t\t\t\t\t\t\t\t\t\tNo rows\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t{showDeletePopup ? (\r\n\t\t\t\t\t\t\t<div className=\"qadpt-modal-overlay\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-usrconfirm-popup qadpt-danger\">\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<div className=\"qadpt-icon\">\r\n\t\t\t\t\t\t\t\t\t{/* <svg\r\n\t\t\t\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\r\n\t\t\t\t\t\t\t\t\t\tfill=\"#d05353\"\r\n\t\t\t\t\t\t\t\t\t\twidth=\"24\"\r\n\t\t\t\t\t\t\t\t\t\theight=\"24\"\r\n\t\t\t\t\t\t\t\t\t\tviewBox=\"0 0 24 24\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<path d=\"M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2Zm0 15.5a1.5 1.5 0 1 1 1.5-1.5A1.5 1.5 0 0 1 12 17.5Zm1-7.5h-2V7h2Z\" />\r\n\t\t\t\t\t\t\t\t\t</svg> */}\r\n\t\t\t\t\t\t\t\t\t\t\t\t<i className='fal fa-trash-alt'></i>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-popup-title\">{translate(\"Delete Account\")}</div>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-warning\">\r\n\t\t\t\t\t\t\t\t{translate(\"The Account cannot be restored once it is deleted.\")}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-buttons\">\r\n  \t\t\t\t\t\t\t\t<button\r\n    \t\t\t\t\t\t\t\t\t onClick={() => setShowDeletePopup(false)}\r\n    \t\t\t\t\t\t\t\t\t className=\"qadpt-cancel-button\"\r\n  \t\t\t\t\t\t\t\t\t>\r\n    \t\t\t\t\t\t\t\t\t{translate(\"Cancel\")}\r\n  \t\t\t\t\t\t\t\t\t</button>\r\n  \t\t\t\t\t\t\t\t\t<button\r\n    \t\t\t\t\t\t\t\t\tclassName=\"qadpt-conform-button\"\r\n    \t\t\t\t\t\t\t\t\tonClick={handleDeleteAccount}\r\n  \t\t\t\t\t\t\t\t\t>\r\n    \t\t\t\t\t\t\t\t\t{translate(\"Delete\")}\r\n  \t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\"\"\r\n\t\t\t\t\t)}\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<Snackbar\r\n\t\t\t\t\t\topen={snackbarOpen}\r\n\t\t\t\t\t\tautoHideDuration={4000}\r\n\t\t\t\t\t\tonClose={handleSnackbarClose}\r\n\t\t\t\t\t\tanchorOrigin={{ vertical: \"top\", horizontal: \"right\" }}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Alert\r\n\t\t\t\t\t\t\tonClose={handleSnackbarClose}\r\n\t\t\t\t\t\t\tseverity={snackbarSeverity}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{translate(snackbarMessage)}\r\n\t\t\t\t\t\t</Alert>\r\n\t\t\t\t\t</Snackbar>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</Container>\r\n\t);\r\n};\r\n\r\nexport default AccountList;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAGCC,oBAAoB,EACpBC,wBAAwB,EACxBC,uBAAuB,EACvBC,0BAA0B,EAC1BC,QAAQ,QAGF,kBAAkB;AACzB,SAASC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,EAAoBC,UAAU,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,GAAG,EAAEC,SAAS,EAAEC,SAAS,EAAEC,OAAO,QAAS,eAAe;AAC1I,OAAOC,uBAAuB,MAAM,yCAAyC;AAG7E,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,MAAM,MAAM,yBAAyB;AAG5C;AACA,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,cAAc,EAAEC,yBAAyB,EAAEC,eAAe,QAAQ,+BAA+B;AAC1G,SAASC,aAAa,EAAEC,SAAS,QAAQ,4BAA4B;AAGrE,OAAOC,uBAAuB,MAAM,sBAAsB;AAC1D,SAASC,WAAW,QAAQ,uBAAuB;AACnD,OAAOC,MAAM,MAAM,2BAA2B;AAC9C;AACA,SAASC,cAAc,QAAQ,gBAAgB;AAE/C,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAoB/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAU;AAEd,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAAA,IAAAC,EAAA,GAAAC,YAAA;EACzB,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGT,cAAc,CAAC,CAAC;EACzC,MAAM,CAACU,SAAS,EAAEC,YAAY,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC8C,MAAM,EAAEC,SAAS,CAAC,GAAG/C,QAAQ,CAAU,EAAE,CAAC;EACjD,MAAM,CAACgD,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAqB,IAAI,CAAC;EAClE,MAAM,CAACkD,OAAO,EAAEC,UAAU,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoD,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACsD,aAAa,EAAEC,gBAAgB,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACwD,aAAa,EAAEC,gBAAgB,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC0D,eAAe,EAAEC,kBAAkB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4D,WAAW,EAAEC,cAAc,CAAC,GAAG7D,QAAQ,CAACyB,aAAa,CAAC,CAAC,CAAC;EAC/D,MAAM,CAACqC,YAAY,EAAEC,eAAe,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACgE,eAAe,EAAEC,kBAAkB,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACkE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnE,QAAQ,CAAsB,SAAS,CAAC;EACxF,MAAMoE,wBAAwB,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;EACrE,MAAMC,kBAAkB,GAAGH,wBAAwB,GAAGI,IAAI,CAACC,KAAK,CAACL,wBAAwB,CAAC,GAAG,IAAI;EACjG,MAAMM,cAAc,GAAG5C,cAAc;EACrC,MAAM,CAAC6C,IAAI,EAAEC,OAAO,CAAC,GAAG5E,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAAC6E,GAAG,EAAEC,MAAM,CAAC,GAAG9E,QAAQ,CAAC,CAAC,CAAC;EACjC,MAAM,CAAC+E,UAAU,EAAEC,aAAa,CAAC,GAAGhF,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACiF,UAAU,EAAEC,aAAa,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM;IAAEmF;EAAa,CAAC,GAAGvD,WAAW,CAAC,CAAC;EACtC,MAAM,CAACwD,aAAa,EAAEC,gBAAgB,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACsF,MAAM,EAAEC,SAAS,CAAC,GAAGvF,QAAQ,CAAc;IACjDwF,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC4F,OAAO,EAAEC,UAAU,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM8F,qBAAqB,GAAIC,KAAU,IAAK;IAC7CJ,YAAY,CAACI,KAAK,CAAC;IACnB,MAAMC,YAAY,GAAGD,KAAK,CACxBE,GAAG,CAAEC,IAAS,IAAMA,IAAI,CAACC,IAAI,KAAK,MAAM,GAAG,GAAGD,IAAI,CAACE,KAAK,OAAO,GAAGF,IAAI,CAACE,KAAM,CAAC,CAC9EC,IAAI,CAAC,IAAI,CAAC;IAEZhB,gBAAgB,CAACW,YAAY,CAAC;IAC9BxE,eAAe,CAACuB,SAAS,EAAEI,UAAU,EAAEuB,cAAc,EAAEC,IAAI,EAAEE,GAAG,EAAEG,aAAa,EAAEgB,YAAY,EAAEJ,OAAO,CAAC;EACxG,CAAC;EACD,MAAM,CAACU,eAAe,EAAEC,kBAAkB,CAAC,GAAGvG,QAAQ,CAAsB;IAC3EwG,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG3G,QAAQ,CAAc;IACjDwF,WAAW,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACoB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7G,QAAQ,CAAC;IAChE8G,SAAS,EAAE,EAAE;IACbtB,WAAW,EAAE,EAAE;IACfuB,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfvC,cAAc,EAAE,EAAE;IAClBwC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAEC,OAAO,CAAC,IAAI;EACrB,CAAC,CAAC;EAEF,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC3BnE,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMoE,SAAS,GAAGjB,eAAe,CAACG,QAAQ,IAAI,EAAE;IAChD,MAAMe,UAAU,GAAGlB,eAAe,CAACE,IAAI,GAAGe,SAAS;IACnD,MAAME,KAAK,GAAGD,UAAU;IACxB,MAAM3C,GAAG,GAAGyB,eAAe,CAACG,QAAQ;IACpC7B,OAAO,CAAC6C,KAAK,CAAC;IACd3C,MAAM,CAACD,GAAG,CAAC;IACXrD,eAAe,CAACuB,SAAS,EAAEI,UAAU,EAAEuB,cAAc,EAAE+C,KAAK,EAAE5C,GAAG,EAAEG,aAAa,EAAEI,aAAa,EAAEQ,OAAO,CAAC;EAC1G,CAAC;EAED3F,SAAS,CAAC,MAAM;IACfkD,UAAU,CAAC,IAAI,CAAC;IAChB;IACAmE,aAAa,CAAC,CAAC;EAChB,CAAC,EAAE,CAAChB,eAAe,CAAC,CAAC;EAErBrG,SAAS,CAAC,MAAM;IACf;IACAkD,UAAU,CAAC,KAAK,CAAC;EAClB,CAAC,EAAE,CAACL,MAAM,CAAC,CAAC;EAEZ7C,SAAS,CAAC,MAAM;IACf,MAAMyH,WAAW,GAAGhG,SAAS,CAACmC,cAAc,CAAC;IAC7C,OAAO,MAAM6D,WAAW,CAAC,CAAC;EAC3B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IACjC5D,eAAe,CAAC,KAAK,CAAC;EACvB,CAAC;EACD,MAAM6D,iBAAiB,GAAGA,CAACC,OAAe,EAAEC,SAAiB,KAAK;IACjEnE,kBAAkB,CAAC,IAAI,CAAC;IACxBF,gBAAgB,CAACoE,OAAO,CAAC;IACzBxE,gBAAgB,CAACyE,SAAS,CAAC;EAC5B,CAAC;EACD,MAAMC,eAAe,GAAID,SAAiB,IAAK;IAC9CvE,gBAAgB,CAAC,IAAI,CAAC;IACtBF,gBAAgB,CAACyE,SAAS,CAAC;EAC5B,CAAC;EACD,MAAM,CAACzF,KAAK,EAAE2F,QAAQ,CAAC,GAAGhI,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACiI,YAAY,EAAEC,eAAe,CAAC,GAAGlI,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAMmI,OAAqB,GAAG,CAE7B;IACC/B,KAAK,EAAE,aAAa;IACpBgC,UAAU,EAAEzF,SAAS,CAAC,cAAc,CAAC;IACrC0F,KAAK,EAAE,GAAG;IACVC,IAAI,EAAE,CAAC;IACPC,iBAAiB,EAAE,IAAI;IACvBC,SAAS,EAAE;EACZ,CAAC,EACD;IACCpC,KAAK,EAAE,aAAa;IACpBgC,UAAU,EAAEzF,SAAS,CAAC,cAAc,CAAC;IACrC0F,KAAK,EAAE,GAAG;IACVC,IAAI,EAAE,CAAC;IACPC,iBAAiB,EAAE,IAAI;IACvBE,UAAU,EAAGC,MAAM,IAAK;MACvB,MAAMC,WAAW,GAAGD,MAAM,CAACE,KAAK,GAAG,IAAIC,IAAI,CAACH,MAAM,CAACE,KAAK,CAAC,GAAG,IAAI;MAEhE,OAAOD,WAAW,GAAGA,WAAW,CAACG,kBAAkB,CAAC,OAAO,CAAC,GAAG,EAAE;IAChE,CAAC;IACHN,SAAS,EAAE;EACZ,CAAC,EAED;IACCpC,KAAK,EAAE,SAAS;IAChBgC,UAAU,EAAEzF,SAAS,CAAC,SAAS,CAAC;IAChCoG,QAAQ,EAAE,KAAK;IACfV,KAAK,EAAE,GAAG;IACVC,IAAI,EAAE,CAAC;IACPC,iBAAiB,EAAE,IAAI;IACvBC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAGC,MAAM,IAAK;MACvB,IAAIb,OAAO,GAAGa,MAAM,CAACM,GAAG,CAAChC,SAAS,IAAI,KAAK;MAC3CgB,QAAQ,CAACH,OAAO,CAAC;MACjB,MAAMC,SAAS,GAAGY,MAAM,CAACM,GAAG,CAAClC,SAAS,IAAI,KAAK;MAC/CoB,eAAe,CAACJ,SAAS,CAAC;MAC1B,oBACC1F,OAAA,CAACtB,GAAG;QAACmI,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC9B9G,OAAA,CAACnB,OAAO;UAACkI,KAAK;UAACC,KAAK,EAAEzG,SAAS,CAAC,cAAc,CAAE;UAAAuG,QAAA,eAC7B9G,OAAA,CAAClB,uBAAuB;YAACmI,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAU,CAAE;YACjDL,SAAS,EAAEpH,MAAM,CAAC0H,aAAc;YAChCC,OAAO,EAAEA,CAAA,KAAMzB,eAAe,CAACD,SAAS;UAAE;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACVxH,OAAA,CAACnB,OAAO;UAACkI,KAAK;UAACC,KAAK,EAAEzG,SAAS,CAAC,gBAAgB,CAAE;UAAAuG,QAAA,eAC/D9G,OAAA,CAACzB,UAAU;YACU6I,OAAO,EAAEA,CAAA,KAAM5B,iBAAiB,CAACC,OAAO,EAACC,SAAS,CAAE;YAAAoB,QAAA,eAEvE9G,OAAA;cAAG6G,SAAS,EAAC;YAAkB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAEvB;EACD,CAAC,CACD;EAED,MAAMC,YAAY,GAAIC,OAAe,IAAK,CAC1C,CAAC;EACD,MAAMC,gBAAgB,GAAIC,WAAmB,IAAK,CAClD,CAAC;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IACjC1I,yBAAyB,CACxB6B,aAAa,EACbD,UAAU,EACVJ,SAAS,EACTY,kBAAkB,EAClBe,cAAc,EACdC,IAAI,EACJE,GAAG,EACHG,aAAa,EACbf,kBAAkB,EAClBE,mBAAmB,EACnBJ,eAAe,EACfoB,YAAY,EACZC,aAAa,EACbQ,OACD,CAAC;IACD;EACD,CAAC;EAED,MAAMsE,aAA4B,GAAGA,CAAA,KAAM;IAAA1H,EAAA;IAC1C,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAqB,IAAI,CAAC;IAElE,MAAMmK,qBAAqB,GAAIC,KAA0C,IAAK;MAC7EnH,WAAW,CAACmH,KAAK,CAACC,aAAa,CAAC;IACjC,CAAC;IAED,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;MACnCrH,WAAW,CAAC,IAAI,CAAC;IAClB,CAAC;IAED,MAAMsH,wBAAwB,GAAGA,CAAA,KAAM;MACtCD,qBAAqB,CAAC,CAAC;IACxB,CAAC;IAED,oBACClI,OAAA;MAAKiH,KAAK,EAAE;QAAEmB,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAvB,QAAA,gBACrD9G,OAAA,CAACjC,oBAAoB;QAAA+I,QAAA,gBACpB9G,OAAA,CAAChC,wBAAwB;UAAAqJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5BxH,OAAA,CAAC/B,uBAAuB;UAAAoJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3BxH,OAAA,CAAC9B,0BAA0B;UAAAmJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACvBxH,OAAA,CAAC5B,MAAM;QACN,iBAAc,aAAa;QAC3B,iBAAc,MAAM;QACpBgJ,OAAO,EAAEW,qBAAsB;QAC/Bd,KAAK,EAAE;UAAEqB,UAAU,EAAE;QAAO,CAAE;QAC9BC,SAAS,eAAEvI,OAAA,CAACjB,WAAW;UAAAsI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAV,QAAA,EAE1BvG,SAAS,CAAC,QAAQ;MAAC;QAAA8G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eACTxH,OAAA,CAAC3B,IAAI;QACJmK,EAAE,EAAC,aAAa;QAChB5H,QAAQ,EAAEA,QAAS;QACnB6H,WAAW;QACXC,IAAI,EAAEzD,OAAO,CAACrE,QAAQ,CAAE;QACxB+H,OAAO,EAAET,qBAAsB;QAAApB,QAAA,eAE/B9G,OAAA,CAAC1B,QAAQ;UACR8I,OAAO,EAAEA,CAAA,KAAM;YACd;YACAc,qBAAqB,CAAC,CAAC;UACxB,CAAE;UAAApB,QAAA,EAEDvG,SAAS,CAAC,gBAAgB;QAAC;UAAA8G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAER,CAAC;EAACpH,EAAA,CAjDI0H,aAA4B;EAoDlC,MAAMc,SAAS,GAAGA,CAAA,KAAM;IACvBnI,YAAY,CAAC,IAAI,CAAC;EACnB,CAAC;EACD,MAAMoI,YAAY,GAAIrC,KAAe,IAAK;IACzC1D,aAAa,CAAC0D,KAAK,CAACvC,IAAI,CAAC,GAAG,CAAC,CAAC;EAC/B,CAAC;EAED,MAAM6E,kBAAkB,GAAIC,WAAgB,IAAK;IAChD,MAAMC,SAAS,GAAG;MACjBC,SAAS,EAAE,aAAa;MACxBC,WAAW,EAAE,QAAQ;MACrBC,SAAS,EAAE,UAAU;MACrBC,KAAK,EAAEL,WAAW;MAClBM,aAAa,EAAE;IAChB,CAAC;IACD,IAAIhE,KAAK,GAAG,CAAC;IACb,IAAI5C,GAAG,GAAG,CAAC;IACX,MAAM6G,SAAS,GAAGpF,eAAe,CAACG,QAAQ,IAAI,EAAE;IAChD,MAAMkF,UAAU,GAAGrF,eAAe,CAACE,IAAI,GAAGkF,SAAS;IACnD7G,GAAG,GAAGyB,eAAe,CAACG,QAAQ;IAC9BgB,KAAK,GAAGkE,UAAU;IAClB/G,OAAO,CAAC6C,KAAK,CAAC;IACd3C,MAAM,CAACD,GAAG,CAAC;IAEX,IAAI;MACHrD,eAAe,CAACuB,SAAS,EAAEI,UAAU,EAAEuB,cAAc,EAAEC,IAAI,EAAEE,GAAG,EAAEG,aAAa,EAAEI,aAAa,EAAE,CAACgG,SAAS,CAAC,CAAC;IAC7G,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACfC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACpD;EACD,CAAC;EACD,MAAME,WAAW,GAAGA,CAAA,KAAM;IACzBvG,SAAS,CAAC;MAAE,GAAGD,MAAM;MAAEG,WAAW,EAAE;IAAG,CAAC,CAAC;IACzCjE,eAAe,CAACuB,SAAS,EAAEI,UAAU,EAAEuB,cAAc,EAAEC,IAAI,EAAEE,GAAG,EAAEG,aAAa,EAAEI,aAAa,EAAEQ,OAAO,CAAC;EACzG,CAAC;EACD,MAAMmG,WAAW,GAAG5D,OAAO,CAAClC,GAAG,CAAE+F,GAAG,IAAKA,GAAG,CAAC5D,UAAU,CAAC;EACxD,MAAM6D,aAAa,GAAG,CAAC,SAAS,EAAE,WAAW,CAAC;EAC9C,MAAMC,mBAAmB,GAAGH,WAAW,CACrCI,MAAM,CAAEC,IAAI,IAAqBA,IAAI,KAAKC,SAAS,IAAI,CAACJ,aAAa,CAACK,QAAQ,CAACF,IAAI,CAAC,CAAC,CACrFnG,GAAG,CAAEmG,IAAI,IAAMA,IAAI,KAAK,WAAW,GAAGA,IAAI,CAACG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GAAGH,IAAK,CAAC;EACzE,MAAMI,kBAAkB,GAAG,MAAO5G,OAAY,IAAK;IAClD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA,CACA;EAED,oBACCxD,OAAA,CAACpB,SAAS;IAAAkI,QAAA,eACT9G,OAAA;MAAK6G,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACvC9G,OAAA;QAAK6G,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBACnC9G,OAAA;UAAA8G,QAAA,gBACC9G,OAAA;YAAK6G,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAC1B9G,OAAA;cAAK6G,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC/B9G,OAAA;gBAAK6G,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEvG,SAAS,CAAC,SAAS;cAAC;gBAAA8G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzDxH,OAAA;gBAAK6G,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAEvG,SAAS,CAAC,kCAAkC;cAAC;gBAAA8G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC,eACNxH,OAAA;cAAK6G,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAChC9G,OAAA;gBACCoH,OAAO,EAAEwB,SAAU;gBACnB/B,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBAE9B9G,OAAA;kBAAG6G,SAAS,EAAC;gBAAiB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnCxH,OAAA;kBAAA8G,QAAA,EAAOvG,SAAS,CAAC,gBAAgB;gBAAC;kBAAA8G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEF,CAAC,eACNxH,OAAA;YAAA8G,QAAA,EACEtG,SAAS,gBACTR,OAAA,CAAClC,aAAa;cACb6C,SAAS,EAAEA,SAAU;cACrBI,UAAU,EAAEA,UAAW;cACvBP,SAAS,EAAEA,SAAU;cACrBC,YAAY,EAAEA,YAAa;cAC3BmD,YAAY,EAAEZ;YAAc;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,GAEF;UACA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,EAELtG,aAAa,gBACblB,OAAA,CAACf,WAAW;YACXoL,aAAa,EAAEnJ,aAAc;YAC7BC,gBAAgB,EAAEA,gBAAiB;YACnCH,aAAa,EAAEA,aAAc;YAC7B9B,cAAc,EAAEA,cAAe;YAC/ByB,SAAS,EAAEA,SAAU;YACrBI,UAAU,EAAEA,UAAW;YACvBuB,cAAc,EAAEA,cAAe;YAC/BC,IAAI,EAAEA,IAAK;YACXE,GAAG,EAAEA,GAAI;YACTG,aAAa,EAAEA,aAAc;YAC7BgB,YAAY,EAAEZ,aAAc;YAC5BQ,OAAO,EAAEA,OAAQ;YACjBC,UAAU,EAAEA;UAAW;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,GAEF,EACA,eAEDxH,OAAA;YAAA8G,QAAA,eACC9G,OAAA,CAACtB,GAAG;cAACmI,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBACjC9G,OAAA;gBAAK6G,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC7B9G,OAAA;kBAAA8G,QAAA,eACA9G,OAAA,CAACrB,SAAS;oBACRkI,SAAS,EAAE,oBAAoBvC,MAAM,CAACjB,WAAW,GAAG,wBAAwB,GAAG,EAAE;oBACjF;oBAAA;oBACA2G,IAAI,EAAC,aAAa;oBAClBxD,KAAK,EAAEtD,MAAM,CAACG,WAAY;oBAC1BiH,QAAQ,EAAGC,CAAC,IAAK;sBAChB,MAAMC,QAAQ,GAAGD,CAAC,CAACE,MAAM,CAACjE,KAAK;sBAC/BrD,SAAS,CAAC;wBAAE,GAAGD,MAAM;wBAAEG,WAAW,EAAEmH;sBAAS,CAAC,CAAC;sBAC/C,IAAIA,QAAQ,KAAK,EAAE,EAAE;wBACpBd,WAAW,CAAC,CAAC;wBACbtK,eAAe,CACduB,SAAS,EACTI,UAAU,EACVuB,cAAc,EACdC,IAAI,EACJE,GAAG,EACHG,aAAa,EACbI,aAAa,EACbQ,OACD,CAAC;sBACF;oBACD,CAAE;oBACFkH,SAAS,EAAGH,CAAC,IAAK;sBACjB,IAAIA,CAAC,CAACI,GAAG,KAAK,OAAO,EAAE;wBACrB7B,kBAAkB,CAAC5F,MAAM,CAACG,WAAW,CAAC;sBACxC;oBACC,CAAE;oBACJuH,WAAW,EAAErK,SAAS,CAAC,iBAAiB,CAAE;oBAC1CsK,OAAO,EAAC,UAAU;oBAClBrB,KAAK,EAAE,CAAC,CAAClF,MAAM,CAACjB;oBAChB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBAAA;oBACAyH,UAAU,EAAE;sBACXC,cAAc,eACb/K,OAAA,CAACL,cAAc;wBAACqL,QAAQ,EAAC,OAAO;wBAAAlE,QAAA,eAC/B9G,OAAA,CAACzB,UAAU;0BACV,cAAW,QAAQ;0BACnB6I,OAAO,EAAEA,CAAA,KAAM0B,kBAAkB,CAAC5F,MAAM,CAACG,WAAW,CAAE;0BACtD4H,WAAW,EAAGjD,KAAK,IAAKA,KAAK,CAACkD,cAAc,CAAC,CAAE;0BAAApE,QAAA,eAE/C9G,OAAA,CAACJ,UAAU;4BAAAyH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAChB;sBACD2D,YAAY,EAAEjI,MAAM,CAACG,WAAW,iBAC/BrD,OAAA,CAACL,cAAc;wBAACqL,QAAQ,EAAC,KAAK;wBAAAlE,QAAA,eAC7B9G,OAAA,CAACzB,UAAU;0BACV,cAAW,OAAO;0BAClB6I,OAAO,EAAEA,CAAA,KAAM;4BACdjE,SAAS,CAAC;8BAAE,GAAGD,MAAM;8BAAEG,WAAW,EAAE;4BAAG,CAAC,CAAC;4BACzCqG,WAAW,CAAC,CAAC;4BACbtK,eAAe,CACduB,SAAS,EACTI,UAAU,EACVuB,cAAc,EACdC,IAAI,EACJE,GAAG,EACHG,aAAa,EACbI,aAAa,EACbQ,OACD,CAAC;0BACF,CAAE;0BAAAsD,QAAA,eAEF9G,OAAA,CAACH,SAAS;4BAAAwH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE;oBAElB;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EAAC,GAAG;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNxH,OAAA,CAAC7B,QAAQ;gBACR0I,SAAS,EAAC,mBAAmB;gBAC7BuE,IAAI,EAAE1K,MAAM,CAAC2K,MAAM,GAAG,CAAC,GAAG3K,MAAM,GAAG,EAAG,CAAC;gBAAA;gBACvCqF,OAAO,EAAEA,OAAQ;gBACjBuF,UAAU;gBACVC,cAAc,EAAC,QAAQ;gBACvBC,KAAK,EAAE;kBACNC,UAAU,EAAGC,SAAS,IACrBA,SAAS,CAACC,MAAM,CAAC3H,KAAK,KAAK,aAAa,gBACvChE,OAAA,CAACT,uBAAuB;oBACvBqM,MAAM,EAAEF,SAAS,CAACC,MAAM,CAAC3H,KAAM;oBAC/BrD,SAAS,EAAEA,SAAU;oBACrBI,UAAU,EAAEA,UAAW;oBACvBwB,IAAI,EAAEA,IAAK;oBACXE,GAAG,EAAEA,GAAI;oBACT/C,cAAc,EAAE4C,cAAe;oBAC/BM,aAAa,EAAEA,aAAc;oBAC7BI,aAAa,EAAEA,aAAc;oBAC7BQ,OAAO,EAAEA,OAAQ;oBACjBC,UAAU,EAAEA,UAAW;oBAAA,GACnBiI,SAAS;oBACbG,OAAO,EAAEnL,MAAM,CAACmD,GAAG,CAAEF,KAAU,IAAKA,KAAK,CAACP,WAAW,IAAI,EAAE,CAAE;oBAC7D0I,QAAQ,EAAEjD;kBAAa;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,GACC;gBACN,CAAE;gBACFuE,QAAQ,EAAGnF,GAAG,IAAKA,GAAG,CAAClC,SAAU;gBACjCR,eAAe,EAAEA,eAAgB;gBACjC8H,uBAAuB,EAAGrI,KAAK,IAAK;kBACnCQ,kBAAkB,CAACR,KAAK,CAAC;kBACzB;gBACD,CAAE;gBACFsI,QAAQ,EAAEtJ,UAAW;gBACrBuJ,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAE;gBACnCC,UAAU,EAAE;kBACXC,kBAAkB,EAAE;oBACnBC,gBAAgB,EAAE9L,SAAS,CAAC,kBAAkB;kBAC/C;gBACD,CAAE;gBACF+L,0BAA0B,EAAE;gBAC5B;gBAAA;gBACAhJ,SAAS,EAAEA,SAAU;gBACrBiJ,iBAAiB,EAAE7I;cAAsB;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,EACD1G,OAAO,IAAIJ,MAAM,CAAC2K,MAAM,KAAK,CAAC,iBAC9BrL,OAAA;gBAAK6G,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC5B9G,OAAA;kBACCwM,GAAG,EAAExN,MAAO;kBACZyN,GAAG,EAAC,SAAS;kBACb5F,SAAS,EAAC;gBAAqB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACL,EACA,CAAC1G,OAAO,IAAIJ,MAAM,CAAC2K,MAAM,KAAK,CAAC,iBAC/BrL,OAAA;gBAAKiH,KAAK,EAAE;kBACXmB,OAAO,EAAE,MAAM;kBACfsE,cAAc,EAAE,QAAQ;kBACxBrE,UAAU,EAAE,QAAQ;kBACpBsE,MAAM,EAAE,OAAO;kBACfzF,KAAK,EAAE,MAAM;kBACb0F,QAAQ,EAAE;gBACX,CAAE;gBAAA9F,QAAA,EAAC;cAEH;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACL;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,EAELlG,eAAe,gBACftB,OAAA;YAAK6G,SAAS,EAAC,qBAAqB;YAAAC,QAAA,eACpC9G,OAAA;cAAK6G,SAAS,EAAC,qCAAqC;cAAAC,QAAA,gBACpD9G,OAAA;gBAAA8G,QAAA,eACC9G,OAAA;kBAAK6G,SAAS,EAAC,YAAY;kBAAAC,QAAA,eAUvB9G,OAAA;oBAAG6G,SAAS,EAAC;kBAAkB;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNxH,OAAA;gBAAK6G,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAEvG,SAAS,CAAC,gBAAgB;cAAC;gBAAA8G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtExH,OAAA;gBAAK6G,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAC5BvG,SAAS,CAAC,oDAAoD;cAAC;gBAAA8G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,eACNxH,OAAA;gBAAK6G,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC3B9G,OAAA;kBACIoH,OAAO,EAAEA,CAAA,KAAM7F,kBAAkB,CAAC,KAAK,CAAE;kBACzCsF,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAE/BvG,SAAS,CAAC,QAAQ;gBAAC;kBAAA8G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,eACTxH,OAAA;kBACE6G,SAAS,EAAC,sBAAsB;kBAChCO,OAAO,EAAES,mBAAoB;kBAAAf,QAAA,EAE5BvG,SAAS,CAAC,QAAQ;gBAAC;kBAAA8G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,GAEL,EACA;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACNxH,OAAA,CAACxB,QAAQ;UACRkK,IAAI,EAAEhH,YAAa;UACnBmL,gBAAgB,EAAE,IAAK;UACvBlE,OAAO,EAAEpD,mBAAoB;UAC7BuH,YAAY,EAAE;YAAEC,QAAQ,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAQ,CAAE;UAAAlG,QAAA,eAEvD9G,OAAA,CAACvB,KAAK;YACLkK,OAAO,EAAEpD,mBAAoB;YAC7B0H,QAAQ,EAAEnL,gBAAiB;YAAAgF,QAAA,EAE1BvG,SAAS,CAACqB,eAAe;UAAC;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEd,CAAC;AAACrH,GAAA,CA3mBID,WAAW;EAAA,QACSJ,cAAc,EAoBdN,WAAW;AAAA;AAAA0N,EAAA,GArB/BhN,WAAW;AA6mBjB,eAAeA,WAAW;AAAC,IAAAgN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}