{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptWeb\\\\src\\\\components\\\\webappsettingspage\\\\WebAppSettings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useContext } from 'react';\nimport { Container, Typography, Button, Grid, Switch, TextField, Select, MenuItem, FormControl, Card, CardContent, FormControlLabel, IconButton, Box, InputAdornment, Tooltip, RadioGroup } from '@mui/material';\nimport { useTranslation } from 'react-i18next';\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\nimport { useLocation, useNavigate, useParams } from 'react-router-dom';\nimport EditIcon from '@mui/icons-material/Edit';\nimport OpeninNewWindow from '../../assets/icons/OpenNewWindow.svg';\nimport PublishIcon from '../../assets/icons/PublishIcon.svg';\nimport checksmall from '../../assets/icons/check_small.svg';\nimport Saveicon from '../../assets/icons/SaveIcon.svg';\nimport Warning from '../../assets/icons/Warning.svg';\nimport Targetdelete from '../../assets/icons/Targetdelete.svg';\nimport DrawOutlinedIcon from '@mui/icons-material/DrawOutlined';\nimport Radio from '@mui/material/Radio';\nimport { styled } from '@mui/material/styles';\nimport { UpdateGuidName, PublishGuide, UnPublishGuide, SubmitUpdateGuid, SavePageTarget, DeletePageTarget, UpdatePageTarget, GetPageTargets, GetGudeDetailsByGuideId } from '../../services/GuideService';\nimport { useSnackbar } from '../../SnackbarContext';\nimport AddOutlinedIcon from '@mui/icons-material/AddOutlined';\nimport { cloudoff } from '../../assets/icons/icons';\nimport { openGuideInBuilder } from '../../utils/openGuideInBuilder';\nimport { useExtension } from '../../ExtensionContext';\nimport ExtensionRequiredPopup from '../common/ExtensionRequiredPopup';\nimport { AccountContext } from '../account/AccountContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst WebappSettings = () => {\n  _s();\n  var _guide$GuideDetails, _guide$GuideDetails2, _guide$GuideDetails3, _guide$GuideDetails4, _guide$GuideDetails5, _guide$GuideDetails6, _guide$GuideDetails7, _guide$GuideDetails8, _guide$GuideDetails9, _guide$GuideDetails0, _guide$GuideDetails1, _guide$GuideDetails10, _guide$GuideDetails11, _guide$GuideDetails12, _guide$GuideDetails13, _guide$GuideDetails14;\n  const {\n    t: translate\n  } = useTranslation();\n  const currentDateTime = new Date().toISOString().slice(0, 16);\n  const currentDate = new Date();\n  // const utcDate = new Date(currentDate);\n\n  // Add 2 hours to the current date and time\n\n  // Format both the current date and the unpublish date\n  //const currentdatTime = utcDate.toLocaleString();\n  // utcDate.setHours(utcDate.getHours() + 2);\n\n  const [hasChanges, setHasChanges] = useState(false);\n  const [guide, setGuide] = useState(null);\n  const [pageTargetsSaved, setPageTargetsSaved] = useState(false); // Track if page targets are saved\n  const [newTriggersAdded, setNewTriggersAdded] = useState(false); // Track if new page targets are added\n  const location = useLocation();\n  const navigate = useNavigate();\n  const {\n    openSnackbar\n  } = useSnackbar();\n  const [guideName, setGuideName] = useState((guide === null || guide === void 0 ? void 0 : (_guide$GuideDetails = guide.GuideDetails) === null || _guide$GuideDetails === void 0 ? void 0 : _guide$GuideDetails.Name) || \" \");\n  const {\n    guideId\n  } = useParams();\n  const [currentGuideId, setGuideId] = useState(guideId || \"\");\n  const [organizationId, setOrganizationId] = useState((guide === null || guide === void 0 ? void 0 : guide.GuideDetails.OrganizationId) || \"\");\n  const [accountId, setAccountId] = useState((guide === null || guide === void 0 ? void 0 : (_guide$GuideDetails2 = guide.GuideDetails) === null || _guide$GuideDetails2 === void 0 ? void 0 : _guide$GuideDetails2.AccountId) || \"\");\n  const [guideStatus, setGuideStatus] = useState((guide === null || guide === void 0 ? void 0 : (_guide$GuideDetails3 = guide.GuideDetails) === null || _guide$GuideDetails3 === void 0 ? void 0 : _guide$GuideDetails3.GuideStatus) || \"\");\n  const [isEditing, setIsEditing] = useState(false);\n  const [selectedFrequency, setSelectedFrequency] = useState((guide === null || guide === void 0 ? void 0 : (_guide$GuideDetails4 = guide.GuideDetails) === null || _guide$GuideDetails4 === void 0 ? void 0 : _guide$GuideDetails4.Frequency) || 'onceInSession');\n  const [guideType, setguideType] = useState((guide === null || guide === void 0 ? void 0 : (_guide$GuideDetails5 = guide.GuideDetails) === null || _guide$GuideDetails5 === void 0 ? void 0 : _guide$GuideDetails5.GuideType) || \"\");\n  const [updatedBy, setUpdatedBy] = useState(guide === null || guide === void 0 ? void 0 : (_guide$GuideDetails6 = guide.GuideDetails) === null || _guide$GuideDetails6 === void 0 ? void 0 : _guide$GuideDetails6.UpdatedBy);\n  const [createdBy, setCreatedBy] = useState(guide === null || guide === void 0 ? void 0 : (_guide$GuideDetails7 = guide.GuideDetails) === null || _guide$GuideDetails7 === void 0 ? void 0 : _guide$GuideDetails7.CreatedBy);\n  const [isAutoTriggerEnabled, setIsAutoTriggerEnabled] = useState(guide === null || guide === void 0 ? void 0 : (_guide$GuideDetails8 = guide.GuideDetails) === null || _guide$GuideDetails8 === void 0 ? void 0 : _guide$GuideDetails8.AutoTrigger);\n  const [publishOption, setPublishOption] = useState((guide === null || guide === void 0 ? void 0 : (_guide$GuideDetails9 = guide.GuideDetails) === null || _guide$GuideDetails9 === void 0 ? void 0 : _guide$GuideDetails9.PublishType) || \"immediately\");\n  const [unpublishOption, setUnpublishOption] = useState((guide === null || guide === void 0 ? void 0 : (_guide$GuideDetails0 = guide.GuideDetails) === null || _guide$GuideDetails0 === void 0 ? void 0 : _guide$GuideDetails0.UnPublishType) || \"Manually\");\n  const [Descriptionvalue, setDescriptionValue] = useState(guide === null || guide === void 0 ? void 0 : (_guide$GuideDetails1 = guide.GuideDetails) === null || _guide$GuideDetails1 === void 0 ? void 0 : _guide$GuideDetails1.Description);\n  const [targetUrl, setTargetUrl] = useState((guide === null || guide === void 0 ? void 0 : (_guide$GuideDetails10 = guide.GuideDetails) === null || _guide$GuideDetails10 === void 0 ? void 0 : _guide$GuideDetails10.TargetUrl) || \"\");\n  const [CreatedDate, setCreatedDate] = useState(guide === null || guide === void 0 ? void 0 : (_guide$GuideDetails11 = guide.GuideDetails) === null || _guide$GuideDetails11 === void 0 ? void 0 : _guide$GuideDetails11.CreatedDate);\n  const [PublishDate, setPublishDate] = useState((guide === null || guide === void 0 ? void 0 : (_guide$GuideDetails12 = guide.GuideDetails) === null || _guide$GuideDetails12 === void 0 ? void 0 : _guide$GuideDetails12.PublishDate) || currentDate);\n  const [UnPublishDate, setUnPublishDate] = useState((guide === null || guide === void 0 ? void 0 : (_guide$GuideDetails13 = guide.GuideDetails) === null || _guide$GuideDetails13 === void 0 ? void 0 : _guide$GuideDetails13.UnPublishDate) || currentDate);\n  const [frequencyDropdown, setfrequencyDropdown] = useState('');\n  const [isPublished, setIsPublished] = useState(false);\n  const [isUnPublished, setIsUnPublished] = useState(false);\n  const [dialogOpen, setDialogOpen] = useState(false);\n  const [deleteIndex, setDeleteIndex] = useState(null);\n  const [triggers, setTriggers] = useState([{\n    pageRule: \"Equals\",\n    url: (guide === null || guide === void 0 ? void 0 : (_guide$GuideDetails14 = guide.GuideDetails) === null || _guide$GuideDetails14 === void 0 ? void 0 : _guide$GuideDetails14.TargetUrl) || '',\n    logicalOperator: \"\",\n    PageTargetId: \"\"\n  }]);\n  const [errorMessage, setErrorMessage] = useState([]);\n  const [open, setOpen] = useState(false); // Controls the popup visibility\n  const [initialGuide, setInitialGuide] = useState({\n    GuideId: '',\n    GuideType: '',\n    Name: '',\n    OrganizationId: '',\n    CreatedBy: '',\n    UpdatedBy: '',\n    Frequency: '',\n    AccountId: '',\n    GuideStatus: '',\n    AutoTrigger: false,\n    Publish: false,\n    UnPublish: false,\n    Description: '',\n    TargetUrl: ''\n  });\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false); // To track unsaved changes\n  const [customPublishDate, setCustomPublishDate] = useState(\"2024-09-26T07:00\");\n  const [customUnPublishDate, setCustomUnPublishDate] = useState(\"2024-09-26T07:00\");\n  const {\n    roles\n  } = useContext(AccountContext);\n  const handleCustomDateChange = event => {\n    setPublishDate(event.target.value); // Update state with new date value\n  };\n  const handleCustomDateChangeTwo = event => {\n    setUnPublishDate(event.target.value); // Update state with new date value\n  };\n  const [guidestep, setGuidestep] = useState(Array.isArray(guide === null || guide === void 0 ? void 0 : guide.GuideDetails.GuideStep) ? guide === null || guide === void 0 ? void 0 : guide.GuideDetails.GuideStep : []);\n  const CustomDivider = styled('div')(({\n    theme\n  }) => ({\n    display: 'flex',\n    alignItems: 'center',\n    textAlign: 'center',\n    // margin: '8px 0',\n    '&:before, &:after': {\n      content: '\"\"',\n      //flex: 1,\n      //borderBottom: `1px solid ${theme.palette.divider}`,\n      margin: '0 8px'\n    }\n  }));\n  useEffect(() => {\n    const fetchGuideDetails = async () => {\n      const details = await GetGudeDetailsByGuideId(currentGuideId);\n      if (details) {\n        setGuide(details);\n        setGuideStatus(details.GuideDetails.GuideStatus);\n        setIsPublished(details.GuideDetails.GuideStatus === \"Active\");\n        setIsUnPublished(details.GuideDetails.GuideStatus === \"InActive\");\n        setOrganizationId(details.GuideDetails.OrganizationId);\n        setguideType(details.GuideDetails.GuideType);\n        setGuideName(details.GuideDetails.Name);\n        setAccountId(details.GuideDetails.AccountId);\n        setUpdatedBy(details.GuideDetails.UpdatedBy);\n        setCreatedBy(details.GuideDetails.CreatedBy);\n        setGuidestep(details.GuideDetails.GuideStep);\n        setDescriptionValue(details.GuideDetails.Description);\n        setSelectedFrequency(details.GuideDetails.Frequency);\n        setIsAutoTriggerEnabled(details.GuideDetails.AutoTrigger);\n        setPublishOption(details.GuideDetails.publishOption);\n        setUnpublishOption(details.GuideDetails.unpublishOption);\n        setTargetUrl(details.GuideDetails.TargetUrl);\n        setCreatedDate(details.GuideDetails.CreatedDate);\n        setPublishDate(details.GuideDetails.PublishDate);\n        setUnPublishDate(details.GuideDetails.UnpublishDate);\n        navigate(location.pathname, {\n          state: {\n            response: details\n          },\n          replace: true\n        });\n      }\n    };\n    fetchGuideDetails();\n  }, [currentGuideId, guideStatus]);\n  const HandlePublishToggle = async () => {\n    try {\n      if (![\"Account Admin\", \"Publisher\"].some(role => roles.includes(role))) {\n        return;\n      }\n      await handleFinalSaveClick();\n      if (isPublished) {\n        const result = await UnPublishGuide(currentGuideId);\n        if (result.Success) {\n          openSnackbar(`${guideName} ${translate(guideType)} ${translate(\"Unpublished Successfully\")}`, \"success\");\n          setGuideStatus(\"InActive\");\n          setIsPublished(false);\n          setIsUnPublished(true);\n          // Fetch and update the latest guide details\n          const updatedDetails = await GetGudeDetailsByGuideId(currentGuideId);\n          if (updatedDetails) {\n            setGuide(updatedDetails);\n            setGuideStatus(updatedDetails.GuideDetails.GuideStatus);\n            setIsPublished(updatedDetails.GuideDetails.GuideStatus === \"Active\");\n            setIsUnPublished(updatedDetails.GuideDetails.GuideStatus === \"InActive\");\n          }\n        } else {\n          openSnackbar(translate(result.SuccessMessage), \"error\");\n        }\n      } else {\n        const result = await PublishGuide(currentGuideId);\n        if (result.Success) {\n          openSnackbar(`${guideName} ${translate(guideType)} ${translate(\"Published Successfully\")}`, \"success\");\n          setGuideStatus(\"Active\");\n          setIsPublished(true);\n          setIsUnPublished(false);\n          // Fetch and update the latest guide details\n          const updatedDetails = await GetGudeDetailsByGuideId(currentGuideId);\n          if (updatedDetails) {\n            setGuide(updatedDetails);\n            setGuideStatus(updatedDetails.GuideDetails.GuideStatus);\n            setIsPublished(updatedDetails.GuideDetails.GuideStatus === \"Active\");\n            setIsUnPublished(updatedDetails.GuideDetails.GuideStatus === \"InActive\");\n          }\n        } else {\n          openSnackbar(translate(result.SuccessMessage), \"error\");\n        }\n      }\n    } catch (error) {\n      console.error('Error updating guide status:', error);\n    }\n  };\n  const handleBackClick = () => {\n    const GuideType = `${guideType.toLowerCase()}s`;\n    navigate(`/${GuideType}`);\n  };\n  const handleEditClick = () => {\n    setOriginalGuideName(guideName);\n    setIsEditing(true);\n  };\n  const handleSave = async () => {\n    setIsEditing(false);\n    try {\n      const result = await UpdateGuidName(currentGuideId, organizationId, guideName, accountId, guideType);\n      if (result.Success === true) {\n        openSnackbar(translate(result.SuccessMessage), \"success\");\n      } else {\n        openSnackbar(translate(result.ErrorMessage), \"error\");\n      }\n    } catch (error) {\n      console.error('Error updating guide name:', error);\n    }\n  };\n  const handleDrawClick = () => {\n    if (targetUrl) {\n      window.open(targetUrl, '_blank');\n    }\n  };\n  useEffect(() => {\n    if (guide !== null && guide !== void 0 && guide.PageTargets) {\n      const mappedTriggers = guide === null || guide === void 0 ? void 0 : guide.PageTargets.map(trigger => ({\n        pageRule: trigger.Condition,\n        url: trigger.Value,\n        logicalOperator: trigger.Operator,\n        PageTargetId: trigger.PageTargetId\n      }));\n      setTriggers(mappedTriggers);\n    }\n  }, [guide === null || guide === void 0 ? void 0 : guide.PageTargets]);\n  useEffect(() => {\n    if (triggers.length === 0) {\n      setTriggers([{\n        pageRule: \"Equals\",\n        url: (guide === null || guide === void 0 ? void 0 : guide.GuideDetails.TargetUrl) || '',\n        PageTargetId: \"\",\n        logicalOperator: \"\"\n      }]);\n    }\n  }, [triggers]);\n  const handleAddTrigger = () => {\n    if (triggers.length < 5) {\n      setTriggers([...triggers, {\n        pageRule: \"Equals\",\n        url: \"\",\n        PageTargetId: \"\",\n        logicalOperator: \"OR\"\n      }]);\n      setPageTargetsSaved(false);\n      setNewTriggersAdded(true);\n      setErrorMessage([]);\n    }\n  };\n  const pageRuleOptions = [{\n    value: \"Equals\",\n    label: translate(\"Equals\")\n  }, {\n    value: \"Not Equals\",\n    label: translate(\"Not Equals\")\n  }, {\n    value: \"Starts With\",\n    label: translate(\"Starts With\")\n  }, {\n    value: \"Ends With\",\n    label: translate(\"Ends With\")\n  }, {\n    value: \"Contains\",\n    label: translate(\"Contains\")\n  }, {\n    value: \"Not Contains\",\n    label: translate(\"Does Not Contain\")\n  }];\n  const handleTriggerChange = (index, field, value) => {\n    const newErrors = [];\n    const updatedTriggers = [...triggers];\n    updatedTriggers[index][field] = value;\n    let error = \"\";\n    if (value.length < 1 && field === 'url') {\n      error = translate(\"Minimum Length : 1 Character\");\n    } else if (value.length > 500) {\n      error = translate(\"Maximum Length : 500 characters.\");\n    } else if (/\\s/.test(value) && field === 'url') {\n      error = translate(\"Restriction : Spaces are not allowed\");\n    } else {\n      setTriggers(updatedTriggers);\n    }\n    if (error.length > 0) {\n      newErrors[index] = error;\n    }\n    ;\n    setErrorMessage(newErrors);\n    setHasChanges(true);\n  };\n  const parseFrequency = frequency => {\n    if (frequency !== null && frequency !== void 0 && frequency.startsWith('onceIn') && frequency !== \"onceInSession\") {\n      const parts = frequency.split('-');\n      const base = parts[0];\n      const dropdownValue = parts.length > 1 ? parts[1] : '';\n      return {\n        frequencyBase: base,\n        dropdownValue\n      };\n    }\n    return {\n      frequencyBase: frequency,\n      dropdownValue: ''\n    };\n  };\n  useEffect(() => {\n    var _guide$GuideDetails15;\n    const initialFrequency = guide === null || guide === void 0 ? void 0 : (_guide$GuideDetails15 = guide.GuideDetails) === null || _guide$GuideDetails15 === void 0 ? void 0 : _guide$GuideDetails15.Frequency;\n    if (initialFrequency !== \"onceInSession\") {\n      const {\n        frequencyBase,\n        dropdownValue\n      } = parseFrequency(initialFrequency);\n      setSelectedFrequency(frequencyBase);\n      setfrequencyDropdown(dropdownValue);\n    }\n  }, [location.state]);\n  const [originalGuideName, setOriginalGuideName] = useState(\"\");\n  const [errors, setErrors] = useState({});\n  const handleChange = event => {\n    const {\n      name,\n      value\n    } = event.target;\n    let error = \"\";\n    if (name === \"GuideName\" && value.trim().length < 3) {\n      error = translate(\"Guide Name must be at least 3 characters.\");\n    }\n    setGuideName(event.target.value);\n    setErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n  };\n  useEffect(() => {\n    if (isAutoTriggerEnabled) {\n      setSelectedFrequency('onceInSession');\n    }\n  }, [isAutoTriggerEnabled]);\n  const handleFrequencyChange = value => {\n    if (value === \"onceIn\") {\n      setSelectedFrequency(`onceIn${frequencyDropdown}`);\n    } else {\n      setSelectedFrequency(value);\n    }\n    if (value !== \"onceIn\") {\n      setfrequencyDropdown(\"\");\n    }\n  };\n  const handleFrequencyDropdown = e => {\n    const value = e.target.value;\n    if (selectedFrequency !== null && selectedFrequency !== void 0 && selectedFrequency.startsWith(\"onceIn\") && selectedFrequency !== \"onceInSession\") {\n      setfrequencyDropdown(value);\n      setSelectedFrequency(`onceIn-${value}`);\n    }\n  };\n  const handleKeyDown = event => {\n    if (event.key === 'Enter') {\n      handleSave();\n    } else if (event.key === 'Escape') {\n      setGuideName(originalGuideName);\n      setIsEditing(false);\n    }\n  };\n  const handleDeleteTrigger = async () => {\n    if (deleteIndex === null) return; // No index to delete\n\n    const triggerToDelete = triggers[deleteIndex];\n    try {\n      if (triggerToDelete.PageTargetId) {\n        const pageTargetId = triggerToDelete.PageTargetId;\n        const reqObj = {\n          currentGuideId,\n          pageTargetId\n        };\n        const response = await DeletePageTarget(reqObj);\n        if (response.Success) {\n          openSnackbar(translate(response.SuccessMessage), \"success\");\n          const updatedTriggers = triggers.filter((_, i) => i !== deleteIndex);\n          setTriggers(updatedTriggers);\n        } else {\n          openSnackbar(translate(response.ErrorMessage), \"error\");\n        }\n      } else {\n        if (triggers.length > 1) {\n          const updatedTriggers = triggers.filter((_, i) => i !== deleteIndex);\n          setTriggers(updatedTriggers);\n        }\n      }\n    } catch (error) {\n      console.error(\"Error deleting page target:\", error);\n      openSnackbar(translate(\"Failed to delete the trigger. Please try again.\"), \"error\");\n    } finally {\n      // Reset the state regardless of the outcome\n      setDeleteIndex(null);\n      setDialogOpen(false);\n    }\n  };\n  const handleOperatorChange = (index, value) => {\n    const updatedTriggers = [...triggers];\n\n    // Since logicalOperator is for the next trigger, update the next trigger's operator\n    if (index < updatedTriggers.length - 1) {\n      updatedTriggers[index + 1].logicalOperator = value; // Update the next trigger's operator\n    }\n    setTriggers(updatedTriggers);\n  };\n  const [originalPageTargets, setOriginalPageTargets] = useState((guide === null || guide === void 0 ? void 0 : guide.PageTargets) || []);\n  useEffect(() => {\n    if (guide !== null && guide !== void 0 && guide.PageTargets && (guide === null || guide === void 0 ? void 0 : guide.PageTargets.length) > 0) {\n      setOriginalPageTargets(guide === null || guide === void 0 ? void 0 : guide.PageTargets);\n    }\n  }, [guide === null || guide === void 0 ? void 0 : guide.PageTargets]);\n  const handleSavePageTriggers = async () => {\n    setHasChanges(false);\n    const newTriggers = triggers.filter(trigger => !trigger.PageTargetId);\n    const existingTriggers = triggers.filter(trigger => trigger.PageTargetId);\n    if (newTriggers.length > 0) {\n      const formattedNewPageTargets = newTriggers.map(trigger => ({\n        PageTargetId: \"\",\n        Condition: trigger.pageRule,\n        Operator: (trigger === null || trigger === void 0 ? void 0 : trigger.logicalOperator) || 'OR',\n        Value: trigger.url,\n        GuideId: currentGuideId,\n        OrganizationId: \"\",\n        AccountId: accountId\n      }));\n      try {\n        const response = await SavePageTarget(formattedNewPageTargets);\n        if (response.Success) {\n          openSnackbar(translate(response.SuccessMessage), \"success\");\n          setPageTargetsSaved(true);\n          setTriggers(prev => prev.filter(trigger => !!trigger.PageTargetId));\n          setNewTriggersAdded(false);\n        } else {\n          openSnackbar(translate(response.ErrorMessage), \"error\");\n        }\n      } catch (error) {\n        console.error(\"Error saving new page targets:\", error);\n      }\n    }\n\n    // Updating existing page targets\n    const modifiedTriggers = existingTriggers.filter(trigger => {\n      const originalTrigger = originalPageTargets.find(origTrigger => origTrigger.PageTargetId === trigger.PageTargetId);\n      return originalTrigger && (originalTrigger.Condition !== trigger.pageRule || originalTrigger.Operator !== trigger.logicalOperator || originalTrigger.Value !== trigger.url);\n    });\n    if (modifiedTriggers.length > 0) {\n      const formattedExistingPageTargets = modifiedTriggers.map(trigger => ({\n        PageTargetId: trigger.PageTargetId,\n        Condition: trigger.pageRule,\n        Operator: (trigger === null || trigger === void 0 ? void 0 : trigger.logicalOperator) || 'OR',\n        Value: trigger.url,\n        GuideId: currentGuideId,\n        OrganizationId: \"\"\n      }));\n      try {\n        const response = await UpdatePageTarget(formattedExistingPageTargets);\n        if (response.Success) {\n          openSnackbar(translate(response.SuccessMessage), \"success\");\n          setPageTargetsSaved(true);\n          setNewTriggersAdded(false);\n        } else {\n          openSnackbar(translate(response.ErrorMessage), \"error\");\n        }\n      } catch (error) {\n        console.error(\"Error updating existing page targets:\", error);\n      }\n    }\n    try {\n      const pageTargetsResponse = await GetPageTargets(currentGuideId);\n      if (pageTargetsResponse) {\n        // Map the response to a format suitable for your triggers\n        const updatedPageTargets = pageTargetsResponse.map(trigger => ({\n          pageRule: trigger.Condition,\n          url: trigger.Value,\n          logicalOperator: trigger.Operator,\n          PageTargetId: trigger.PageTargetId\n        }));\n\n        // Combine the existing targets and new targets\n        const combinedTriggers = [...updatedPageTargets];\n\n        // Use a Set to keep track of seen URLs\n        // const seenUrls = new Set();\n        // const uniqueTriggers = combinedTriggers.filter(trigger => {\n        //     // If the URL is not in the seen set, add it and return true to keep the trigger\n        //     if (!seenUrls.has(trigger.url)) {\n        //         seenUrls.add(trigger.url);\n        //         return true; // Keep this trigger\n        //     }\n        //     return false; // Ignore this trigger (duplicate)\n        // });\n\n        // Set the original page targets and the unique triggers\n        setOriginalPageTargets(pageTargetsResponse);\n        setTriggers(combinedTriggers);\n      } else {\n        openSnackbar(translate(pageTargetsResponse.ErrorMessage), \"error\");\n      }\n    } catch (error) {\n      console.error(\"Error fetching page targets:\", error);\n    }\n  };\n  const handleFinalSaveClick = async () => {\n    var _guide$GuideDetails16;\n    // Determine the guide status based on changes and current state\n    let newStatus = guideStatus;\n\n    // If the guide was inactive (isUnPublished) but changes were made, set to Draft\n    if (isUnPublished || isPublished && (hasChanges || newTriggersAdded || isEditing || Descriptionvalue !== (guide === null || guide === void 0 ? void 0 : (_guide$GuideDetails16 = guide.GuideDetails) === null || _guide$GuideDetails16 === void 0 ? void 0 : _guide$GuideDetails16.Description))) {\n      newStatus = 'Draft';\n    } else if (isPublished) {\n      newStatus = 'Active';\n    } else if (isUnPublished) {\n      newStatus = 'InActive';\n    } else {\n      newStatus = 'Draft';\n    }\n\n    // Update the state\n    setGuideStatus(newStatus);\n    const newGuide = {\n      GuideId: currentGuideId,\n      GuideType: guideType,\n      Name: guideName.trim(),\n      Content: `${guideType} content`,\n      OrganizationId: organizationId,\n      CreatedDate: CreatedDate,\n      UpdatedDate: new Date().toISOString(),\n      CreatedBy: createdBy,\n      UpdatedBy: updatedBy,\n      Frequency: \"onceInSession\",\n      Segment: \"All users\",\n      AccountId: accountId,\n      GuideStep: guidestep,\n      GuideStatus: newStatus,\n      // Using newly determined status\n      AutoTrigger: true,\n      PublishType: publishOption,\n      UnPublishType: unpublishOption,\n      Description: Descriptionvalue,\n      TargetUrl: targetUrl,\n      //  PublishDate: publishOption === 'custom' ? customPublishDate :  new Date().toISOString(),\n      //  UnpublishDate: unpublishOption === 'custom' ? customUnPublishDate :  new Date().toISOString(),\n      ...((newStatus === 'Draft' || newStatus === 'InActive') && {\n        PublishDate: publishOption === 'custom' ? PublishDate : currentDate,\n        UnPublishDate: unpublishOption === 'custom' ? UnPublishDate : null\n      }),\n      ...(isPublished && {\n        UnpublishDate: unpublishOption === 'custom' ? UnPublishDate : null,\n        PublishDate: publishOption === 'custom' ? PublishDate : currentDate\n      })\n    };\n    if (!pageTargetsSaved && newTriggersAdded) {\n      await handleSavePageTriggers();\n    }\n    const response = await SubmitUpdateGuid(newGuide);\n    if (response.Success === true) {\n      openSnackbar(`${guideName} ${translate(guideType)} ${translate(\"updated Successfully\")}`, \"success\");\n      setPageTargetsSaved(false);\n      setNewTriggersAdded(false);\n\n      // Update states to reflect the new status\n      setIsUnPublished(newStatus === 'InActive');\n    } else {\n      openSnackbar(translate(response.ErrorMessage), \"error\");\n    }\n    setIsEditing(false);\n  };\n  useEffect(() => {\n    if ((guide === null || guide === void 0 ? void 0 : guide.GuideDetails.GuideStatus) === \"Active\" && (guide === null || guide === void 0 ? void 0 : guide.GuideDetails.UnPublishType) === \"custom\" && guide !== null && guide !== void 0 && guide.GuideDetails.UnPublishDate) {\n      const interval = setInterval(async () => {\n        const now = new Date();\n        const unpublishDate = guide.GuideDetails.UnPublishDate ? new Date(guide.GuideDetails.UnPublishDate) : null;\n        if (unpublishDate && now >= unpublishDate) {\n          await HandlePublishToggle();\n          const details = await GetGudeDetailsByGuideId(currentGuideId);\n          if (details) {\n            setGuide(details);\n            setGuideStatus(details.GuideDetails.GuideStatus);\n            setIsPublished(details.GuideDetails.GuideStatus === \"Active\");\n            setIsUnPublished(details.GuideDetails.GuideStatus === \"InActive\");\n          }\n          clearInterval(interval);\n        }\n      }, 10);\n      return () => clearInterval(interval);\n    }\n  }, [guide]);\n  const {\n    isExtensionInstalled,\n    checkExtensionStatus\n  } = useExtension();\n  const [showExtensionRequiredPopup, setShowExtensionRequiredPopup] = useState(false);\n  const handleEditInBuilder = () => {\n    checkExtensionStatus();\n    setTimeout(() => {\n      if (isExtensionInstalled) {\n        openGuideInBuilder(targetUrl, currentGuideId, accountId);\n      } else {\n        setShowExtensionRequiredPopup(true);\n      }\n    }, 100);\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"xl\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-web\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-webcontent\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-setting-title\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-backbtn\",\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              alignItems: \"center\",\n              className: \"qadpt-titsec-grid\",\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: handleBackClick,\n                  children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 731,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 730,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 729,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  className: \"qadpt-back-text\",\n                  onClick: handleBackClick,\n                  children: [translate('Back to'), \" \", `${translate(guideType)}`]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 736,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 735,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 728,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 727,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              className: \"qadpt-titsec\",\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  className: \"qadpt-name-box\",\n                  children: [isEditing ? /*#__PURE__*/_jsxDEV(TextField, {\n                    name: \"GuideName\",\n                    variant: \"outlined\",\n                    value: guideName,\n                    onChange: handleChange,\n                    onKeyDown: handleKeyDown,\n                    helperText: errors.GuideName,\n                    error: !!errors.GuideName,\n                    sx: {\n                      marginRight: '16px',\n                      width: '300px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 748,\n                    columnNumber: 45\n                  }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h5\",\n                    fontWeight: \"bold\",\n                    className: \"qadpt-name-text\",\n                    children: guideName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 759,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [\"Account Admin\", \"Editor\"].some(role => roles.includes(role)) && /*#__PURE__*/_jsxDEV(IconButton, {\n                      onClick: isEditing ? handleSave : handleEditClick,\n                      disabled: isEditing && !!errors.GuideName,\n                      children: isEditing ? /*#__PURE__*/_jsxDEV(Tooltip, {\n                        arrow: true,\n                        title: translate(\"Save\"),\n                        children: /*#__PURE__*/_jsxDEV(\"img\", {\n                          src: Saveicon,\n                          alt: \"saveicon\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 773,\n                          columnNumber: 53\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 768,\n                        columnNumber: 63\n                      }, this) : /*#__PURE__*/_jsxDEV(Tooltip, {\n                        arrow: true,\n                        title: translate(\"Edit\"),\n                        children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 777,\n                          columnNumber: 57\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 776,\n                        columnNumber: 53\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 765,\n                      columnNumber: 45\n                    }, this)\n                  }, void 0, false)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 746,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 745,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                children: /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    children: /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outlined\",\n                      color: \"primary\",\n                      className: \"qadpt-action-btn\",\n                      startIcon: /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: OpeninNewWindow,\n                        alt: \"openinnewwindow\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 793,\n                        columnNumber: 60\n                      }, this),\n                      onClick: handleEditInBuilder,\n                      disabled: ![\"Account Admin\", \"Editor\"].some(role => roles.includes(role)),\n                      children: [translate(\"Edit\"), \" \", translate(guideType)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 789,\n                      columnNumber: 45\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 788,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    children: /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outlined\",\n                      onClick: handleFinalSaveClick,\n                      disabled: ![\"Account Admin\", \"Editor\"].some(role => roles.includes(role)),\n                      className: \"qadpt-action-btn\",\n                      startIcon: /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: Saveicon,\n                        alt: \"saveicon\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 806,\n                        columnNumber: 60\n                      }, this),\n                      children: translate(\"Save\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 801,\n                      columnNumber: 45\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 800,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    children: /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"contained\",\n                      onClick: HandlePublishToggle,\n                      disabled: ![\"Account Admin\", \"Publisher\"].some(role => roles.includes(role)),\n                      className: `qadpt-action-btn qadpt-action-btn-primary ${isPublished ? 'qadpt-unpublish' : ''}`,\n                      startIcon: /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: isPublished ? cloudoff : PublishIcon,\n                        alt: isPublished ? 'cloudoff' : 'PublishIcon'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 816,\n                        columnNumber: 156\n                      }, this),\n                      children: isPublished ? translate('UnPublish') : translate('Publish')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 812,\n                      columnNumber: 45\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 811,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 787,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 786,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 744,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 743,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 726,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-set-left\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-description\",\n              children: /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"qadpt-card\",\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      htmlFor: \"description\",\n                      className: \"qadpt-label\",\n                      children: translate(\"Description\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 843,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                      id: \"description\",\n                      value: Descriptionvalue,\n                      maxLength: 500,\n                      placeholder: translate(\"Enter your description here (max 500 characters)\"),\n                      onChange: e => setDescriptionValue(e.target.value)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 844,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 842,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 841,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 840,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 839,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-buildsteps\",\n              children: /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"qadpt-card qadpt-buildcard\",\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"qadpt-label\",\n                      children: translate(\"Build Steps\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 862,\n                      columnNumber: 45\n                    }, this), guidestep.length > 0 && /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      sx: {\n                        marginTop: '10',\n                        fontSize: '1rem',\n                        color: '#555'\n                      },\n                      children: [guidestep.length, \" step\", guidestep.length > 1 ? 's' : '']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 867,\n                      columnNumber: 26\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"qadpt-build-content\",\n                      children: guidestep.length > 0 ? guidestep.map((step, index) => /*#__PURE__*/_jsxDEV(Box, {\n                        className: \"qadpt-steps\",\n                        children: /*#__PURE__*/_jsxDEV(TextField, {\n                          variant: \"outlined\",\n                          multiline: true,\n                          minRows: 1,\n                          maxRows: 1,\n                          fullWidth: true,\n                          tabIndex: -1 // Added tabIndex=\"-1\" to make it unfocusable via tab\n                          ,\n                          inputProps: {\n                            tabIndex: -1\n                          } // Make the input element unfocusable\n                          ,\n                          sx: {\n                            borderRadius: \"8px\",\n                            '& .MuiOutlinedInput-root': {\n                              '& fieldset': {\n                                borderColor: 'transparent'\n                              },\n                              '&:hover fieldset': {\n                                borderColor: 'transparent'\n                              },\n                              '&.Mui-focused fieldset': {\n                                borderColor: 'transparent'\n                              }\n                            },\n                            '& textarea': {\n                              overflow: \"hidden\",\n                              resize: \"none\",\n                              paddingTop: \"4px\",\n                              lineHeight: \"1.2\"\n                            }\n                          },\n                          InputProps: {\n                            endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                              position: \"end\",\n                              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                                tabIndex: -1,\n                                children: [\" \", /*#__PURE__*/_jsxDEV(DrawOutlinedIcon, {}, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 907,\n                                  columnNumber: 58\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 906,\n                                columnNumber: 54\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 905,\n                              columnNumber: 50\n                            }, this),\n                            readOnly: true // Added readOnly property to enforce read-only behavior\n                          },\n                          defaultValue: `${(step === null || step === void 0 ? void 0 : step.StepTitle) || `Step-${index + 1}`}\\n${targetUrl}`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 875,\n                          columnNumber: 38\n                        }, this)\n                      }, step.StepId || index, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 874,\n                        columnNumber: 34\n                      }, this)) : /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body1\",\n                        sx: {\n                          marginTop: '16px',\n                          color: '#888'\n                        },\n                        children: translate(\"No steps available. Please check back later.\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 918,\n                        columnNumber: 30\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 871,\n                      columnNumber: 22\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 861,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 860,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 859,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 858,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 838,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-set-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-page-target\",\n              children: /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"qadpt-card qadpt-target\",\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"qadpt-header-container\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"qadpt-label-sublabel\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"qadpt-label\",\n                          children: translate(\"Page Targeting\")\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 940,\n                          columnNumber: 53\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"qadpt-sublabel\",\n                          children: translate(\"Which pages should the guide be visible?\")\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 941,\n                          columnNumber: 53\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 939,\n                        columnNumber: 49\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(Button, {\n                          variant: \"outlined\",\n                          className: \"qadpt-add-target-btn\",\n                          onClick: handleAddTrigger,\n                          disabled: triggers.length >= 5,\n                          startIcon: /*#__PURE__*/_jsxDEV(AddOutlinedIcon, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 953,\n                            columnNumber: 68\n                          }, this),\n                          children: translate(\"Add Target\")\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 948,\n                          columnNumber: 53\n                        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                          onClick: handleSavePageTriggers,\n                          disabled: !hasChanges || errorMessage.length > 0,\n                          className: \"qadpt-add-target-btn save\",\n                          style: {\n                            opacity: !hasChanges || errorMessage.length > 0 ? 0.5 : 1\n                          },\n                          children: /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: Saveicon,\n                            alt: \"saveicon\",\n                            style: {\n                              opacity: !hasChanges || errorMessage.length > 0 ? 0.5 : 1\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 959,\n                            columnNumber: 57\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 958,\n                          columnNumber: 53\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 947,\n                        columnNumber: 49\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 938,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"qadpt-conditions\",\n                      children: [triggers.length > 1 && /*#__PURE__*/_jsxDEV(FormControl, {\n                        className: \"qadpt-operator\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          children: translate(\"Condition\")\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 966,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(Select, {\n                          value: 'OR',\n                          disabled: true,\n                          onChange: e => handleOperatorChange(0, e.target.value) // Adjust handler as needed\n                          ,\n                          children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                            value: \"AND\",\n                            children: translate(\"AND\")\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 972,\n                            columnNumber: 61\n                          }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                            value: \"OR\",\n                            children: translate(\"OR\")\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 973,\n                            columnNumber: 61\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 967,\n                          columnNumber: 57\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 965,\n                        columnNumber: 53\n                      }, this), triggers.map((trigger, index) => /*#__PURE__*/_jsxDEV(Grid, {\n                        container: true,\n                        spacing: 2,\n                        children: [/*#__PURE__*/_jsxDEV(Grid, {\n                          item: true,\n                          xs: 6,\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                              className: \"qadpt-field-label\",\n                              children: translate(\"Page Rule\")\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 982,\n                              columnNumber: 65\n                            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                              fullWidth: true,\n                              children: /*#__PURE__*/_jsxDEV(Select, {\n                                value: trigger.pageRule,\n                                onChange: e => handleTriggerChange(index, 'pageRule', e.target.value),\n                                renderValue: selected => {\n                                  var _pageRuleOptions$find;\n                                  return ((_pageRuleOptions$find = pageRuleOptions.find(opt => opt.value === selected)) === null || _pageRuleOptions$find === void 0 ? void 0 : _pageRuleOptions$find.label) || selected;\n                                },\n                                sx: {\n                                  \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                                    border: \"1px solid #ccc\"\n                                  },\n                                  \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                                    border: \"1px solid #ccc\"\n                                  },\n                                  \"&.MuiOutlinedInput-notchedOutline\": {\n                                    height: \"40px !important\"\n                                  }\n                                },\n                                children: pageRuleOptions.map(option => /*#__PURE__*/_jsxDEV(MenuItem, {\n                                  value: option.value,\n                                  sx: {\n                                    fontSize: '14px',\n                                    display: 'flex',\n                                    justifyContent: 'space-between'\n                                  },\n                                  children: [option.label, /*#__PURE__*/_jsxDEV(\"img\", {\n                                    src: checksmall,\n                                    alt: \"check\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 1009,\n                                    columnNumber: 81\n                                  }, this)]\n                                }, option.value, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1003,\n                                  columnNumber: 77\n                                }, this))\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 984,\n                                columnNumber: 69\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 983,\n                              columnNumber: 65\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 981,\n                            columnNumber: 62\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 980,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                          item: true,\n                          xs: 5,\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                              className: \"qadpt-field-label\",\n                              children: translate(\"URL\")\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1020,\n                              columnNumber: 65\n                            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                              fullWidth: true,\n                              value: trigger.url,\n                              onChange: e => handleTriggerChange(index, 'url', e.target.value),\n                              error: !!errorMessage[index],\n                              helperText: !!errorMessage[index] && /*#__PURE__*/_jsxDEV(\"span\", {\n                                style: {\n                                  display: \"flex\",\n                                  alignItems: \"center\",\n                                  gap: \"5px\"\n                                },\n                                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                                  src: Warning,\n                                  alt: \"error-icon\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1029,\n                                  columnNumber: 21\n                                }, this), errorMessage[index]]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1028,\n                                columnNumber: 17\n                              }, this),\n                              InputProps: {\n                                sx: {\n                                  \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                                    border: \"1px solid #ccc\" // Border on hover\n                                  },\n                                  \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                                    border: \"1px solid #ccc\" // Removes the blue border on focus\n                                  },\n                                  \"&.MuiOutlinedInput-notchedOutline\": {\n                                    height: \"40px !important\"\n                                  },\n                                  \"&.Mui-error .MuiOutlinedInput-notchedOutline\": {\n                                    borderColor: \"#e6a957 !important\" // Custom error border color\n                                  }\n                                }\n                              }\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1021,\n                              columnNumber: 5\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1019,\n                            columnNumber: 57\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1018,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                          item: true,\n                          xs: 1,\n                          className: `qadpt-btngrid ${!!errorMessage[index] ? 'qadpt-error-btn' : ''}`,\n                          style: {\n                            opacity: triggers.length === 1 ? 0.5 : 1\n                          },\n                          children: /*#__PURE__*/_jsxDEV(IconButton, {\n                            onClick: () => {\n                              setDeleteIndex(index); // Set the index of the trigger to delete\n                              setDialogOpen(true); // Open the confirmation dialog\n                            },\n                            disabled: triggers.length === 1,\n                            children: /*#__PURE__*/_jsxDEV(\"img\", {\n                              src: Targetdelete,\n                              alt: \"tardelete\",\n                              style: {\n                                opacity: triggers.length === 1 ? 0.5 : 1\n                              }\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1059,\n                              columnNumber: 51\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1054,\n                            columnNumber: 61\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1053,\n                          columnNumber: 57\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 978,\n                        columnNumber: 53\n                      }, this)), dialogOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"qadpt-modal-overlay\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"qadpt-usrconfirm-popup qadpt-danger\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"qadpt-icon\",\n                              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                                className: \"qadpt-svg\",\n                                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                                  className: \"fal fa-trash-alt\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1113,\n                                  columnNumber: 37\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1111,\n                                columnNumber: 15\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1110,\n                              columnNumber: 13\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1109,\n                            columnNumber: 11\n                          }, this), \"                                                      \", /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"qadpt-popup-title\",\n                            children: translate(\"Delete Trigger\")\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1116,\n                            columnNumber: 121\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"qadpt-warning\",\n                            children: translate(\"Are you sure you want to delete this trigger?\")\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1120,\n                            columnNumber: 13\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"qadpt-buttons\",\n                            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                              onClick: () => setDialogOpen(false),\n                              className: \"qadpt-cancel-button\",\n                              children: translate(\"Cancel\")\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1125,\n                              columnNumber: 9\n                            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                              onClick: handleDeleteTrigger,\n                              className: \"qadpt-conform-button\",\n                              type: \"button\",\n                              children: translate(\"Confirm\")\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1132,\n                              columnNumber: 9\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1124,\n                            columnNumber: 7\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1108,\n                          columnNumber: 9\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1107,\n                        columnNumber: 12\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 963,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 936,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 935,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 934,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 933,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-auto-trigger\",\n              children: /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"qadpt-card qadpt-trigger\",\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(Grid, {\n                      container: true,\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"qadpt-label\",\n                        children: translate(\"Auto Trigger\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1160,\n                        columnNumber: 49\n                      }, this), /*#__PURE__*/_jsxDEV(Switch, {\n                        checked: true,\n                        onChange: e => setIsAutoTriggerEnabled(e.target.checked),\n                        disabled: true\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1161,\n                        columnNumber: 49\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1159,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"qadpt-sublabel\",\n                      style: {\n                        lineHeight: 1\n                      },\n                      children: [translate(\"Enable this to automatically display the\"), \" \", translate(guideType), \" \", translate(\"on target pages, or trigger manually via checklists.\")]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1167,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1158,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1157,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1156,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1154,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-frequency\",\n              children: /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"qadpt-card\",\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"qadpt-label\",\n                      children: translate(\"Frequency\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1182,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"qadpt-sublabel\",\n                      children: translate(\"How often do you want it to trigger\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1183,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                      component: \"fieldset\",\n                      disabled: !isAutoTriggerEnabled,\n                      children: /*#__PURE__*/_jsxDEV(Grid, {\n                        container: true,\n                        spacing: 1,\n                        alignItems: \"center\",\n                        wrap: \"nowrap\",\n                        children: [/*#__PURE__*/_jsxDEV(Grid, {\n                          item: true,\n                          children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                            control: /*#__PURE__*/_jsxDEV(Radio, {\n                              checked: selectedFrequency === 'onlyOnce',\n                              onChange: () => handleFrequencyChange('onlyOnce'),\n                              disabled: true\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1189,\n                              columnNumber: 65\n                            }, this),\n                            label: /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"qadpt-freqselect\",\n                              children: translate(\"Only Once\")\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1195,\n                              columnNumber: 68\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1187,\n                            columnNumber: 57\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1186,\n                          columnNumber: 53\n                        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                          item: true,\n                          children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                            control: /*#__PURE__*/_jsxDEV(Radio, {\n                              checked: true,\n                              onChange: () => handleFrequencyChange('onceInSession')\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1201,\n                              columnNumber: 65\n                            }, this),\n                            label: /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"qadpt-freqselect\",\n                              children: translate(\"Once in a session\")\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1207,\n                              columnNumber: 68\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1199,\n                            columnNumber: 57\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1198,\n                          columnNumber: 53\n                        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                          item: true,\n                          children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                            control: /*#__PURE__*/_jsxDEV(Radio, {\n                              checked: selectedFrequency === 'onceADay',\n                              onChange: () => handleFrequencyChange('onceADay'),\n                              disabled: true\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1213,\n                              columnNumber: 65\n                            }, this),\n                            label: /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"qadpt-freqselect\",\n                              children: translate(\"Once a day\")\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1219,\n                              columnNumber: 68\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1211,\n                            columnNumber: 57\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1210,\n                          columnNumber: 53\n                        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                          item: true,\n                          children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                            control: /*#__PURE__*/_jsxDEV(Radio, {\n                              checked: (selectedFrequency === null || selectedFrequency === void 0 ? void 0 : selectedFrequency.startsWith('onceIn')) && selectedFrequency !== 'onceInSession',\n                              onChange: () => handleFrequencyChange('onceIn'),\n                              disabled: true\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1225,\n                              columnNumber: 65\n                            }, this),\n                            label: /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"qadpt-freqselect\",\n                              children: translate(\"Once in\")\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1231,\n                              columnNumber: 68\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1223,\n                            columnNumber: 57\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1222,\n                          columnNumber: 53\n                        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                          item: true,\n                          children: /*#__PURE__*/_jsxDEV(FormControl, {\n                            sx: {\n                              marginLeft: '8px',\n                              minWidth: '80px'\n                            },\n                            disabled: !isAutoTriggerEnabled || !(selectedFrequency !== null && selectedFrequency !== void 0 && selectedFrequency.startsWith('onceIn')),\n                            children: /*#__PURE__*/_jsxDEV(Select, {\n                              labelId: \"frequency-select-label\",\n                              value: frequencyDropdown,\n                              defaultValue: \"2days\",\n                              onChange: handleFrequencyDropdown,\n                              disabled: true,\n                              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                                value: \"2days\",\n                                children: translate(\"2 days\")\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1243,\n                                columnNumber: 65\n                              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                                value: \"3days\",\n                                children: translate(\"3 days\")\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1244,\n                                columnNumber: 65\n                              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                                value: \"Week\",\n                                children: translate(\"Week\")\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1245,\n                                columnNumber: 65\n                              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                                value: \"month\",\n                                children: translate(\"Month\")\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1246,\n                                columnNumber: 65\n                              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                                value: \"Quarter\",\n                                children: translate(\"Quarter\")\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1247,\n                                columnNumber: 65\n                              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                                value: \"Semi-Yearly\",\n                                children: translate(\"Semi-Yearly\")\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1248,\n                                columnNumber: 65\n                              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                                value: \"Yearly\",\n                                children: translate(\"Yearly\")\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1249,\n                                columnNumber: 65\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1236,\n                              columnNumber: 61\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1235,\n                            columnNumber: 57\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1234,\n                          columnNumber: 53\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1185,\n                        columnNumber: 49\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1184,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1181,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1180,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1179,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1177,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-rev-publish\",\n              children: /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"qadpt-card qadpt-rev\",\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"qadpt-label\",\n                      children: translate(\"Review & Publish\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1268,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                      container: true,\n                      spacing: 2,\n                      children: [/*#__PURE__*/_jsxDEV(Grid, {\n                        item: true,\n                        xs: 6,\n                        className: \"qadpt-gridleft\",\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          children: [translate(\"Publish\"), \" \", `${translate(guideType)}`, \":\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1271,\n                          columnNumber: 53\n                        }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                          component: \"fieldset\",\n                          children: /*#__PURE__*/_jsxDEV(RadioGroup, {\n                            value: publishOption,\n                            onChange: e => setPublishOption(e.target.value),\n                            children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n                              value: \"immediately\",\n                              control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1279,\n                                columnNumber: 74\n                              }, this),\n                              label: translate(\"Immediately\")\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1277,\n                              columnNumber: 61\n                            }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                              value: \"custom\",\n                              control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1284,\n                                columnNumber: 74\n                              }, this),\n                              label: translate(\"Custom date\")\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1282,\n                              columnNumber: 61\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1273,\n                            columnNumber: 57\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1272,\n                          columnNumber: 53\n                        }, this), publishOption === 'custom' && /*#__PURE__*/_jsxDEV(TextField, {\n                          type: \"datetime-local\",\n                          value: PublishDate,\n                          onChange: handleCustomDateChange,\n                          fullWidth: true,\n                          sx: {\n                            marginTop: '8px'\n                          },\n                          inputProps: {\n                            min: currentDateTime\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1290,\n                          columnNumber: 57\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1270,\n                        columnNumber: 49\n                      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                        item: true,\n                        xs: 6,\n                        className: \"qadpt-gridright\",\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          children: [translate(\"UnPublish\"), \" \", `${translate(guideType)}`, \":\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1304,\n                          columnNumber: 53\n                        }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                          component: \"fieldset\",\n                          children: /*#__PURE__*/_jsxDEV(RadioGroup, {\n                            value: unpublishOption,\n                            onChange: e => setUnpublishOption(e.target.value),\n                            children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n                              value: \"Manually\",\n                              control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1312,\n                                columnNumber: 74\n                              }, this),\n                              label: translate(\"Manually\")\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1310,\n                              columnNumber: 61\n                            }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                              value: \"custom\",\n                              control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1317,\n                                columnNumber: 74\n                              }, this),\n                              label: translate(\"Custom date\")\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1315,\n                              columnNumber: 61\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1306,\n                            columnNumber: 57\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1305,\n                          columnNumber: 53\n                        }, this), unpublishOption === 'custom' && /*#__PURE__*/_jsxDEV(TextField, {\n                          type: \"datetime-local\",\n                          value: UnPublishDate // Use value instead of defaultValue for controlled input\n                          ,\n                          onChange: handleCustomDateChangeTwo // Handle input changes\n                          ,\n                          fullWidth: true,\n                          sx: {\n                            marginTop: '8px'\n                          },\n                          inputProps: {\n                            min: currentDateTime\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1324,\n                          columnNumber: 57\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1303,\n                        columnNumber: 49\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1269,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1267,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1266,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1265,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1264,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 930,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 837,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 725,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 724,\n      columnNumber: 13\n    }, this), showExtensionRequiredPopup && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        width: '100vw',\n        height: '100vh',\n        backgroundColor: 'rgba(0,0,0,0.5)',\n        zIndex: 999\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1347,\n      columnNumber: 5\n    }, this), showExtensionRequiredPopup && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: '50%',\n        left: '50%',\n        transform: 'translate(-50%, -50%)',\n        zIndex: 1000,\n        backgroundColor: 'white',\n        borderRadius: '8px',\n        boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)'\n      },\n      children: /*#__PURE__*/_jsxDEV(ExtensionRequiredPopup, {\n        setShowPopup: setShowExtensionRequiredPopup,\n        showPopup: showExtensionRequiredPopup,\n        name: guideType\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1351,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1350,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 723,\n    columnNumber: 9\n  }, this);\n};\n_s(WebappSettings, \"3lI+NBPCfRLXVdogpe9XXVu/buc=\", false, function () {\n  return [useTranslation, useLocation, useNavigate, useSnackbar, useParams, useExtension];\n});\n_c = WebappSettings;\nexport default WebappSettings;\nvar _c;\n$RefreshReg$(_c, \"WebappSettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "Container", "Typography", "<PERSON><PERSON>", "Grid", "Switch", "TextField", "Select", "MenuItem", "FormControl", "Card", "<PERSON><PERSON><PERSON><PERSON>", "FormControlLabel", "IconButton", "Box", "InputAdornment", "<PERSON><PERSON><PERSON>", "RadioGroup", "useTranslation", "ArrowBackIcon", "useLocation", "useNavigate", "useParams", "EditIcon", "OpeninNewWindow", "PublishIcon", "checksmall", "Saveicon", "Warning", "Targetdelete", "DrawOutlinedIcon", "Radio", "styled", "UpdateGuidName", "PublishGuide", "UnPublishGuide", "SubmitUpdateGuid", "SavePageTarget", "DeletePageTarget", "UpdatePageTarget", "GetPageTargets", "GetGudeDetailsByGuideId", "useSnackbar", "AddOutlinedIcon", "cloudoff", "openGuideInBuilder", "useExtension", "ExtensionRequiredPopup", "AccountContext", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "WebappSettings", "_s", "_guide$GuideDetails", "_guide$GuideDetails2", "_guide$GuideDetails3", "_guide$GuideDetails4", "_guide$GuideDetails5", "_guide$GuideDetails6", "_guide$GuideDetails7", "_guide$GuideDetails8", "_guide$GuideDetails9", "_guide$GuideDetails0", "_guide$GuideDetails1", "_guide$GuideDetails10", "_guide$GuideDetails11", "_guide$GuideDetails12", "_guide$GuideDetails13", "_guide$GuideDetails14", "t", "translate", "currentDateTime", "Date", "toISOString", "slice", "currentDate", "has<PERSON><PERSON><PERSON>", "set<PERSON>as<PERSON><PERSON><PERSON>", "guide", "setGuide", "pageTargetsSaved", "setPageTargetsSaved", "newTriggersAdded", "setNewTriggersAdded", "location", "navigate", "openSnackbar", "guideName", "setGuideName", "GuideDetails", "Name", "guideId", "currentGuideId", "setGuideId", "organizationId", "setOrganizationId", "OrganizationId", "accountId", "setAccountId", "AccountId", "guideStatus", "setGuideStatus", "GuideStatus", "isEditing", "setIsEditing", "selectedFrequency", "setSelectedFrequency", "Frequency", "guideType", "setguideType", "GuideType", "updatedBy", "setUpdatedBy", "UpdatedBy", "created<PERSON>y", "setCreated<PERSON>y", "CreatedBy", "isAutoTriggerEnabled", "setIsAutoTriggerEnabled", "AutoTrigger", "publishOption", "setPublishOption", "PublishType", "unpublishOption", "setUnpublishOption", "UnPublishType", "Descriptionvalue", "setDescriptionValue", "Description", "targetUrl", "setTargetUrl", "TargetUrl", "CreatedDate", "setCreatedDate", "PublishDate", "setPublishDate", "UnPublishDate", "setUnPublishDate", "frequencyDropdown", "setfrequencyDropdown", "isPublished", "setIsPublished", "isUnPublished", "setIsUnPublished", "dialogOpen", "setDialogOpen", "deleteIndex", "setDeleteIndex", "triggers", "setTriggers", "pageRule", "url", "logicalOperator", "PageTargetId", "errorMessage", "setErrorMessage", "open", "<PERSON><PERSON><PERSON>", "initialGuide", "setInitialGuide", "GuideId", "Publish", "UnPublish", "hasUnsavedChanges", "setHasUnsavedChanges", "customPublishDate", "setCustomPublishDate", "customUnPublishDate", "setCustomUnPublishDate", "roles", "handleCustomDateChange", "event", "target", "value", "handleCustomDateChangeTwo", "guidestep", "setGuidestep", "Array", "isArray", "GuideStep", "CustomDivider", "theme", "display", "alignItems", "textAlign", "content", "margin", "fetchGuideDetails", "details", "UnpublishDate", "pathname", "state", "response", "replace", "HandlePublishToggle", "some", "role", "includes", "handleFinalSaveClick", "result", "Success", "updatedDetails", "SuccessMessage", "error", "console", "handleBackClick", "toLowerCase", "handleEditClick", "setOriginalGuideName", "handleSave", "ErrorMessage", "handleDrawClick", "window", "PageTargets", "mappedTriggers", "map", "trigger", "Condition", "Value", "Operator", "length", "handleAddTrigger", "pageRuleOptions", "label", "handleTriggerChange", "index", "field", "newErrors", "updatedTriggers", "test", "parseFrequency", "frequency", "startsWith", "parts", "split", "base", "dropdownValue", "frequencyBase", "_guide$GuideDetails15", "initialFrequency", "originalGuideName", "errors", "setErrors", "handleChange", "name", "trim", "prev", "handleFrequencyChange", "handleFrequencyDropdown", "e", "handleKeyDown", "key", "handleDeleteTrigger", "triggerToDelete", "pageTargetId", "req<PERSON>bj", "filter", "_", "i", "handleOperatorChange", "originalPageTargets", "setOriginalPageTargets", "handleSavePageTriggers", "newTriggers", "existingTriggers", "formattedNewPageTargets", "modifiedTriggers", "originalTrigger", "find", "origTrigger", "formattedExistingPageTargets", "pageTargetsResponse", "updatedPageTargets", "combinedTriggers", "_guide$GuideDetails16", "newStatus", "newGuide", "Content", "UpdatedDate", "Segment", "interval", "setInterval", "now", "unpublishDate", "clearInterval", "isExtensionInstalled", "checkExtensionStatus", "showExtensionRequiredPopup", "setShowExtensionRequiredPopup", "handleEditInBuilder", "setTimeout", "max<PERSON><PERSON><PERSON>", "children", "className", "container", "item", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onChange", "onKeyDown", "helperText", "GuideName", "sx", "marginRight", "width", "fontWeight", "disabled", "arrow", "title", "src", "alt", "spacing", "color", "startIcon", "xs", "md", "htmlFor", "id", "max<PERSON><PERSON><PERSON>", "placeholder", "marginTop", "fontSize", "step", "multiline", "minRows", "maxRows", "fullWidth", "tabIndex", "inputProps", "borderRadius", "borderColor", "overflow", "resize", "paddingTop", "lineHeight", "InputProps", "endAdornment", "position", "readOnly", "defaultValue", "<PERSON><PERSON><PERSON><PERSON>", "StepId", "style", "opacity", "renderValue", "selected", "_pageRuleOptions$find", "opt", "border", "height", "option", "justifyContent", "gap", "type", "checked", "component", "wrap", "control", "marginLeft", "min<PERSON><PERSON><PERSON>", "labelId", "min", "top", "left", "backgroundColor", "zIndex", "transform", "boxShadow", "setShowPopup", "showPopup", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/components/webappsettingspage/WebAppSettings.tsx"], "sourcesContent": ["import React, { useState, useEffect, ChangeEvent, useContext } from 'react';\r\nimport {\r\n    Con<PERSON><PERSON>, <PERSON>po<PERSON>, Button, Grid, Switch, TextField, Select, MenuItem, FormControl, InputLabel, Card, CardContent, Divider, FormControlLabel, IconButton, Box, InputAdornment, Tooltip, RadioGroup\r\n} from '@mui/material';\r\nimport { useTranslation } from 'react-i18next';\r\nimport UnPublishIcon from '../../assets/icons/UnPublishIcon.svg';\r\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\r\nimport { useLocation, useNavigate, useParams } from 'react-router-dom';\r\nimport EditIcon from '@mui/icons-material/Edit';\r\nimport OpeninNewWindow from '../../assets/icons/OpenNewWindow.svg';\r\nimport PublishIcon from '../../assets/icons/PublishIcon.svg';\r\nimport checksmall from '../../assets/icons/check_small.svg';\r\nimport Saveicon from '../../assets/icons/SaveIcon.svg';\r\nimport Warning from '../../assets/icons/Warning.svg';\r\nimport ShareIcon from '../../assets/icons/ShareIcon.svg';\r\nimport Targetdelete from '../../assets/icons/Targetdelete.svg';\r\nimport DrawOutlinedIcon from '@mui/icons-material/DrawOutlined';\r\nimport Radio, { RadioProps } from '@mui/material/Radio';\r\nimport { styled } from '@mui/material/styles';\r\nimport { SavePageTargets } from '../../models/SavePageTarget';\r\nimport { UpdateGuidName, PublishGuide, UnPublishGuide, SubmitUpdateGuid, SavePageTarget, DeletePageTarget, UpdatePageTarget, GetPageTargets, GetGudeDetailsByGuideId } from '../../services/GuideService';\r\nimport { useSnackbar } from '../../SnackbarContext';\r\nimport DeleteIcon from '@mui/icons-material/Delete';\r\nimport AddOutlinedIcon from '@mui/icons-material/AddOutlined';\r\nimport ConfirmationDialog from './ConfirmationDialog';\r\nimport { cloudoff, Delete } from '../../assets/icons/icons';\r\nimport { getAllGuides } from '../../services/ProfileSettingPageService';\r\nimport { openGuideInBuilder } from '../../utils/openGuideInBuilder';\r\nimport { useExtension } from '../../ExtensionContext';\r\nimport ExtensionRequiredPopup from '../common/ExtensionRequiredPopup';\r\nimport { AccountContext } from '../account/AccountContext';\r\n\r\ninterface Trigger {\r\n    pageRule: string;\r\n    url: string;\r\n    logicalOperator?: string;\r\n    PageTargetId: string;\r\n}\r\ninterface TextFieldProperties {\r\n    Id: string; \r\n    Text: string;\r\n    Alignment: string; \r\n    Hyperlink: string;\r\n    Emoji: string;\r\n    TextProperties: string; \r\n  }\r\ninterface GuideStep {\r\n    TextFieldProperties: TextFieldProperties[]; \r\n   // ImageProperties: ImageProperties[]; // Array of ImageProperties\r\n    setting: string;\r\n    VideoEmbedCode: string; \r\n    StepId: string; \r\n    Overlay: boolean;\r\n    StepTitle: string;\r\n    Arrow: boolean; \r\n    ElementPath: string; \r\n    Classes: string; \r\n    IsClickable: boolean;\r\n    AutoPosition: boolean; \r\n    Position: string; \r\n    HtmlSnippet: string; \r\n    Modal: string;\r\n    Canvas: string; \r\n    Design: string; \r\n    Hotspot: string; \r\n    Tooltip: string; \r\n    Advanced: string; \r\n    Animation: string; \r\n    //ButtonSection: ButtonSection[]; \r\n    //LayoutPositions: SectionLayout[]; \r\n}\r\n\r\n  \r\n  \r\ninterface GuideDetails {\r\n    GuideId: string;\r\n    GuideType: string;\r\n    Description: string; \r\n    Name: string;\r\n    Content: string; \r\n    OrganizationId: string;\r\n    CreatedDate: string | null; \r\n    UpdatedDate: string | null; \r\n    CreatedBy: string;\r\n    UpdatedBy: string;\r\n    TargetUrl: string;\r\n    Frequency: string;\r\n    TemplateId: string; \r\n    Segment: string; \r\n    AccountId: string;\r\n    GuideStatus: string;\r\n    PublishType: string;\r\n    UnPublishType: string; \r\n    PublishDate: string | null; \r\n    UnPublishDate: string | null; \r\n    Visited: boolean; \r\n    VisitedDate: string | null; \r\n    AutoTrigger: boolean;\r\n    GuideStep: GuideStep[];\r\n  }\r\n  \r\n  interface PageTarget {\r\n    PageTargetId: string; \r\n    GuideId: string; \r\n    OrganizationId: string; \r\n    Condition: string; \r\n    Operator: string; \r\n    Value: string;\r\n    CreatedBy: string; \r\n    CreatedDate: string; \r\n    UpdatedBy: string; \r\n    UpdatedDate: string; \r\n}\r\n  \r\n  interface Guide {\r\n      GuideDetails: GuideDetails;\r\n      PageTargets: PageTarget[];   \r\n  }\r\nconst WebappSettings: React.FC = () => {\r\n    const { t: translate } = useTranslation();\r\n    const currentDateTime = new Date().toISOString().slice(0, 16);\r\n    const currentDate = new Date();\r\n    // const utcDate = new Date(currentDate);\r\n\r\n    // Add 2 hours to the current date and time\r\n\r\n    // Format both the current date and the unpublish date\r\n    //const currentdatTime = utcDate.toLocaleString();\r\n    // utcDate.setHours(utcDate.getHours() + 2);\r\n   \r\n    const [hasChanges, setHasChanges] = useState(false);\r\n    const [guide, setGuide] = useState<Guide | null>(null);\r\n    const [pageTargetsSaved, setPageTargetsSaved] = useState(false);  // Track if page targets are saved\r\n    const [newTriggersAdded, setNewTriggersAdded] = useState(false);  // Track if new page targets are added\r\n    const location = useLocation();\r\n    const navigate = useNavigate();\r\n    const { openSnackbar } = useSnackbar();\r\n    const [guideName, setGuideName] = useState(guide?.GuideDetails?.Name || \" \");\r\n    const { guideId } = useParams<{ guideId: string }>();\r\n    const [currentGuideId, setGuideId] = useState(guideId || \"\");\r\n    const [organizationId, setOrganizationId] = useState(guide?.GuideDetails.OrganizationId || \"\");\r\n    const [accountId, setAccountId] = useState(guide?.GuideDetails?.AccountId || \"\");\r\n    const [guideStatus, setGuideStatus] = useState(guide?.GuideDetails?.GuideStatus || \"\");\r\n    const [isEditing, setIsEditing] = useState(false);\r\n    const [selectedFrequency, setSelectedFrequency] = useState<string>(guide?.GuideDetails?.Frequency || 'onceInSession');\r\n    const [guideType, setguideType] = useState(guide?.GuideDetails?.GuideType || \"\");\r\n    const [updatedBy, setUpdatedBy] = useState(guide?.GuideDetails?.UpdatedBy);\r\n    const [createdBy, setCreatedBy] = useState(guide?.GuideDetails?.CreatedBy);\r\n    const [isAutoTriggerEnabled, setIsAutoTriggerEnabled] = useState(guide?.GuideDetails?.AutoTrigger);\r\n    const [publishOption, setPublishOption] = useState(guide?.GuideDetails?.PublishType || \"immediately\");\r\n    const [unpublishOption, setUnpublishOption] = useState(guide?.GuideDetails?.UnPublishType || \"Manually\");\r\n    const [Descriptionvalue, setDescriptionValue] = useState(guide?.GuideDetails?.Description);\r\n    const [targetUrl, setTargetUrl] = useState(guide?.GuideDetails?.TargetUrl || \"\");\r\n    const [CreatedDate, setCreatedDate] = useState(guide?.GuideDetails?.CreatedDate);\r\n    const [PublishDate, setPublishDate] = useState(guide?.GuideDetails?.PublishDate || currentDate);\r\n    const [UnPublishDate, setUnPublishDate] = useState(guide?.GuideDetails?.UnPublishDate || currentDate);\r\n    const [frequencyDropdown, setfrequencyDropdown] = useState('');\r\n    const [isPublished, setIsPublished] = useState(false);\r\n    const [isUnPublished, setIsUnPublished] = useState(false);\r\n    const [dialogOpen, setDialogOpen] = useState(false);\r\n    const [deleteIndex, setDeleteIndex] = useState<number | null>(null);\r\n    const [triggers, setTriggers] = useState<Trigger[]>([{ pageRule: \"Equals\", url: guide?.GuideDetails?.TargetUrl || '', logicalOperator: \"\", PageTargetId: \"\" }]);\r\n    const [errorMessage, setErrorMessage] = useState<string[]>([]);\r\n    const [open, setOpen] = useState(false); // Controls the popup visibility\r\n    const [initialGuide, setInitialGuide] = useState({\r\n        GuideId: '',\r\n        GuideType: '',\r\n        Name: '',\r\n        OrganizationId: '',\r\n        CreatedBy: '',\r\n        UpdatedBy: '',\r\n        Frequency: '',\r\n        AccountId: '',\r\n        GuideStatus: '',\r\n        AutoTrigger: false,\r\n        Publish: false,\r\n        UnPublish: false,\r\n        Description: '',\r\n        TargetUrl: '',\r\n    })\r\n\r\n    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false); // To track unsaved changes\r\n    const [customPublishDate, setCustomPublishDate] = useState(\"2024-09-26T07:00\");\r\n    const [customUnPublishDate, setCustomUnPublishDate] = useState(\"2024-09-26T07:00\");\r\n    const { roles } = useContext(AccountContext);\r\n    \r\n   \r\n    \r\n    const handleCustomDateChange = (event: any) => {\r\n        setPublishDate(event.target.value); // Update state with new date value\r\n    };\r\n\r\n    const handleCustomDateChangeTwo = (event: any) => {\r\n        setUnPublishDate(event.target.value); // Update state with new date value\r\n    };\r\n    const [guidestep, setGuidestep] = useState(\r\n        Array.isArray(guide?.GuideDetails.GuideStep)\r\n            ? guide?.GuideDetails.GuideStep\r\n            : []\r\n    );\r\n\r\n    const CustomDivider = styled('div')(({ theme }) => ({\r\n        display: 'flex',\r\n        alignItems: 'center',\r\n        textAlign: 'center',\r\n        // margin: '8px 0',\r\n        '&:before, &:after': {\r\n            content: '\"\"',\r\n            //flex: 1,\r\n            //borderBottom: `1px solid ${theme.palette.divider}`,\r\n            margin: '0 8px',\r\n        },\r\n    }));\r\n    useEffect(() => {\r\n        const fetchGuideDetails = async () => {\r\n            const details = await GetGudeDetailsByGuideId(currentGuideId);\r\n            if (details) {\r\n                setGuide(details)\r\n                setGuideStatus(details.GuideDetails.GuideStatus);\r\n                setIsPublished(details.GuideDetails.GuideStatus === \"Active\");\r\n                setIsUnPublished(details.GuideDetails.GuideStatus === \"InActive\");\r\n                setOrganizationId(details.GuideDetails.OrganizationId);\r\n                setguideType(details.GuideDetails.GuideType);\r\n                setGuideName(details.GuideDetails.Name);\r\n                setAccountId(details.GuideDetails.AccountId);\r\n                setUpdatedBy(details.GuideDetails.UpdatedBy);\r\n                setCreatedBy(details.GuideDetails.CreatedBy);\r\n                setGuidestep(details.GuideDetails.GuideStep);\r\n                setDescriptionValue(details.GuideDetails.Description);\r\n                setSelectedFrequency(details.GuideDetails.Frequency);\r\n                setIsAutoTriggerEnabled(details.GuideDetails.AutoTrigger);\r\n                setPublishOption(details.GuideDetails.publishOption);\r\n                setUnpublishOption(details.GuideDetails.unpublishOption);\r\n                setTargetUrl(details.GuideDetails.TargetUrl);\r\n                setCreatedDate(details.GuideDetails.CreatedDate);\r\n                setPublishDate(details.GuideDetails.PublishDate);\r\n                setUnPublishDate(details.GuideDetails.UnpublishDate);\r\n                navigate(location.pathname, {\r\n                    state: { response: details },\r\n                    replace: true,\r\n                  });\r\n            }\r\n        };\r\n\r\n        fetchGuideDetails();\r\n    }, [currentGuideId, guideStatus]);\r\n\r\n    const HandlePublishToggle = async () => {\r\n        try {\r\n            if (![\"Account Admin\", \"Publisher\"].some(role => roles.includes(role)))\r\n            {\r\n                return;\r\n            }\r\n            await handleFinalSaveClick();\r\n\r\n            if (isPublished) {\r\n                const result = await UnPublishGuide(currentGuideId);\r\n                if (result.Success) {\r\n                    openSnackbar(`${guideName} ${translate(guideType)} ${translate(\"Unpublished Successfully\")}`, \"success\");\r\n                    setGuideStatus(\"InActive\");\r\n                    setIsPublished(false);\r\n                    setIsUnPublished(true);\r\n                    // Fetch and update the latest guide details\r\n                    const updatedDetails = await GetGudeDetailsByGuideId(currentGuideId);\r\n                    if (updatedDetails) {\r\n                        setGuide(updatedDetails);\r\n                        setGuideStatus(updatedDetails.GuideDetails.GuideStatus);\r\n                        setIsPublished(updatedDetails.GuideDetails.GuideStatus === \"Active\");\r\n                        setIsUnPublished(updatedDetails.GuideDetails.GuideStatus === \"InActive\");\r\n                    }\r\n                } else {\r\n                    openSnackbar(translate(result.SuccessMessage), \"error\");\r\n                }\r\n            } else {\r\n\r\n                const result = await PublishGuide(currentGuideId);\r\n                if (result.Success) {\r\n                    openSnackbar(`${guideName} ${translate(guideType)} ${translate(\"Published Successfully\")}`, \"success\");\r\n                    setGuideStatus(\"Active\");\r\n                    setIsPublished(true);\r\n                    setIsUnPublished(false);\r\n                    // Fetch and update the latest guide details\r\n                    const updatedDetails = await GetGudeDetailsByGuideId(currentGuideId);\r\n                    if (updatedDetails) {\r\n                        setGuide(updatedDetails);\r\n                        setGuideStatus(updatedDetails.GuideDetails.GuideStatus);\r\n                        setIsPublished(updatedDetails.GuideDetails.GuideStatus === \"Active\");\r\n                        setIsUnPublished(updatedDetails.GuideDetails.GuideStatus === \"InActive\");\r\n                    }\r\n                } else {\r\n                    openSnackbar(translate(result.SuccessMessage), \"error\");\r\n                }\r\n            }\r\n\r\n        } catch (error) {\r\n            console.error('Error updating guide status:', error);\r\n        }\r\n    };\r\n\r\n\r\n\r\n\r\n    const handleBackClick = () => {\r\n        const GuideType: any = `${guideType.toLowerCase()}s`;\r\n        navigate(`/${GuideType}`);\r\n    };\r\n    const handleEditClick = () => {\r\n        setOriginalGuideName(guideName);\r\n        setIsEditing(true);\r\n    };\r\n    const handleSave = async () => {\r\n        setIsEditing(false);\r\n        try {\r\n            const result = await UpdateGuidName(currentGuideId, organizationId, guideName, accountId, guideType);\r\n            if (result.Success === true) {\r\n                openSnackbar(translate(result.SuccessMessage), \"success\");\r\n            } else {\r\n                openSnackbar(translate(result.ErrorMessage), \"error\");\r\n            }\r\n        } catch (error) {\r\n            console.error('Error updating guide name:', error);\r\n        }\r\n    };\r\n    const handleDrawClick = () => {\r\n        if (targetUrl) {\r\n            window.open(targetUrl, '_blank');\r\n        }\r\n\r\n    };\r\n    useEffect(() => {\r\n        if (guide?.PageTargets) {\r\n            const mappedTriggers = guide?.PageTargets.map((trigger: any) => ({\r\n                pageRule: trigger.Condition,\r\n                url: trigger.Value,\r\n                logicalOperator: trigger.Operator,\r\n                PageTargetId: trigger.PageTargetId,\r\n            }));\r\n            setTriggers(mappedTriggers);\r\n        }\r\n    }, [guide?.PageTargets]);\r\n\r\n    useEffect(() => {\r\n        if (triggers.length === 0) {\r\n            setTriggers([{ pageRule: \"Equals\", url: guide?.GuideDetails.TargetUrl || '', PageTargetId: \"\", logicalOperator: \"\" }]);\r\n        }\r\n    }, [triggers]);\r\n\r\n    const handleAddTrigger = () => {\r\n        if (triggers.length < 5) {\r\n            setTriggers([...triggers, { pageRule: \"Equals\", url: \"\", PageTargetId: \"\", logicalOperator: \"OR\" }]);\r\n            setPageTargetsSaved(false);\r\n            setNewTriggersAdded(true);\r\n            setErrorMessage([]);\r\n        }\r\n    };\r\n    const pageRuleOptions = [\r\n        { value: \"Equals\", label: translate(\"Equals\") },\r\n        { value: \"Not Equals\", label: translate(\"Not Equals\") },\r\n        { value: \"Starts With\", label: translate(\"Starts With\") },\r\n        { value: \"Ends With\", label: translate(\"Ends With\") },\r\n        { value: \"Contains\", label: translate(\"Contains\") },\r\n        { value: \"Not Contains\", label: translate(\"Does Not Contain\") },\r\n    ];\r\n\r\n    const handleTriggerChange = (index: number, field: keyof Trigger, value: string) => {\r\n        const newErrors = [];\r\n        const updatedTriggers = [...triggers];\r\n        updatedTriggers[index][field] = value;\r\n        let error = \"\";    \r\n        if (value.length < 1 && field === 'url')  {\r\n            error = translate(\"Minimum Length : 1 Character\");\r\n        } else if (value.length > 500 ) {\r\n            error = translate(\"Maximum Length : 500 characters.\");\r\n        } else if (/\\s/.test(value) &&  field === 'url') {\r\n            error = translate(\"Restriction : Spaces are not allowed\");\r\n        } \r\n        else {\r\n            setTriggers(updatedTriggers);\r\n        }\r\n        if (error.length > 0) { newErrors[index] = error };\r\n        setErrorMessage(newErrors); \r\n        setHasChanges(true);\r\n\r\n    };\r\n    const parseFrequency = (frequency: any) => {\r\n        if (frequency?.startsWith('onceIn') && frequency !== \"onceInSession\") {\r\n            const parts = frequency.split('-');\r\n            const base = parts[0];\r\n            const dropdownValue = parts.length > 1 ? parts[1] : '';\r\n            return { frequencyBase: base, dropdownValue };\r\n        }\r\n        return { frequencyBase: frequency, dropdownValue: '' };\r\n    };\r\n    useEffect(() => {\r\n        const initialFrequency = guide?.GuideDetails?.Frequency;\r\n        if (initialFrequency !== \"onceInSession\") {\r\n            const { frequencyBase, dropdownValue } = parseFrequency(initialFrequency);\r\n            setSelectedFrequency(frequencyBase);\r\n            setfrequencyDropdown(dropdownValue);\r\n        }\r\n    }, [location.state]);\r\n    const [originalGuideName, setOriginalGuideName] = useState(\"\");\r\n    const [errors, setErrors] = useState<Partial<Record<string, string>>>({});\r\n\r\n    const handleChange = (event: ChangeEvent<HTMLInputElement>) => {\r\n        const { name, value } = event.target;\r\n        let error = \"\";\r\n\r\n        if (name === \"GuideName\" && value.trim().length < 3) {\r\n            error = translate(\"Guide Name must be at least 3 characters.\");\r\n        }\r\n        setGuideName(event.target.value)\r\n        setErrors((prev) => ({ ...prev, [name]: error }));\r\n    };\r\n    useEffect(() => {\r\n        if (isAutoTriggerEnabled) {\r\n            setSelectedFrequency('onceInSession');\r\n        }\r\n    }, [isAutoTriggerEnabled]);\r\n\r\n    const handleFrequencyChange = (value: string) => {\r\n        if (value === \"onceIn\") {\r\n            setSelectedFrequency(`onceIn${frequencyDropdown}`)\r\n        } else {\r\n            setSelectedFrequency(value);\r\n        }\r\n        if (value !== \"onceIn\") {\r\n            setfrequencyDropdown(\"\");\r\n        }\r\n    };\r\n    const handleFrequencyDropdown = (e: any) => {\r\n        const value = e.target.value;\r\n        if (selectedFrequency?.startsWith(\"onceIn\") && selectedFrequency !== \"onceInSession\") {\r\n            setfrequencyDropdown(value);\r\n            setSelectedFrequency(`onceIn-${value}`);\r\n        }\r\n    };\r\n\r\n    const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {\r\n        if (event.key === 'Enter') {\r\n            handleSave();\r\n        } else if (event.key === 'Escape') {\r\n            setGuideName(originalGuideName);\r\n            setIsEditing(false);\r\n        }\r\n    };\r\n    const handleDeleteTrigger = async () => {\r\n        if (deleteIndex === null) return; // No index to delete\r\n\r\n        const triggerToDelete = triggers[deleteIndex];\r\n\r\n        try {\r\n            if (triggerToDelete.PageTargetId) {\r\n                const pageTargetId = triggerToDelete.PageTargetId;\r\n                const reqObj = {\r\n                    currentGuideId,\r\n                    pageTargetId,\r\n                };\r\n                const response = await DeletePageTarget(reqObj);\r\n                if (response.Success) {\r\n                    openSnackbar(translate(response.SuccessMessage), \"success\");\r\n                    const updatedTriggers = triggers.filter((_, i) => i !== deleteIndex);\r\n                    setTriggers(updatedTriggers);\r\n                } else {\r\n                    openSnackbar(translate(response.ErrorMessage), \"error\");\r\n                }\r\n            } else {\r\n                if (triggers.length > 1) {\r\n                    const updatedTriggers = triggers.filter((_, i) => i !== deleteIndex);\r\n                    setTriggers(updatedTriggers);\r\n                }\r\n            }\r\n        } catch (error) {\r\n            console.error(\"Error deleting page target:\", error);\r\n            openSnackbar(translate(\"Failed to delete the trigger. Please try again.\"), \"error\");\r\n        } finally {\r\n            // Reset the state regardless of the outcome\r\n            setDeleteIndex(null);\r\n            setDialogOpen(false);\r\n        }\r\n    };\r\n\r\n    const handleOperatorChange = (index: number, value: string) => {\r\n        const updatedTriggers = [...triggers];\r\n\r\n        // Since logicalOperator is for the next trigger, update the next trigger's operator\r\n        if (index < updatedTriggers.length - 1) {\r\n            updatedTriggers[index + 1].logicalOperator = value; // Update the next trigger's operator\r\n        }\r\n\r\n        setTriggers(updatedTriggers);\r\n    };\r\n\r\n    const [originalPageTargets, setOriginalPageTargets] = useState<any[]>(guide?.PageTargets || []);\r\n\r\n    useEffect(() => {\r\n        if (guide?.PageTargets && guide?.PageTargets.length > 0) {\r\n            setOriginalPageTargets(guide?.PageTargets);\r\n        }\r\n    }, [guide?.PageTargets]);\r\n    const handleSavePageTriggers = async () => {\r\n        setHasChanges(false)\r\n        const newTriggers = triggers.filter(trigger => !trigger.PageTargetId);\r\n        const existingTriggers = triggers.filter(trigger => trigger.PageTargetId);\r\n\r\n        if (newTriggers.length > 0) {\r\n            const formattedNewPageTargets = newTriggers.map((trigger) => ({\r\n                PageTargetId: \"\",\r\n                Condition: trigger.pageRule,\r\n                Operator: trigger?.logicalOperator || 'OR',\r\n                Value: trigger.url,\r\n                GuideId: currentGuideId,\r\n                OrganizationId: \"\",\r\n                AccountId: accountId,\r\n            }));\r\n            try {\r\n                const response = await SavePageTarget(formattedNewPageTargets);\r\n                if (response.Success) {\r\n                    openSnackbar(translate(response.SuccessMessage), \"success\");\r\n                    setPageTargetsSaved(true);\r\n                    setTriggers(prev => prev.filter(trigger => !!trigger.PageTargetId));\r\n                    setNewTriggersAdded(false);\r\n                    \r\n                } else {\r\n                    openSnackbar(translate(response.ErrorMessage), \"error\");\r\n                }\r\n            } catch (error) {\r\n                console.error(\"Error saving new page targets:\", error);\r\n            }\r\n        }\r\n\r\n        // Updating existing page targets\r\n        const modifiedTriggers = existingTriggers.filter((trigger) => {\r\n            const originalTrigger = originalPageTargets.find(\r\n                (origTrigger: any) => origTrigger.PageTargetId === trigger.PageTargetId\r\n            );\r\n\r\n            return originalTrigger && (\r\n                originalTrigger.Condition !== trigger.pageRule ||\r\n                originalTrigger.Operator !== trigger.logicalOperator ||\r\n                originalTrigger.Value !== trigger.url\r\n            );\r\n        });\r\n\r\n        if (modifiedTriggers.length > 0) {\r\n            const formattedExistingPageTargets = modifiedTriggers.map((trigger) => ({\r\n                PageTargetId: trigger.PageTargetId,\r\n                Condition: trigger.pageRule,\r\n                Operator: trigger?.logicalOperator || 'OR',\r\n                Value: trigger.url,\r\n                GuideId: currentGuideId,\r\n                OrganizationId: \"\",\r\n            }));\r\n\r\n            try {\r\n                const response = await UpdatePageTarget(formattedExistingPageTargets);\r\n                if (response.Success) {\r\n                    openSnackbar(translate(response.SuccessMessage), \"success\");\r\n                    setPageTargetsSaved(true);\r\n                    setNewTriggersAdded(false);\r\n                } else {\r\n                    openSnackbar(translate(response.ErrorMessage), \"error\");\r\n                }\r\n            } catch (error) {\r\n                console.error(\"Error updating existing page targets:\", error);\r\n            }\r\n        }\r\n\r\n        try {\r\n            const pageTargetsResponse = await GetPageTargets(currentGuideId);\r\n            if (pageTargetsResponse) {\r\n                // Map the response to a format suitable for your triggers\r\n                const updatedPageTargets = pageTargetsResponse.map((trigger: any) => ({\r\n                    pageRule: trigger.Condition,\r\n                    url: trigger.Value,\r\n                    logicalOperator: trigger.Operator,\r\n                    PageTargetId: trigger.PageTargetId,\r\n                }));\r\n\r\n                // Combine the existing targets and new targets\r\n                const combinedTriggers = [...updatedPageTargets];\r\n\r\n                // Use a Set to keep track of seen URLs\r\n                // const seenUrls = new Set();\r\n                // const uniqueTriggers = combinedTriggers.filter(trigger => {\r\n                //     // If the URL is not in the seen set, add it and return true to keep the trigger\r\n                //     if (!seenUrls.has(trigger.url)) {\r\n                //         seenUrls.add(trigger.url);\r\n                //         return true; // Keep this trigger\r\n                //     }\r\n                //     return false; // Ignore this trigger (duplicate)\r\n                // });\r\n\r\n                // Set the original page targets and the unique triggers\r\n                setOriginalPageTargets(pageTargetsResponse);\r\n                setTriggers(combinedTriggers);\r\n            } else {\r\n                openSnackbar(translate(pageTargetsResponse.ErrorMessage), \"error\");\r\n            }\r\n        } catch (error) {\r\n            console.error(\"Error fetching page targets:\", error);\r\n        }\r\n    };\r\n\r\n\r\n    const handleFinalSaveClick = async () => {\r\n        // Determine the guide status based on changes and current state\r\n        let newStatus = guideStatus;\r\n        \r\n        // If the guide was inactive (isUnPublished) but changes were made, set to Draft\r\n        if (isUnPublished || isPublished && (hasChanges || newTriggersAdded || isEditing || Descriptionvalue !== guide?.GuideDetails?.Description)) {\r\n            newStatus = 'Draft';\r\n        } else if (isPublished) {\r\n            newStatus = 'Active';\r\n        } else if (isUnPublished) {\r\n            newStatus = 'InActive';\r\n        } else {\r\n            newStatus = 'Draft';\r\n        }\r\n    \r\n        // Update the state\r\n        setGuideStatus(newStatus);\r\n    \r\n        const newGuide = {\r\n            GuideId: currentGuideId,\r\n            GuideType: guideType,\r\n            Name: guideName.trim(),\r\n            Content: `${guideType} content`,\r\n            OrganizationId: organizationId,\r\n            CreatedDate: CreatedDate,\r\n            UpdatedDate: new Date().toISOString(),\r\n            CreatedBy: createdBy,\r\n            UpdatedBy: updatedBy,\r\n            Frequency: \"onceInSession\",\r\n            Segment: \"All users\",\r\n            AccountId: accountId,\r\n            GuideStep: guidestep,\r\n            GuideStatus: newStatus, // Using newly determined status\r\n            AutoTrigger: true,\r\n            PublishType: publishOption,\r\n            UnPublishType: unpublishOption,\r\n            Description: Descriptionvalue,\r\n            TargetUrl: targetUrl,\r\n             //  PublishDate: publishOption === 'custom' ? customPublishDate :  new Date().toISOString(),\r\n            //  UnpublishDate: unpublishOption === 'custom' ? customUnPublishDate :  new Date().toISOString(),\r\n            ...((newStatus === 'Draft' || newStatus === 'InActive') && {\r\n                PublishDate: publishOption === 'custom'\r\n                    ? PublishDate\r\n                    : currentDate,\r\n                UnPublishDate: unpublishOption === 'custom'\r\n                    ? UnPublishDate\r\n                    : null,\r\n            }),\r\n    \r\n            ...(isPublished && {\r\n                UnpublishDate: unpublishOption === 'custom'\r\n                    ? UnPublishDate\r\n                    : null,\r\n                PublishDate: publishOption === 'custom'\r\n                    ? PublishDate\r\n                    : currentDate,\r\n            }),\r\n        };\r\n    \r\n        if (!pageTargetsSaved && newTriggersAdded) {\r\n            await handleSavePageTriggers();\r\n        }       \r\n        const response = await SubmitUpdateGuid(newGuide);\r\n    \r\n        if (response.Success === true) {\r\n            openSnackbar(`${guideName} ${translate(guideType)} ${translate(\"updated Successfully\")}`, \"success\");\r\n            setPageTargetsSaved(false);\r\n            setNewTriggersAdded(false);\r\n            \r\n            // Update states to reflect the new status\r\n            setIsUnPublished(newStatus === 'InActive');\r\n        } else {\r\n            openSnackbar(translate(response.ErrorMessage), \"error\");\r\n        }\r\n        setIsEditing(false);\r\n    };\r\n\r\nuseEffect(() => {\r\n    if (\r\n        guide?.GuideDetails.GuideStatus === \"Active\" &&\r\n        guide?.GuideDetails.UnPublishType === \"custom\" &&\r\n        guide?.GuideDetails.UnPublishDate\r\n    ) {\r\n        const interval = setInterval(async () => {\r\n            const now = new Date();\r\n            const unpublishDate = guide.GuideDetails.UnPublishDate ? new Date(guide.GuideDetails.UnPublishDate) : null;\r\n            if (unpublishDate && now >= unpublishDate) {\r\n                await HandlePublishToggle();\r\n                const details = await GetGudeDetailsByGuideId(currentGuideId);\r\n                if (details) {\r\n                    setGuide(details);\r\n                    setGuideStatus(details.GuideDetails.GuideStatus);\r\n                    setIsPublished(details.GuideDetails.GuideStatus === \"Active\");\r\n                    setIsUnPublished(details.GuideDetails.GuideStatus === \"InActive\");\r\n                }\r\n                clearInterval(interval);\r\n            }\r\n        }, 10);\r\n        return () => clearInterval(interval);\r\n    }\r\n}, [guide]);\r\n\r\nconst { isExtensionInstalled, checkExtensionStatus } = useExtension();\r\n    const [showExtensionRequiredPopup, setShowExtensionRequiredPopup] = useState(false);\r\n\r\n    const handleEditInBuilder = () => {\r\n        checkExtensionStatus();\r\n        setTimeout(() => {\r\n            if (isExtensionInstalled) {\r\n                openGuideInBuilder(targetUrl, currentGuideId, accountId);\r\n            } else {\r\n                setShowExtensionRequiredPopup(true);\r\n            }\r\n        }, 100);\r\n    };\r\n\r\n    return (\r\n        <Container maxWidth=\"xl\">\r\n            <div className='qadpt-web'>\r\n                <div className='qadpt-webcontent'>\r\n                    <div className='qadpt-setting-title'>\r\n                        <div className='qadpt-backbtn'>\r\n                            <Grid container alignItems=\"center\" className='qadpt-titsec-grid'>\r\n                                <Grid item>\r\n                                    <IconButton onClick={handleBackClick}>\r\n                                        <ArrowBackIcon />\r\n                                    </IconButton>\r\n                                </Grid>\r\n\r\n                                <Grid item>\r\n                                    <Typography variant=\"body1\" className='qadpt-back-text' onClick={handleBackClick}>\r\n                                        {translate('Back to')} {`${translate(guideType)}`}\r\n                                    </Typography>\r\n                                </Grid>\r\n                            </Grid>\r\n                        </div>\r\n\r\n                        <div>\r\n                            <Grid container className='qadpt-titsec'>\r\n                                <Grid item>\r\n                                    <Box className='qadpt-name-box'>\r\n                                        {isEditing ? (\r\n                                            <TextField\r\n                                                name=\"GuideName\"\r\n                                                variant=\"outlined\"\r\n                                                value={guideName}\r\n                                                onChange={handleChange}\r\n                                                onKeyDown={handleKeyDown}\r\n                                                helperText={errors.GuideName}\r\n                                                error={!!errors.GuideName}\r\n                                                sx={{ marginRight: '16px', width: '300px' }}\r\n                                            />\r\n                                        ) : (\r\n                                            <Typography variant=\"h5\" fontWeight=\"bold\" className='qadpt-name-text'>\r\n                                                {guideName}\r\n                                            </Typography>\r\n                                        )}\r\n                                        <>{\r\n                                            [\"Account Admin\", \"Editor\"].some(role => roles.includes(role)) &&\r\n                                            <IconButton onClick={isEditing ? handleSave : handleEditClick}\r\n                                                disabled={isEditing && !!errors.GuideName}\r\n                                            >\r\n                                                {isEditing ? (<Tooltip\r\n                                                    arrow\r\n                                                    title={translate(\"Save\")}\r\n\r\n                                                >\r\n                                                    <img src={Saveicon} alt='saveicon' />\r\n                                                </Tooltip>\r\n                                                ) : (\r\n                                                    <Tooltip arrow title={translate(\"Edit\")}>\r\n                                                        <EditIcon />\r\n                                                    </Tooltip>\r\n                                            \r\n                                                )}\r\n                                            </IconButton>\r\n                                        }</>\r\n                                    </Box>\r\n                                </Grid>\r\n\r\n                                <Grid item>\r\n                                    <Grid container spacing={1}>\r\n                                        <Grid item>\r\n                                            <Button\r\n                                                variant=\"outlined\"\r\n                                                color=\"primary\"\r\n                                                className=\"qadpt-action-btn\"\r\n                                                startIcon={<img src={OpeninNewWindow} alt=\"openinnewwindow\" />}\r\n                                                onClick={handleEditInBuilder}\r\n                                                disabled={ ![\"Account Admin\",\"Editor\"].some(role => roles.includes(role)) }\r\n                                            >\r\n                                                {translate(\"Edit\")} {translate(guideType)}\r\n                                            </Button>\r\n                                        </Grid>\r\n                                        <Grid item>\r\n                                            <Button\r\n                                                variant=\"outlined\"\r\n                                                onClick={handleFinalSaveClick}\r\n                                                disabled={ ![\"Account Admin\",\"Editor\"].some(role => roles.includes(role)) }\r\n                                                className=\"qadpt-action-btn\"\r\n                                                startIcon={<img src={Saveicon} alt=\"saveicon\" />}\r\n                                            >\r\n                                                {translate(\"Save\")}\r\n                                            </Button>\r\n                                        </Grid>\r\n                                        <Grid item>\r\n                                            <Button\r\n                                                variant=\"contained\"\r\n                                                onClick={HandlePublishToggle}\r\n                                                disabled={ ![\"Account Admin\",\"Publisher\"].some(role => roles.includes(role)) }\r\n                                                className={`qadpt-action-btn qadpt-action-btn-primary ${isPublished ? 'qadpt-unpublish' : ''}`} startIcon={<img src={isPublished ? cloudoff : PublishIcon} alt={isPublished ? 'cloudoff' : 'PublishIcon'} />}\r\n                                            >\r\n                                                {isPublished ? translate('UnPublish') : translate('Publish')}\r\n                                            </Button>\r\n                                        </Grid>\r\n                                        {/* <Grid item>\r\n                                            <Button\r\n                                                variant=\"outlined\"\r\n                                                disabled={true}\r\n                                                className=\"qadpt-action-btn qadpt-share\"\r\n                                                startIcon={<img src={ShareIcon} alt=\"Shareicon\" />}\r\n                                            />\r\n                                        </Grid> */}\r\n                                    </Grid>\r\n                                </Grid>\r\n\r\n                            </Grid>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Description and Auto Trigger */}\r\n                    <div className='qadpt-content'>\r\n                        <div className='qadpt-set-left'>\r\n                            <div className='qadpt-description'>\r\n                                <Grid item xs={12} md={6}>\r\n                                    <Card className='qadpt-card'>\r\n                                        <CardContent>\r\n                                            <label htmlFor=\"description\" className=\"qadpt-label\" >{translate(\"Description\")}</label>\r\n                                            <textarea id=\"description\"\r\n                                                value={Descriptionvalue}\r\n                                                maxLength={500}\r\n                                                placeholder={translate(\"Enter your description here (max 500 characters)\")}\r\n                                                onChange={(e) => setDescriptionValue(e.target.value)} />\r\n\r\n                                            {/* <div className=\"character-count\">\r\n                    Count: {Descriptionvalue.length}/500\r\n                </div> */}\r\n\r\n                                        </CardContent>\r\n                                    </Card>\r\n                                </Grid>\r\n                            </div>\r\n                            <div className='qadpt-buildsteps'>\r\n                                <Grid item xs={12} md={6}>\r\n                                    <Card className='qadpt-card qadpt-buildcard'>\r\n                                        <CardContent>\r\n                                            <div className=\"qadpt-label\">\r\n                                                {translate(\"Build Steps\")}\r\n                                            </div>\r\n\r\n\t\t\t\t\t                {guidestep.length > 0 && (\r\n\t\t\t\t\t                    <Typography variant=\"body1\" sx={{ marginTop: '10', fontSize: '1rem', color: '#555' }}>\r\n\t\t\t\t\t                        {guidestep.length} step{guidestep.length > 1 ? 's' : ''}\r\n\t\t\t\t\t                    </Typography>\r\n\t\t\t\t\t                )}\r\n\t\t\t\t\t                <div className=\"qadpt-build-content\">\r\n\t\t\t\t\t                    {guidestep.length > 0 ? (\r\n\t\t\t\t\t                        guidestep.map((step: any, index: any) => (\r\n\t\t\t\t\t                            <Box key={step.StepId || index} className=\"qadpt-steps\">\r\n\t\t\t\t\t                                <TextField\r\n\t\t\t\t\t                                    variant=\"outlined\"\r\n\t\t\t\t\t                                    multiline\r\n\t\t\t\t\t                                    minRows={1}\r\n\t\t\t\t\t                                    maxRows={1}\r\n\t\t\t\t\t                                    fullWidth\r\n\t\t\t\t\t                                    tabIndex={ -1} // Added tabIndex=\"-1\" to make it unfocusable via tab\r\n\t\t\t\t\t                                    inputProps={{ tabIndex: -1 }} // Make the input element unfocusable\r\n\t\t\t\t\t                                    sx={{\r\n\t\t\t\t\t                                        borderRadius: \"8px\",\r\n\t\t\t\t\t                                        '& .MuiOutlinedInput-root': {\r\n\t\t\t\t\t                                            '& fieldset': {\r\n\t\t\t\t\t                                                borderColor: 'transparent',\r\n\t\t\t\t\t                                            },\r\n\t\t\t\t\t                                            '&:hover fieldset': {\r\n\t\t\t\t\t                                                borderColor: 'transparent',\r\n\t\t\t\t\t                                            },\r\n\t\t\t\t\t                                            '&.Mui-focused fieldset': {\r\n\t\t\t\t\t                                                borderColor: 'transparent',\r\n\t\t\t\t\t                                            },\r\n\t\t\t\t\t                                        },\r\n\t\t\t\t\t                                        '& textarea': {\r\n\t\t\t\t\t                                            overflow: \"hidden\",\r\n\t\t\t\t\t                                            resize: \"none\",\r\n\t\t\t\t\t                                            paddingTop: \"4px\",\r\n\t\t\t\t\t                                            lineHeight: \"1.2\",\r\n\t\t\t\t\t                                        },\r\n\t\t\t\t\t                                    }}\r\n\t\t\t\t\t                                    InputProps={{\r\n\t\t\t\t\t                                        endAdornment: (\r\n\t\t\t\t\t                                            <InputAdornment position=\"end\">\r\n\t\t\t\t\t                                                <IconButton tabIndex={ -1}> {/* Added tabIndex=\"-1\" to IconButton */}\r\n\t\t\t\t\t                                                    <DrawOutlinedIcon />\r\n\t\t\t\t\t                                                </IconButton>\r\n\t\t\t\t\t                                            </InputAdornment>\r\n\t\t\t\t\t                                        ),\r\n\t\t\t\t\t                                        readOnly: true, // Added readOnly property to enforce read-only behavior\r\n\t\t\t\t\t                                    }}\r\n\t\t\t\t\t                                    defaultValue={`${step?.StepTitle || `Step-${index + 1}`}\\n${targetUrl}`}\r\n\t\t\t\t\t                                />\r\n\t\t\t\t\t                            </Box>\r\n\t\t\t\t\t                        ))\r\n\t\t\t\t\t                    ) : (\r\n\t\t\t\t\t                        <Typography variant=\"body1\" sx={{ marginTop: '16px', color: '#888' }}>\r\n                                                            {translate(\"No steps available. Please check back later.\")}\r\n\t\t\t\t\t                        </Typography>\r\n\t\t\t\t\t                    )}\r\n\t\t\t\t\t                </div>\r\n\t\t\t\t\t            </CardContent>\r\n\t\t\t\t\t        </Card>\r\n\t\t\t\t\t    </Grid>\r\n\t\t\t\t\t</div>\r\n\r\n                        </div>\r\n\r\n                        <div className='qadpt-set-right'>\r\n\r\n                            {/* Page Targeting Section */}\r\n                            <div className=\"qadpt-page-target\">\r\n                                <Grid item xs={12} md={6}>\r\n                                    <Card className=\"qadpt-card qadpt-target\">\r\n                                        <CardContent>\r\n                                            {/* Flex container for label, sublabel, and buttons */}\r\n                                            <div className=\"qadpt-header-container\">\r\n                                                <div className=\"qadpt-label-sublabel\">\r\n                                                    <div className=\"qadpt-label\">{translate(\"Page Targeting\")}</div>\r\n                                                    <div className=\"qadpt-sublabel\">\r\n                                                        {translate(\"Which pages should the guide be visible?\")}\r\n                                                    </div>\r\n                                                </div>\r\n\r\n                                                {/* Align Add Target and Save buttons on the same row */}\r\n                                                <div>\r\n                                                    <Button\r\n                                                        variant=\"outlined\"\r\n                                                        className=\"qadpt-add-target-btn\"\r\n                                                        onClick={handleAddTrigger}\r\n                                                        disabled={triggers.length >= 5}\r\n                                                        startIcon={<AddOutlinedIcon />}\r\n                                                    >\r\n                                                        {translate(\"Add Target\")}\r\n                                                    </Button>\r\n\r\n                                                    <IconButton onClick={handleSavePageTriggers} disabled={ !hasChanges || errorMessage.length > 0} className='qadpt-add-target-btn save'  style={{ opacity: !hasChanges || errorMessage.length > 0 ? 0.5 : 1 }} >                                                        \r\n                                                        <img src={Saveicon} alt=\"saveicon\"   style={{ opacity: !hasChanges || errorMessage.length > 0 ? 0.5 : 1 }} />\r\n                                                    </IconButton>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div className='qadpt-conditions'>  \r\n                                            {triggers.length > 1 && (\r\n                                                    <FormControl className='qadpt-operator'>\r\n                                                        <span>{translate(\"Condition\")}</span>\r\n                                                        <Select\r\n                                                            value={'OR'}\r\n                                                            disabled\r\n                                                            onChange={(e) => handleOperatorChange(0, e.target.value)} // Adjust handler as needed\r\n                                                        >\r\n                                                            <MenuItem value=\"AND\">{translate(\"AND\")}</MenuItem>\r\n                                                            <MenuItem value=\"OR\">{translate(\"OR\")}</MenuItem>\r\n                                                        </Select>\r\n                                                    </FormControl>\r\n                                                )}    \r\n                                                {triggers.map((trigger, index) => (  \r\n                                                    <Grid container spacing={2} key={index}>\r\n                                                       \r\n                                                        <Grid item xs={6}>\r\n                                                             <div>\r\n                                                                <label className=\"qadpt-field-label\">{translate(\"Page Rule\")}</label>\r\n                                                                <FormControl fullWidth>\r\n                                                                    <Select\r\n                                                                        value={trigger.pageRule}\r\n                                                                        onChange={(e) => handleTriggerChange(index, 'pageRule', e.target.value)}\r\n                                                                        renderValue={(selected) =>\r\n                                                                            pageRuleOptions.find(opt => opt.value === selected)?.label || selected\r\n                                                                        }\r\n                                                                        sx={{\r\n                                                                            \"&:hover .MuiOutlinedInput-notchedOutline\": {\r\n                                                                                border: \"1px solid #ccc\",\r\n                                                                            },\r\n                                                                            \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\r\n                                                                                border: \"1px solid #ccc\",\r\n                                                                            },\r\n                                                                            \"&.MuiOutlinedInput-notchedOutline\": {\r\n                                                                                height: \"40px !important\",\r\n                                                                            },\r\n                                                                        }}\r\n                                                                    >\r\n                                                                        {pageRuleOptions.map((option) => (\r\n                                                                            <MenuItem\r\n                                                                                key={option.value}\r\n                                                                                value={option.value}\r\n                                                                                sx={{ fontSize: '14px', display: 'flex', justifyContent: 'space-between' }}\r\n                                                                            >\r\n                                                                                {option.label}\r\n                                                                                <img src={checksmall} alt=\"check\" />\r\n                                                                            </MenuItem>\r\n                                                                        ))}\r\n                                                                    </Select>\r\n                                                                </FormControl>\r\n\r\n</div>\r\n\r\n                                                        </Grid>\r\n                                                        <Grid item xs={5}>\r\n                                                        <div>\r\n                                                                <label className=\"qadpt-field-label\">{translate(\"URL\")}</label>\r\n    <TextField\r\n        fullWidth\r\n        value={trigger.url}\r\n        onChange={(e) => handleTriggerChange(index, 'url', e.target.value)}\r\n        error={!!errorMessage[index]} \r\n        helperText={\r\n            !!errorMessage[index] && (\r\n                <span style={{ display: \"flex\", alignItems: \"center\", gap: \"5px\" }}>\r\n                    <img src={Warning} alt=\"error-icon\"/>\r\n                    {errorMessage[index]}\r\n                </span>\r\n            )\r\n        }\r\n        InputProps={{\r\n            sx: {\r\n                \"&:hover .MuiOutlinedInput-notchedOutline\": {\r\n                    border: \"1px solid #ccc\", // Border on hover\r\n                },\r\n              \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\r\n                border: \"1px solid #ccc\", // Removes the blue border on focus\r\n                },\r\n                \"&.MuiOutlinedInput-notchedOutline\": {\r\n                    height :\"40px !important\",\r\n                },\r\n                \"&.Mui-error .MuiOutlinedInput-notchedOutline\": {\r\n                borderColor: \"#e6a957 !important\", // Custom error border color\r\n            },\r\n            },\r\n          }}\r\n    />\r\n</div>\r\n                                                        </Grid>\r\n                                                        <Grid item xs={1} className={`qadpt-btngrid ${!!errorMessage[index] ? 'qadpt-error-btn' : ''}`}  style={{ opacity: triggers.length === 1 ? 0.5 : 1 }}>\r\n                                                            <IconButton onClick={() => {\r\n                                                                setDeleteIndex(index); // Set the index of the trigger to delete\r\n                                                                setDialogOpen(true); // Open the confirmation dialog\r\n                                                            }} disabled={triggers.length === 1} >\r\n\r\n                                                  <img \r\n                                                src={Targetdelete} \r\n                                                alt='tardelete' \r\n                                                style={{ opacity: triggers.length === 1 ? 0.5 : 1 }} \r\n                                                                />\r\n                                                            </IconButton>\r\n                                                        </Grid>\r\n                                                        \r\n                                                       \r\n                                                    </Grid>\r\n                                                ))}\r\n                                                {/* <ConfirmationDialog\r\n                                                    open={dialogOpen}\r\n                                                    onClose={() => setDialogOpen(false)}\r\n                                                    onConfirm={handleDeleteTrigger} // Call delete function on confirmation\r\n                                                    title=\"Delete Trigger\"\r\n                                                    message=\"Are you sure you want to delete this trigger?\"\r\n                                                /> */}\r\n {/* {dialogOpen && (\r\n  <div className=\"qadpt-modal-overlay\">\r\n    <div className=\"qadpt-usrconfirm-popup qadpt-danger qadpt-deltrigger\">\r\n      <div className=\"qadpt-popup-title\">Delete Trigger</div>\r\n\r\n      <div className=\"qadpt-warning\">\r\n        Are you sure you want to delete this trigger?\r\n      </div>\r\n\r\n      <div className=\"qadpt-buttons\">\r\n        <button\r\n          onClick={() => setDialogOpen(false)}\r\n          className=\"qadpt-cancel-button\"\r\n        >\r\n          Cancel\r\n        </button>\r\n\r\n        <button\r\n          onClick={handleDeleteTrigger}\r\n          className=\"qadpt-conform-button\"\r\n          type=\"button\"\r\n        >\r\n          Confirm\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n)} */}\r\n\r\n        {dialogOpen && (\r\n           <div className=\"qadpt-modal-overlay\">       \r\n        <div className=\"qadpt-usrconfirm-popup qadpt-danger\">\r\n          <div>\r\n            <div className=\"qadpt-icon\">      \r\n              <IconButton\r\n                                className=\"qadpt-svg\">\r\n                                    <i className='fal fa-trash-alt'></i>\r\n                                    </IconButton>\r\n            </div>\r\n                                                            </div>                                                      <div className=\"qadpt-popup-title\">\r\n                                                                {translate(\"Delete Trigger\")}\r\n            </div>\r\n      \r\n            <div className=\"qadpt-warning\">\r\n                                                                {translate(\"Are you sure you want to delete this trigger?\")}\r\n      </div>\r\n      \r\n      <div className=\"qadpt-buttons\">\r\n        <button\r\n          onClick={() => setDialogOpen(false)}\r\n          className=\"qadpt-cancel-button\"\r\n        >\r\n                                                                    {translate(\"Cancel\")}\r\n        </button>\r\n\r\n        <button\r\n          onClick={handleDeleteTrigger}\r\n          className=\"qadpt-conform-button\"\r\n          type=\"button\"\r\n        >\r\n                                                                    {translate(\"Confirm\")}\r\n        </button>\r\n      </div>\r\n                    </div>\r\n                    </div>\r\n      )}\r\n\r\n\r\n\r\n                                            </div>\r\n\r\n                                            \r\n                                        </CardContent>\r\n                                    </Card>\r\n                                </Grid>\r\n                            </div>\r\n\r\n                            <div className='qadpt-auto-trigger'>\r\n                                {/* Auto Trigger Section */}\r\n                                <Grid item xs={12} md={6}>\r\n                                    <Card className='qadpt-card qadpt-trigger'>\r\n                                        <CardContent>\r\n                                            <Grid container>\r\n                                                <div className=\"qadpt-label\">{translate(\"Auto Trigger\")}</div>\r\n                                                <Switch\r\n                                                    checked={true}\r\n                                                    onChange={(e) => setIsAutoTriggerEnabled(e.target.checked)}\r\n                                                    disabled={true}\r\n                                                />\r\n                                            </Grid>\r\n                                            <div className='qadpt-sublabel' style={{ lineHeight: 1 }}>\r\n                                                {translate(\"Enable this to automatically display the\")} {translate(guideType)} {translate(\"on target pages, or trigger manually via checklists.\")}\r\n                                            </div>\r\n                                        </CardContent>\r\n                                    </Card>\r\n                                </Grid>\r\n                            </div>\r\n\r\n\r\n                            {/* Frequency Section */}\r\n                            <div className='qadpt-frequency'>\r\n\r\n                                <Grid item xs={12} md={6}>\r\n                                    <Card className='qadpt-card'>\r\n                                        <CardContent>\r\n                                            <div className=\"qadpt-label\">{translate(\"Frequency\")}</div>\r\n                                            <div className='qadpt-sublabel'>{translate(\"How often do you want it to trigger\")}</div>\r\n                                            <FormControl component=\"fieldset\" disabled={!isAutoTriggerEnabled}>\r\n                                                <Grid container spacing={1} alignItems=\"center\" wrap=\"nowrap\">\r\n                                                    <Grid item>\r\n                                                        <FormControlLabel\r\n                                                            control={\r\n                                                                <Radio\r\n                                                                    checked={selectedFrequency === 'onlyOnce'}\r\n                                                                    onChange={() => handleFrequencyChange('onlyOnce')}\r\n                                                                    disabled\r\n                                                                />\r\n                                                            }\r\n                                                            label={<span className=\"qadpt-freqselect\">{translate(\"Only Once\")}</span>}\r\n                                                        />\r\n                                                    </Grid>\r\n                                                    <Grid item>\r\n                                                        <FormControlLabel\r\n                                                            control={\r\n                                                                <Radio\r\n\r\n                                                                    checked={true}\r\n                                                                    onChange={() => handleFrequencyChange('onceInSession')}\r\n                                                                />\r\n                                                            }\r\n                                                            label={<span className=\"qadpt-freqselect\">{translate(\"Once in a session\")}</span>}\r\n                                                        />\r\n                                                    </Grid>\r\n                                                    <Grid item>\r\n                                                        <FormControlLabel\r\n                                                            control={\r\n                                                                <Radio\r\n                                                                    checked={selectedFrequency === 'onceADay'}\r\n                                                                    onChange={() => handleFrequencyChange('onceADay')}\r\n                                                                    disabled\r\n                                                                />\r\n                                                            }\r\n                                                            label={<span className=\"qadpt-freqselect\">{translate(\"Once a day\")}</span>}\r\n                                                        />\r\n                                                    </Grid>\r\n                                                    <Grid item>\r\n                                                        <FormControlLabel\r\n                                                            control={\r\n                                                                <Radio\r\n                                                                    checked={selectedFrequency?.startsWith('onceIn') && selectedFrequency !== 'onceInSession'}\r\n                                                                    onChange={() => handleFrequencyChange('onceIn')}\r\n                                                                    disabled\r\n                                                                />\r\n                                                            }\r\n                                                            label={<span className=\"qadpt-freqselect\">{translate(\"Once in\")}</span>}\r\n                                                        />\r\n                                                    </Grid>\r\n                                                    <Grid item>\r\n                                                        <FormControl sx={{ marginLeft: '8px', minWidth: '80px' }} disabled={!isAutoTriggerEnabled || !selectedFrequency?.startsWith('onceIn')}>\r\n                                                            <Select\r\n                                                                labelId=\"frequency-select-label\"\r\n                                                                value={frequencyDropdown}\r\n                                                                defaultValue='2days'\r\n                                                                onChange={handleFrequencyDropdown}\r\n                                                                disabled\r\n                                                            >\r\n                                                                <MenuItem value=\"2days\">{translate(\"2 days\")}</MenuItem>\r\n                                                                <MenuItem value=\"3days\">{translate(\"3 days\")}</MenuItem>\r\n                                                                <MenuItem value=\"Week\">{translate(\"Week\")}</MenuItem>\r\n                                                                <MenuItem value=\"month\">{translate(\"Month\")}</MenuItem>\r\n                                                                <MenuItem value=\"Quarter\">{translate(\"Quarter\")}</MenuItem>\r\n                                                                <MenuItem value=\"Semi-Yearly\">{translate(\"Semi-Yearly\")}</MenuItem>\r\n                                                                <MenuItem value=\"Yearly\">{translate(\"Yearly\")}</MenuItem>\r\n                                                            </Select>\r\n                                                        </FormControl>\r\n                                                    </Grid>\r\n                                                </Grid>\r\n                                            </FormControl>\r\n\r\n\r\n                                        </CardContent>\r\n                                    </Card>\r\n                                </Grid>\r\n\r\n                            </div>\r\n\r\n                            {/* Review & Publish Section */}\r\n                            <div className='qadpt-rev-publish'>\r\n                                <Grid item xs={12} md={6}>\r\n                                    <Card className='qadpt-card qadpt-rev'>\r\n                                        <CardContent>\r\n                                            <div className=\"qadpt-label\">{translate(\"Review & Publish\")}</div>\r\n                                            <Grid container spacing={2}>\r\n                                                <Grid item xs={6} className='qadpt-gridleft'>\r\n                                                    <Typography>{translate(\"Publish\")} {`${translate(guideType)}`}:</Typography>\r\n                                                    <FormControl component=\"fieldset\">\r\n                                                        <RadioGroup\r\n                                                            value={publishOption}\r\n                                                            onChange={(e) => setPublishOption(e.target.value)}\r\n                                                        >\r\n                                                            <FormControlLabel\r\n                                                                value=\"immediately\"\r\n                                                                control={<Radio />}\r\n                                                                label={translate(\"Immediately\")}\r\n                                                            />\r\n                                                            <FormControlLabel\r\n                                                                value=\"custom\"\r\n                                                                control={<Radio />}\r\n                                                                label={translate(\"Custom date\")}\r\n                                                            />\r\n                                                        </RadioGroup>\r\n                                                    </FormControl>\r\n                                                    {publishOption === 'custom' && (\r\n                                                        <TextField\r\n                                                            type=\"datetime-local\"\r\n                                                            value={PublishDate}\r\n                                                            onChange={handleCustomDateChange}\r\n                                                            fullWidth\r\n                                                            sx={{ marginTop: '8px' }}\r\n                                                            inputProps={{\r\n                                                                min: currentDateTime,\r\n                                                            }}\r\n                                                        />\r\n                                                    )}\r\n                                                </Grid>\r\n\r\n                                                <Grid item xs={6} className='qadpt-gridright'>\r\n                                                    <Typography>{translate(\"UnPublish\")} {`${translate(guideType)}`}:</Typography>\r\n                                                    <FormControl component=\"fieldset\">\r\n                                                        <RadioGroup\r\n                                                            value={unpublishOption}\r\n                                                            onChange={(e) => setUnpublishOption(e.target.value)}\r\n                                                        >\r\n                                                            <FormControlLabel\r\n                                                                value=\"Manually\"\r\n                                                                control={<Radio />}\r\n                                                                label={translate(\"Manually\")}\r\n                                                            />\r\n                                                            <FormControlLabel\r\n                                                                value=\"custom\"\r\n                                                                control={<Radio />}\r\n                                                                label={translate(\"Custom date\")}\r\n\r\n                                                            />\r\n                                                        </RadioGroup>\r\n                                                    </FormControl>\r\n                                                    {unpublishOption === 'custom' && (\r\n                                                        <TextField\r\n                                                            type=\"datetime-local\"\r\n                                                            value={UnPublishDate} // Use value instead of defaultValue for controlled input\r\n                                                            onChange={handleCustomDateChangeTwo} // Handle input changes\r\n                                                            fullWidth\r\n                                                            sx={{ marginTop: '8px' }}\r\n                                                            inputProps={{\r\n                                                                min: currentDateTime, \r\n                                                            }}\r\n                                                        />\r\n                                                    )}\r\n                                                </Grid>\r\n                                            </Grid>\r\n                                        </CardContent>\r\n                                    </Card>\r\n                                </Grid>\r\n                            </div>\r\n\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            {showExtensionRequiredPopup && (\r\n    <div style={{ position: 'fixed', top: 0, left: 0, width: '100vw', height: '100vh', backgroundColor: 'rgba(0,0,0,0.5)', zIndex: 999 }} />\r\n)}\r\n{showExtensionRequiredPopup && (\r\n    <div style={{ position: 'fixed', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', zIndex: 1000, backgroundColor: 'white', borderRadius: '8px', boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)' }}>\r\n        <ExtensionRequiredPopup\r\n            setShowPopup={setShowExtensionRequiredPopup}\r\n            showPopup={showExtensionRequiredPopup}\r\n            name={guideType}\r\n        />\r\n    </div>\r\n)}\r\n        </Container>\r\n    );\r\n};\r\n\r\nexport default WebappSettings;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAeC,UAAU,QAAQ,OAAO;AAC3E,SACIC,SAAS,EAAEC,UAAU,EAAEC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAcC,IAAI,EAAEC,WAAW,EAAWC,gBAAgB,EAAEC,UAAU,EAAEC,GAAG,EAAEC,cAAc,EAAEC,OAAO,EAAEC,UAAU,QAClM,eAAe;AACtB,SAASC,cAAc,QAAQ,eAAe;AAE9C,OAAOC,aAAa,MAAM,+BAA+B;AACzD,SAASC,WAAW,EAAEC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACtE,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,eAAe,MAAM,sCAAsC;AAClE,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,QAAQ,MAAM,iCAAiC;AACtD,OAAOC,OAAO,MAAM,gCAAgC;AAEpD,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,KAAK,MAAsB,qBAAqB;AACvD,SAASC,MAAM,QAAQ,sBAAsB;AAE7C,SAASC,cAAc,EAAEC,YAAY,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,uBAAuB,QAAQ,6BAA6B;AACzM,SAASC,WAAW,QAAQ,uBAAuB;AAEnD,OAAOC,eAAe,MAAM,iCAAiC;AAE7D,SAASC,QAAQ,QAAgB,0BAA0B;AAE3D,SAASC,kBAAkB,QAAQ,gCAAgC;AACnE,SAASC,YAAY,QAAQ,wBAAwB;AACrD,OAAOC,sBAAsB,MAAM,kCAAkC;AACrE,SAASC,cAAc,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAwF3D,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,mBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EACnC,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGtD,cAAc,CAAC,CAAC;EACzC,MAAMuD,eAAe,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;EAC7D,MAAMC,WAAW,GAAG,IAAIH,IAAI,CAAC,CAAC;EAC9B;;EAEA;;EAEA;EACA;EACA;;EAEA,MAAM,CAACI,UAAU,EAAEC,aAAa,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACkF,KAAK,EAAEC,QAAQ,CAAC,GAAGnF,QAAQ,CAAe,IAAI,CAAC;EACtD,MAAM,CAACoF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAE;EAClE,MAAM,CAACsF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAE;EAClE,MAAMwF,QAAQ,GAAGlE,WAAW,CAAC,CAAC;EAC9B,MAAMmE,QAAQ,GAAGlE,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmE;EAAa,CAAC,GAAG9C,WAAW,CAAC,CAAC;EACtC,MAAM,CAAC+C,SAAS,EAAEC,YAAY,CAAC,GAAG5F,QAAQ,CAAC,CAAAkF,KAAK,aAALA,KAAK,wBAAAzB,mBAAA,GAALyB,KAAK,CAAEW,YAAY,cAAApC,mBAAA,uBAAnBA,mBAAA,CAAqBqC,IAAI,KAAI,GAAG,CAAC;EAC5E,MAAM;IAAEC;EAAQ,CAAC,GAAGvE,SAAS,CAAsB,CAAC;EACpD,MAAM,CAACwE,cAAc,EAAEC,UAAU,CAAC,GAAGjG,QAAQ,CAAC+F,OAAO,IAAI,EAAE,CAAC;EAC5D,MAAM,CAACG,cAAc,EAAEC,iBAAiB,CAAC,GAAGnG,QAAQ,CAAC,CAAAkF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEW,YAAY,CAACO,cAAc,KAAI,EAAE,CAAC;EAC9F,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGtG,QAAQ,CAAC,CAAAkF,KAAK,aAALA,KAAK,wBAAAxB,oBAAA,GAALwB,KAAK,CAAEW,YAAY,cAAAnC,oBAAA,uBAAnBA,oBAAA,CAAqB6C,SAAS,KAAI,EAAE,CAAC;EAChF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGzG,QAAQ,CAAC,CAAAkF,KAAK,aAALA,KAAK,wBAAAvB,oBAAA,GAALuB,KAAK,CAAEW,YAAY,cAAAlC,oBAAA,uBAAnBA,oBAAA,CAAqB+C,WAAW,KAAI,EAAE,CAAC;EACtF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5G,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6G,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9G,QAAQ,CAAS,CAAAkF,KAAK,aAALA,KAAK,wBAAAtB,oBAAA,GAALsB,KAAK,CAAEW,YAAY,cAAAjC,oBAAA,uBAAnBA,oBAAA,CAAqBmD,SAAS,KAAI,eAAe,CAAC;EACrH,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjH,QAAQ,CAAC,CAAAkF,KAAK,aAALA,KAAK,wBAAArB,oBAAA,GAALqB,KAAK,CAAEW,YAAY,cAAAhC,oBAAA,uBAAnBA,oBAAA,CAAqBqD,SAAS,KAAI,EAAE,CAAC;EAChF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGpH,QAAQ,CAACkF,KAAK,aAALA,KAAK,wBAAApB,oBAAA,GAALoB,KAAK,CAAEW,YAAY,cAAA/B,oBAAA,uBAAnBA,oBAAA,CAAqBuD,SAAS,CAAC;EAC1E,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGvH,QAAQ,CAACkF,KAAK,aAALA,KAAK,wBAAAnB,oBAAA,GAALmB,KAAK,CAAEW,YAAY,cAAA9B,oBAAA,uBAAnBA,oBAAA,CAAqByD,SAAS,CAAC;EAC1E,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1H,QAAQ,CAACkF,KAAK,aAALA,KAAK,wBAAAlB,oBAAA,GAALkB,KAAK,CAAEW,YAAY,cAAA7B,oBAAA,uBAAnBA,oBAAA,CAAqB2D,WAAW,CAAC;EAClG,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG7H,QAAQ,CAAC,CAAAkF,KAAK,aAALA,KAAK,wBAAAjB,oBAAA,GAALiB,KAAK,CAAEW,YAAY,cAAA5B,oBAAA,uBAAnBA,oBAAA,CAAqB6D,WAAW,KAAI,aAAa,CAAC;EACrG,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGhI,QAAQ,CAAC,CAAAkF,KAAK,aAALA,KAAK,wBAAAhB,oBAAA,GAALgB,KAAK,CAAEW,YAAY,cAAA3B,oBAAA,uBAAnBA,oBAAA,CAAqB+D,aAAa,KAAI,UAAU,CAAC;EACxG,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnI,QAAQ,CAACkF,KAAK,aAALA,KAAK,wBAAAf,oBAAA,GAALe,KAAK,CAAEW,YAAY,cAAA1B,oBAAA,uBAAnBA,oBAAA,CAAqBiE,WAAW,CAAC;EAC1F,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGtI,QAAQ,CAAC,CAAAkF,KAAK,aAALA,KAAK,wBAAAd,qBAAA,GAALc,KAAK,CAAEW,YAAY,cAAAzB,qBAAA,uBAAnBA,qBAAA,CAAqBmE,SAAS,KAAI,EAAE,CAAC;EAChF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGzI,QAAQ,CAACkF,KAAK,aAALA,KAAK,wBAAAb,qBAAA,GAALa,KAAK,CAAEW,YAAY,cAAAxB,qBAAA,uBAAnBA,qBAAA,CAAqBmE,WAAW,CAAC;EAChF,MAAM,CAACE,WAAW,EAAEC,cAAc,CAAC,GAAG3I,QAAQ,CAAC,CAAAkF,KAAK,aAALA,KAAK,wBAAAZ,qBAAA,GAALY,KAAK,CAAEW,YAAY,cAAAvB,qBAAA,uBAAnBA,qBAAA,CAAqBoE,WAAW,KAAI3D,WAAW,CAAC;EAC/F,MAAM,CAAC6D,aAAa,EAAEC,gBAAgB,CAAC,GAAG7I,QAAQ,CAAC,CAAAkF,KAAK,aAALA,KAAK,wBAAAX,qBAAA,GAALW,KAAK,CAAEW,YAAY,cAAAtB,qBAAA,uBAAnBA,qBAAA,CAAqBqE,aAAa,KAAI7D,WAAW,CAAC;EACrG,MAAM,CAAC+D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/I,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACgJ,WAAW,EAAEC,cAAc,CAAC,GAAGjJ,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACkJ,aAAa,EAAEC,gBAAgB,CAAC,GAAGnJ,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACoJ,UAAU,EAAEC,aAAa,CAAC,GAAGrJ,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsJ,WAAW,EAAEC,cAAc,CAAC,GAAGvJ,QAAQ,CAAgB,IAAI,CAAC;EACnE,MAAM,CAACwJ,QAAQ,EAAEC,WAAW,CAAC,GAAGzJ,QAAQ,CAAY,CAAC;IAAE0J,QAAQ,EAAE,QAAQ;IAAEC,GAAG,EAAE,CAAAzE,KAAK,aAALA,KAAK,wBAAAV,qBAAA,GAALU,KAAK,CAAEW,YAAY,cAAArB,qBAAA,uBAAnBA,qBAAA,CAAqB+D,SAAS,KAAI,EAAE;IAAEqB,eAAe,EAAE,EAAE;IAAEC,YAAY,EAAE;EAAG,CAAC,CAAC,CAAC;EAC/J,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG/J,QAAQ,CAAW,EAAE,CAAC;EAC9D,MAAM,CAACgK,IAAI,EAAEC,OAAO,CAAC,GAAGjK,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACzC,MAAM,CAACkK,YAAY,EAAEC,eAAe,CAAC,GAAGnK,QAAQ,CAAC;IAC7CoK,OAAO,EAAE,EAAE;IACXlD,SAAS,EAAE,EAAE;IACbpB,IAAI,EAAE,EAAE;IACRM,cAAc,EAAE,EAAE;IAClBoB,SAAS,EAAE,EAAE;IACbH,SAAS,EAAE,EAAE;IACbN,SAAS,EAAE,EAAE;IACbR,SAAS,EAAE,EAAE;IACbG,WAAW,EAAE,EAAE;IACfiB,WAAW,EAAE,KAAK;IAClB0C,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,KAAK;IAChBlC,WAAW,EAAE,EAAE;IACfG,SAAS,EAAE;EACf,CAAC,CAAC;EAEF,MAAM,CAACgC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxK,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACnE,MAAM,CAACyK,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1K,QAAQ,CAAC,kBAAkB,CAAC;EAC9E,MAAM,CAAC2K,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5K,QAAQ,CAAC,kBAAkB,CAAC;EAClF,MAAM;IAAE6K;EAAM,CAAC,GAAG3K,UAAU,CAACgD,cAAc,CAAC;EAI5C,MAAM4H,sBAAsB,GAAIC,KAAU,IAAK;IAC3CpC,cAAc,CAACoC,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;EACxC,CAAC;EAED,MAAMC,yBAAyB,GAAIH,KAAU,IAAK;IAC9ClC,gBAAgB,CAACkC,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;EAC1C,CAAC;EACD,MAAM,CAACE,SAAS,EAAEC,YAAY,CAAC,GAAGpL,QAAQ,CACtCqL,KAAK,CAACC,OAAO,CAACpG,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEW,YAAY,CAAC0F,SAAS,CAAC,GACtCrG,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEW,YAAY,CAAC0F,SAAS,GAC7B,EACV,CAAC;EAED,MAAMC,aAAa,GAAGtJ,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IAAEuJ;EAAM,CAAC,MAAM;IAChDC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,SAAS,EAAE,QAAQ;IACnB;IACA,mBAAmB,EAAE;MACjBC,OAAO,EAAE,IAAI;MACb;MACA;MACAC,MAAM,EAAE;IACZ;EACJ,CAAC,CAAC,CAAC;EACH7L,SAAS,CAAC,MAAM;IACZ,MAAM8L,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MAClC,MAAMC,OAAO,GAAG,MAAMrJ,uBAAuB,CAACqD,cAAc,CAAC;MAC7D,IAAIgG,OAAO,EAAE;QACT7G,QAAQ,CAAC6G,OAAO,CAAC;QACjBvF,cAAc,CAACuF,OAAO,CAACnG,YAAY,CAACa,WAAW,CAAC;QAChDuC,cAAc,CAAC+C,OAAO,CAACnG,YAAY,CAACa,WAAW,KAAK,QAAQ,CAAC;QAC7DyC,gBAAgB,CAAC6C,OAAO,CAACnG,YAAY,CAACa,WAAW,KAAK,UAAU,CAAC;QACjEP,iBAAiB,CAAC6F,OAAO,CAACnG,YAAY,CAACO,cAAc,CAAC;QACtDa,YAAY,CAAC+E,OAAO,CAACnG,YAAY,CAACqB,SAAS,CAAC;QAC5CtB,YAAY,CAACoG,OAAO,CAACnG,YAAY,CAACC,IAAI,CAAC;QACvCQ,YAAY,CAAC0F,OAAO,CAACnG,YAAY,CAACU,SAAS,CAAC;QAC5Ca,YAAY,CAAC4E,OAAO,CAACnG,YAAY,CAACwB,SAAS,CAAC;QAC5CE,YAAY,CAACyE,OAAO,CAACnG,YAAY,CAAC2B,SAAS,CAAC;QAC5C4D,YAAY,CAACY,OAAO,CAACnG,YAAY,CAAC0F,SAAS,CAAC;QAC5CpD,mBAAmB,CAAC6D,OAAO,CAACnG,YAAY,CAACuC,WAAW,CAAC;QACrDtB,oBAAoB,CAACkF,OAAO,CAACnG,YAAY,CAACkB,SAAS,CAAC;QACpDW,uBAAuB,CAACsE,OAAO,CAACnG,YAAY,CAAC8B,WAAW,CAAC;QACzDE,gBAAgB,CAACmE,OAAO,CAACnG,YAAY,CAAC+B,aAAa,CAAC;QACpDI,kBAAkB,CAACgE,OAAO,CAACnG,YAAY,CAACkC,eAAe,CAAC;QACxDO,YAAY,CAAC0D,OAAO,CAACnG,YAAY,CAAC0C,SAAS,CAAC;QAC5CE,cAAc,CAACuD,OAAO,CAACnG,YAAY,CAAC2C,WAAW,CAAC;QAChDG,cAAc,CAACqD,OAAO,CAACnG,YAAY,CAAC6C,WAAW,CAAC;QAChDG,gBAAgB,CAACmD,OAAO,CAACnG,YAAY,CAACoG,aAAa,CAAC;QACpDxG,QAAQ,CAACD,QAAQ,CAAC0G,QAAQ,EAAE;UACxBC,KAAK,EAAE;YAAEC,QAAQ,EAAEJ;UAAQ,CAAC;UAC5BK,OAAO,EAAE;QACX,CAAC,CAAC;MACR;IACJ,CAAC;IAEDN,iBAAiB,CAAC,CAAC;EACvB,CAAC,EAAE,CAAC/F,cAAc,EAAEQ,WAAW,CAAC,CAAC;EAEjC,MAAM8F,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACA,IAAI,CAAC,CAAC,eAAe,EAAE,WAAW,CAAC,CAACC,IAAI,CAACC,IAAI,IAAI3B,KAAK,CAAC4B,QAAQ,CAACD,IAAI,CAAC,CAAC,EACtE;QACI;MACJ;MACA,MAAME,oBAAoB,CAAC,CAAC;MAE5B,IAAI1D,WAAW,EAAE;QACb,MAAM2D,MAAM,GAAG,MAAMtK,cAAc,CAAC2D,cAAc,CAAC;QACnD,IAAI2G,MAAM,CAACC,OAAO,EAAE;UAChBlH,YAAY,CAAC,GAAGC,SAAS,IAAIjB,SAAS,CAACsC,SAAS,CAAC,IAAItC,SAAS,CAAC,0BAA0B,CAAC,EAAE,EAAE,SAAS,CAAC;UACxG+B,cAAc,CAAC,UAAU,CAAC;UAC1BwC,cAAc,CAAC,KAAK,CAAC;UACrBE,gBAAgB,CAAC,IAAI,CAAC;UACtB;UACA,MAAM0D,cAAc,GAAG,MAAMlK,uBAAuB,CAACqD,cAAc,CAAC;UACpE,IAAI6G,cAAc,EAAE;YAChB1H,QAAQ,CAAC0H,cAAc,CAAC;YACxBpG,cAAc,CAACoG,cAAc,CAAChH,YAAY,CAACa,WAAW,CAAC;YACvDuC,cAAc,CAAC4D,cAAc,CAAChH,YAAY,CAACa,WAAW,KAAK,QAAQ,CAAC;YACpEyC,gBAAgB,CAAC0D,cAAc,CAAChH,YAAY,CAACa,WAAW,KAAK,UAAU,CAAC;UAC5E;QACJ,CAAC,MAAM;UACHhB,YAAY,CAAChB,SAAS,CAACiI,MAAM,CAACG,cAAc,CAAC,EAAE,OAAO,CAAC;QAC3D;MACJ,CAAC,MAAM;QAEH,MAAMH,MAAM,GAAG,MAAMvK,YAAY,CAAC4D,cAAc,CAAC;QACjD,IAAI2G,MAAM,CAACC,OAAO,EAAE;UAChBlH,YAAY,CAAC,GAAGC,SAAS,IAAIjB,SAAS,CAACsC,SAAS,CAAC,IAAItC,SAAS,CAAC,wBAAwB,CAAC,EAAE,EAAE,SAAS,CAAC;UACtG+B,cAAc,CAAC,QAAQ,CAAC;UACxBwC,cAAc,CAAC,IAAI,CAAC;UACpBE,gBAAgB,CAAC,KAAK,CAAC;UACvB;UACA,MAAM0D,cAAc,GAAG,MAAMlK,uBAAuB,CAACqD,cAAc,CAAC;UACpE,IAAI6G,cAAc,EAAE;YAChB1H,QAAQ,CAAC0H,cAAc,CAAC;YACxBpG,cAAc,CAACoG,cAAc,CAAChH,YAAY,CAACa,WAAW,CAAC;YACvDuC,cAAc,CAAC4D,cAAc,CAAChH,YAAY,CAACa,WAAW,KAAK,QAAQ,CAAC;YACpEyC,gBAAgB,CAAC0D,cAAc,CAAChH,YAAY,CAACa,WAAW,KAAK,UAAU,CAAC;UAC5E;QACJ,CAAC,MAAM;UACHhB,YAAY,CAAChB,SAAS,CAACiI,MAAM,CAACG,cAAc,CAAC,EAAE,OAAO,CAAC;QAC3D;MACJ;IAEJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACxD;EACJ,CAAC;EAKD,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC1B,MAAM/F,SAAc,GAAG,GAAGF,SAAS,CAACkG,WAAW,CAAC,CAAC,GAAG;IACpDzH,QAAQ,CAAC,IAAIyB,SAAS,EAAE,CAAC;EAC7B,CAAC;EACD,MAAMiG,eAAe,GAAGA,CAAA,KAAM;IAC1BC,oBAAoB,CAACzH,SAAS,CAAC;IAC/BiB,YAAY,CAAC,IAAI,CAAC;EACtB,CAAC;EACD,MAAMyG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3BzG,YAAY,CAAC,KAAK,CAAC;IACnB,IAAI;MACA,MAAM+F,MAAM,GAAG,MAAMxK,cAAc,CAAC6D,cAAc,EAAEE,cAAc,EAAEP,SAAS,EAAEU,SAAS,EAAEW,SAAS,CAAC;MACpG,IAAI2F,MAAM,CAACC,OAAO,KAAK,IAAI,EAAE;QACzBlH,YAAY,CAAChB,SAAS,CAACiI,MAAM,CAACG,cAAc,CAAC,EAAE,SAAS,CAAC;MAC7D,CAAC,MAAM;QACHpH,YAAY,CAAChB,SAAS,CAACiI,MAAM,CAACW,YAAY,CAAC,EAAE,OAAO,CAAC;MACzD;IACJ,CAAC,CAAC,OAAOP,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACtD;EACJ,CAAC;EACD,MAAMQ,eAAe,GAAGA,CAAA,KAAM;IAC1B,IAAIlF,SAAS,EAAE;MACXmF,MAAM,CAACxD,IAAI,CAAC3B,SAAS,EAAE,QAAQ,CAAC;IACpC;EAEJ,CAAC;EACDpI,SAAS,CAAC,MAAM;IACZ,IAAIiF,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEuI,WAAW,EAAE;MACpB,MAAMC,cAAc,GAAGxI,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEuI,WAAW,CAACE,GAAG,CAAEC,OAAY,KAAM;QAC7DlE,QAAQ,EAAEkE,OAAO,CAACC,SAAS;QAC3BlE,GAAG,EAAEiE,OAAO,CAACE,KAAK;QAClBlE,eAAe,EAAEgE,OAAO,CAACG,QAAQ;QACjClE,YAAY,EAAE+D,OAAO,CAAC/D;MAC1B,CAAC,CAAC,CAAC;MACHJ,WAAW,CAACiE,cAAc,CAAC;IAC/B;EACJ,CAAC,EAAE,CAACxI,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEuI,WAAW,CAAC,CAAC;EAExBxN,SAAS,CAAC,MAAM;IACZ,IAAIuJ,QAAQ,CAACwE,MAAM,KAAK,CAAC,EAAE;MACvBvE,WAAW,CAAC,CAAC;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,GAAG,EAAE,CAAAzE,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEW,YAAY,CAAC0C,SAAS,KAAI,EAAE;QAAEsB,YAAY,EAAE,EAAE;QAAED,eAAe,EAAE;MAAG,CAAC,CAAC,CAAC;IAC1H;EACJ,CAAC,EAAE,CAACJ,QAAQ,CAAC,CAAC;EAEd,MAAMyE,gBAAgB,GAAGA,CAAA,KAAM;IAC3B,IAAIzE,QAAQ,CAACwE,MAAM,GAAG,CAAC,EAAE;MACrBvE,WAAW,CAAC,CAAC,GAAGD,QAAQ,EAAE;QAAEE,QAAQ,EAAE,QAAQ;QAAEC,GAAG,EAAE,EAAE;QAAEE,YAAY,EAAE,EAAE;QAAED,eAAe,EAAE;MAAK,CAAC,CAAC,CAAC;MACpGvE,mBAAmB,CAAC,KAAK,CAAC;MAC1BE,mBAAmB,CAAC,IAAI,CAAC;MACzBwE,eAAe,CAAC,EAAE,CAAC;IACvB;EACJ,CAAC;EACD,MAAMmE,eAAe,GAAG,CACpB;IAAEjD,KAAK,EAAE,QAAQ;IAAEkD,KAAK,EAAEzJ,SAAS,CAAC,QAAQ;EAAE,CAAC,EAC/C;IAAEuG,KAAK,EAAE,YAAY;IAAEkD,KAAK,EAAEzJ,SAAS,CAAC,YAAY;EAAE,CAAC,EACvD;IAAEuG,KAAK,EAAE,aAAa;IAAEkD,KAAK,EAAEzJ,SAAS,CAAC,aAAa;EAAE,CAAC,EACzD;IAAEuG,KAAK,EAAE,WAAW;IAAEkD,KAAK,EAAEzJ,SAAS,CAAC,WAAW;EAAE,CAAC,EACrD;IAAEuG,KAAK,EAAE,UAAU;IAAEkD,KAAK,EAAEzJ,SAAS,CAAC,UAAU;EAAE,CAAC,EACnD;IAAEuG,KAAK,EAAE,cAAc;IAAEkD,KAAK,EAAEzJ,SAAS,CAAC,kBAAkB;EAAE,CAAC,CAClE;EAED,MAAM0J,mBAAmB,GAAGA,CAACC,KAAa,EAAEC,KAAoB,EAAErD,KAAa,KAAK;IAChF,MAAMsD,SAAS,GAAG,EAAE;IACpB,MAAMC,eAAe,GAAG,CAAC,GAAGhF,QAAQ,CAAC;IACrCgF,eAAe,CAACH,KAAK,CAAC,CAACC,KAAK,CAAC,GAAGrD,KAAK;IACrC,IAAI8B,KAAK,GAAG,EAAE;IACd,IAAI9B,KAAK,CAAC+C,MAAM,GAAG,CAAC,IAAIM,KAAK,KAAK,KAAK,EAAG;MACtCvB,KAAK,GAAGrI,SAAS,CAAC,8BAA8B,CAAC;IACrD,CAAC,MAAM,IAAIuG,KAAK,CAAC+C,MAAM,GAAG,GAAG,EAAG;MAC5BjB,KAAK,GAAGrI,SAAS,CAAC,kCAAkC,CAAC;IACzD,CAAC,MAAM,IAAI,IAAI,CAAC+J,IAAI,CAACxD,KAAK,CAAC,IAAKqD,KAAK,KAAK,KAAK,EAAE;MAC7CvB,KAAK,GAAGrI,SAAS,CAAC,sCAAsC,CAAC;IAC7D,CAAC,MACI;MACD+E,WAAW,CAAC+E,eAAe,CAAC;IAChC;IACA,IAAIzB,KAAK,CAACiB,MAAM,GAAG,CAAC,EAAE;MAAEO,SAAS,CAACF,KAAK,CAAC,GAAGtB,KAAK;IAAC;IAAC;IAClDhD,eAAe,CAACwE,SAAS,CAAC;IAC1BtJ,aAAa,CAAC,IAAI,CAAC;EAEvB,CAAC;EACD,MAAMyJ,cAAc,GAAIC,SAAc,IAAK;IACvC,IAAIA,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEC,UAAU,CAAC,QAAQ,CAAC,IAAID,SAAS,KAAK,eAAe,EAAE;MAClE,MAAME,KAAK,GAAGF,SAAS,CAACG,KAAK,CAAC,GAAG,CAAC;MAClC,MAAMC,IAAI,GAAGF,KAAK,CAAC,CAAC,CAAC;MACrB,MAAMG,aAAa,GAAGH,KAAK,CAACb,MAAM,GAAG,CAAC,GAAGa,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE;MACtD,OAAO;QAAEI,aAAa,EAAEF,IAAI;QAAEC;MAAc,CAAC;IACjD;IACA,OAAO;MAAEC,aAAa,EAAEN,SAAS;MAAEK,aAAa,EAAE;IAAG,CAAC;EAC1D,CAAC;EACD/O,SAAS,CAAC,MAAM;IAAA,IAAAiP,qBAAA;IACZ,MAAMC,gBAAgB,GAAGjK,KAAK,aAALA,KAAK,wBAAAgK,qBAAA,GAALhK,KAAK,CAAEW,YAAY,cAAAqJ,qBAAA,uBAAnBA,qBAAA,CAAqBnI,SAAS;IACvD,IAAIoI,gBAAgB,KAAK,eAAe,EAAE;MACtC,MAAM;QAAEF,aAAa;QAAED;MAAc,CAAC,GAAGN,cAAc,CAACS,gBAAgB,CAAC;MACzErI,oBAAoB,CAACmI,aAAa,CAAC;MACnClG,oBAAoB,CAACiG,aAAa,CAAC;IACvC;EACJ,CAAC,EAAE,CAACxJ,QAAQ,CAAC2G,KAAK,CAAC,CAAC;EACpB,MAAM,CAACiD,iBAAiB,EAAEhC,oBAAoB,CAAC,GAAGpN,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACqP,MAAM,EAAEC,SAAS,CAAC,GAAGtP,QAAQ,CAAkC,CAAC,CAAC,CAAC;EAEzE,MAAMuP,YAAY,GAAIxE,KAAoC,IAAK;IAC3D,MAAM;MAAEyE,IAAI;MAAEvE;IAAM,CAAC,GAAGF,KAAK,CAACC,MAAM;IACpC,IAAI+B,KAAK,GAAG,EAAE;IAEd,IAAIyC,IAAI,KAAK,WAAW,IAAIvE,KAAK,CAACwE,IAAI,CAAC,CAAC,CAACzB,MAAM,GAAG,CAAC,EAAE;MACjDjB,KAAK,GAAGrI,SAAS,CAAC,2CAA2C,CAAC;IAClE;IACAkB,YAAY,CAACmF,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;IAChCqE,SAAS,CAAEI,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAE,CAACF,IAAI,GAAGzC;IAAM,CAAC,CAAC,CAAC;EACrD,CAAC;EACD9M,SAAS,CAAC,MAAM;IACZ,IAAIwH,oBAAoB,EAAE;MACtBX,oBAAoB,CAAC,eAAe,CAAC;IACzC;EACJ,CAAC,EAAE,CAACW,oBAAoB,CAAC,CAAC;EAE1B,MAAMkI,qBAAqB,GAAI1E,KAAa,IAAK;IAC7C,IAAIA,KAAK,KAAK,QAAQ,EAAE;MACpBnE,oBAAoB,CAAC,SAASgC,iBAAiB,EAAE,CAAC;IACtD,CAAC,MAAM;MACHhC,oBAAoB,CAACmE,KAAK,CAAC;IAC/B;IACA,IAAIA,KAAK,KAAK,QAAQ,EAAE;MACpBlC,oBAAoB,CAAC,EAAE,CAAC;IAC5B;EACJ,CAAC;EACD,MAAM6G,uBAAuB,GAAIC,CAAM,IAAK;IACxC,MAAM5E,KAAK,GAAG4E,CAAC,CAAC7E,MAAM,CAACC,KAAK;IAC5B,IAAIpE,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAE+H,UAAU,CAAC,QAAQ,CAAC,IAAI/H,iBAAiB,KAAK,eAAe,EAAE;MAClFkC,oBAAoB,CAACkC,KAAK,CAAC;MAC3BnE,oBAAoB,CAAC,UAAUmE,KAAK,EAAE,CAAC;IAC3C;EACJ,CAAC;EAED,MAAM6E,aAAa,GAAI/E,KAA4C,IAAK;IACpE,IAAIA,KAAK,CAACgF,GAAG,KAAK,OAAO,EAAE;MACvB1C,UAAU,CAAC,CAAC;IAChB,CAAC,MAAM,IAAItC,KAAK,CAACgF,GAAG,KAAK,QAAQ,EAAE;MAC/BnK,YAAY,CAACwJ,iBAAiB,CAAC;MAC/BxI,YAAY,CAAC,KAAK,CAAC;IACvB;EACJ,CAAC;EACD,MAAMoJ,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI1G,WAAW,KAAK,IAAI,EAAE,OAAO,CAAC;;IAElC,MAAM2G,eAAe,GAAGzG,QAAQ,CAACF,WAAW,CAAC;IAE7C,IAAI;MACA,IAAI2G,eAAe,CAACpG,YAAY,EAAE;QAC9B,MAAMqG,YAAY,GAAGD,eAAe,CAACpG,YAAY;QACjD,MAAMsG,MAAM,GAAG;UACXnK,cAAc;UACdkK;QACJ,CAAC;QACD,MAAM9D,QAAQ,GAAG,MAAM5J,gBAAgB,CAAC2N,MAAM,CAAC;QAC/C,IAAI/D,QAAQ,CAACQ,OAAO,EAAE;UAClBlH,YAAY,CAAChB,SAAS,CAAC0H,QAAQ,CAACU,cAAc,CAAC,EAAE,SAAS,CAAC;UAC3D,MAAM0B,eAAe,GAAGhF,QAAQ,CAAC4G,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKhH,WAAW,CAAC;UACpEG,WAAW,CAAC+E,eAAe,CAAC;QAChC,CAAC,MAAM;UACH9I,YAAY,CAAChB,SAAS,CAAC0H,QAAQ,CAACkB,YAAY,CAAC,EAAE,OAAO,CAAC;QAC3D;MACJ,CAAC,MAAM;QACH,IAAI9D,QAAQ,CAACwE,MAAM,GAAG,CAAC,EAAE;UACrB,MAAMQ,eAAe,GAAGhF,QAAQ,CAAC4G,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKhH,WAAW,CAAC;UACpEG,WAAW,CAAC+E,eAAe,CAAC;QAChC;MACJ;IACJ,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDrH,YAAY,CAAChB,SAAS,CAAC,iDAAiD,CAAC,EAAE,OAAO,CAAC;IACvF,CAAC,SAAS;MACN;MACA6E,cAAc,CAAC,IAAI,CAAC;MACpBF,aAAa,CAAC,KAAK,CAAC;IACxB;EACJ,CAAC;EAED,MAAMkH,oBAAoB,GAAGA,CAAClC,KAAa,EAAEpD,KAAa,KAAK;IAC3D,MAAMuD,eAAe,GAAG,CAAC,GAAGhF,QAAQ,CAAC;;IAErC;IACA,IAAI6E,KAAK,GAAGG,eAAe,CAACR,MAAM,GAAG,CAAC,EAAE;MACpCQ,eAAe,CAACH,KAAK,GAAG,CAAC,CAAC,CAACzE,eAAe,GAAGqB,KAAK,CAAC,CAAC;IACxD;IAEAxB,WAAW,CAAC+E,eAAe,CAAC;EAChC,CAAC;EAED,MAAM,CAACgC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzQ,QAAQ,CAAQ,CAAAkF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEuI,WAAW,KAAI,EAAE,CAAC;EAE/FxN,SAAS,CAAC,MAAM;IACZ,IAAIiF,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEuI,WAAW,IAAI,CAAAvI,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEuI,WAAW,CAACO,MAAM,IAAG,CAAC,EAAE;MACrDyC,sBAAsB,CAACvL,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEuI,WAAW,CAAC;IAC9C;EACJ,CAAC,EAAE,CAACvI,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEuI,WAAW,CAAC,CAAC;EACxB,MAAMiD,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACvCzL,aAAa,CAAC,KAAK,CAAC;IACpB,MAAM0L,WAAW,GAAGnH,QAAQ,CAAC4G,MAAM,CAACxC,OAAO,IAAI,CAACA,OAAO,CAAC/D,YAAY,CAAC;IACrE,MAAM+G,gBAAgB,GAAGpH,QAAQ,CAAC4G,MAAM,CAACxC,OAAO,IAAIA,OAAO,CAAC/D,YAAY,CAAC;IAEzE,IAAI8G,WAAW,CAAC3C,MAAM,GAAG,CAAC,EAAE;MACxB,MAAM6C,uBAAuB,GAAGF,WAAW,CAAChD,GAAG,CAAEC,OAAO,KAAM;QAC1D/D,YAAY,EAAE,EAAE;QAChBgE,SAAS,EAAED,OAAO,CAAClE,QAAQ;QAC3BqE,QAAQ,EAAE,CAAAH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEhE,eAAe,KAAI,IAAI;QAC1CkE,KAAK,EAAEF,OAAO,CAACjE,GAAG;QAClBS,OAAO,EAAEpE,cAAc;QACvBI,cAAc,EAAE,EAAE;QAClBG,SAAS,EAAEF;MACf,CAAC,CAAC,CAAC;MACH,IAAI;QACA,MAAM+F,QAAQ,GAAG,MAAM7J,cAAc,CAACsO,uBAAuB,CAAC;QAC9D,IAAIzE,QAAQ,CAACQ,OAAO,EAAE;UAClBlH,YAAY,CAAChB,SAAS,CAAC0H,QAAQ,CAACU,cAAc,CAAC,EAAE,SAAS,CAAC;UAC3DzH,mBAAmB,CAAC,IAAI,CAAC;UACzBoE,WAAW,CAACiG,IAAI,IAAIA,IAAI,CAACU,MAAM,CAACxC,OAAO,IAAI,CAAC,CAACA,OAAO,CAAC/D,YAAY,CAAC,CAAC;UACnEtE,mBAAmB,CAAC,KAAK,CAAC;QAE9B,CAAC,MAAM;UACHG,YAAY,CAAChB,SAAS,CAAC0H,QAAQ,CAACkB,YAAY,CAAC,EAAE,OAAO,CAAC;QAC3D;MACJ,CAAC,CAAC,OAAOP,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MAC1D;IACJ;;IAEA;IACA,MAAM+D,gBAAgB,GAAGF,gBAAgB,CAACR,MAAM,CAAExC,OAAO,IAAK;MAC1D,MAAMmD,eAAe,GAAGP,mBAAmB,CAACQ,IAAI,CAC3CC,WAAgB,IAAKA,WAAW,CAACpH,YAAY,KAAK+D,OAAO,CAAC/D,YAC/D,CAAC;MAED,OAAOkH,eAAe,KAClBA,eAAe,CAAClD,SAAS,KAAKD,OAAO,CAAClE,QAAQ,IAC9CqH,eAAe,CAAChD,QAAQ,KAAKH,OAAO,CAAChE,eAAe,IACpDmH,eAAe,CAACjD,KAAK,KAAKF,OAAO,CAACjE,GAAG,CACxC;IACL,CAAC,CAAC;IAEF,IAAImH,gBAAgB,CAAC9C,MAAM,GAAG,CAAC,EAAE;MAC7B,MAAMkD,4BAA4B,GAAGJ,gBAAgB,CAACnD,GAAG,CAAEC,OAAO,KAAM;QACpE/D,YAAY,EAAE+D,OAAO,CAAC/D,YAAY;QAClCgE,SAAS,EAAED,OAAO,CAAClE,QAAQ;QAC3BqE,QAAQ,EAAE,CAAAH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEhE,eAAe,KAAI,IAAI;QAC1CkE,KAAK,EAAEF,OAAO,CAACjE,GAAG;QAClBS,OAAO,EAAEpE,cAAc;QACvBI,cAAc,EAAE;MACpB,CAAC,CAAC,CAAC;MAEH,IAAI;QACA,MAAMgG,QAAQ,GAAG,MAAM3J,gBAAgB,CAACyO,4BAA4B,CAAC;QACrE,IAAI9E,QAAQ,CAACQ,OAAO,EAAE;UAClBlH,YAAY,CAAChB,SAAS,CAAC0H,QAAQ,CAACU,cAAc,CAAC,EAAE,SAAS,CAAC;UAC3DzH,mBAAmB,CAAC,IAAI,CAAC;UACzBE,mBAAmB,CAAC,KAAK,CAAC;QAC9B,CAAC,MAAM;UACHG,YAAY,CAAChB,SAAS,CAAC0H,QAAQ,CAACkB,YAAY,CAAC,EAAE,OAAO,CAAC;QAC3D;MACJ,CAAC,CAAC,OAAOP,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MACjE;IACJ;IAEA,IAAI;MACA,MAAMoE,mBAAmB,GAAG,MAAMzO,cAAc,CAACsD,cAAc,CAAC;MAChE,IAAImL,mBAAmB,EAAE;QACrB;QACA,MAAMC,kBAAkB,GAAGD,mBAAmB,CAACxD,GAAG,CAAEC,OAAY,KAAM;UAClElE,QAAQ,EAAEkE,OAAO,CAACC,SAAS;UAC3BlE,GAAG,EAAEiE,OAAO,CAACE,KAAK;UAClBlE,eAAe,EAAEgE,OAAO,CAACG,QAAQ;UACjClE,YAAY,EAAE+D,OAAO,CAAC/D;QAC1B,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMwH,gBAAgB,GAAG,CAAC,GAAGD,kBAAkB,CAAC;;QAEhD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACAX,sBAAsB,CAACU,mBAAmB,CAAC;QAC3C1H,WAAW,CAAC4H,gBAAgB,CAAC;MACjC,CAAC,MAAM;QACH3L,YAAY,CAAChB,SAAS,CAACyM,mBAAmB,CAAC7D,YAAY,CAAC,EAAE,OAAO,CAAC;MACtE;IACJ,CAAC,CAAC,OAAOP,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACxD;EACJ,CAAC;EAGD,MAAML,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IAAA,IAAA4E,qBAAA;IACrC;IACA,IAAIC,SAAS,GAAG/K,WAAW;;IAE3B;IACA,IAAI0C,aAAa,IAAIF,WAAW,KAAKhE,UAAU,IAAIM,gBAAgB,IAAIqB,SAAS,IAAIuB,gBAAgB,MAAKhD,KAAK,aAALA,KAAK,wBAAAoM,qBAAA,GAALpM,KAAK,CAAEW,YAAY,cAAAyL,qBAAA,uBAAnBA,qBAAA,CAAqBlJ,WAAW,EAAC,EAAE;MACxImJ,SAAS,GAAG,OAAO;IACvB,CAAC,MAAM,IAAIvI,WAAW,EAAE;MACpBuI,SAAS,GAAG,QAAQ;IACxB,CAAC,MAAM,IAAIrI,aAAa,EAAE;MACtBqI,SAAS,GAAG,UAAU;IAC1B,CAAC,MAAM;MACHA,SAAS,GAAG,OAAO;IACvB;;IAEA;IACA9K,cAAc,CAAC8K,SAAS,CAAC;IAEzB,MAAMC,QAAQ,GAAG;MACbpH,OAAO,EAAEpE,cAAc;MACvBkB,SAAS,EAAEF,SAAS;MACpBlB,IAAI,EAAEH,SAAS,CAAC8J,IAAI,CAAC,CAAC;MACtBgC,OAAO,EAAE,GAAGzK,SAAS,UAAU;MAC/BZ,cAAc,EAAEF,cAAc;MAC9BsC,WAAW,EAAEA,WAAW;MACxBkJ,WAAW,EAAE,IAAI9M,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACrC2C,SAAS,EAAEF,SAAS;MACpBD,SAAS,EAAEF,SAAS;MACpBJ,SAAS,EAAE,eAAe;MAC1B4K,OAAO,EAAE,WAAW;MACpBpL,SAAS,EAAEF,SAAS;MACpBkF,SAAS,EAAEJ,SAAS;MACpBzE,WAAW,EAAE6K,SAAS;MAAE;MACxB5J,WAAW,EAAE,IAAI;MACjBG,WAAW,EAAEF,aAAa;MAC1BK,aAAa,EAAEF,eAAe;MAC9BK,WAAW,EAAEF,gBAAgB;MAC7BK,SAAS,EAAEF,SAAS;MACnB;MACD;MACA,IAAI,CAACkJ,SAAS,KAAK,OAAO,IAAIA,SAAS,KAAK,UAAU,KAAK;QACvD7I,WAAW,EAAEd,aAAa,KAAK,QAAQ,GACjCc,WAAW,GACX3D,WAAW;QACjB6D,aAAa,EAAEb,eAAe,KAAK,QAAQ,GACrCa,aAAa,GACb;MACV,CAAC,CAAC;MAEF,IAAII,WAAW,IAAI;QACfiD,aAAa,EAAElE,eAAe,KAAK,QAAQ,GACrCa,aAAa,GACb,IAAI;QACVF,WAAW,EAAEd,aAAa,KAAK,QAAQ,GACjCc,WAAW,GACX3D;MACV,CAAC;IACL,CAAC;IAED,IAAI,CAACK,gBAAgB,IAAIE,gBAAgB,EAAE;MACvC,MAAMoL,sBAAsB,CAAC,CAAC;IAClC;IACA,MAAMtE,QAAQ,GAAG,MAAM9J,gBAAgB,CAACkP,QAAQ,CAAC;IAEjD,IAAIpF,QAAQ,CAACQ,OAAO,KAAK,IAAI,EAAE;MAC3BlH,YAAY,CAAC,GAAGC,SAAS,IAAIjB,SAAS,CAACsC,SAAS,CAAC,IAAItC,SAAS,CAAC,sBAAsB,CAAC,EAAE,EAAE,SAAS,CAAC;MACpGW,mBAAmB,CAAC,KAAK,CAAC;MAC1BE,mBAAmB,CAAC,KAAK,CAAC;;MAE1B;MACA4D,gBAAgB,CAACoI,SAAS,KAAK,UAAU,CAAC;IAC9C,CAAC,MAAM;MACH7L,YAAY,CAAChB,SAAS,CAAC0H,QAAQ,CAACkB,YAAY,CAAC,EAAE,OAAO,CAAC;IAC3D;IACA1G,YAAY,CAAC,KAAK,CAAC;EACvB,CAAC;EAEL3G,SAAS,CAAC,MAAM;IACZ,IACI,CAAAiF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEW,YAAY,CAACa,WAAW,MAAK,QAAQ,IAC5C,CAAAxB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEW,YAAY,CAACoC,aAAa,MAAK,QAAQ,IAC9C/C,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEW,YAAY,CAAC+C,aAAa,EACnC;MACE,MAAMgJ,QAAQ,GAAGC,WAAW,CAAC,YAAY;QACrC,MAAMC,GAAG,GAAG,IAAIlN,IAAI,CAAC,CAAC;QACtB,MAAMmN,aAAa,GAAG7M,KAAK,CAACW,YAAY,CAAC+C,aAAa,GAAG,IAAIhE,IAAI,CAACM,KAAK,CAACW,YAAY,CAAC+C,aAAa,CAAC,GAAG,IAAI;QAC1G,IAAImJ,aAAa,IAAID,GAAG,IAAIC,aAAa,EAAE;UACvC,MAAMzF,mBAAmB,CAAC,CAAC;UAC3B,MAAMN,OAAO,GAAG,MAAMrJ,uBAAuB,CAACqD,cAAc,CAAC;UAC7D,IAAIgG,OAAO,EAAE;YACT7G,QAAQ,CAAC6G,OAAO,CAAC;YACjBvF,cAAc,CAACuF,OAAO,CAACnG,YAAY,CAACa,WAAW,CAAC;YAChDuC,cAAc,CAAC+C,OAAO,CAACnG,YAAY,CAACa,WAAW,KAAK,QAAQ,CAAC;YAC7DyC,gBAAgB,CAAC6C,OAAO,CAACnG,YAAY,CAACa,WAAW,KAAK,UAAU,CAAC;UACrE;UACAsL,aAAa,CAACJ,QAAQ,CAAC;QAC3B;MACJ,CAAC,EAAE,EAAE,CAAC;MACN,OAAO,MAAMI,aAAa,CAACJ,QAAQ,CAAC;IACxC;EACJ,CAAC,EAAE,CAAC1M,KAAK,CAAC,CAAC;EAEX,MAAM;IAAE+M,oBAAoB;IAAEC;EAAqB,CAAC,GAAGlP,YAAY,CAAC,CAAC;EACjE,MAAM,CAACmP,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGpS,QAAQ,CAAC,KAAK,CAAC;EAEnF,MAAMqS,mBAAmB,GAAGA,CAAA,KAAM;IAC9BH,oBAAoB,CAAC,CAAC;IACtBI,UAAU,CAAC,MAAM;MACb,IAAIL,oBAAoB,EAAE;QACtBlP,kBAAkB,CAACsF,SAAS,EAAErC,cAAc,EAAEK,SAAS,CAAC;MAC5D,CAAC,MAAM;QACH+L,6BAA6B,CAAC,IAAI,CAAC;MACvC;IACJ,CAAC,EAAE,GAAG,CAAC;EACX,CAAC;EAED,oBACIhP,OAAA,CAACjD,SAAS;IAACoS,QAAQ,EAAC,IAAI;IAAAC,QAAA,gBACpBpP,OAAA;MAAKqP,SAAS,EAAC,WAAW;MAAAD,QAAA,eACtBpP,OAAA;QAAKqP,SAAS,EAAC,kBAAkB;QAAAD,QAAA,gBAC7BpP,OAAA;UAAKqP,SAAS,EAAC,qBAAqB;UAAAD,QAAA,gBAChCpP,OAAA;YAAKqP,SAAS,EAAC,eAAe;YAAAD,QAAA,eAC1BpP,OAAA,CAAC9C,IAAI;cAACoS,SAAS;cAAC/G,UAAU,EAAC,QAAQ;cAAC8G,SAAS,EAAC,mBAAmB;cAAAD,QAAA,gBAC7DpP,OAAA,CAAC9C,IAAI;gBAACqS,IAAI;gBAAAH,QAAA,eACNpP,OAAA,CAACrC,UAAU;kBAAC6R,OAAO,EAAE3F,eAAgB;kBAAAuF,QAAA,eACjCpP,OAAA,CAAC/B,aAAa;oBAAAwR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eAEP5P,OAAA,CAAC9C,IAAI;gBAACqS,IAAI;gBAAAH,QAAA,eACNpP,OAAA,CAAChD,UAAU;kBAAC6S,OAAO,EAAC,OAAO;kBAACR,SAAS,EAAC,iBAAiB;kBAACG,OAAO,EAAE3F,eAAgB;kBAAAuF,QAAA,GAC5E9N,SAAS,CAAC,SAAS,CAAC,EAAC,GAAC,EAAC,GAAGA,SAAS,CAACsC,SAAS,CAAC,EAAE;gBAAA;kBAAA6L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN5P,OAAA;YAAAoP,QAAA,eACIpP,OAAA,CAAC9C,IAAI;cAACoS,SAAS;cAACD,SAAS,EAAC,cAAc;cAAAD,QAAA,gBACpCpP,OAAA,CAAC9C,IAAI;gBAACqS,IAAI;gBAAAH,QAAA,eACNpP,OAAA,CAACpC,GAAG;kBAACyR,SAAS,EAAC,gBAAgB;kBAAAD,QAAA,GAC1B7L,SAAS,gBACNvD,OAAA,CAAC5C,SAAS;oBACNgP,IAAI,EAAC,WAAW;oBAChByD,OAAO,EAAC,UAAU;oBAClBhI,KAAK,EAAEtF,SAAU;oBACjBuN,QAAQ,EAAE3D,YAAa;oBACvB4D,SAAS,EAAErD,aAAc;oBACzBsD,UAAU,EAAE/D,MAAM,CAACgE,SAAU;oBAC7BtG,KAAK,EAAE,CAAC,CAACsC,MAAM,CAACgE,SAAU;oBAC1BC,EAAE,EAAE;sBAAEC,WAAW,EAAE,MAAM;sBAAEC,KAAK,EAAE;oBAAQ;kBAAE;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC,gBAEF5P,OAAA,CAAChD,UAAU;oBAAC6S,OAAO,EAAC,IAAI;oBAACQ,UAAU,EAAC,MAAM;oBAAChB,SAAS,EAAC,iBAAiB;oBAAAD,QAAA,EACjE7M;kBAAS;oBAAAkN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CACf,eACD5P,OAAA,CAAAE,SAAA;oBAAAkP,QAAA,EACI,CAAC,eAAe,EAAE,QAAQ,CAAC,CAACjG,IAAI,CAACC,IAAI,IAAI3B,KAAK,CAAC4B,QAAQ,CAACD,IAAI,CAAC,CAAC,iBAC9DpJ,OAAA,CAACrC,UAAU;sBAAC6R,OAAO,EAAEjM,SAAS,GAAG0G,UAAU,GAAGF,eAAgB;sBAC1DuG,QAAQ,EAAE/M,SAAS,IAAI,CAAC,CAAC0I,MAAM,CAACgE,SAAU;sBAAAb,QAAA,EAEzC7L,SAAS,gBAAIvD,OAAA,CAAClC,OAAO;wBAClByS,KAAK;wBACLC,KAAK,EAAElP,SAAS,CAAC,MAAM,CAAE;wBAAA8N,QAAA,eAGzBpP,OAAA;0BAAKyQ,GAAG,EAAEhS,QAAS;0BAACiS,GAAG,EAAC;wBAAU;0BAAAjB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC,gBAEN5P,OAAA,CAAClC,OAAO;wBAACyS,KAAK;wBAACC,KAAK,EAAElP,SAAS,CAAC,MAAM,CAAE;wBAAA8N,QAAA,eACpCpP,OAAA,CAAC3B,QAAQ;0BAAAoR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACP;oBAEZ;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO;kBAAC,gBACd,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAEP5P,OAAA,CAAC9C,IAAI;gBAACqS,IAAI;gBAAAH,QAAA,eACNpP,OAAA,CAAC9C,IAAI;kBAACoS,SAAS;kBAACqB,OAAO,EAAE,CAAE;kBAAAvB,QAAA,gBACvBpP,OAAA,CAAC9C,IAAI;oBAACqS,IAAI;oBAAAH,QAAA,eACNpP,OAAA,CAAC/C,MAAM;sBACH4S,OAAO,EAAC,UAAU;sBAClBe,KAAK,EAAC,SAAS;sBACfvB,SAAS,EAAC,kBAAkB;sBAC5BwB,SAAS,eAAE7Q,OAAA;wBAAKyQ,GAAG,EAAEnS,eAAgB;wBAACoS,GAAG,EAAC;sBAAiB;wBAAAjB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBAC/DJ,OAAO,EAAEP,mBAAoB;sBAC7BqB,QAAQ,EAAG,CAAC,CAAC,eAAe,EAAC,QAAQ,CAAC,CAACnH,IAAI,CAACC,IAAI,IAAI3B,KAAK,CAAC4B,QAAQ,CAACD,IAAI,CAAC,CAAG;sBAAAgG,QAAA,GAE1E9N,SAAS,CAAC,MAAM,CAAC,EAAC,GAAC,EAACA,SAAS,CAACsC,SAAS,CAAC;oBAAA;sBAAA6L,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACP5P,OAAA,CAAC9C,IAAI;oBAACqS,IAAI;oBAAAH,QAAA,eACNpP,OAAA,CAAC/C,MAAM;sBACH4S,OAAO,EAAC,UAAU;sBAClBL,OAAO,EAAElG,oBAAqB;sBAC9BgH,QAAQ,EAAG,CAAC,CAAC,eAAe,EAAC,QAAQ,CAAC,CAACnH,IAAI,CAACC,IAAI,IAAI3B,KAAK,CAAC4B,QAAQ,CAACD,IAAI,CAAC,CAAG;sBAC3EiG,SAAS,EAAC,kBAAkB;sBAC5BwB,SAAS,eAAE7Q,OAAA;wBAAKyQ,GAAG,EAAEhS,QAAS;wBAACiS,GAAG,EAAC;sBAAU;wBAAAjB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBAAAR,QAAA,EAEhD9N,SAAS,CAAC,MAAM;oBAAC;sBAAAmO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACP5P,OAAA,CAAC9C,IAAI;oBAACqS,IAAI;oBAAAH,QAAA,eACNpP,OAAA,CAAC/C,MAAM;sBACH4S,OAAO,EAAC,WAAW;sBACnBL,OAAO,EAAEtG,mBAAoB;sBAC7BoH,QAAQ,EAAG,CAAC,CAAC,eAAe,EAAC,WAAW,CAAC,CAACnH,IAAI,CAACC,IAAI,IAAI3B,KAAK,CAAC4B,QAAQ,CAACD,IAAI,CAAC,CAAG;sBAC9EiG,SAAS,EAAE,6CAA6CzJ,WAAW,GAAG,iBAAiB,GAAG,EAAE,EAAG;sBAACiL,SAAS,eAAE7Q,OAAA;wBAAKyQ,GAAG,EAAE7K,WAAW,GAAGlG,QAAQ,GAAGnB,WAAY;wBAACmS,GAAG,EAAE9K,WAAW,GAAG,UAAU,GAAG;sBAAc;wBAAA6J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBAAAR,QAAA,EAE5MxJ,WAAW,GAAGtE,SAAS,CAAC,WAAW,CAAC,GAAGA,SAAS,CAAC,SAAS;oBAAC;sBAAAmO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGN5P,OAAA;UAAKqP,SAAS,EAAC,eAAe;UAAAD,QAAA,gBAC1BpP,OAAA;YAAKqP,SAAS,EAAC,gBAAgB;YAAAD,QAAA,gBAC3BpP,OAAA;cAAKqP,SAAS,EAAC,mBAAmB;cAAAD,QAAA,eAC9BpP,OAAA,CAAC9C,IAAI;gBAACqS,IAAI;gBAACuB,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA3B,QAAA,eACrBpP,OAAA,CAACxC,IAAI;kBAAC6R,SAAS,EAAC,YAAY;kBAAAD,QAAA,eACxBpP,OAAA,CAACvC,WAAW;oBAAA2R,QAAA,gBACRpP,OAAA;sBAAOgR,OAAO,EAAC,aAAa;sBAAC3B,SAAS,EAAC,aAAa;sBAAAD,QAAA,EAAG9N,SAAS,CAAC,aAAa;oBAAC;sBAAAmO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxF5P,OAAA;sBAAUiR,EAAE,EAAC,aAAa;sBACtBpJ,KAAK,EAAE/C,gBAAiB;sBACxBoM,SAAS,EAAE,GAAI;sBACfC,WAAW,EAAE7P,SAAS,CAAC,kDAAkD,CAAE;sBAC3EwO,QAAQ,EAAGrD,CAAC,IAAK1H,mBAAmB,CAAC0H,CAAC,CAAC7E,MAAM,CAACC,KAAK;oBAAE;sBAAA4H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAMnD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN5P,OAAA;cAAKqP,SAAS,EAAC,kBAAkB;cAAAD,QAAA,eAC7BpP,OAAA,CAAC9C,IAAI;gBAACqS,IAAI;gBAACuB,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA3B,QAAA,eACrBpP,OAAA,CAACxC,IAAI;kBAAC6R,SAAS,EAAC,4BAA4B;kBAAAD,QAAA,eACxCpP,OAAA,CAACvC,WAAW;oBAAA2R,QAAA,gBACRpP,OAAA;sBAAKqP,SAAS,EAAC,aAAa;sBAAAD,QAAA,EACvB9N,SAAS,CAAC,aAAa;oBAAC;sBAAAmO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC,EAE5B7H,SAAS,CAAC6C,MAAM,GAAG,CAAC,iBACjB5K,OAAA,CAAChD,UAAU;sBAAC6S,OAAO,EAAC,OAAO;sBAACK,EAAE,EAAE;wBAAEkB,SAAS,EAAE,IAAI;wBAAEC,QAAQ,EAAE,MAAM;wBAAET,KAAK,EAAE;sBAAO,CAAE;sBAAAxB,QAAA,GAChFrH,SAAS,CAAC6C,MAAM,EAAC,OAAK,EAAC7C,SAAS,CAAC6C,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;oBAAA;sBAAA6E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/C,CACf,eACD5P,OAAA;sBAAKqP,SAAS,EAAC,qBAAqB;sBAAAD,QAAA,EAC/BrH,SAAS,CAAC6C,MAAM,GAAG,CAAC,GACjB7C,SAAS,CAACwC,GAAG,CAAC,CAAC+G,IAAS,EAAErG,KAAU,kBAChCjL,OAAA,CAACpC,GAAG;wBAA4ByR,SAAS,EAAC,aAAa;wBAAAD,QAAA,eACnDpP,OAAA,CAAC5C,SAAS;0BACNyS,OAAO,EAAC,UAAU;0BAClB0B,SAAS;0BACTC,OAAO,EAAE,CAAE;0BACXC,OAAO,EAAE,CAAE;0BACXC,SAAS;0BACTC,QAAQ,EAAG,CAAC,CAAE,CAAC;0BAAA;0BACfC,UAAU,EAAE;4BAAED,QAAQ,EAAE,CAAC;0BAAE,CAAE,CAAC;0BAAA;0BAC9BzB,EAAE,EAAE;4BACA2B,YAAY,EAAE,KAAK;4BACnB,0BAA0B,EAAE;8BACxB,YAAY,EAAE;gCACVC,WAAW,EAAE;8BACjB,CAAC;8BACD,kBAAkB,EAAE;gCAChBA,WAAW,EAAE;8BACjB,CAAC;8BACD,wBAAwB,EAAE;gCACtBA,WAAW,EAAE;8BACjB;4BACJ,CAAC;4BACD,YAAY,EAAE;8BACVC,QAAQ,EAAE,QAAQ;8BAClBC,MAAM,EAAE,MAAM;8BACdC,UAAU,EAAE,KAAK;8BACjBC,UAAU,EAAE;4BAChB;0BACJ,CAAE;0BACFC,UAAU,EAAE;4BACRC,YAAY,eACRpS,OAAA,CAACnC,cAAc;8BAACwU,QAAQ,EAAC,KAAK;8BAAAjD,QAAA,eAC1BpP,OAAA,CAACrC,UAAU;gCAACgU,QAAQ,EAAG,CAAC,CAAE;gCAAAvC,QAAA,GAAC,GAAC,eACxBpP,OAAA,CAACpB,gBAAgB;kCAAA6Q,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACZ;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACD,CACnB;4BACD0C,QAAQ,EAAE,IAAI,CAAE;0BACpB,CAAE;0BACFC,YAAY,EAAE,GAAG,CAAAjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,SAAS,KAAI,QAAQvH,KAAK,GAAG,CAAC,EAAE,KAAKhG,SAAS;wBAAG;0BAAAwK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3E;sBAAC,GAxCI0B,IAAI,CAACmB,MAAM,IAAIxH,KAAK;wBAAAwE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAyCzB,CACR,CAAC,gBAEF5P,OAAA,CAAChD,UAAU;wBAAC6S,OAAO,EAAC,OAAO;wBAACK,EAAE,EAAE;0BAAEkB,SAAS,EAAE,MAAM;0BAAER,KAAK,EAAE;wBAAO,CAAE;wBAAAxB,QAAA,EACrC9N,SAAS,CAAC,8CAA8C;sBAAC;wBAAAmO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7E;oBACf;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEkB,CAAC,eAEN5P,OAAA;YAAKqP,SAAS,EAAC,iBAAiB;YAAAD,QAAA,gBAG5BpP,OAAA;cAAKqP,SAAS,EAAC,mBAAmB;cAAAD,QAAA,eAC9BpP,OAAA,CAAC9C,IAAI;gBAACqS,IAAI;gBAACuB,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA3B,QAAA,eACrBpP,OAAA,CAACxC,IAAI;kBAAC6R,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,eACrCpP,OAAA,CAACvC,WAAW;oBAAA2R,QAAA,gBAERpP,OAAA;sBAAKqP,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,gBACnCpP,OAAA;wBAAKqP,SAAS,EAAC,sBAAsB;wBAAAD,QAAA,gBACjCpP,OAAA;0BAAKqP,SAAS,EAAC,aAAa;0BAAAD,QAAA,EAAE9N,SAAS,CAAC,gBAAgB;wBAAC;0BAAAmO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAChE5P,OAAA;0BAAKqP,SAAS,EAAC,gBAAgB;0BAAAD,QAAA,EAC1B9N,SAAS,CAAC,0CAA0C;wBAAC;0BAAAmO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eAGN5P,OAAA;wBAAAoP,QAAA,gBACIpP,OAAA,CAAC/C,MAAM;0BACH4S,OAAO,EAAC,UAAU;0BAClBR,SAAS,EAAC,sBAAsB;0BAChCG,OAAO,EAAE3E,gBAAiB;0BAC1ByF,QAAQ,EAAElK,QAAQ,CAACwE,MAAM,IAAI,CAAE;0BAC/BiG,SAAS,eAAE7Q,OAAA,CAACP,eAAe;4BAAAgQ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAE;0BAAAR,QAAA,EAE9B9N,SAAS,CAAC,YAAY;wBAAC;0BAAAmO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpB,CAAC,eAET5P,OAAA,CAACrC,UAAU;0BAAC6R,OAAO,EAAElC,sBAAuB;0BAACgD,QAAQ,EAAG,CAAC1O,UAAU,IAAI8E,YAAY,CAACkE,MAAM,GAAG,CAAE;0BAACyE,SAAS,EAAC,2BAA2B;0BAAEqD,KAAK,EAAE;4BAAEC,OAAO,EAAE,CAAC/Q,UAAU,IAAI8E,YAAY,CAACkE,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG;0BAAE,CAAE;0BAAAwE,QAAA,eACxMpP,OAAA;4BAAKyQ,GAAG,EAAEhS,QAAS;4BAACiS,GAAG,EAAC,UAAU;4BAAGgC,KAAK,EAAE;8BAAEC,OAAO,EAAE,CAAC/Q,UAAU,IAAI8E,YAAY,CAACkE,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG;4BAAE;0BAAE;4BAAA6E,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACN5P,OAAA;sBAAKqP,SAAS,EAAC,kBAAkB;sBAAAD,QAAA,GAChChJ,QAAQ,CAACwE,MAAM,GAAG,CAAC,iBACZ5K,OAAA,CAACzC,WAAW;wBAAC8R,SAAS,EAAC,gBAAgB;wBAAAD,QAAA,gBACnCpP,OAAA;0BAAAoP,QAAA,EAAO9N,SAAS,CAAC,WAAW;wBAAC;0BAAAmO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eACrC5P,OAAA,CAAC3C,MAAM;0BACHwK,KAAK,EAAE,IAAK;0BACZyI,QAAQ;0BACRR,QAAQ,EAAGrD,CAAC,IAAKU,oBAAoB,CAAC,CAAC,EAAEV,CAAC,CAAC7E,MAAM,CAACC,KAAK,CAAE,CAAC;0BAAA;0BAAAuH,QAAA,gBAE1DpP,OAAA,CAAC1C,QAAQ;4BAACuK,KAAK,EAAC,KAAK;4BAAAuH,QAAA,EAAE9N,SAAS,CAAC,KAAK;0BAAC;4BAAAmO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAW,CAAC,eACnD5P,OAAA,CAAC1C,QAAQ;4BAACuK,KAAK,EAAC,IAAI;4BAAAuH,QAAA,EAAE9N,SAAS,CAAC,IAAI;0BAAC;4BAAAmO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAW,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC7C,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAChB,EACAxJ,QAAQ,CAACmE,GAAG,CAAC,CAACC,OAAO,EAAES,KAAK,kBACzBjL,OAAA,CAAC9C,IAAI;wBAACoS,SAAS;wBAACqB,OAAO,EAAE,CAAE;wBAAAvB,QAAA,gBAEvBpP,OAAA,CAAC9C,IAAI;0BAACqS,IAAI;0BAACuB,EAAE,EAAE,CAAE;0BAAA1B,QAAA,eACZpP,OAAA;4BAAAoP,QAAA,gBACGpP,OAAA;8BAAOqP,SAAS,EAAC,mBAAmB;8BAAAD,QAAA,EAAE9N,SAAS,CAAC,WAAW;4BAAC;8BAAAmO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,eACrE5P,OAAA,CAACzC,WAAW;8BAACmU,SAAS;8BAAAtC,QAAA,eAClBpP,OAAA,CAAC3C,MAAM;gCACHwK,KAAK,EAAE2C,OAAO,CAAClE,QAAS;gCACxBwJ,QAAQ,EAAGrD,CAAC,IAAKzB,mBAAmB,CAACC,KAAK,EAAE,UAAU,EAAEwB,CAAC,CAAC7E,MAAM,CAACC,KAAK,CAAE;gCACxE+K,WAAW,EAAGC,QAAQ;kCAAA,IAAAC,qBAAA;kCAAA,OAClB,EAAAA,qBAAA,GAAAhI,eAAe,CAAC8C,IAAI,CAACmF,GAAG,IAAIA,GAAG,CAAClL,KAAK,KAAKgL,QAAQ,CAAC,cAAAC,qBAAA,uBAAnDA,qBAAA,CAAqD/H,KAAK,KAAI8H,QAAQ;gCAAA,CACzE;gCACD3C,EAAE,EAAE;kCACA,0CAA0C,EAAE;oCACxC8C,MAAM,EAAE;kCACZ,CAAC;kCACD,gDAAgD,EAAE;oCAC9CA,MAAM,EAAE;kCACZ,CAAC;kCACD,mCAAmC,EAAE;oCACjCC,MAAM,EAAE;kCACZ;gCACJ,CAAE;gCAAA7D,QAAA,EAEDtE,eAAe,CAACP,GAAG,CAAE2I,MAAM,iBACxBlT,OAAA,CAAC1C,QAAQ;kCAELuK,KAAK,EAAEqL,MAAM,CAACrL,KAAM;kCACpBqI,EAAE,EAAE;oCAAEmB,QAAQ,EAAE,MAAM;oCAAE/I,OAAO,EAAE,MAAM;oCAAE6K,cAAc,EAAE;kCAAgB,CAAE;kCAAA/D,QAAA,GAE1E8D,MAAM,CAACnI,KAAK,eACb/K,OAAA;oCAAKyQ,GAAG,EAAEjS,UAAW;oCAACkS,GAAG,EAAC;kCAAO;oCAAAjB,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE,CAAC;gCAAA,GAL/BsD,MAAM,CAACrL,KAAK;kCAAA4H,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAMX,CACb;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACA,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAEzE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAEwD,CAAC,eACP5P,OAAA,CAAC9C,IAAI;0BAACqS,IAAI;0BAACuB,EAAE,EAAE,CAAE;0BAAA1B,QAAA,eACjBpP,OAAA;4BAAAoP,QAAA,gBACQpP,OAAA;8BAAOqP,SAAS,EAAC,mBAAmB;8BAAAD,QAAA,EAAE9N,SAAS,CAAC,KAAK;4BAAC;8BAAAmO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,eAC3H5P,OAAA,CAAC5C,SAAS;8BACNsU,SAAS;8BACT7J,KAAK,EAAE2C,OAAO,CAACjE,GAAI;8BACnBuJ,QAAQ,EAAGrD,CAAC,IAAKzB,mBAAmB,CAACC,KAAK,EAAE,KAAK,EAAEwB,CAAC,CAAC7E,MAAM,CAACC,KAAK,CAAE;8BACnE8B,KAAK,EAAE,CAAC,CAACjD,YAAY,CAACuE,KAAK,CAAE;8BAC7B+E,UAAU,EACN,CAAC,CAACtJ,YAAY,CAACuE,KAAK,CAAC,iBACjBjL,OAAA;gCAAM0S,KAAK,EAAE;kCAAEpK,OAAO,EAAE,MAAM;kCAAEC,UAAU,EAAE,QAAQ;kCAAE6K,GAAG,EAAE;gCAAM,CAAE;gCAAAhE,QAAA,gBAC/DpP,OAAA;kCAAKyQ,GAAG,EAAE/R,OAAQ;kCAACgS,GAAG,EAAC;gCAAY;kCAAAjB,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAC,CAAC,EACpClJ,YAAY,CAACuE,KAAK,CAAC;8BAAA;gCAAAwE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAClB,CAEb;8BACDuC,UAAU,EAAE;gCACRjC,EAAE,EAAE;kCACA,0CAA0C,EAAE;oCACxC8C,MAAM,EAAE,gBAAgB,CAAE;kCAC9B,CAAC;kCACH,gDAAgD,EAAE;oCAChDA,MAAM,EAAE,gBAAgB,CAAE;kCAC1B,CAAC;kCACD,mCAAmC,EAAE;oCACjCC,MAAM,EAAE;kCACZ,CAAC;kCACD,8CAA8C,EAAE;oCAChDnB,WAAW,EAAE,oBAAoB,CAAE;kCACvC;gCACA;8BACF;4BAAE;8BAAArC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACP,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACwD,CAAC,eACP5P,OAAA,CAAC9C,IAAI;0BAACqS,IAAI;0BAACuB,EAAE,EAAE,CAAE;0BAACzB,SAAS,EAAE,iBAAiB,CAAC,CAAC3I,YAAY,CAACuE,KAAK,CAAC,GAAG,iBAAiB,GAAG,EAAE,EAAG;0BAAEyH,KAAK,EAAE;4BAAEC,OAAO,EAAEvM,QAAQ,CAACwE,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG;0BAAE,CAAE;0BAAAwE,QAAA,eACjJpP,OAAA,CAACrC,UAAU;4BAAC6R,OAAO,EAAEA,CAAA,KAAM;8BACvBrJ,cAAc,CAAC8E,KAAK,CAAC,CAAC,CAAC;8BACvBhF,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;4BACzB,CAAE;4BAACqK,QAAQ,EAAElK,QAAQ,CAACwE,MAAM,KAAK,CAAE;4BAAAwE,QAAA,eAE7CpP,OAAA;8BACFyQ,GAAG,EAAE9R,YAAa;8BAClB+R,GAAG,EAAC,WAAW;8BACfgC,KAAK,EAAE;gCAAEC,OAAO,EAAEvM,QAAQ,CAACwE,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG;8BAAE;4BAAE;8BAAA6E,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACnC;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACM;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACX,CAAC;sBAAA,GAvFsB3E,KAAK;wBAAAwE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA0FhC,CACT,CAAC,EAqCzC5J,UAAU,iBACRhG,OAAA;wBAAKqP,SAAS,EAAC,qBAAqB;wBAAAD,QAAA,eACvCpP,OAAA;0BAAKqP,SAAS,EAAC,qCAAqC;0BAAAD,QAAA,gBAClDpP,OAAA;4BAAAoP,QAAA,eACEpP,OAAA;8BAAKqP,SAAS,EAAC,YAAY;8BAAAD,QAAA,eACzBpP,OAAA,CAACrC,UAAU;gCACO0R,SAAS,EAAC,WAAW;gCAAAD,QAAA,eACjBpP,OAAA;kCAAGqP,SAAS,EAAC;gCAAkB;kCAAAI,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAI;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACxB;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAChC;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC+C,CAAC,0DAAsD,eAAA5P,OAAA;4BAAKqP,SAAS,EAAC,mBAAmB;4BAAAD,QAAA,EACzF9N,SAAS,CAAC,gBAAgB;0BAAC;4BAAAmO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3E,CAAC,eAEN5P,OAAA;4BAAKqP,SAAS,EAAC,eAAe;4BAAAD,QAAA,EACuB9N,SAAS,CAAC,+CAA+C;0BAAC;4BAAAmO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChH,CAAC,eAEN5P,OAAA;4BAAKqP,SAAS,EAAC,eAAe;4BAAAD,QAAA,gBAC5BpP,OAAA;8BACEwP,OAAO,EAAEA,CAAA,KAAMvJ,aAAa,CAAC,KAAK,CAAE;8BACpCoJ,SAAS,EAAC,qBAAqB;8BAAAD,QAAA,EAE4B9N,SAAS,CAAC,QAAQ;4BAAC;8BAAAmO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACxE,CAAC,eAET5P,OAAA;8BACEwP,OAAO,EAAE5C,mBAAoB;8BAC7ByC,SAAS,EAAC,sBAAsB;8BAChCgE,IAAI,EAAC,QAAQ;8BAAAjE,QAAA,EAE8C9N,SAAS,CAAC,SAAS;4BAAC;8BAAAmO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACzE,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACa;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAClB;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAI0C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAGG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN5P,OAAA;cAAKqP,SAAS,EAAC,oBAAoB;cAAAD,QAAA,eAE/BpP,OAAA,CAAC9C,IAAI;gBAACqS,IAAI;gBAACuB,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA3B,QAAA,eACrBpP,OAAA,CAACxC,IAAI;kBAAC6R,SAAS,EAAC,0BAA0B;kBAAAD,QAAA,eACtCpP,OAAA,CAACvC,WAAW;oBAAA2R,QAAA,gBACRpP,OAAA,CAAC9C,IAAI;sBAACoS,SAAS;sBAAAF,QAAA,gBACXpP,OAAA;wBAAKqP,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAE9N,SAAS,CAAC,cAAc;sBAAC;wBAAAmO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC9D5P,OAAA,CAAC7C,MAAM;wBACHmW,OAAO,EAAE,IAAK;wBACdxD,QAAQ,EAAGrD,CAAC,IAAKnI,uBAAuB,CAACmI,CAAC,CAAC7E,MAAM,CAAC0L,OAAO,CAAE;wBAC3DhD,QAAQ,EAAE;sBAAK;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACP5P,OAAA;sBAAKqP,SAAS,EAAC,gBAAgB;sBAACqD,KAAK,EAAE;wBAAER,UAAU,EAAE;sBAAE,CAAE;sBAAA9C,QAAA,GACpD9N,SAAS,CAAC,0CAA0C,CAAC,EAAC,GAAC,EAACA,SAAS,CAACsC,SAAS,CAAC,EAAC,GAAC,EAACtC,SAAS,CAAC,sDAAsD,CAAC;oBAAA;sBAAAmO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAIN5P,OAAA;cAAKqP,SAAS,EAAC,iBAAiB;cAAAD,QAAA,eAE5BpP,OAAA,CAAC9C,IAAI;gBAACqS,IAAI;gBAACuB,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA3B,QAAA,eACrBpP,OAAA,CAACxC,IAAI;kBAAC6R,SAAS,EAAC,YAAY;kBAAAD,QAAA,eACxBpP,OAAA,CAACvC,WAAW;oBAAA2R,QAAA,gBACRpP,OAAA;sBAAKqP,SAAS,EAAC,aAAa;sBAAAD,QAAA,EAAE9N,SAAS,CAAC,WAAW;oBAAC;sBAAAmO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC3D5P,OAAA;sBAAKqP,SAAS,EAAC,gBAAgB;sBAAAD,QAAA,EAAE9N,SAAS,CAAC,qCAAqC;oBAAC;sBAAAmO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACxF5P,OAAA,CAACzC,WAAW;sBAACgW,SAAS,EAAC,UAAU;sBAACjD,QAAQ,EAAE,CAACjM,oBAAqB;sBAAA+K,QAAA,eAC9DpP,OAAA,CAAC9C,IAAI;wBAACoS,SAAS;wBAACqB,OAAO,EAAE,CAAE;wBAACpI,UAAU,EAAC,QAAQ;wBAACiL,IAAI,EAAC,QAAQ;wBAAApE,QAAA,gBACzDpP,OAAA,CAAC9C,IAAI;0BAACqS,IAAI;0BAAAH,QAAA,eACNpP,OAAA,CAACtC,gBAAgB;4BACb+V,OAAO,eACHzT,OAAA,CAACnB,KAAK;8BACFyU,OAAO,EAAE7P,iBAAiB,KAAK,UAAW;8BAC1CqM,QAAQ,EAAEA,CAAA,KAAMvD,qBAAqB,CAAC,UAAU,CAAE;8BAClD+D,QAAQ;4BAAA;8BAAAb,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACX,CACJ;4BACD7E,KAAK,eAAE/K,OAAA;8BAAMqP,SAAS,EAAC,kBAAkB;8BAAAD,QAAA,EAAE9N,SAAS,CAAC,WAAW;4BAAC;8BAAAmO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO;0BAAE;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC7E;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC,eACP5P,OAAA,CAAC9C,IAAI;0BAACqS,IAAI;0BAAAH,QAAA,eACNpP,OAAA,CAACtC,gBAAgB;4BACb+V,OAAO,eACHzT,OAAA,CAACnB,KAAK;8BAEFyU,OAAO,EAAE,IAAK;8BACdxD,QAAQ,EAAEA,CAAA,KAAMvD,qBAAqB,CAAC,eAAe;4BAAE;8BAAAkD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC1D,CACJ;4BACD7E,KAAK,eAAE/K,OAAA;8BAAMqP,SAAS,EAAC,kBAAkB;8BAAAD,QAAA,EAAE9N,SAAS,CAAC,mBAAmB;4BAAC;8BAAAmO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO;0BAAE;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrF;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC,eACP5P,OAAA,CAAC9C,IAAI;0BAACqS,IAAI;0BAAAH,QAAA,eACNpP,OAAA,CAACtC,gBAAgB;4BACb+V,OAAO,eACHzT,OAAA,CAACnB,KAAK;8BACFyU,OAAO,EAAE7P,iBAAiB,KAAK,UAAW;8BAC1CqM,QAAQ,EAAEA,CAAA,KAAMvD,qBAAqB,CAAC,UAAU,CAAE;8BAClD+D,QAAQ;4BAAA;8BAAAb,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACX,CACJ;4BACD7E,KAAK,eAAE/K,OAAA;8BAAMqP,SAAS,EAAC,kBAAkB;8BAAAD,QAAA,EAAE9N,SAAS,CAAC,YAAY;4BAAC;8BAAAmO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO;0BAAE;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC9E;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC,eACP5P,OAAA,CAAC9C,IAAI;0BAACqS,IAAI;0BAAAH,QAAA,eACNpP,OAAA,CAACtC,gBAAgB;4BACb+V,OAAO,eACHzT,OAAA,CAACnB,KAAK;8BACFyU,OAAO,EAAE,CAAA7P,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE+H,UAAU,CAAC,QAAQ,CAAC,KAAI/H,iBAAiB,KAAK,eAAgB;8BAC1FqM,QAAQ,EAAEA,CAAA,KAAMvD,qBAAqB,CAAC,QAAQ,CAAE;8BAChD+D,QAAQ;4BAAA;8BAAAb,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACX,CACJ;4BACD7E,KAAK,eAAE/K,OAAA;8BAAMqP,SAAS,EAAC,kBAAkB;8BAAAD,QAAA,EAAE9N,SAAS,CAAC,SAAS;4BAAC;8BAAAmO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO;0BAAE;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3E;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC,eACP5P,OAAA,CAAC9C,IAAI;0BAACqS,IAAI;0BAAAH,QAAA,eACNpP,OAAA,CAACzC,WAAW;4BAAC2S,EAAE,EAAE;8BAAEwD,UAAU,EAAE,KAAK;8BAAEC,QAAQ,EAAE;4BAAO,CAAE;4BAACrD,QAAQ,EAAE,CAACjM,oBAAoB,IAAI,EAACZ,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAE+H,UAAU,CAAC,QAAQ,CAAC,CAAC;4BAAA4D,QAAA,eAClIpP,OAAA,CAAC3C,MAAM;8BACHuW,OAAO,EAAC,wBAAwB;8BAChC/L,KAAK,EAAEnC,iBAAkB;8BACzB6M,YAAY,EAAC,OAAO;8BACpBzC,QAAQ,EAAEtD,uBAAwB;8BAClC8D,QAAQ;8BAAAlB,QAAA,gBAERpP,OAAA,CAAC1C,QAAQ;gCAACuK,KAAK,EAAC,OAAO;gCAAAuH,QAAA,EAAE9N,SAAS,CAAC,QAAQ;8BAAC;gCAAAmO,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAW,CAAC,eACxD5P,OAAA,CAAC1C,QAAQ;gCAACuK,KAAK,EAAC,OAAO;gCAAAuH,QAAA,EAAE9N,SAAS,CAAC,QAAQ;8BAAC;gCAAAmO,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAW,CAAC,eACxD5P,OAAA,CAAC1C,QAAQ;gCAACuK,KAAK,EAAC,MAAM;gCAAAuH,QAAA,EAAE9N,SAAS,CAAC,MAAM;8BAAC;gCAAAmO,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAW,CAAC,eACrD5P,OAAA,CAAC1C,QAAQ;gCAACuK,KAAK,EAAC,OAAO;gCAAAuH,QAAA,EAAE9N,SAAS,CAAC,OAAO;8BAAC;gCAAAmO,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAW,CAAC,eACvD5P,OAAA,CAAC1C,QAAQ;gCAACuK,KAAK,EAAC,SAAS;gCAAAuH,QAAA,EAAE9N,SAAS,CAAC,SAAS;8BAAC;gCAAAmO,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAW,CAAC,eAC3D5P,OAAA,CAAC1C,QAAQ;gCAACuK,KAAK,EAAC,aAAa;gCAAAuH,QAAA,EAAE9N,SAAS,CAAC,aAAa;8BAAC;gCAAAmO,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAW,CAAC,eACnE5P,OAAA,CAAC1C,QAAQ;gCAACuK,KAAK,EAAC,QAAQ;gCAAAuH,QAAA,EAAE9N,SAAS,CAAC,QAAQ;8BAAC;gCAAAmO,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAW,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACrD;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACA;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAGL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEN,CAAC,eAGN5P,OAAA;cAAKqP,SAAS,EAAC,mBAAmB;cAAAD,QAAA,eAC9BpP,OAAA,CAAC9C,IAAI;gBAACqS,IAAI;gBAACuB,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA3B,QAAA,eACrBpP,OAAA,CAACxC,IAAI;kBAAC6R,SAAS,EAAC,sBAAsB;kBAAAD,QAAA,eAClCpP,OAAA,CAACvC,WAAW;oBAAA2R,QAAA,gBACRpP,OAAA;sBAAKqP,SAAS,EAAC,aAAa;sBAAAD,QAAA,EAAE9N,SAAS,CAAC,kBAAkB;oBAAC;sBAAAmO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAClE5P,OAAA,CAAC9C,IAAI;sBAACoS,SAAS;sBAACqB,OAAO,EAAE,CAAE;sBAAAvB,QAAA,gBACvBpP,OAAA,CAAC9C,IAAI;wBAACqS,IAAI;wBAACuB,EAAE,EAAE,CAAE;wBAACzB,SAAS,EAAC,gBAAgB;wBAAAD,QAAA,gBACxCpP,OAAA,CAAChD,UAAU;0BAAAoS,QAAA,GAAE9N,SAAS,CAAC,SAAS,CAAC,EAAC,GAAC,EAAC,GAAGA,SAAS,CAACsC,SAAS,CAAC,EAAE,EAAC,GAAC;wBAAA;0BAAA6L,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eAC5E5P,OAAA,CAACzC,WAAW;0BAACgW,SAAS,EAAC,UAAU;0BAAAnE,QAAA,eAC7BpP,OAAA,CAACjC,UAAU;4BACP8J,KAAK,EAAErD,aAAc;4BACrBsL,QAAQ,EAAGrD,CAAC,IAAKhI,gBAAgB,CAACgI,CAAC,CAAC7E,MAAM,CAACC,KAAK,CAAE;4BAAAuH,QAAA,gBAElDpP,OAAA,CAACtC,gBAAgB;8BACbmK,KAAK,EAAC,aAAa;8BACnB4L,OAAO,eAAEzT,OAAA,CAACnB,KAAK;gCAAA4Q,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAE;8BACnB7E,KAAK,EAAEzJ,SAAS,CAAC,aAAa;4BAAE;8BAAAmO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACnC,CAAC,eACF5P,OAAA,CAACtC,gBAAgB;8BACbmK,KAAK,EAAC,QAAQ;8BACd4L,OAAO,eAAEzT,OAAA,CAACnB,KAAK;gCAAA4Q,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAE;8BACnB7E,KAAK,EAAEzJ,SAAS,CAAC,aAAa;4BAAE;8BAAAmO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACnC,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACM;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,EACbpL,aAAa,KAAK,QAAQ,iBACvBxE,OAAA,CAAC5C,SAAS;0BACNiW,IAAI,EAAC,gBAAgB;0BACrBxL,KAAK,EAAEvC,WAAY;0BACnBwK,QAAQ,EAAEpI,sBAAuB;0BACjCgK,SAAS;0BACTxB,EAAE,EAAE;4BAAEkB,SAAS,EAAE;0BAAM,CAAE;0BACzBQ,UAAU,EAAE;4BACRiC,GAAG,EAAEtS;0BACT;wBAAE;0BAAAkO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CACJ;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC,eAEP5P,OAAA,CAAC9C,IAAI;wBAACqS,IAAI;wBAACuB,EAAE,EAAE,CAAE;wBAACzB,SAAS,EAAC,iBAAiB;wBAAAD,QAAA,gBACzCpP,OAAA,CAAChD,UAAU;0BAAAoS,QAAA,GAAE9N,SAAS,CAAC,WAAW,CAAC,EAAC,GAAC,EAAC,GAAGA,SAAS,CAACsC,SAAS,CAAC,EAAE,EAAC,GAAC;wBAAA;0BAAA6L,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eAC9E5P,OAAA,CAACzC,WAAW;0BAACgW,SAAS,EAAC,UAAU;0BAAAnE,QAAA,eAC7BpP,OAAA,CAACjC,UAAU;4BACP8J,KAAK,EAAElD,eAAgB;4BACvBmL,QAAQ,EAAGrD,CAAC,IAAK7H,kBAAkB,CAAC6H,CAAC,CAAC7E,MAAM,CAACC,KAAK,CAAE;4BAAAuH,QAAA,gBAEpDpP,OAAA,CAACtC,gBAAgB;8BACbmK,KAAK,EAAC,UAAU;8BAChB4L,OAAO,eAAEzT,OAAA,CAACnB,KAAK;gCAAA4Q,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAE;8BACnB7E,KAAK,EAAEzJ,SAAS,CAAC,UAAU;4BAAE;8BAAAmO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAChC,CAAC,eACF5P,OAAA,CAACtC,gBAAgB;8BACbmK,KAAK,EAAC,QAAQ;8BACd4L,OAAO,eAAEzT,OAAA,CAACnB,KAAK;gCAAA4Q,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAE;8BACnB7E,KAAK,EAAEzJ,SAAS,CAAC,aAAa;4BAAE;8BAAAmO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAEnC,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACM;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,EACbjL,eAAe,KAAK,QAAQ,iBACzB3E,OAAA,CAAC5C,SAAS;0BACNiW,IAAI,EAAC,gBAAgB;0BACrBxL,KAAK,EAAErC,aAAc,CAAC;0BAAA;0BACtBsK,QAAQ,EAAEhI,yBAA0B,CAAC;0BAAA;0BACrC4J,SAAS;0BACTxB,EAAE,EAAE;4BAAEkB,SAAS,EAAE;0BAAM,CAAE;0BACzBQ,UAAU,EAAE;4BACRiC,GAAG,EAAEtS;0BACT;wBAAE;0BAAAkO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CACJ;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EACLb,0BAA0B,iBACnC/O,OAAA;MAAK0S,KAAK,EAAE;QAAEL,QAAQ,EAAE,OAAO;QAAEyB,GAAG,EAAE,CAAC;QAAEC,IAAI,EAAE,CAAC;QAAE3D,KAAK,EAAE,OAAO;QAAE6C,MAAM,EAAE,OAAO;QAAEe,eAAe,EAAE,iBAAiB;QAAEC,MAAM,EAAE;MAAI;IAAE;MAAAxE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAC1I,EACAb,0BAA0B,iBACvB/O,OAAA;MAAK0S,KAAK,EAAE;QAAEL,QAAQ,EAAE,OAAO;QAAEyB,GAAG,EAAE,KAAK;QAAEC,IAAI,EAAE,KAAK;QAAEG,SAAS,EAAE,uBAAuB;QAAED,MAAM,EAAE,IAAI;QAAED,eAAe,EAAE,OAAO;QAAEnC,YAAY,EAAE,KAAK;QAAEsC,SAAS,EAAE;MAAkC,CAAE;MAAA/E,QAAA,eACtMpP,OAAA,CAACH,sBAAsB;QACnBuU,YAAY,EAAEpF,6BAA8B;QAC5CqF,SAAS,EAAEtF,0BAA2B;QACtC3C,IAAI,EAAExI;MAAU;QAAA6L,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACkB,CAAC;AAEpB,CAAC;AAACxP,EAAA,CAztCID,cAAwB;EAAA,QACDnC,cAAc,EAetBE,WAAW,EACXC,WAAW,EACHqB,WAAW,EAEhBpB,SAAS,EAyjBsBwB,YAAY;AAAA;AAAA0U,EAAA,GA7kB7DnU,cAAwB;AA2tC9B,eAAeA,cAAc;AAAC,IAAAmU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}