{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptWeb\\\\src\\\\components\\\\common\\\\ImageUploadSection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Box, Typography, Popover, Grid, IconButton } from \"@mui/material\";\nimport { useTranslation } from \"react-i18next\";\nimport { uploadfile, replaceimageicon } from \"../../assets/icons/icons\";\nimport \"./ImageUploadSection.css\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport { useSnackbar } from \"../../SnackbarContext\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ImageUploadSection = ({\n  selectedFiles,\n  setSelectedFiles\n}) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    openSnackbar\n  } = useSnackbar();\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [isDragging, setIsDragging] = useState(false);\n\n  // File validation constants\n  const MAX_FILES = 10;\n  const MAX_FILE_SIZE = 2 * 1024 * 1024; // 2MB in bytes\n\n  // Validation function for file uploads\n  const validateFiles = (newFiles, currentFiles) => {\n    // Check file count limit\n    if (currentFiles.length + newFiles.length > MAX_FILES) {\n      openSnackbar(`Cannot upload more than ${MAX_FILES} files. You can upload ${MAX_FILES - currentFiles.length} more file(s).`, \"error\");\n      return false;\n    }\n\n    // Check individual file sizes\n    const oversizedFiles = newFiles.filter(file => file.size > MAX_FILE_SIZE);\n    if (oversizedFiles.length > 0) {\n      openSnackbar(`Some files exceed the 2MB limit: ${oversizedFiles.map(f => f.name).join(', ')}`, \"error\");\n      return false;\n    }\n    return true;\n  };\n  const handleDragOver = e => {\n    e.preventDefault();\n    setIsDragging(true);\n  };\n  const handleDragLeave = () => {\n    setIsDragging(false);\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    setIsDragging(false);\n    const droppedFiles = Array.from(e.dataTransfer.files).filter(file => file.type.startsWith(\"image/\"));\n    if (droppedFiles.length === 0) {\n      openSnackbar(\"Please drop only image files\", \"error\");\n      return;\n    }\n    if (validateFiles(droppedFiles, selectedFiles)) {\n      setSelectedFiles(prev => [...prev, ...droppedFiles]);\n      openSnackbar(`${droppedFiles.length} file(s) added successfully`, \"success\");\n    }\n  };\n\n  // Enhanced file input handler with validation\n  const handleValidatedFileUpload = event => {\n    const files = event.target.files;\n    if (!files) return;\n    const fileArray = Array.from(files).filter(file => file.type.startsWith(\"image/\"));\n    if (fileArray.length === 0) {\n      openSnackbar(\"Please select only image files\", \"error\");\n      return;\n    }\n    if (validateFiles(fileArray, selectedFiles)) {\n      setSelectedFiles(prev => [...prev, ...fileArray]);\n      // openSnackbar(`${fileArray.length} file(s) added successfully`, \"success\");\n    }\n\n    // Reset the input value to allow selecting the same files again\n    event.target.value = '';\n  };\n  const handleClose = () => {\n    setAnchorEl(null);\n  };\n  const formatFileSize = sizeInBytes => {\n    if (sizeInBytes >= 1024 * 1024) {\n      return (sizeInBytes / (1024 * 1024)).toFixed(2) + \" MB\";\n    } else {\n      return (sizeInBytes / 1024).toFixed(2) + \" KB\";\n    }\n  };\n  const handleRemoveFile = index => {\n    const updatedFiles = [...selectedFiles];\n    updatedFiles.splice(index, 1);\n    setSelectedFiles(updatedFiles);\n  };\n  const open = Boolean(anchorEl);\n  const id = open ? \"image-popover\" : undefined;\n  const imagePreviews = selectedFiles.map(file => ({\n    file,\n    preview: URL.createObjectURL(file),\n    size: formatFileSize(file.size)\n  }));\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      className: \"qadpt-uploadcontainer\",\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        className: `qadpt-drag-drop-container ${isDragging ? 'dragging' : ''}`,\n        onDragOver: handleDragOver,\n        onDragLeave: handleDragLeave,\n        onDrop: handleDrop,\n        sx: {\n          cursor: \"pointer\"\n        },\n        onClick: () => {\n          var _document$getElementB;\n          return (_document$getElementB = document.getElementById(\"new-file-upload\")) === null || _document$getElementB === void 0 ? void 0 : _document$getElementB.click();\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-imageupload\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            // onClick={() => document.getElementById(\"file-upload\")?.click()}\n            dangerouslySetInnerHTML: {\n              __html: uploadfile\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          align: \"center\",\n          color: \"textSecondary\",\n          sx: {\n            fontSize: \"14px\",\n            flexDirection: \"column\"\n          },\n          children: translate(\"Drag & Drop to upload file\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          align: \"center\",\n          color: \"textSecondary\",\n          sx: {\n            fontSize: \"12px\",\n            marginTop: \"4px\",\n            opacity: 0.7\n          },\n          children: translate(`Max ${MAX_FILES} files, 2MB each`)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-imageupload\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            id: \"new-file-upload\",\n            accept: \"image/*\",\n            onChange: handleValidatedFileUpload,\n            multiple: true,\n            style: {\n              display: \"none\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 13\n      }, this), selectedFiles.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n        className: \"selected-files\",\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          className: \"qadpt-title\",\n          children: translate(\"Uploaded Files\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 5\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          className: \"qadpt-upload-grid\",\n          children: imagePreviews.map((file, index) => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-upload-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-upload-left\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: file.preview,\n                  alt: `preview-${index}`,\n                  className: \"qadpt-upload-img\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"qadpt-upload-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"qadpt-upload-filename\",\n                    children: file.file.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"qadpt-upload-size\",\n                    children: file.size\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                className: \"qadpt-upload-close\",\n                size: \"small\",\n                onClick: () => handleRemoveFile(index),\n                children: /*#__PURE__*/_jsxDEV(CloseIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 17\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 5\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Popover, {\n      id: id,\n      open: open,\n      anchorEl: anchorEl,\n      onClose: handleClose,\n      anchorOrigin: {\n        vertical: \"bottom\",\n        horizontal: \"center\"\n      },\n      transformOrigin: {\n        vertical: \"top\",\n        horizontal: \"center\"\n      },\n      className: \"qadpt-imagepopup\",\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        className: \"qadpt-imagepopup-content\",\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-imagepopup-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            dangerouslySetInnerHTML: {\n              __html: replaceimageicon\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            className: \"qadpt-imagepopup-text\",\n            children: translate(\"Replace Image\", {\n              defaultValue: \"Replace Image\"\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            id: \"replace-upload\",\n            className: \"qadpt-imagepopup-upload\",\n            accept: \"image/*\",\n            onChange: handleValidatedFileUpload,\n            multiple: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s(ImageUploadSection, \"ttbyIf9Z0OA13TtXif9ay85116I=\", false, function () {\n  return [useTranslation, useSnackbar];\n});\n_c = ImageUploadSection;\nexport default ImageUploadSection;\nvar _c;\n$RefreshReg$(_c, \"ImageUploadSection\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Popover", "Grid", "IconButton", "useTranslation", "uploadfile", "replaceimageicon", "CloseIcon", "useSnackbar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ImageUploadSection", "selectedFiles", "setSelectedFiles", "_s", "t", "translate", "openSnackbar", "anchorEl", "setAnchorEl", "isDragging", "setIsDragging", "MAX_FILES", "MAX_FILE_SIZE", "validateFiles", "newFiles", "currentFiles", "length", "oversizedFiles", "filter", "file", "size", "map", "f", "name", "join", "handleDragOver", "e", "preventDefault", "handleDragLeave", "handleDrop", "droppedFiles", "Array", "from", "dataTransfer", "files", "type", "startsWith", "prev", "handleValidatedFileUpload", "event", "target", "fileArray", "value", "handleClose", "formatFileSize", "sizeInBytes", "toFixed", "handleRemoveFile", "index", "updatedFiles", "splice", "open", "Boolean", "id", "undefined", "imagePreviews", "preview", "URL", "createObjectURL", "children", "className", "onDragOver", "onDragLeave", "onDrop", "sx", "cursor", "onClick", "_document$getElementB", "document", "getElementById", "click", "dangerouslySetInnerHTML", "__html", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "align", "color", "fontSize", "flexDirection", "marginTop", "opacity", "accept", "onChange", "multiple", "style", "display", "container", "spacing", "item", "xs", "src", "alt", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "defaultValue", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/components/common/ImageUploadSection.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { Box, Typography, Popover, Grid, IconButton } from \"@mui/material\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport {\r\n    uploadfile,\r\n    hyperlink,\r\n    files,\r\n    uploadicon,\r\n    replaceimageicon,\r\n    galleryicon,\r\n} from \"../../assets/icons/icons\";\r\nimport \"./ImageUploadSection.css\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport { useSnackbar } from \"../../SnackbarContext\";\r\n\r\ninterface ImageUploadSectionProps {\r\n    handleFileUpload?: (event: React.ChangeEvent<HTMLInputElement>) => void;\r\n    selectedFiles: File[];\r\n    setSelectedFiles: React.Dispatch<React.SetStateAction<File[]>>;\r\n}\r\n\r\nconst ImageUploadSection: React.FC<ImageUploadSectionProps> = ({\r\n    selectedFiles,\r\n    setSelectedFiles\r\n}) => {\r\n    const { t: translate } = useTranslation();\r\n    const { openSnackbar } = useSnackbar();\r\n    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);\r\n\r\n\r\nconst [isDragging, setIsDragging] = useState(false);\r\n\r\n// File validation constants\r\nconst MAX_FILES = 10;\r\nconst MAX_FILE_SIZE = 2 * 1024 * 1024; // 2MB in bytes\r\n\r\n// Validation function for file uploads\r\nconst validateFiles = (newFiles: File[], currentFiles: File[]) => {\r\n    // Check file count limit\r\n    if (currentFiles.length + newFiles.length > MAX_FILES) {\r\n        openSnackbar(`Cannot upload more than ${MAX_FILES} files. You can upload ${MAX_FILES - currentFiles.length} more file(s).`, \"error\");\r\n        return false;\r\n    }\r\n\r\n    // Check individual file sizes\r\n    const oversizedFiles = newFiles.filter(file => file.size > MAX_FILE_SIZE);\r\n    if (oversizedFiles.length > 0) {\r\n        openSnackbar(`Some files exceed the 2MB limit: ${oversizedFiles.map(f => f.name).join(', ')}`, \"error\");\r\n        return false;\r\n    }\r\n\r\n    return true;\r\n};\r\n\r\nconst handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {\r\n    e.preventDefault();\r\n    setIsDragging(true);\r\n};\r\n\r\nconst handleDragLeave = () => {\r\n    setIsDragging(false);\r\n};\r\n\r\nconst handleDrop = (e: React.DragEvent<HTMLDivElement>) => {\r\n    e.preventDefault();\r\n    setIsDragging(false);\r\n    const droppedFiles = Array.from(e.dataTransfer.files).filter(file =>\r\n        file.type.startsWith(\"image/\")\r\n    );\r\n\r\n    if (droppedFiles.length === 0) {\r\n        openSnackbar(\"Please drop only image files\", \"error\");\r\n        return;\r\n    }\r\n\r\n    if (validateFiles(droppedFiles, selectedFiles)) {\r\n        setSelectedFiles(prev => [...prev, ...droppedFiles]);\r\n        openSnackbar(`${droppedFiles.length} file(s) added successfully`, \"success\");\r\n    }\r\n};\r\n\r\n// Enhanced file input handler with validation\r\nconst handleValidatedFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const files = event.target.files;\r\n    if (!files) return;\r\n\r\n    const fileArray = Array.from(files).filter(file =>\r\n        file.type.startsWith(\"image/\")\r\n    );\r\n\r\n    if (fileArray.length === 0) {\r\n        openSnackbar(\"Please select only image files\", \"error\");\r\n        return;\r\n    }\r\n\r\n    if (validateFiles(fileArray, selectedFiles)) {\r\n        setSelectedFiles(prev => [...prev, ...fileArray]);\r\n        // openSnackbar(`${fileArray.length} file(s) added successfully`, \"success\");\r\n    }\r\n\r\n    // Reset the input value to allow selecting the same files again\r\n    event.target.value = '';\r\n};\r\n\r\n    const handleClose = () => {\r\n        setAnchorEl(null);\r\n    };\r\n\r\n    const formatFileSize = (sizeInBytes:any) => {\r\n        if (sizeInBytes >= 1024 * 1024) {\r\n          return (sizeInBytes / (1024 * 1024)).toFixed(2) + \" MB\";\r\n        } else {\r\n          return (sizeInBytes / 1024).toFixed(2) + \" KB\";\r\n        }\r\n    };\r\n\r\n    const handleRemoveFile = (index: number) => {\r\n        const updatedFiles = [...selectedFiles];\r\n        updatedFiles.splice(index, 1);\r\n        setSelectedFiles(updatedFiles);\r\n    };\r\n\r\n\r\n    const open = Boolean(anchorEl);\r\n    const id = open ? \"image-popover\" : undefined;\r\n\r\n    const imagePreviews = selectedFiles.map((file) => ({\r\n        file,\r\n        preview: URL.createObjectURL(file),\r\n        size: formatFileSize(file.size),\r\n    }));\r\n\r\n    \r\n    \r\n\r\n    return (\r\n        <>\r\n            <Box className=\"qadpt-uploadcontainer\">\r\n               \r\n                \r\n            <Box\r\n                className={`qadpt-drag-drop-container ${isDragging ? 'dragging' : ''}`}\r\n                onDragOver={handleDragOver}\r\n                onDragLeave={handleDragLeave}\r\n                    onDrop={handleDrop}\r\n                    sx={{ cursor: \"pointer\" }}\r\n                    onClick={() => document.getElementById(\"new-file-upload\")?.click()}\r\n                     \r\n            >\r\n                    <Box className=\"qadpt-imageupload\">\r\n                            <span\r\n                                // onClick={() => document.getElementById(\"file-upload\")?.click()}\r\n                                dangerouslySetInnerHTML={{ __html: uploadfile }}\r\n                        />\r\n                        \r\n                    </Box>\r\n                    <Typography\r\n\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\tcolor=\"textSecondary\"\r\n                        sx={{ fontSize: \"14px\", flexDirection: \"column\" }}\r\n                    \t>\r\n\t\t\t\t\t\t{translate(\"Drag & Drop to upload file\")}\r\n                    </Typography>\r\n                    <Typography\r\n\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\tcolor=\"textSecondary\"\r\n                        sx={{ fontSize: \"12px\", marginTop: \"4px\", opacity: 0.7 }}\r\n                    \t>\r\n\t\t\t\t\t\t{translate(`Max ${MAX_FILES} files, 2MB each`)}\r\n                    </Typography>\r\n                    {/* <Typography\r\n\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\tcolor=\"textSecondary\"\r\n                        sx={{ marginTop: \"8px\", fontSize: \"14px\" }}\r\n                        >\r\n\t\t\t\t\t\t{translate(\"Or\")}\r\n\t\t\t\t\t</Typography>  */}\r\n                    <Box className=\"qadpt-imageupload\">\r\n                        {/* <span\r\n                            onClick={() => document.getElementById(\"new-file-upload\")?.click()}\r\n                            dangerouslySetInnerHTML={{ __html: uploadicon }}\r\n                        /> */}\r\n                        <input\r\n                            type=\"file\"\r\n                            id=\"new-file-upload\"\r\n                            accept=\"image/*\"\r\n                            onChange={handleValidatedFileUpload}\r\n                            multiple\r\n                            style={{display:\"none\"}}\r\n                            />\r\n                    </Box>\r\n                                \r\n                                \r\n                </Box>\r\n                {selectedFiles.length > 0 && (\r\n        <Box className=\"selected-files\">\r\n    <Typography className=\"qadpt-title\">\r\n        {translate(\"Uploaded Files\")}\r\n    </Typography>\r\n\r\n    <Grid container spacing={2} className=\"qadpt-upload-grid\">\r\n        {imagePreviews.map((file, index) => (\r\n            <Grid item xs={6} key={index}>\r\n                <Box className=\"qadpt-upload-card\">\r\n                    <div className=\"qadpt-upload-left\">\r\n                        <img\r\n                            src={file.preview}\r\n                            alt={`preview-${index}`}\r\n                            className=\"qadpt-upload-img\"\r\n                        />\r\n                        <div className=\"qadpt-upload-info\">\r\n                            <div className=\"qadpt-upload-filename\">{file.file.name}</div>\r\n                            <span className=\"qadpt-upload-size\">{file.size}</span>\r\n                        </div>\r\n                    </div>\r\n                    <IconButton\r\n                        className=\"qadpt-upload-close\"\r\n                        size=\"small\"\r\n                        onClick={() => handleRemoveFile(index)}\r\n                    >\r\n                        <CloseIcon fontSize=\"small\" />\r\n                    </IconButton>\r\n                </Box>\r\n            </Grid>\r\n        ))}\r\n    </Grid>\r\n</Box>\r\n\r\n\r\n                )}\r\n                    \r\n            </Box>\r\n\r\n            <Popover\r\n                id={id}\r\n                open={open}\r\n                anchorEl={anchorEl}\r\n                onClose={handleClose}\r\n                anchorOrigin={{\r\n                    vertical: \"bottom\",\r\n                    horizontal: \"center\",\r\n                }}\r\n                transformOrigin={{\r\n                    vertical: \"top\",\r\n                    horizontal: \"center\",\r\n                }}\r\n                className=\"qadpt-imagepopup\"\r\n            >\r\n                <Box className=\"qadpt-imagepopup-content\">\r\n                    <Box className=\"qadpt-imagepopup-item\">\r\n                        <span dangerouslySetInnerHTML={{ __html: replaceimageicon }} />\r\n                        <Typography className=\"qadpt-imagepopup-text\">\r\n                            {translate(\"Replace Image\", { defaultValue: \"Replace Image\" })}\r\n                        </Typography>\r\n                        <input\r\n                            type=\"file\"\r\n                            id=\"replace-upload\"\r\n                            className=\"qadpt-imagepopup-upload\"\r\n                            accept=\"image/*\"\r\n                            onChange={handleValidatedFileUpload}\r\n                            multiple\r\n                        />\r\n                    </Box>\r\n\r\n                </Box>\r\n            </Popover>\r\n        </>\r\n    );\r\n};\r\n\r\nexport default ImageUploadSection;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,GAAG,EAAEC,UAAU,EAAEC,OAAO,EAAEC,IAAI,EAAEC,UAAU,QAAQ,eAAe;AAC1E,SAASC,cAAc,QAAQ,eAAe;AAC9C,SACIC,UAAU,EAIVC,gBAAgB,QAEb,0BAA0B;AACjC,OAAO,0BAA0B;AACjC,OAAOC,SAAS,MAAM,2BAA2B;AACjD,SAASC,WAAW,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQpD,MAAMC,kBAAqD,GAAGA,CAAC;EAC3DC,aAAa;EACbC;AACJ,CAAC,KAAK;EAAAC,EAAA;EACF,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGd,cAAc,CAAC,CAAC;EACzC,MAAM;IAAEe;EAAa,CAAC,GAAGX,WAAW,CAAC,CAAC;EACtC,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAqB,IAAI,CAAC;EAGtE,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM0B,SAAS,GAAG,EAAE;EACpB,MAAMC,aAAa,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;;EAEvC;EACA,MAAMC,aAAa,GAAGA,CAACC,QAAgB,EAAEC,YAAoB,KAAK;IAC9D;IACA,IAAIA,YAAY,CAACC,MAAM,GAAGF,QAAQ,CAACE,MAAM,GAAGL,SAAS,EAAE;MACnDL,YAAY,CAAC,2BAA2BK,SAAS,0BAA0BA,SAAS,GAAGI,YAAY,CAACC,MAAM,gBAAgB,EAAE,OAAO,CAAC;MACpI,OAAO,KAAK;IAChB;;IAEA;IACA,MAAMC,cAAc,GAAGH,QAAQ,CAACI,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,GAAGR,aAAa,CAAC;IACzE,IAAIK,cAAc,CAACD,MAAM,GAAG,CAAC,EAAE;MAC3BV,YAAY,CAAC,oCAAoCW,cAAc,CAACI,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC;MACvG,OAAO,KAAK;IAChB;IAEA,OAAO,IAAI;EACf,CAAC;EAED,MAAMC,cAAc,GAAIC,CAAkC,IAAK;IAC3DA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBjB,aAAa,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMkB,eAAe,GAAGA,CAAA,KAAM;IAC1BlB,aAAa,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMmB,UAAU,GAAIH,CAAkC,IAAK;IACvDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBjB,aAAa,CAAC,KAAK,CAAC;IACpB,MAAMoB,YAAY,GAAGC,KAAK,CAACC,IAAI,CAACN,CAAC,CAACO,YAAY,CAACC,KAAK,CAAC,CAAChB,MAAM,CAACC,IAAI,IAC7DA,IAAI,CAACgB,IAAI,CAACC,UAAU,CAAC,QAAQ,CACjC,CAAC;IAED,IAAIN,YAAY,CAACd,MAAM,KAAK,CAAC,EAAE;MAC3BV,YAAY,CAAC,8BAA8B,EAAE,OAAO,CAAC;MACrD;IACJ;IAEA,IAAIO,aAAa,CAACiB,YAAY,EAAE7B,aAAa,CAAC,EAAE;MAC5CC,gBAAgB,CAACmC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGP,YAAY,CAAC,CAAC;MACpDxB,YAAY,CAAC,GAAGwB,YAAY,CAACd,MAAM,6BAA6B,EAAE,SAAS,CAAC;IAChF;EACJ,CAAC;;EAED;EACA,MAAMsB,yBAAyB,GAAIC,KAA0C,IAAK;IAC9E,MAAML,KAAK,GAAGK,KAAK,CAACC,MAAM,CAACN,KAAK;IAChC,IAAI,CAACA,KAAK,EAAE;IAEZ,MAAMO,SAAS,GAAGV,KAAK,CAACC,IAAI,CAACE,KAAK,CAAC,CAAChB,MAAM,CAACC,IAAI,IAC3CA,IAAI,CAACgB,IAAI,CAACC,UAAU,CAAC,QAAQ,CACjC,CAAC;IAED,IAAIK,SAAS,CAACzB,MAAM,KAAK,CAAC,EAAE;MACxBV,YAAY,CAAC,gCAAgC,EAAE,OAAO,CAAC;MACvD;IACJ;IAEA,IAAIO,aAAa,CAAC4B,SAAS,EAAExC,aAAa,CAAC,EAAE;MACzCC,gBAAgB,CAACmC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGI,SAAS,CAAC,CAAC;MACjD;IACJ;;IAEA;IACAF,KAAK,CAACC,MAAM,CAACE,KAAK,GAAG,EAAE;EAC3B,CAAC;EAEG,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACtBnC,WAAW,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMoC,cAAc,GAAIC,WAAe,IAAK;IACxC,IAAIA,WAAW,IAAI,IAAI,GAAG,IAAI,EAAE;MAC9B,OAAO,CAACA,WAAW,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;IACzD,CAAC,MAAM;MACL,OAAO,CAACD,WAAW,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;IAChD;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAIC,KAAa,IAAK;IACxC,MAAMC,YAAY,GAAG,CAAC,GAAGhD,aAAa,CAAC;IACvCgD,YAAY,CAACC,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IAC7B9C,gBAAgB,CAAC+C,YAAY,CAAC;EAClC,CAAC;EAGD,MAAME,IAAI,GAAGC,OAAO,CAAC7C,QAAQ,CAAC;EAC9B,MAAM8C,EAAE,GAAGF,IAAI,GAAG,eAAe,GAAGG,SAAS;EAE7C,MAAMC,aAAa,GAAGtD,aAAa,CAACoB,GAAG,CAAEF,IAAI,KAAM;IAC/CA,IAAI;IACJqC,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACvC,IAAI,CAAC;IAClCC,IAAI,EAAEwB,cAAc,CAACzB,IAAI,CAACC,IAAI;EAClC,CAAC,CAAC,CAAC;EAKH,oBACIvB,OAAA,CAAAE,SAAA;IAAA4D,QAAA,gBACI9D,OAAA,CAACX,GAAG;MAAC0E,SAAS,EAAC,uBAAuB;MAAAD,QAAA,gBAGtC9D,OAAA,CAACX,GAAG;QACA0E,SAAS,EAAE,6BAA6BnD,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;QACvEoD,UAAU,EAAEpC,cAAe;QAC3BqC,WAAW,EAAElC,eAAgB;QACzBmC,MAAM,EAAElC,UAAW;QACnBmC,EAAE,EAAE;UAAEC,MAAM,EAAE;QAAU,CAAE;QAC1BC,OAAO,EAAEA,CAAA;UAAA,IAAAC,qBAAA;UAAA,QAAAA,qBAAA,GAAMC,QAAQ,CAACC,cAAc,CAAC,iBAAiB,CAAC,cAAAF,qBAAA,uBAA1CA,qBAAA,CAA4CG,KAAK,CAAC,CAAC;QAAA,CAAC;QAAAX,QAAA,gBAGnE9D,OAAA,CAACX,GAAG;UAAC0E,SAAS,EAAC,mBAAmB;UAAAD,QAAA,eAC1B9D,OAAA;YACI;YACA0E,uBAAuB,EAAE;cAAEC,MAAM,EAAEhF;YAAW;UAAE;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAED,CAAC,eACN/E,OAAA,CAACV,UAAU;UACzB0F,OAAO,EAAC,OAAO;UACfC,KAAK,EAAC,QAAQ;UACdC,KAAK,EAAC,eAAe;UACHf,EAAE,EAAE;YAAEgB,QAAQ,EAAE,MAAM;YAAEC,aAAa,EAAE;UAAS,CAAE;UAAAtB,QAAA,EAEnEtD,SAAS,CAAC,4BAA4B;QAAC;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACb/E,OAAA,CAACV,UAAU;UACzB0F,OAAO,EAAC,OAAO;UACfC,KAAK,EAAC,QAAQ;UACdC,KAAK,EAAC,eAAe;UACHf,EAAE,EAAE;YAAEgB,QAAQ,EAAE,MAAM;YAAEE,SAAS,EAAE,KAAK;YAAEC,OAAO,EAAE;UAAI,CAAE;UAAAxB,QAAA,EAE1EtD,SAAS,CAAC,OAAOM,SAAS,kBAAkB;QAAC;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eASb/E,OAAA,CAACX,GAAG;UAAC0E,SAAS,EAAC,mBAAmB;UAAAD,QAAA,eAK9B9D,OAAA;YACIsC,IAAI,EAAC,MAAM;YACXkB,EAAE,EAAC,iBAAiB;YACpB+B,MAAM,EAAC,SAAS;YAChBC,QAAQ,EAAE/C,yBAA0B;YACpCgD,QAAQ;YACRC,KAAK,EAAE;cAACC,OAAO,EAAC;YAAM;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGL,CAAC,EACL3E,aAAa,CAACe,MAAM,GAAG,CAAC,iBACjCnB,OAAA,CAACX,GAAG;QAAC0E,SAAS,EAAC,gBAAgB;QAAAD,QAAA,gBACnC9D,OAAA,CAACV,UAAU;UAACyE,SAAS,EAAC,aAAa;UAAAD,QAAA,EAC9BtD,SAAS,CAAC,gBAAgB;QAAC;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eAEb/E,OAAA,CAACR,IAAI;UAACoG,SAAS;UAACC,OAAO,EAAE,CAAE;UAAC9B,SAAS,EAAC,mBAAmB;UAAAD,QAAA,EACpDJ,aAAa,CAAClC,GAAG,CAAC,CAACF,IAAI,EAAE6B,KAAK,kBAC3BnD,OAAA,CAACR,IAAI;YAACsG,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAjC,QAAA,eACb9D,OAAA,CAACX,GAAG;cAAC0E,SAAS,EAAC,mBAAmB;cAAAD,QAAA,gBAC9B9D,OAAA;gBAAK+D,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAC9B9D,OAAA;kBACIgG,GAAG,EAAE1E,IAAI,CAACqC,OAAQ;kBAClBsC,GAAG,EAAE,WAAW9C,KAAK,EAAG;kBACxBY,SAAS,EAAC;gBAAkB;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACF/E,OAAA;kBAAK+D,SAAS,EAAC,mBAAmB;kBAAAD,QAAA,gBAC9B9D,OAAA;oBAAK+D,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,EAAExC,IAAI,CAACA,IAAI,CAACI;kBAAI;oBAAAkD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7D/E,OAAA;oBAAM+D,SAAS,EAAC,mBAAmB;oBAAAD,QAAA,EAAExC,IAAI,CAACC;kBAAI;oBAAAqD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACN/E,OAAA,CAACP,UAAU;gBACPsE,SAAS,EAAC,oBAAoB;gBAC9BxC,IAAI,EAAC,OAAO;gBACZ8C,OAAO,EAAEA,CAAA,KAAMnB,gBAAgB,CAACC,KAAK,CAAE;gBAAAW,QAAA,eAEvC9D,OAAA,CAACH,SAAS;kBAACsF,QAAQ,EAAC;gBAAO;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ;UAAC,GApBa5B,KAAK;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqBtB,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAGY;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEA,CAAC,eAEN/E,OAAA,CAACT,OAAO;MACJiE,EAAE,EAAEA,EAAG;MACPF,IAAI,EAAEA,IAAK;MACX5C,QAAQ,EAAEA,QAAS;MACnBwF,OAAO,EAAEpD,WAAY;MACrBqD,YAAY,EAAE;QACVC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MAChB,CAAE;MACFC,eAAe,EAAE;QACbF,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MAChB,CAAE;MACFtC,SAAS,EAAC,kBAAkB;MAAAD,QAAA,eAE5B9D,OAAA,CAACX,GAAG;QAAC0E,SAAS,EAAC,0BAA0B;QAAAD,QAAA,eACrC9D,OAAA,CAACX,GAAG;UAAC0E,SAAS,EAAC,uBAAuB;UAAAD,QAAA,gBAClC9D,OAAA;YAAM0E,uBAAuB,EAAE;cAAEC,MAAM,EAAE/E;YAAiB;UAAE;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/D/E,OAAA,CAACV,UAAU;YAACyE,SAAS,EAAC,uBAAuB;YAAAD,QAAA,EACxCtD,SAAS,CAAC,eAAe,EAAE;cAAE+F,YAAY,EAAE;YAAgB,CAAC;UAAC;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACb/E,OAAA;YACIsC,IAAI,EAAC,MAAM;YACXkB,EAAE,EAAC,gBAAgB;YACnBO,SAAS,EAAC,yBAAyB;YACnCwB,MAAM,EAAC,SAAS;YAChBC,QAAQ,EAAE/C,yBAA0B;YACpCgD,QAAQ;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA,eACZ,CAAC;AAEX,CAAC;AAACzE,EAAA,CA1PIH,kBAAqD;EAAA,QAI9BT,cAAc,EACdI,WAAW;AAAA;AAAA0G,EAAA,GALlCrG,kBAAqD;AA4P3D,eAAeA,kBAAkB;AAAC,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}