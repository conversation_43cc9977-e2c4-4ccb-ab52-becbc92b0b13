import React, { useState, useEffect, ChangeEvent } from "react";
import { v4 as uuidv4 } from "uuid";
import { getAllOrganizations } from "../../services/OrganizationService";
import { SubmitCreateAccount } from "../../services/AccountService";
import { TextField, Select, MenuItem, Button, IconButton, Snackbar, Alert, Grid, FormControl, Switch, FormControlLabel, Radio, RadioGroup } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useSnackbar } from "../../SnackbarContext";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../auth/AuthProvider";
import { useTranslation } from "react-i18next";
import { useRtl } from "../../RtlContext";

type InputFields = {
	AccountName: string;
	DomainUrl: string;
	AccountType: string;
	OrganizationId: string;
	AccountId: string;
	Active: boolean;
	Rtl: boolean;
	IsAIEnabled: boolean;
};
type ErrorFields = Partial<InputFields>;

const CreateAccount = (props: any) => {
	const { t: translate } = useTranslation();
	const { setLoading, setModels, showPopup, setShowPopup, orderByField, filters } = props;
	// const [showPopup, setShowPopup] = useState(false);
	const [snackbarOpen, setSnackbarOpen] = useState(false);
	const [snackbarMessage, setSnackbarMessage] = useState("");
	const [snackbarSeverity, setSnackbarSeverity] = useState<"success" | "error">("success");
	const { signOut, userDetails } = useAuth();
	const [OrganizationId, setOrganizationId] = useState(userDetails?.OrganizationId??"");
	const [skip, setskip] = useState("0");
	const [top, settop] = useState("100");
	const [totalcount, setTotalcount] = useState(0);
	const { openSnackbar } = useSnackbar();
	const [errors, setErrors] = useState<ErrorFields>({
		AccountName: "",
		DomainUrl: ""
	});
	const navigate = useNavigate();
	const { isRtl } = useRtl();

	const generateCustomUserId = () => {
		const now = new Date();
		const day = String(now.getDate()).padStart(2, "0");
		const month = String(now.getMonth() + 1).padStart(2, "0");
		const year = now.getFullYear();
		const datePart = `${day}${month}${year}`;
		const guidPart = uuidv4();
		return `${datePart}-${guidPart}`;
	};

	const [inputs, setInputs] = useState({
		AccountId: generateCustomUserId(),
		AccountName: "",
		CreatedBy: userDetails?.UserName,
		AccountType: "",
		CreatedDate: "",
		OrganizationId: userDetails?.OrganizationId,
		UpdatedBy: "",
		Active: Boolean(true),
		DomainUrl: "",
		Rtl: false,
		IsAIEnabled: false
	});	
	const [organizations, setOrganizations] = useState<any[]>([]);
	const [selectedOrganizationId, setSelectedOrganizationId] = useState(
		OrganizationId
	);

	const openPopup = () => {
		setShowPopup(true);
		handleOrganizationDropdownOpen();
	};
	const handleSubmit = (event: any) => {
		event.preventDefault();
	};
	const alphanumericRegex = /^[a-zA-Z0-9]*$/;

	const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
		const { name, value } = event.target;
		let error = "";
		let processedValue = value;

		if (name === "AccountType") {
			const selectedOrganization = organizations.find((org) => org.Name === value);
			if (selectedOrganization) {
				setInputs((values) => ({
					...values,
					AccountType: value,
					OrganizationId: OrganizationId, // Placeholder ID
				}));
			}
		} else if (name === "AccountName") {
			// Allow only letters and spaces, remove special characters and numbers
			processedValue = value.replace(/[^a-zA-Z\s]/g, "");

			// Validate trimmed value but keep original spacing for user experience
			const trimmedValue = processedValue.trim();
			if (trimmedValue === "") {
				error = translate("Account Name cannot be only spaces.");
			} else if (trimmedValue.length < 3) {
				error = translate("Account Name must be at least 3 characters.");
			} else if (trimmedValue.length > 50) {
				error = translate("Account Name cannot exceed 50 characters.");
			}
		} else if (name === "DomainUrl") {
			// Trim spaces for domain URL
			processedValue = value.trim();
			const domainPattern = /^(https?:\/\/)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/)?$/;

			// Check if value matches a valid full domain URL format
			if (processedValue && !domainPattern.test(processedValue)) {
				error = translate("Please enter a valid full domain URL (e.g., https://example.com).");
			}

		}
		

		// Update the state with the processed value
		setInputs((prev) => ({ ...prev, [name]: processedValue }));

		// Set error state
		setErrors((prev) => ({ ...prev, [name]: error }));
	};

	// In CreateAccount component

	// const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
	// 	const { name, value } = event.target;
	// 	let error = "";
	// 	let processedValue = value;

	// 	if (name === "AccountType") {
	// 		const selectedOrganization = organizations.find((org) => org.Name === value);
	// 		if (selectedOrganization) {
	// 			setInputs((values) => ({
	// 				...values,
	// 				AccountType: value,
	// 				OrganizationId: "********-*********-134dc53c-f123-4655-aa39-0529fa976863", // Placeholder ID
	// 			}));
	// 		}
	// 	} else if (name === "AccountName") {
	// 		// Allow only letters and spaces, remove special characters and numbers
	// 		processedValue = value.replace(/[^a-zA-Z\s]/g, "");

	// 		// Check if the length is less than 5 characters
	// 		if (processedValue.length < 5) {
	// 			error = "Account Name must be at least 5 characters.";
	// 		}
	// 	}

	// 	// Update the state with the processed value
	// 	setInputs((prev) => ({ ...prev, [name]: processedValue }));

	// 	// Set error state
	// 	setErrors((prev) => ({ ...prev, [name]: error }));
	// };

	const handleSubmitAccount = async (event: any) => {
		event.preventDefault();

		// Trim leading and trailing spaces from AccountName and DomainUrl before validation and submission
		const trimmedAccountName = inputs.AccountName.trim();
		const trimmedDomainUrl = inputs.DomainUrl.trim();
		const updatedInputs = {
			...inputs,
			AccountName: trimmedAccountName,
			DomainUrl: trimmedDomainUrl
		};

		const newErrors: ErrorFields = {};
		let isValid = true;

		// Validate AccountName
		if (!trimmedAccountName) {
			newErrors.AccountName = translate("Account Name is required.");
			isValid = false;
		} else if (trimmedAccountName.length < 3) {
			newErrors.AccountName = translate("Account Name must be at least 3 characters.");
			isValid = false;
		} else if (trimmedAccountName.length > 50) {
			newErrors.AccountName = translate("Account Name cannot exceed 50 characters.");
			isValid = false;
		} else if (/[^a-zA-Z\s]/g.test(trimmedAccountName)) {
			newErrors.AccountName = translate("Account Name can only contain letters and spaces.");
			isValid = false;
		}

		// Validate DomainUrl
		if (!trimmedDomainUrl) {
			newErrors.DomainUrl = translate("Domain Url is required.");
			isValid = false;
		} else {
			const domainPattern = /^(https?:\/\/)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/)?$/;
			if (!domainPattern.test(trimmedDomainUrl)) {
				newErrors.DomainUrl = translate("Please enter a valid full domain URL (e.g., https://example.com).");
				isValid = false;
			}
		}

		setErrors(newErrors);

		if (!isValid) {
			// Show first error in snackbar
			const firstError = newErrors.AccountName || newErrors.DomainUrl;
			if (firstError) {
				setSnackbarMessage(firstError);
				setSnackbarSeverity("error");
				setSnackbarOpen(true);
			}
			return;
		}

		const redirectUrl = `/${OrganizationId}/accounts`;
		setTimeout(() => {
			navigate(redirectUrl);
		}, 3000);
		const newInputs = {
			...updatedInputs,
			AccountId: generateCustomUserId(), // Generate a new ID on submit
			Active: true,
			// Ensure Rtl value is included (it's already in inputs, but being explicit here)
			Rtl: inputs.Rtl,
			IsAIEnabled: inputs.IsAIEnabled
		};

		setLoading(true);
		try {
			SubmitCreateAccount(
				setLoading,
				setShowPopup,
				setModels,
				newInputs,
				OrganizationId,
				skip,
				top,
				setTotalcount,
				openSnackbar,
				orderByField,
				filters
			);
			//openSnackbar("User  created successfully!", "success");
		} catch (error) {
		//openSnackbar("Failed to create user .", "error");
		}
	};

	const isAccountNameValid = (name: string): boolean => {
		const processedValue = name.replace(/[^a-zA-Z\s]/g, "");
		const trimmedValue = processedValue.trim();
		return trimmedValue.length >= 3 && trimmedValue.length <= 50;
	};

	const handleOrganizationDropdownOpen = async () => {
		try {
			const response = await getAllOrganizations(setOrganizations, setLoading);
		} catch (error: any) {
			console.error("Error fetching organizations:", error);
			setSnackbarMessage(translate("Error fetching organizations:") + ` ${error.message}`);
			setSnackbarSeverity("error");
			setSnackbarOpen(true);
		}
	};
	const handleSelectChange = (event: any) => {
		const selectedName = event.target.value;
		const selectedOrganization = organizations.find((org) => org.Name === selectedName);
		if (selectedOrganization) {
			setSelectedOrganizationId(OrganizationId);
		}

	};

	const handleSnackbarClose = () => {
		setSnackbarOpen(false);
	};

	return (
		<div>
			{showPopup ? (
				<div className="qadpt-modal-overlay">
				<div className="qadpt-accountcreatepopup">
				  <div className="qadpt-title-sec">
					<div className="qadpt-title">{translate('Create Account')}</div>
					<svg
					  onClick={() => setShowPopup(false)}
					  className="qadpt-closeicon"
					  xmlns="http://www.w3.org/2000/svg"
					  x="0px"
					  y="0px"
					  width="24"
					  height="24"
					  viewBox="0 0 50 50"
					>
					  <path d="M 7.71875 6.28125 L 6.28125 7.71875 L 23.5625 25 L 6.28125 42.28125 L 7.71875 43.71875 L 25 26.4375 L 42.28125 43.71875 L 43.71875 42.28125 L 26.4375 25 L 43.71875 7.71875 L 42.28125 6.28125 L 25 23.5625 Z"></path>
					</svg>
				  </div>
				  <div className="qadpt-accountcreatefield">
					<Grid container>
					  <Grid item>
						<FormControl fullWidth required>
						  <label htmlFor="account-name">{translate('Account Name')}</label>
						  <TextField
							id="account-name"
							name="AccountName"
							required
							value={inputs.AccountName}
							onChange={handleChange}
							placeholder={translate('E.g., Google')}
							helperText={errors.AccountName}
							variant="outlined"
							error={!!errors.AccountName}
											inputProps={{ maxLength: 60 }}
							className="qadpt-acctfield"
						  />
						</FormControl>
						<FormControl fullWidth required>
						  <label htmlFor="account-name">{translate('Domain Url')}</label>
						  <TextField
							id="domain-url"
							name="DomainUrl"
							required
							value={inputs.DomainUrl}
							onChange={handleChange}
							placeholder={translate('Domain Url Here')}
							helperText={errors.DomainUrl}
							variant="outlined"
							error={!!errors.DomainUrl}
							inputProps={{ maxLength: 50 }}
							className="qadpt-acctfield"
						  />
						</FormControl>
						<div className="qadpt-txtfld">
										
										<div style={{display:"flex",justifyContent:"space-between", margin: "10px 0"}}>
										<span style={{ marginLeft: '10px' }}>
			RTL
		</span>
		<label className="toggle-switch">
			<input
				type="checkbox"
				checked={inputs.Rtl} // true = RTL, false = LTR
				onChange={(e) =>
					setInputs(prev => ({
						...prev,
						Rtl: e.target.checked
					}))
				}
													name={translate("Rtl")}
			/>
			<span className="slider"></span>
		</label>
		
	</div>
									</div>
									
									<div className="qadpt-txtfld">
                                       
                                        <div style={{display:"flex",justifyContent:"space-between", margin: "10px 0"}}>
                                        <span style={{ marginLeft: '10px' }}>
            Dona
        </span>
        <label className="toggle-switch">
            <input
                type="checkbox"
                checked={inputs.IsAIEnabled} 
                onChange={(e) =>
                    setInputs(prev => ({
                        ...prev,
                        IsAIEnabled: e.target.checked
                    }))
                }
                                                    name={translate("Dona")}
            />
            <span className="slider"></span>
        </label>
       
    </div>
</div>

					  </Grid>
					</Grid>
				  </div>
				  <div className="qadpt-account-buttons">
      <Button
        className={`qadpt-save-btn ${
          !isAccountNameValid(inputs.AccountName) ? 'invalid' : ''
        }`}
        onClick={handleSubmitAccount}
						//disabled={!isAccountNameValid(inputs.AccountName)}
      >
        {translate('Save')}
      </Button>
    </div>
				</div>
			  </div>
			  
			) : (
				""
			)}
			<Snackbar
				className="qadpt-accountalert"
				open={snackbarOpen}
				autoHideDuration={4000}
				onClose={handleSnackbarClose}
				anchorOrigin={{ vertical: "top", horizontal: "center" }}
			>
				<Alert
					onClose={handleSnackbarClose}
					severity={snackbarSeverity}
					// sx={{ width: "100%" }}
				>
					{snackbarMessage}
				</Alert>
			</Snackbar>
		</div>
	);
};

export default CreateAccount;
