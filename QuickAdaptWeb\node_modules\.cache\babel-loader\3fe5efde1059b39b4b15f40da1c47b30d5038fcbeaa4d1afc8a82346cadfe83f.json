{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptWeb\\\\src\\\\components\\\\settings\\\\Settings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useContext } from \"react\";\nimport { NavLink, useLocation } from \"react-router-dom\";\nimport { subscribe } from \"../adminMenu/sidemenustate\";\nimport { Container, List, ListItem, ListItemText, Typography, Box } from \"@mui/material\";\nimport { styled } from '@mui/material/styles';\nimport { useAuth } from \"../auth/AuthProvider\";\nimport { useTranslation } from \"react-i18next\";\nimport { AccountContext } from \"../account/AccountContext\";\n\n// Styled components for modern design\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ModernSidebar = styled(Box)(({\n  sidebarOpen\n}) => ({\n  width: sidebarOpen ? '280px' : '80px',\n  height: '100vh',\n  backgroundColor: 'var(--color-white)',\n  borderRight: '1px solid var(--color-gray-200)',\n  transition: 'var(--transition-normal)',\n  position: 'fixed',\n  left: 0,\n  top: '45px',\n  zIndex: 10,\n  boxShadow: 'var(--shadow-sm)'\n}));\nconst SidebarHeader = styled(Box)({\n  padding: 'var(--spacing-6) var(--spacing-4) var(--spacing-4)',\n  borderBottom: '1px solid var(--color-gray-100)'\n});\nconst SidebarTitle = styled(Typography)({\n  fontSize: 'var(--font-size-lg)',\n  fontWeight: 'var(--font-weight-semibold)',\n  color: 'var(--color-gray-900)',\n  margin: 0\n});\nconst ModernList = styled(List)({\n  padding: 'var(--spacing-2) 0'\n});\nconst ModernListItemWrapper = styled(NavLink)(({\n  active\n}) => ({\n  margin: '0 var(--spacing-2)',\n  borderRadius: 'var(--radius-md)',\n  marginBottom: 'var(--spacing-1)',\n  padding: 'var(--spacing-3) var(--spacing-4)',\n  transition: 'var(--transition-fast)',\n  textDecoration: 'none',\n  display: 'block',\n  cursor: 'pointer',\n  ...(active && {\n    backgroundColor: 'var(--color-primary-50)',\n    borderLeft: '3px solid var(--color-primary-600)'\n  }),\n  '&:hover': {\n    backgroundColor: active ? 'var(--color-primary-50)' : 'var(--color-gray-50)'\n  }\n}));\nconst ModernListItemText = styled(Typography)(({\n  active\n}) => ({\n  fontSize: 'var(--font-size-sm)',\n  fontWeight: active ? 'var(--font-weight-medium)' : 'var(--font-weight-normal)',\n  color: active ? 'var(--color-primary-700)' : 'var(--color-gray-700)',\n  transition: 'var(--transition-fast)'\n}));\nconst Settings = () => {\n  _s();\n  var _userDetails$Organiza, _userDetails$UserType;\n  const [sidebarOpen, setSidebarOpen] = useState(true);\n  const location = useLocation();\n  const isHidden = location.pathname === \"/settings/team\";\n  const {\n    signOut,\n    userDetails\n  } = useAuth();\n  const [OrganizationId, setOrganizationId] = useState((_userDetails$Organiza = userDetails === null || userDetails === void 0 ? void 0 : userDetails.OrganizationId) !== null && _userDetails$Organiza !== void 0 ? _userDetails$Organiza : \"\");\n  const [userType, setUserType] = useState((_userDetails$UserType = userDetails === null || userDetails === void 0 ? void 0 : userDetails.UserType) !== null && _userDetails$UserType !== void 0 ? _userDetails$UserType : \"\");\n  const {\n    roles\n  } = useContext(AccountContext);\n  const [user, setUser] = useState(null);\n  useEffect(() => {\n    const unsubscribe = subscribe(setSidebarOpen);\n    return () => unsubscribe();\n  }, []);\n  useEffect(() => {\n    const userInfoString = localStorage.getItem(\"userInfo\");\n    if (userInfoString) {\n      try {\n        const userInfo = JSON.parse(userInfoString);\n        if (userInfo['user']) {\n          const parsedUser = JSON.parse(userInfo['user']);\n          setUser(parsedUser);\n          if (parsedUser) {\n            var _parsedUser$Organizat;\n            const OrgId = (_parsedUser$Organizat = parsedUser.OrganizationId) !== null && _parsedUser$Organizat !== void 0 ? _parsedUser$Organizat : '';\n            setOrganizationId(OrgId);\n          }\n        }\n      } catch (error) {\n        console.error(\"Error parsing userInfo: \", error);\n      }\n    } else if (userDetails) {\n      setUser(userDetails);\n      if (userDetails) {\n        var _userDetails$Organiza2;\n        const OrgId = (_userDetails$Organiza2 = userDetails.OrganizationId) !== null && _userDetails$Organiza2 !== void 0 ? _userDetails$Organiza2 : '';\n        setOrganizationId(OrgId);\n      }\n    }\n  }, []);\n  const {\n    t: translate\n  } = useTranslation();\n  const settingsItems = [{\n    text: translate('Team'),\n    path: `/${OrganizationId}/team`,\n    access: ['admin']\n  }, {\n    text: translate('Roles'),\n    path: `/${OrganizationId}/roles`,\n    access: ['admin']\n  }, {\n    text: translate('Account'),\n    path: `/${OrganizationId}/accounts`,\n    access: ['admin']\n  }, {\n    text: translate('Multilingual'),\n    path: \"/settings/multilingual\",\n    access: ['admin', 'user']\n  }, {\n    text: translate('Domain'),\n    path: \"/settings/domains\",\n    access: ['admin']\n  }, {\n    text: translate('Rights'),\n    path: \"/settings/rights\",\n    access: ['admin', 'user']\n  }, {\n    text: translate('Alerts'),\n    path: \"/settings/alerts\",\n    access: ['admin', 'user']\n  }, {\n    text: translate('Billing'),\n    path: \"/settings/billing\",\n    access: ['admin']\n  }, {\n    text: translate('Installation'),\n    path: \"/settings/install\",\n    access: ['admin', 'user']\n  }, {\n    text: translate('Activity Log'),\n    path: \"/settings/activitylog\",\n    access: ['admin']\n  }, {\n    text: translate('Agents'),\n    path: \"/settings/agents\",\n    access: ['admin', 'user']\n  }, {\n    text: translate('Training'),\n    path: \"/settings/training\",\n    access: ['admin', 'user']\n  }];\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      className: \"sidemenubar\",\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        className: `qadpt-setsidebar ${sidebarOpen ? \"sidebar-open\" : \"sidebar-closed\"}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-sidebarlist\",\n          children: translate('Settings')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          className: \"qadpt-sidebarele\",\n          children: [{\n            text: translate('Team'),\n            path: `/${OrganizationId}/team`,\n            access: ['admin'],\n            roles: []\n          }, {\n            text: translate('Roles'),\n            path: `/${OrganizationId}/roles`,\n            access: ['admin'],\n            roles: []\n          }, {\n            text: translate('Account'),\n            path: `/${OrganizationId}/accounts`,\n            access: ['admin'],\n            roles: []\n          }, {\n            text: translate('Domain'),\n            path: \"/settings/domains\",\n            access: ['admin'],\n            roles: []\n          }, {\n            text: translate('Rights'),\n            path: \"/settings/rights\",\n            access: ['admin', 'user'],\n            roles: [\"Account Admin\", \"Editor\", \"Publisher\"]\n          }, {\n            text: translate('Alerts'),\n            path: \"/settings/alerts\",\n            access: ['admin', 'user'],\n            roles: [\"Account Admin\", \"Editor\", \"Publisher\"]\n          }, {\n            text: translate('Billing'),\n            path: \"/settings/billing\",\n            access: ['admin'],\n            roles: [\"Account Admin\", \"Editor\", \"Publisher\"]\n          }, {\n            text: translate('Installation'),\n            path: \"/settings/install\",\n            access: ['admin', 'user'],\n            roles: [\"Account Admin\"]\n          }, {\n            text: translate('Activity Log'),\n            path: \"/settings/activitylog\",\n            access: ['admin'],\n            roles: [\"Account Admin\", \"Editor\", \"Publisher\"]\n          }, {\n            text: translate('Agents'),\n            path: \"/settings/agents\",\n            access: ['admin', 'user'],\n            roles: [\"Account Admin\", \"Editor\", \"Publisher\"]\n          }, {\n            text: translate('Training'),\n            path: \"/settings/training\",\n            access: ['admin', 'user'],\n            roles: [\"Account Admin\", \"Editor\", \"Publisher\"]\n          }].map((item, index) => /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: item.access.includes(userType.toLocaleLowerCase()) && (userType.toLocaleLowerCase() == \"user\" ? item.roles.some(role => roles === null || roles === void 0 ? void 0 : roles.includes(role)) : true) && /*#__PURE__*/_jsxDEV(ListItem, {\n              component: NavLink,\n              to: item.path,\n              className: `qadpt-sidebarinput ${location.pathname === item.path || item.path === \"/settings/agents\" && (location.pathname === \"/settings/scripts\" || location.pathname === \"/settings/scripthistory\" || location.pathname === \"/settings/scripthistoryviewer\") || item.path === `/${OrganizationId}/accounts` && location.pathname === `/${OrganizationId}/accounts/free-trial` ? \"active\" : \"\"}`,\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: /*#__PURE__*/_jsxDEV(Typography, {\n                  className: `qadpt-sidebarval ${location.pathname === item.path || item.path === \"/settings/agents\" && (location.pathname === \"/settings/scripts\" || location.pathname === \"/settings/scripthistory\" || location.pathname === \"/settings/scripthistoryviewer\") || item.path === `/${OrganizationId}/accounts` && location.pathname === `/${OrganizationId}/accounts/free-trial` ? \"active\" : \"\"}`\n                  // sx={{\n                  // \tfontFamily: \"Poppins\",\n                  // \tfontSize: \"14px\",\n                  // \tfontWeight: 400,\n                  // \tlineHeight: \"21px\",\n                  // \tletterSpacing: \"0.3px\",\n                  // \ttextAlign: \"left\",\n                  // \tcolor: location.pathname === item.path ? \"#ffffff\" : \"#202224\",\n                  // \tmarginLeft: \"-2px\",\n                  // }}\n                  ,\n                  children: item.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 12\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 10\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 9\n            }, this)\n          }, void 0, false))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 4\n    }, this)\n  }, void 0, false);\n};\n_s(Settings, \"/wuiqF32hajxRECbgV5QJ+YHfvw=\", false, function () {\n  return [useLocation, useAuth, useTranslation];\n});\n_c = Settings;\nexport default Settings;\nvar _c;\n$RefreshReg$(_c, \"Settings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "NavLink", "useLocation", "subscribe", "Container", "List", "ListItem", "ListItemText", "Typography", "Box", "styled", "useAuth", "useTranslation", "AccountContext", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ModernSidebar", "sidebarOpen", "width", "height", "backgroundColor", "borderRight", "transition", "position", "left", "top", "zIndex", "boxShadow", "SidebarHeader", "padding", "borderBottom", "SidebarTitle", "fontSize", "fontWeight", "color", "margin", "ModernList", "ModernListItemWrapper", "active", "borderRadius", "marginBottom", "textDecoration", "display", "cursor", "borderLeft", "ModernListItemText", "Settings", "_s", "_userDetails$Organiza", "_userDetails$UserType", "setSidebarOpen", "location", "isHidden", "pathname", "signOut", "userDetails", "OrganizationId", "setOrganizationId", "userType", "setUserType", "UserType", "roles", "user", "setUser", "unsubscribe", "userInfoString", "localStorage", "getItem", "userInfo", "JSON", "parse", "parsedUser", "_parsedUser$Organizat", "OrgId", "error", "console", "_userDetails$Organiza2", "t", "translate", "settingsItems", "text", "path", "access", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "index", "includes", "toLocaleLowerCase", "some", "role", "component", "to", "primary", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/components/settings/Settings.tsx"], "sourcesContent": ["import React, { useState, useEffect, useContext } from \"react\";\r\nimport { NavLink, useLocation } from \"react-router-dom\";\r\nimport { isSidebarOpen, subscribe } from \"../adminMenu/sidemenustate\";\r\nimport { Container, List, ListItem, ListItemText, Typography, Box } from \"@mui/material\";\r\nimport { styled } from '@mui/material/styles';\r\nimport { useAuth } from \"../auth/AuthProvider\";\r\nimport { User } from \"../../models/User\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport Card from \"../common/Card\";\r\nimport { AccountContext } from \"../account/AccountContext\";\r\n\r\n\r\n// Styled components for modern design\r\nconst ModernSidebar = styled(Box)<{ sidebarOpen: boolean }>(({ sidebarOpen }) => ({\r\n  width: sidebarOpen ? '280px' : '80px',\r\n  height: '100vh',\r\n  backgroundColor: 'var(--color-white)',\r\n  borderRight: '1px solid var(--color-gray-200)',\r\n  transition: 'var(--transition-normal)',\r\n  position: 'fixed',\r\n  left: 0,\r\n  top: '45px',\r\n  zIndex: 10,\r\n  boxShadow: 'var(--shadow-sm)',\r\n}));\r\n\r\nconst SidebarHeader = styled(Box)({\r\n  padding: 'var(--spacing-6) var(--spacing-4) var(--spacing-4)',\r\n  borderBottom: '1px solid var(--color-gray-100)',\r\n});\r\n\r\nconst SidebarTitle = styled(Typography)({\r\n  fontSize: 'var(--font-size-lg)',\r\n  fontWeight: 'var(--font-weight-semibold)',\r\n  color: 'var(--color-gray-900)',\r\n  margin: 0,\r\n});\r\n\r\nconst ModernList = styled(List)({\r\n  padding: 'var(--spacing-2) 0',\r\n});\r\n\r\nconst ModernListItemWrapper = styled(NavLink)<{ active: boolean }>(({ active }) => ({\r\n  margin: '0 var(--spacing-2)',\r\n  borderRadius: 'var(--radius-md)',\r\n  marginBottom: 'var(--spacing-1)',\r\n  padding: 'var(--spacing-3) var(--spacing-4)',\r\n  transition: 'var(--transition-fast)',\r\n  textDecoration: 'none',\r\n  display: 'block',\r\n  cursor: 'pointer',\r\n\r\n  ...(active && {\r\n    backgroundColor: 'var(--color-primary-50)',\r\n    borderLeft: '3px solid var(--color-primary-600)',\r\n  }),\r\n\r\n  '&:hover': {\r\n    backgroundColor: active ? 'var(--color-primary-50)' : 'var(--color-gray-50)',\r\n  },\r\n}));\r\n\r\nconst ModernListItemText = styled(Typography)<{ active: boolean }>(({ active }) => ({\r\n  fontSize: 'var(--font-size-sm)',\r\n  fontWeight: active ? 'var(--font-weight-medium)' : 'var(--font-weight-normal)',\r\n  color: active ? 'var(--color-primary-700)' : 'var(--color-gray-700)',\r\n  transition: 'var(--transition-fast)',\r\n}));\r\n\r\nconst Settings = () => {\r\n\tconst [sidebarOpen, setSidebarOpen] = useState(true);\r\n\tconst location = useLocation();\r\n\tconst isHidden = location.pathname === \"/settings/team\";\r\n\tconst { signOut, userDetails } = useAuth();\r\n\tconst [OrganizationId, setOrganizationId] = useState(userDetails?.OrganizationId ?? \"\");\r\n\tconst [userType, setUserType] = useState(userDetails?.UserType ?? \"\");\r\n\tconst { roles } = useContext(AccountContext);\r\n\r\n\tconst [user, setUser] = useState<User | null>(null);\r\n\r\n\tuseEffect(() => {\r\n\t\tconst unsubscribe = subscribe(setSidebarOpen);\r\n\t\treturn () => unsubscribe();\r\n\t}, []);\r\n\r\n\tuseEffect(() => {\r\n\t\tconst userInfoString = localStorage.getItem(\"userInfo\");\r\n\t\tif (userInfoString) {\r\n\t\t\ttry {\r\n\t\t\t\tconst userInfo = JSON.parse(userInfoString);\r\n\t\t\t\tif (userInfo['user']) {\r\n\t\t\t\t\tconst parsedUser = JSON.parse(userInfo['user']);\r\n\t\t\t\t\tsetUser(parsedUser);\r\n\t\t\t\t\tif (parsedUser) {\r\n\t\t\t\t\t\tconst OrgId = parsedUser.OrganizationId ?? '';\r\n\t\t\t\t\t\tsetOrganizationId(OrgId);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(\"Error parsing userInfo: \", error);\r\n\t\t\t}\r\n\t\t}\r\n\t\telse if (userDetails) {\r\n\t\t\tsetUser(userDetails);\r\n\t\t\tif (userDetails) {\r\n\t\t\t\tconst OrgId = userDetails.OrganizationId ?? '';\r\n\t\t\t\tsetOrganizationId(OrgId);\r\n\t\t\t}\r\n\t\t}\r\n\t}, []);\r\n\r\n\tconst { t: translate } = useTranslation();\r\n\r\n\tconst settingsItems = [\r\n\t\t{ text: translate('Team'), path: `/${OrganizationId}/team`, access: ['admin'] },\r\n\t\t{ text: translate('Roles'), path: `/${OrganizationId}/roles`, access: ['admin'] },\r\n\t\t{ text: translate('Account'), path: `/${OrganizationId}/accounts`, access: ['admin'] },\r\n\t\t{ text: translate('Multilingual'), path: \"/settings/multilingual\", access: ['admin','user'] },\r\n\t\t{ text: translate('Domain'), path: \"/settings/domains\", access: ['admin'] },\r\n\t\t{ text: translate('Rights'), path: \"/settings/rights\", access: ['admin','user'] },\r\n\t\t{ text: translate('Alerts'), path: \"/settings/alerts\", access: ['admin','user'] },\r\n\t\t{ text: translate('Billing'), path: \"/settings/billing\", access: ['admin'] },\r\n\t\t{ text: translate('Installation'), path: \"/settings/install\", access: ['admin','user'] },\r\n\t\t{ text: translate('Activity Log'), path: \"/settings/activitylog\", access: ['admin'] },\r\n\t\t{ text: translate('Agents'), path: \"/settings/agents\", access: ['admin','user'] },\r\n\t\t{ text: translate('Training'), path: \"/settings/training\", access: ['admin','user'] },\r\n\t];\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t<Container className=\"sidemenubar\">\r\n\t\t\t\t<Box className={`qadpt-setsidebar ${sidebarOpen ? \"sidebar-open\" : \"sidebar-closed\"}`}>\r\n\t\t\t\t\t<div className=\"qadpt-sidebarlist\">{translate('Settings')}</div>\r\n\t\t\t\t\t<List className=\"qadpt-sidebarele\">\r\n\t\t\t\t\t\t{[\r\n\t\t\t\t\t\t\t{ text: translate('Team'), path: `/${OrganizationId}/team`, access: ['admin'],roles:[] },\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\ttext: translate('Roles'), path: `/${OrganizationId}/roles`, access: ['admin'],roles:[]\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{ text: translate('Account'), path: `/${OrganizationId}/accounts`, access: ['admin'],roles:[] },\r\n\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\ttext: translate('Domain'), path: \"/settings/domains\",\r\n\t\t\t\t\t\t\t\taccess: ['admin'],roles:[]\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\ttext: translate('Rights'), path: \"/settings/rights\",\r\n\t\t\t\t\t\t\t\taccess: ['admin', 'user'],roles:[\"Account Admin\",\"Editor\",\"Publisher\"]\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\ttext: translate('Alerts'), path: \"/settings/alerts\",\r\n\t\t\t\t\t\t\t\taccess: ['admin', 'user'],roles:[\"Account Admin\",\"Editor\",\"Publisher\"]\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\ttext: translate('Billing'), path: \"/settings/billing\",\r\n\t\t\t\t\t\t\t\taccess: ['admin'],roles:[\"Account Admin\",\"Editor\",\"Publisher\"]\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\ttext: translate('Installation'), path: \"/settings/install\",\r\n\t\t\t\t\t\t\t\taccess: ['admin', 'user'],roles:[\"Account Admin\"]\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\ttext: translate('Activity Log'), path: \"/settings/activitylog\",\r\n\t\t\t\t\t\t\t\taccess: ['admin'],roles:[\"Account Admin\",\"Editor\",\"Publisher\"]\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\ttext: translate('Agents'), path: \"/settings/agents\",\r\n\t\t\t\t\t\t\t\taccess: ['admin', 'user'],roles:[\"Account Admin\",\"Editor\",\"Publisher\"]\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\ttext: translate('Training'), path: \"/settings/training\",\r\n\t\t\t\t\t\t\t\taccess: ['admin', 'user'],roles:[\"Account Admin\",\"Editor\",\"Publisher\"]\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t].map((item, index) => (<>\r\n\t\t\t\t\t\t\t{item.access.includes(userType.toLocaleLowerCase()) && (userType.toLocaleLowerCase() == \"user\" ? item.roles.some(role => roles?.includes(role)): true) &&\r\n\t\t\t\t\t\t\t\t<ListItem\r\n\t\t\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\t\t\tcomponent={NavLink}\r\n\t\t\t\t\t\t\t\t\tto={item.path}\r\n\t\t\t\t\t\t\t\t\tclassName={`qadpt-sidebarinput ${location.pathname === item.path ||\r\n\t\t\t\t\t\t\t\t\t\t\t(item.path === \"/settings/agents\" && (location.pathname === \"/settings/scripts\" || location.pathname === \"/settings/scripthistory\" || location.pathname === \"/settings/scripthistoryviewer\")) ||\r\n\t\t\t\t\t\t\t\t\t\t\t(item.path === `/${OrganizationId}/accounts` && location.pathname === `/${OrganizationId}/accounts/free-trial`)\r\n\t\t\t\t\t\t\t\t\t\t\t? \"active\" : \"\"\r\n\t\t\t\t\t\t\t\t\t\t}`}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<ListItemText\r\n\t\t\t\t\t\t\t\t\t\tprimary={\r\n\t\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName={`qadpt-sidebarval ${location.pathname === item.path ||\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t(item.path === \"/settings/agents\" && (location.pathname === \"/settings/scripts\" || location.pathname === \"/settings/scripthistory\" || location.pathname === \"/settings/scripthistoryviewer\")) ||\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t(item.path === `/${OrganizationId}/accounts` && location.pathname === `/${OrganizationId}/accounts/free-trial`)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t? \"active\" : \"\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}`}\r\n\t\t\t\t\t\t\t\t\t\t\t// sx={{\r\n\t\t\t\t\t\t\t\t\t\t\t// \tfontFamily: \"Poppins\",\r\n\t\t\t\t\t\t\t\t\t\t\t// \tfontSize: \"14px\",\r\n\t\t\t\t\t\t\t\t\t\t\t// \tfontWeight: 400,\r\n\t\t\t\t\t\t\t\t\t\t\t// \tlineHeight: \"21px\",\r\n\t\t\t\t\t\t\t\t\t\t\t// \tletterSpacing: \"0.3px\",\r\n\t\t\t\t\t\t\t\t\t\t\t// \ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\t\t\t\t// \tcolor: location.pathname === item.path ? \"#ffffff\" : \"#202224\",\r\n\t\t\t\t\t\t\t\t\t\t\t// \tmarginLeft: \"-2px\",\r\n\t\t\t\t\t\t\t\t\t\t\t// }}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{item.text}\r\n\t\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</ListItem>\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t))}\r\n\t\t\t\t\t</List>\r\n\t\t\t\t</Box>\r\n\t\t\t</Container>\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default Settings;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SAASC,OAAO,EAAEC,WAAW,QAAQ,kBAAkB;AACvD,SAAwBC,SAAS,QAAQ,4BAA4B;AACrE,SAASC,SAAS,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,UAAU,EAAEC,GAAG,QAAQ,eAAe;AACxF,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,OAAO,QAAQ,sBAAsB;AAE9C,SAASC,cAAc,QAAQ,eAAe;AAE9C,SAASC,cAAc,QAAQ,2BAA2B;;AAG1D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,aAAa,GAAGR,MAAM,CAACD,GAAG,CAAC,CAA2B,CAAC;EAAEU;AAAY,CAAC,MAAM;EAChFC,KAAK,EAAED,WAAW,GAAG,OAAO,GAAG,MAAM;EACrCE,MAAM,EAAE,OAAO;EACfC,eAAe,EAAE,oBAAoB;EACrCC,WAAW,EAAE,iCAAiC;EAC9CC,UAAU,EAAE,0BAA0B;EACtCC,QAAQ,EAAE,OAAO;EACjBC,IAAI,EAAE,CAAC;EACPC,GAAG,EAAE,MAAM;EACXC,MAAM,EAAE,EAAE;EACVC,SAAS,EAAE;AACb,CAAC,CAAC,CAAC;AAEH,MAAMC,aAAa,GAAGpB,MAAM,CAACD,GAAG,CAAC,CAAC;EAChCsB,OAAO,EAAE,oDAAoD;EAC7DC,YAAY,EAAE;AAChB,CAAC,CAAC;AAEF,MAAMC,YAAY,GAAGvB,MAAM,CAACF,UAAU,CAAC,CAAC;EACtC0B,QAAQ,EAAE,qBAAqB;EAC/BC,UAAU,EAAE,6BAA6B;EACzCC,KAAK,EAAE,uBAAuB;EAC9BC,MAAM,EAAE;AACV,CAAC,CAAC;AAEF,MAAMC,UAAU,GAAG5B,MAAM,CAACL,IAAI,CAAC,CAAC;EAC9B0B,OAAO,EAAE;AACX,CAAC,CAAC;AAEF,MAAMQ,qBAAqB,GAAG7B,MAAM,CAACT,OAAO,CAAC,CAAsB,CAAC;EAAEuC;AAAO,CAAC,MAAM;EAClFH,MAAM,EAAE,oBAAoB;EAC5BI,YAAY,EAAE,kBAAkB;EAChCC,YAAY,EAAE,kBAAkB;EAChCX,OAAO,EAAE,mCAAmC;EAC5CP,UAAU,EAAE,wBAAwB;EACpCmB,cAAc,EAAE,MAAM;EACtBC,OAAO,EAAE,OAAO;EAChBC,MAAM,EAAE,SAAS;EAEjB,IAAIL,MAAM,IAAI;IACZlB,eAAe,EAAE,yBAAyB;IAC1CwB,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,SAAS,EAAE;IACTxB,eAAe,EAAEkB,MAAM,GAAG,yBAAyB,GAAG;EACxD;AACF,CAAC,CAAC,CAAC;AAEH,MAAMO,kBAAkB,GAAGrC,MAAM,CAACF,UAAU,CAAC,CAAsB,CAAC;EAAEgC;AAAO,CAAC,MAAM;EAClFN,QAAQ,EAAE,qBAAqB;EAC/BC,UAAU,EAAEK,MAAM,GAAG,2BAA2B,GAAG,2BAA2B;EAC9EJ,KAAK,EAAEI,MAAM,GAAG,0BAA0B,GAAG,uBAAuB;EACpEhB,UAAU,EAAE;AACd,CAAC,CAAC,CAAC;AAEH,MAAMwB,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA;EACtB,MAAM,CAAChC,WAAW,EAAEiC,cAAc,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAMuD,QAAQ,GAAGnD,WAAW,CAAC,CAAC;EAC9B,MAAMoD,QAAQ,GAAGD,QAAQ,CAACE,QAAQ,KAAK,gBAAgB;EACvD,MAAM;IAAEC,OAAO;IAAEC;EAAY,CAAC,GAAG9C,OAAO,CAAC,CAAC;EAC1C,MAAM,CAAC+C,cAAc,EAAEC,iBAAiB,CAAC,GAAG7D,QAAQ,EAAAoD,qBAAA,GAACO,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEC,cAAc,cAAAR,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;EACvF,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAG/D,QAAQ,EAAAqD,qBAAA,GAACM,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEK,QAAQ,cAAAX,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;EACrE,MAAM;IAAEY;EAAM,CAAC,GAAG/D,UAAU,CAACa,cAAc,CAAC;EAE5C,MAAM,CAACmD,IAAI,EAAEC,OAAO,CAAC,GAAGnE,QAAQ,CAAc,IAAI,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACf,MAAMmE,WAAW,GAAG/D,SAAS,CAACiD,cAAc,CAAC;IAC7C,OAAO,MAAMc,WAAW,CAAC,CAAC;EAC3B,CAAC,EAAE,EAAE,CAAC;EAENnE,SAAS,CAAC,MAAM;IACf,MAAMoE,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IACvD,IAAIF,cAAc,EAAE;MACnB,IAAI;QACH,MAAMG,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,cAAc,CAAC;QAC3C,IAAIG,QAAQ,CAAC,MAAM,CAAC,EAAE;UACrB,MAAMG,UAAU,GAAGF,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,MAAM,CAAC,CAAC;UAC/CL,OAAO,CAACQ,UAAU,CAAC;UACnB,IAAIA,UAAU,EAAE;YAAA,IAAAC,qBAAA;YACf,MAAMC,KAAK,IAAAD,qBAAA,GAAGD,UAAU,CAACf,cAAc,cAAAgB,qBAAA,cAAAA,qBAAA,GAAI,EAAE;YAC7Cf,iBAAiB,CAACgB,KAAK,CAAC;UACzB;QACD;MACD,CAAC,CAAC,OAAOC,KAAK,EAAE;QACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MACjD;IACD,CAAC,MACI,IAAInB,WAAW,EAAE;MACrBQ,OAAO,CAACR,WAAW,CAAC;MACpB,IAAIA,WAAW,EAAE;QAAA,IAAAqB,sBAAA;QAChB,MAAMH,KAAK,IAAAG,sBAAA,GAAGrB,WAAW,CAACC,cAAc,cAAAoB,sBAAA,cAAAA,sBAAA,GAAI,EAAE;QAC9CnB,iBAAiB,CAACgB,KAAK,CAAC;MACzB;IACD;EACD,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM;IAAEI,CAAC,EAAEC;EAAU,CAAC,GAAGpE,cAAc,CAAC,CAAC;EAEzC,MAAMqE,aAAa,GAAG,CACrB;IAAEC,IAAI,EAAEF,SAAS,CAAC,MAAM,CAAC;IAAEG,IAAI,EAAE,IAAIzB,cAAc,OAAO;IAAE0B,MAAM,EAAE,CAAC,OAAO;EAAE,CAAC,EAC/E;IAAEF,IAAI,EAAEF,SAAS,CAAC,OAAO,CAAC;IAAEG,IAAI,EAAE,IAAIzB,cAAc,QAAQ;IAAE0B,MAAM,EAAE,CAAC,OAAO;EAAE,CAAC,EACjF;IAAEF,IAAI,EAAEF,SAAS,CAAC,SAAS,CAAC;IAAEG,IAAI,EAAE,IAAIzB,cAAc,WAAW;IAAE0B,MAAM,EAAE,CAAC,OAAO;EAAE,CAAC,EACtF;IAAEF,IAAI,EAAEF,SAAS,CAAC,cAAc,CAAC;IAAEG,IAAI,EAAE,wBAAwB;IAAEC,MAAM,EAAE,CAAC,OAAO,EAAC,MAAM;EAAE,CAAC,EAC7F;IAAEF,IAAI,EAAEF,SAAS,CAAC,QAAQ,CAAC;IAAEG,IAAI,EAAE,mBAAmB;IAAEC,MAAM,EAAE,CAAC,OAAO;EAAE,CAAC,EAC3E;IAAEF,IAAI,EAAEF,SAAS,CAAC,QAAQ,CAAC;IAAEG,IAAI,EAAE,kBAAkB;IAAEC,MAAM,EAAE,CAAC,OAAO,EAAC,MAAM;EAAE,CAAC,EACjF;IAAEF,IAAI,EAAEF,SAAS,CAAC,QAAQ,CAAC;IAAEG,IAAI,EAAE,kBAAkB;IAAEC,MAAM,EAAE,CAAC,OAAO,EAAC,MAAM;EAAE,CAAC,EACjF;IAAEF,IAAI,EAAEF,SAAS,CAAC,SAAS,CAAC;IAAEG,IAAI,EAAE,mBAAmB;IAAEC,MAAM,EAAE,CAAC,OAAO;EAAE,CAAC,EAC5E;IAAEF,IAAI,EAAEF,SAAS,CAAC,cAAc,CAAC;IAAEG,IAAI,EAAE,mBAAmB;IAAEC,MAAM,EAAE,CAAC,OAAO,EAAC,MAAM;EAAE,CAAC,EACxF;IAAEF,IAAI,EAAEF,SAAS,CAAC,cAAc,CAAC;IAAEG,IAAI,EAAE,uBAAuB;IAAEC,MAAM,EAAE,CAAC,OAAO;EAAE,CAAC,EACrF;IAAEF,IAAI,EAAEF,SAAS,CAAC,QAAQ,CAAC;IAAEG,IAAI,EAAE,kBAAkB;IAAEC,MAAM,EAAE,CAAC,OAAO,EAAC,MAAM;EAAE,CAAC,EACjF;IAAEF,IAAI,EAAEF,SAAS,CAAC,UAAU,CAAC;IAAEG,IAAI,EAAE,oBAAoB;IAAEC,MAAM,EAAE,CAAC,OAAO,EAAC,MAAM;EAAE,CAAC,CACrF;EAED,oBACCrE,OAAA,CAAAE,SAAA;IAAAoE,QAAA,eACCtE,OAAA,CAACX,SAAS;MAACkF,SAAS,EAAC,aAAa;MAAAD,QAAA,eACjCtE,OAAA,CAACN,GAAG;QAAC6E,SAAS,EAAE,oBAAoBnE,WAAW,GAAG,cAAc,GAAG,gBAAgB,EAAG;QAAAkE,QAAA,gBACrFtE,OAAA;UAAKuE,SAAS,EAAC,mBAAmB;UAAAD,QAAA,EAAEL,SAAS,CAAC,UAAU;QAAC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAChE3E,OAAA,CAACV,IAAI;UAACiF,SAAS,EAAC,kBAAkB;UAAAD,QAAA,EAChC,CACA;YAAEH,IAAI,EAAEF,SAAS,CAAC,MAAM,CAAC;YAAEG,IAAI,EAAE,IAAIzB,cAAc,OAAO;YAAE0B,MAAM,EAAE,CAAC,OAAO,CAAC;YAACrB,KAAK,EAAC;UAAG,CAAC,EACxF;YACCmB,IAAI,EAAEF,SAAS,CAAC,OAAO,CAAC;YAAEG,IAAI,EAAE,IAAIzB,cAAc,QAAQ;YAAE0B,MAAM,EAAE,CAAC,OAAO,CAAC;YAACrB,KAAK,EAAC;UACrF,CAAC,EACD;YAAEmB,IAAI,EAAEF,SAAS,CAAC,SAAS,CAAC;YAAEG,IAAI,EAAE,IAAIzB,cAAc,WAAW;YAAE0B,MAAM,EAAE,CAAC,OAAO,CAAC;YAACrB,KAAK,EAAC;UAAG,CAAC,EAE/F;YACCmB,IAAI,EAAEF,SAAS,CAAC,QAAQ,CAAC;YAAEG,IAAI,EAAE,mBAAmB;YACpDC,MAAM,EAAE,CAAC,OAAO,CAAC;YAACrB,KAAK,EAAC;UACzB,CAAC,EACD;YACCmB,IAAI,EAAEF,SAAS,CAAC,QAAQ,CAAC;YAAEG,IAAI,EAAE,kBAAkB;YACnDC,MAAM,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;YAACrB,KAAK,EAAC,CAAC,eAAe,EAAC,QAAQ,EAAC,WAAW;UACtE,CAAC,EACD;YACCmB,IAAI,EAAEF,SAAS,CAAC,QAAQ,CAAC;YAAEG,IAAI,EAAE,kBAAkB;YACnDC,MAAM,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;YAACrB,KAAK,EAAC,CAAC,eAAe,EAAC,QAAQ,EAAC,WAAW;UACtE,CAAC,EACD;YACCmB,IAAI,EAAEF,SAAS,CAAC,SAAS,CAAC;YAAEG,IAAI,EAAE,mBAAmB;YACrDC,MAAM,EAAE,CAAC,OAAO,CAAC;YAACrB,KAAK,EAAC,CAAC,eAAe,EAAC,QAAQ,EAAC,WAAW;UAC9D,CAAC,EACD;YACCmB,IAAI,EAAEF,SAAS,CAAC,cAAc,CAAC;YAAEG,IAAI,EAAE,mBAAmB;YAC1DC,MAAM,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;YAACrB,KAAK,EAAC,CAAC,eAAe;UACjD,CAAC,EACD;YACCmB,IAAI,EAAEF,SAAS,CAAC,cAAc,CAAC;YAAEG,IAAI,EAAE,uBAAuB;YAC9DC,MAAM,EAAE,CAAC,OAAO,CAAC;YAACrB,KAAK,EAAC,CAAC,eAAe,EAAC,QAAQ,EAAC,WAAW;UAC9D,CAAC,EACD;YACCmB,IAAI,EAAEF,SAAS,CAAC,QAAQ,CAAC;YAAEG,IAAI,EAAE,kBAAkB;YACnDC,MAAM,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;YAACrB,KAAK,EAAC,CAAC,eAAe,EAAC,QAAQ,EAAC,WAAW;UACtE,CAAC,EACD;YACCmB,IAAI,EAAEF,SAAS,CAAC,UAAU,CAAC;YAAEG,IAAI,EAAE,oBAAoB;YACvDC,MAAM,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;YAACrB,KAAK,EAAC,CAAC,eAAe,EAAC,QAAQ,EAAC,WAAW;UACtE,CAAC,CACD,CAAC4B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAAM9E,OAAA,CAAAE,SAAA;YAAAoE,QAAA,EACtBO,IAAI,CAACR,MAAM,CAACU,QAAQ,CAAClC,QAAQ,CAACmC,iBAAiB,CAAC,CAAC,CAAC,KAAKnC,QAAQ,CAACmC,iBAAiB,CAAC,CAAC,IAAI,MAAM,GAAGH,IAAI,CAAC7B,KAAK,CAACiC,IAAI,CAACC,IAAI,IAAIlC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE+B,QAAQ,CAACG,IAAI,CAAC,CAAC,GAAE,IAAI,CAAC,iBACrJlF,OAAA,CAACT,QAAQ;cAER4F,SAAS,EAAEjG,OAAQ;cACnBkG,EAAE,EAAEP,IAAI,CAACT,IAAK;cACdG,SAAS,EAAE,sBAAsBjC,QAAQ,CAACE,QAAQ,KAAKqC,IAAI,CAACT,IAAI,IAC7DS,IAAI,CAACT,IAAI,KAAK,kBAAkB,KAAK9B,QAAQ,CAACE,QAAQ,KAAK,mBAAmB,IAAIF,QAAQ,CAACE,QAAQ,KAAK,yBAAyB,IAAIF,QAAQ,CAACE,QAAQ,KAAK,+BAA+B,CAAE,IAC5LqC,IAAI,CAACT,IAAI,KAAK,IAAIzB,cAAc,WAAW,IAAIL,QAAQ,CAACE,QAAQ,KAAK,IAAIG,cAAc,sBAAuB,GAC7G,QAAQ,GAAG,EAAE,EACb;cAAA2B,QAAA,eAEJtE,OAAA,CAACR,YAAY;gBACZ6F,OAAO,eACNrF,OAAA,CAACP,UAAU;kBACV8E,SAAS,EAAE,oBAAoBjC,QAAQ,CAACE,QAAQ,KAAKqC,IAAI,CAACT,IAAI,IAC3DS,IAAI,CAACT,IAAI,KAAK,kBAAkB,KAAK9B,QAAQ,CAACE,QAAQ,KAAK,mBAAmB,IAAIF,QAAQ,CAACE,QAAQ,KAAK,yBAAyB,IAAIF,QAAQ,CAACE,QAAQ,KAAK,+BAA+B,CAAE,IAC5LqC,IAAI,CAACT,IAAI,KAAK,IAAIzB,cAAc,WAAW,IAAIL,QAAQ,CAACE,QAAQ,KAAK,IAAIG,cAAc,sBAAuB,GAC7G,QAAQ,GAAG,EAAE;kBAElB;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBAAA;kBAAA2B,QAAA,EAEEO,IAAI,CAACV;gBAAI;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cACZ;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC,GA/BGG,KAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgCD;UAAC,gBAEX,CACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC,gBACX,CAAC;AAEL,CAAC;AAACzC,EAAA,CApJID,QAAQ;EAAA,QAEI9C,WAAW,EAEKS,OAAO,EAsCfC,cAAc;AAAA;AAAAyF,EAAA,GA1ClCrD,QAAQ;AAsJd,eAAeA,QAAQ;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}