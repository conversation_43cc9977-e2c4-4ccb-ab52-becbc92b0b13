import React, { useState, useEffect } from 'react';
import { Box, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  TrendingUp,
  TrendingDown,
  People,
  CheckCircle,
  Star,
  Schedule,
} from '@mui/icons-material';
import Card from '../common/Card';
import { 
  getFeedbackAnalyticsWithStoredAccount, 
  FeedbackAnalyticsResponse,
  getGuideAnalyticsWithStoredAccount,
  GuideAnalyticsResponse
} from '../../services/DashboardService';

interface MetricCardProps {
  title: string;
  value: string;
  change: string;
  changeValue: string;
  trend: 'up' | 'down';
  icon: React.ReactNode;
  color: string;
}

const MetricsGrid = styled(Box)({
  display: 'grid',
  gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
  gap: 'var(--spacing-4)',
  marginBottom: 'var(--spacing-6)',
});

const MetricCardContainer = styled(Card)({
  padding: 'var(--spacing-5)',
  display: 'flex',
  alignItems: 'center',
  gap: 'var(--spacing-4)',
});

const MetricIcon = styled(Box)<{ color: string }>(({ color }) => ({
  width: '48px',
  height: '48px',
  borderRadius: 'var(--radius-lg)',
  backgroundColor: `${color}15`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  
  '& svg': {
    color: color,
    fontSize: '24px',
  },
}));

const MetricContent = styled(Box)({
  flex: 1,
});

const MetricTitle = styled(Typography)({
  fontSize: 'var(--font-size-sm)',
  color: 'var(--color-gray-600)',
  marginBottom: 'var(--spacing-1)',
});

const MetricValue = styled(Typography)({
  fontSize: 'var(--font-size-2xl)',
  fontWeight: 'var(--font-weight-bold)',
  color: 'var(--color-gray-900)',
  marginBottom: 'var(--spacing-1)',
});

const MetricChange = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  gap: 'var(--spacing-1)',
});

const ChangeIndicator = styled(Box)<{ trend: 'up' | 'down' }>(({ trend }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: 'var(--spacing-1)',
  fontSize: 'var(--font-size-xs)',
  fontWeight: 'var(--font-weight-medium)',
  color: trend === 'up' ? 'var(--color-success-600)' : 'var(--color-error-600)',
  
  '& svg': {
    fontSize: '16px',
  },
}));

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  change,
  changeValue,
  trend,
  icon,
  color,
}) => {
  return (
    <MetricCardContainer shadow="sm" hover>
      <MetricIcon color={color}>
        {icon}
      </MetricIcon>
      <MetricContent>
        <MetricTitle>{title}</MetricTitle>
        <MetricValue>{value}</MetricValue>
        <MetricChange>
          <ChangeIndicator trend={trend}>
            {trend === 'up' ? <TrendingUp /> : <TrendingDown />}
            {change}
          </ChangeIndicator>
          <Typography variant="caption" color="text.secondary">
            {changeValue}
          </Typography>
        </MetricChange>
      </MetricContent>
    </MetricCardContainer>
  );
};

// Interactive User Activity Chart Component
const UserActivityChart: React.FC = () => {
  const [hoveredPoint, setHoveredPoint] = useState<{week: string, type: string, value: number, x: number, y: number} | null>(null);

  const chartData = {
    weeks: ['Week 1', 'Week 2'],
    data: [
      { week: 'Week 1', active: 30000, retained: 24000, total: 36000, x: 125 },
      { week: 'Week 2', active: 31000, retained: 25000, total: 37000, x: 325 }
    ]
  };

  const getYPosition = (value: number) => {
    // Scale: 0-60000 maps to 200-20 (inverted Y axis)
    return 200 - ((value / 60000) * 180);
  };

  const handlePointHover = (week: string, type: string, value: number, x: number, y: number) => {
    setHoveredPoint({ week, type, value, x, y });
  };

  const handlePointLeave = () => {
    setHoveredPoint(null);
  };

  return (
    <Box sx={{ height: '300px', position: 'relative', backgroundColor: 'white', borderRadius: 'var(--radius-md)' }}>
      {/* Multi-line Chart */}
      <svg width="100%" height="280" viewBox="0 0 450 250" style={{ overflow: 'visible' }}>
        {/* Grid lines */}
        <defs>
          <pattern id="activityGrid" width="200" height="50" patternUnits="userSpaceOnUse">
            <path d="M 200 0 L 0 0 0 50" fill="none" stroke="#f1f5f9" strokeWidth="1"/>
          </pattern>
        </defs>
        <rect width="100%" height="200" fill="url(#activityGrid)" />

        {/* Y-axis */}
        <line x1="50" y1="20" x2="50" y2="200" stroke="#e2e8f0" strokeWidth="1"/>
        {/* X-axis */}
        <line x1="50" y1="200" x2="400" y2="200" stroke="#e2e8f0" strokeWidth="1"/>

        {/* Y-axis labels */}
        <text x="40" y="25" fontSize="11" fill="#64748b" textAnchor="end">60000</text>
        <text x="40" y="65" fontSize="11" fill="#64748b" textAnchor="end">45000</text>
        <text x="40" y="105" fontSize="11" fill="#64748b" textAnchor="end">30000</text>
        <text x="40" y="145" fontSize="11" fill="#64748b" textAnchor="end">15000</text>
        <text x="40" y="205" fontSize="11" fill="#64748b" textAnchor="end">0</text>

        {/* X-axis labels */}
        <text x="125" y="220" fontSize="11" fill="#64748b" textAnchor="middle">Week 1</text>
        <text x="325" y="220" fontSize="11" fill="#64748b" textAnchor="middle">Week 2</text>

        {/* Total Users Line (Purple) */}
        <path d={`M 125 ${getYPosition(36000)} L 325 ${getYPosition(37000)}`} fill="none" stroke="#8b5cf6" strokeWidth="3" strokeLinecap="round"/>

        {/* Active Users Line (Blue) */}
        <path d={`M 125 ${getYPosition(30000)} L 325 ${getYPosition(31000)}`} fill="none" stroke="#3b82f6" strokeWidth="3" strokeLinecap="round"/>

        {/* Retained Users Line (Green) */}
        <path d={`M 125 ${getYPosition(24000)} L 325 ${getYPosition(25000)}`} fill="none" stroke="#10b981" strokeWidth="3" strokeLinecap="round"/>

        {/* Interactive Data Points */}
        {chartData.data.map((point, index) => (
          <g key={index}>
            {/* Total Users Points */}
            <circle
              cx={point.x}
              cy={getYPosition(point.total)}
              r="6"
              fill="#8b5cf6"
              style={{ cursor: 'pointer', transition: 'all 0.2s ease' }}
              onMouseEnter={() => handlePointHover(point.week, 'Total', point.total, point.x, getYPosition(point.total))}
              onMouseLeave={handlePointLeave}
            />

            {/* Active Users Points */}
            <circle
              cx={point.x}
              cy={getYPosition(point.active)}
              r="6"
              fill="#3b82f6"
              style={{ cursor: 'pointer', transition: 'all 0.2s ease' }}
              onMouseEnter={() => handlePointHover(point.week, 'Active', point.active, point.x, getYPosition(point.active))}
              onMouseLeave={handlePointLeave}
            />

            {/* Retained Users Points */}
            <circle
              cx={point.x}
              cy={getYPosition(point.retained)}
              r="6"
              fill="#10b981"
              style={{ cursor: 'pointer', transition: 'all 0.2s ease' }}
              onMouseEnter={() => handlePointHover(point.week, 'Retained', point.retained, point.x, getYPosition(point.retained))}
              onMouseLeave={handlePointLeave}
            />
          </g>
        ))}

        {/* Hover Tooltip */}
        {hoveredPoint && (
          <g>
            {/* Tooltip Background */}
            <rect
              x={hoveredPoint.x - 35}
              y={hoveredPoint.y - 35}
              width="70"
              height="25"
              fill="white"
              stroke="#e2e8f0"
              strokeWidth="1"
              rx="4"
              style={{
                filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))',
                transition: 'all 0.2s ease'
              }}
            />
            {/* Tooltip Text */}
            <text
              x={hoveredPoint.x}
              y={hoveredPoint.y - 25}
              fontSize="10"
              fill="#1f2937"
              textAnchor="middle"
              fontWeight="600"
            >
              {hoveredPoint.type}: {hoveredPoint.value.toLocaleString()}
            </text>
            <text
              x={hoveredPoint.x}
              y={hoveredPoint.y - 15}
              fontSize="9"
              fill="#64748b"
              textAnchor="middle"
            >
              {hoveredPoint.week}
            </text>
          </g>
        )}
      </svg>

      {/* Legend */}
      <Box sx={{
        position: 'absolute',
        bottom: 10,
        left: '50%',
        transform: 'translateX(-50%)',
        display: 'flex',
        gap: 3,
        backgroundColor: 'rgba(255,255,255,0.9)',
        padding: '8px 16px',
        borderRadius: '8px',
        border: '1px solid #e2e8f0'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box sx={{ width: 12, height: 12, backgroundColor: '#3b82f6', borderRadius: '50%' }} />
          <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b' }}>Active</Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box sx={{ width: 12, height: 12, backgroundColor: '#10b981', borderRadius: '50%' }} />
          <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b' }}>Retained</Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box sx={{ width: 12, height: 12, backgroundColor: '#8b5cf6', borderRadius: '50%' }} />
          <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b' }}>Total</Typography>
        </Box>
      </Box>
    </Box>
  );
};

// Interactive Feature Adoption Chart Component
interface FeatureAdoptionChartProps {
  guideData: GuideAnalyticsResponse | null;
}

const FeatureAdoptionChart: React.FC<FeatureAdoptionChartProps> = ({ guideData }) => {
  const [hoveredSegment, setHoveredSegment] = useState<string | null>(null);

  // Generate chart data from API response using Count values
  const chartData = React.useMemo(() => {
    if (!guideData?.GuideTypes) {
      // Fallback static data if API data is not available
      return [
        { name: 'Tours', value: 25, actualValue: 25, color: '#3b82f6', offset: 0 },
        { name: 'Tooltips', value: 20, actualValue: 20, color: '#10b981', offset: 25 },
        { name: 'Announcements', value: 19, actualValue: 19, color: '#8b5cf6', offset: 45 },
        { name: 'Banners', value: 18, actualValue: 18, color: '#f97316', offset: 64 },
        { name: 'Hotspots', value: 12, actualValue: 12, color: '#14b8a6', offset: 82 },
        { name: 'Checklists', value: 6, actualValue: 6, color: '#ef4444', offset: 94 }
      ];
    }

    // Create chart data from API response using Count values
    const guideTypes = guideData.GuideTypes;
    const guideData_array = [
      { name: 'Tours', count: guideTypes.Tours.Count, color: '#3b82f6' },
      { name: 'Tooltips', count: guideTypes.Tooltips.Count, color: '#10b981' },
      { name: 'Announcements', count: guideTypes.Announcements.Count, color: '#8b5cf6' },
      { name: 'Banners', count: guideTypes.Banners.Count, color: '#f97316' },
      { name: 'Hotspots', count: guideTypes.Hotspots.Count, color: '#14b8a6' },
      { name: 'Checklists', count: guideTypes.Checklists.Count, color: '#ef4444' }
    ];

    // Calculate total count for percentage calculation
    const totalCount = guideData_array.reduce((sum, item) => sum + item.count, 0);

    // Sort by count to arrange segments properly
    guideData_array.sort((a, b) => b.count - a.count);

    // Calculate percentage and offset for each segment
    let currentOffset = 0;
    return guideData_array.map(guide => {
      const percentage = totalCount > 0 ? (guide.count / totalCount) * 100 : 0;
      const segment = {
        name: guide.name,
        value: Math.round(percentage * 100) / 100, // Round to 2 decimal places
        actualValue: guide.count, // Use actual count from API
        color: guide.color,
        offset: currentOffset
      };
      currentOffset += segment.value;
      return segment;
    });
  }, [guideData]);

  const radius = 120; // Increased from 60 to 120 for maximum size
  const strokeWidth = 40; // Increased proportionally from 25 to 40

  const createPath = (startAngle: number, endAngle: number, isHovered: boolean) => {
    const centerX = 150; // Increased from 90 to 150 to center in larger viewBox
    const centerY = 150; // Increased from 90 to 150 to center in larger viewBox
    const outerRadius = isHovered ? radius + 8 : radius; // Increased hover effect from 5 to 8
    const innerRadius = outerRadius - strokeWidth;

    const startAngleRad = (startAngle - 90) * (Math.PI / 180);
    const endAngleRad = (endAngle - 90) * (Math.PI / 180);

    const x1 = centerX + outerRadius * Math.cos(startAngleRad);
    const y1 = centerY + outerRadius * Math.sin(startAngleRad);
    const x2 = centerX + outerRadius * Math.cos(endAngleRad);
    const y2 = centerY + outerRadius * Math.sin(endAngleRad);

    const x3 = centerX + innerRadius * Math.cos(endAngleRad);
    const y3 = centerY + innerRadius * Math.sin(endAngleRad);
    const x4 = centerX + innerRadius * Math.cos(startAngleRad);
    const y4 = centerY + innerRadius * Math.sin(startAngleRad);

    const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";

    return `M ${x1} ${y1} A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 1 ${x2} ${y2} L ${x3} ${y3} A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 0 ${x4} ${y4} Z`;
  };

  return (
    <Box sx={{ height: '450px', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', backgroundColor: 'white', borderRadius: 'var(--radius-md)', position: 'relative' }}>
      {/* Interactive Donut Chart */}
      <svg width="320" height="320" viewBox="0 0 300 300" style={{ marginBottom: '15px' }}>
        {chartData.map((segment, index) => {
          const startAngle = (segment.offset / 100) * 360;
          const endAngle = ((segment.offset + segment.value) / 100) * 360;
          const isHovered = hoveredSegment === segment.name;

          // Calculate the middle angle of the segment for text positioning
          const middleAngle = (startAngle + endAngle) / 2;
          const middleAngleRad = (middleAngle - 90) * (Math.PI / 180);

          // Position text directly on the donut edge/corner
          const textRadius = isHovered ? radius + 4 : radius;
          const textX = 150 + textRadius * Math.cos(middleAngleRad);
          const textY = 150 + textRadius * Math.sin(middleAngleRad);

          return (
            <g key={segment.name}>
              <path
                d={createPath(startAngle, endAngle, isHovered)}
                fill={segment.color}
                style={{
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  filter: isHovered ? 'brightness(1.1)' : 'none',
                  opacity: hoveredSegment && !isHovered ? 0.7 : 1
                }}
                onMouseEnter={() => setHoveredSegment(segment.name)}
                onMouseLeave={() => setHoveredSegment(null)}
              />

              {/* Show value on hovered segment */}
              {isHovered && (
                <text
                  x={textX}
                  y={textY + 2}
                  fontSize="16"
                  fill="black"
                  textAnchor="middle"
                  fontWeight="bold"
                  style={{
                    transition: 'all 0.3s ease',
                    filter: 'drop-shadow(0 1px 2px rgba(255,255,255,0.8))'
                  }}
                >
                  {segment.value}%
                </text>
              )}
            </g>
          );
        })}

        {/* Center circle for donut effect */}
        <circle cx="150" cy="150" r="60" fill="white"/>
        <text x="150" y="140" fontSize="24" fill="#1f2937" textAnchor="middle" fontWeight="bold">100%</text>
        <text x="150" y="165" fontSize="16" fill="#6b7280" textAnchor="middle">Total</text>
      </svg>

      {/* Legend - Minimized and positioned below */}
      <Box sx={{
        display: 'grid',
        gridTemplateColumns: 'repeat(3, 1fr)',
        gap: 1,
        width: '100%',
        maxWidth: '400px',
        marginTop: '10px'
      }}>
        {chartData.map((item) => (
          <Box
            key={item.name}
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              opacity: hoveredSegment && hoveredSegment !== item.name ? 0.5 : 1,
              transform: hoveredSegment === item.name ? 'scale(1.05)' : 'scale(1)',
              padding: '4px 6px',
              borderRadius: '4px'
            }}
            onMouseEnter={() => setHoveredSegment(item.name)}
            onMouseLeave={() => setHoveredSegment(null)}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Box sx={{
                width: 8,
                height: 8,
                backgroundColor: item.color,
                borderRadius: '50%',
                transition: 'all 0.2s ease',
                transform: hoveredSegment === item.name ? 'scale(1.2)' : 'scale(1)'
              }} />
              <Typography variant="caption" sx={{ fontSize: '9px', color: '#64748b', fontWeight: 500 }}>
                {item.name}
              </Typography>
            </Box>
            <Box sx={{ textAlign: 'right' }}>
              <Typography variant="caption" sx={{ fontSize: '9px', color: '#64748b', fontWeight: 600 }}>
                {item.value}%
              </Typography>
              <Typography variant="caption" sx={{ fontSize: '8px', color: '#94a3b8', display: 'block' }}>
                {item.actualValue.toLocaleString()}
              </Typography>
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

interface OverviewTabProps {
  timeFilter?: string;
}

const OverviewTab: React.FC<OverviewTabProps> = ({ timeFilter = '7d' }) => {
  // State for API data
  const [feedbackData, setFeedbackData] = useState<FeedbackAnalyticsResponse | null>(null);
  const [guideData, setGuideData] = useState<GuideAnalyticsResponse | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Convert timeFilter to days
  const getDaysFromFilter = (filter: string): number => {
    switch (filter) {
      case '7d': return 7;
      case '30d': return 30;
      case '90d': return 90;
      case '1y': return 365;
      default: return 7;
    }
  };

  // Fetch both feedback and guide analytics data
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      setFeedbackData(null); // Reset previous data to prevent UI flickering
      setGuideData(null);
      try {
        const days = getDaysFromFilter(timeFilter);
        
        // Fetch both APIs in parallel
        const [feedbackResponse, guideResponse] = await Promise.all([
          getFeedbackAnalyticsWithStoredAccount(days),
          getGuideAnalyticsWithStoredAccount(days)
        ]);
        
        setFeedbackData(feedbackResponse);
        setGuideData(guideResponse);
      } catch (err) {
        console.error('Error fetching analytics data:', err);
        setError('Failed to fetch analytics data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [timeFilter]);

  const analyticsMetrics = [
    {
      title: 'Active Users',
      value: '12,847',
      change: '+12.5%',
      changeValue: '+1,432',
      trend: 'up' as const,
      icon: <People />,
      color: 'var(--color-primary-600)',
    },
    {
      title: 'Completion Rate',
      value: '87.3%',
      change: '+3.2%',
      changeValue: '+2.8pp',
      trend: 'up' as const,
      icon: <CheckCircle />,
      color: 'var(--color-success-600)',
    },
    {
      title: 'User Satisfaction',
      value: '4.6',
      change: '+0.2',
      changeValue: 'out of 5.0',
      trend: 'up' as const,
      icon: <Star />,
      color: 'var(--color-warning-600)',
    },
    {
      title: 'Hours Saved',
      value: '2,847',
      change: '+18.7%',
      changeValue: '+447h',
      trend: 'up' as const,
      icon: <Schedule />,
      color: 'var(--color-primary-600)',
    },
  ];

  return (
    <>
        {/* Metrics Overview Cards */}
      <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: 2, mb: 3 }}>
        {/* Active Users */}
        <Box sx={{
          p: 3,
          backgroundColor: '#eff6ff',
          borderRadius: '8px',
          border: '1px solid #bfdbfe',
          textAlign: 'left',
          minHeight: '120px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
          }
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="body2" sx={{ color: '#64748b', fontSize: '13px', fontWeight: 'medium' }}>
              Active Users
            </Typography>
            <Box sx={{
              width: 20,
              height: 20,
              backgroundColor: '#3b82f6',
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Typography sx={{ fontSize: '12px', color: 'white' }}>�</Typography>
            </Box>
          </Box>
          <Typography variant="h3" fontWeight="bold" sx={{ color: '#1e293b', mb: 1, fontSize: '28px' }}>
            34,521
          </Typography>
          <Typography variant="caption" sx={{ color: '#16a34a', fontSize: '11px' }}>
            +2.5% from last month
          </Typography>
        </Box>

        {/* Guide Completion */}
        <Box sx={{
          p: 3,
          backgroundColor: '#f0fdf4',
          borderRadius: '8px',
          border: '1px solid #bbf7d0',
          textAlign: 'left',
          minHeight: '120px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
          }
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="body2" sx={{ color: '#64748b', fontSize: '13px', fontWeight: 'medium' }}>
              Guide Completion
            </Typography>
            <Box sx={{
              width: 20,
              height: 20,
              backgroundColor: '#22c55e',
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Typography sx={{ fontSize: '12px', color: 'white' }}>✓</Typography>
            </Box>
          </Box>
          <Typography variant="h3" fontWeight="bold" sx={{ color: '#1e293b', mb: 1, fontSize: '28px' }}>
            78.2%
          </Typography>
          <Typography variant="caption" sx={{ color: '#16a34a', fontSize: '11px' }}>
            +3.1% from last month
          </Typography>
        </Box>

        {/* Dona Interactions */}
        <Box sx={{
          p: 3,
          backgroundColor: '#faf5ff',
          borderRadius: '8px',
          border: '1px solid #e9d5ff',
          textAlign: 'left',
          minHeight: '120px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
          }
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="body2" sx={{ color: '#64748b', fontSize: '13px', fontWeight: 'medium' }}>
              Dona Interactions
            </Typography>
            <Box sx={{
              width: 20,
              height: 20,
              backgroundColor: '#a855f7',
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Typography sx={{ fontSize: '12px', color: 'white' }}>💬</Typography>
            </Box>
          </Box>
          <Typography variant="h3" fontWeight="bold" sx={{ color: '#1e293b', mb: 1, fontSize: '28px' }}>
            12,847
          </Typography>
          <Typography variant="caption" sx={{ color: '#16a34a', fontSize: '11px' }}>
            +18.7% from last month
          </Typography>
        </Box>

        {/* Overall Satisfaction */}
        <Box sx={{
          p: 3,
          backgroundColor: '#fffbeb',
          borderRadius: '8px',
          border: '1px solid #fed7aa',
          textAlign: 'left',
          minHeight: '120px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
          }
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="body2" sx={{ color: '#64748b', fontSize: '13px', fontWeight: 'medium' }}>
              Overall Satisfaction
            </Typography>
            <Box sx={{
              width: 20,
              height: 20,
              backgroundColor: '#f59e0b',
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Typography sx={{ fontSize: '12px', color: 'white' }}>⭐</Typography>
            </Box>
          </Box>
          <Typography variant="h3" fontWeight="bold" sx={{ color: '#1e293b', mb: 1, fontSize: '28px' }}>
            4.5/5
          </Typography>
          <Typography variant="caption" sx={{ color: '#16a34a', fontSize: '11px' }}>
            +0.2 from last month
          </Typography>
        </Box>
      </Box>

      {/* Overview Charts - Growth Trends and User Satisfaction */}
      <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 'var(--spacing-6)', mb: 4 }}>
        {/* User Activity Trends Chart */}
        <Card title="📈 User Activity Trends" subtitle="Active, retained, and total users over time" padding="lg">
          <UserActivityChart />
        </Card>

        {/* Feature Adoption Distribution Chart */}
        <Card title="🎯 Feature Adoption Distribution" subtitle="Interactive feature usage metrics" padding="lg">
          <FeatureAdoptionChart guideData={guideData} />
        </Card>
      </Box>

      {/* User Feedback & Satisfaction Section */}
      {/* User Satisfaction Ratings and Satisfaction Trend */}
      <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 'var(--spacing-6)', mb: 4 }}>
        {/* User Satisfaction Ratings */}
        <Card title="⭐ User Satisfaction Ratings" padding="lg">
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
              <Typography variant="body2" color="text.secondary">Loading...</Typography>
            </Box>
          ) : error ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
              <Typography variant="body2" color="error">{error}</Typography>
            </Box>
          ) : feedbackData ? (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>
              {/* Excellent (5★) */}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
                  <Box sx={{ width: 8, height: 8, backgroundColor: '#10b981', borderRadius: '50%' }} />
                  <Typography variant="body2" fontWeight="medium">
                    Excellent (5★)
                  </Typography>
                </Box>
                <Box sx={{
                  flex: 1,
                  height: 8,
                  backgroundColor: 'var(--color-gray-200)',
                  borderRadius: 'var(--radius-full)',
                  overflow: 'hidden',
                  mr: 2
                }}>
                  <Box sx={{
                    width: `${(feedbackData.Feedback.Excellent / (feedbackData.Feedback.Excellent + feedbackData.Feedback.Good + feedbackData.Feedback.Average + feedbackData.Feedback.Poor + feedbackData.Feedback.VeryPoor)) * 100}%`,
                    height: '100%',
                    backgroundColor: '#10b981',
                    borderRadius: 'var(--radius-full)'
                  }} />
                </Box>
                <Typography variant="body2" fontWeight="bold" sx={{ minWidth: 40, textAlign: 'right' }}>
                  {feedbackData.Feedback.Excellent}
                </Typography>
              </Box>

              {/* Good (4★) */}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
                  <Box sx={{ width: 8, height: 8, backgroundColor: '#84cc16', borderRadius: '50%' }} />
                  <Typography variant="body2" fontWeight="medium">
                    Good (4★)
                  </Typography>
                </Box>
                <Box sx={{
                  flex: 1,
                  height: 8,
                  backgroundColor: 'var(--color-gray-200)',
                  borderRadius: 'var(--radius-full)',
                  overflow: 'hidden',
                  mr: 2
                }}>
                  <Box sx={{
                    width: `${(feedbackData.Feedback.Good / (feedbackData.Feedback.Excellent + feedbackData.Feedback.Good + feedbackData.Feedback.Average + feedbackData.Feedback.Poor + feedbackData.Feedback.VeryPoor)) * 100}%`,
                    height: '100%',
                    backgroundColor: '#84cc16',
                    borderRadius: 'var(--radius-full)'
                  }} />
                </Box>
                <Typography variant="body2" fontWeight="bold" sx={{ minWidth: 40, textAlign: 'right' }}>
                  {feedbackData.Feedback.Good}
                </Typography>
              </Box>

              {/* Average (3★) */}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
                  <Box sx={{ width: 8, height: 8, backgroundColor: '#f59e0b', borderRadius: '50%' }} />
                  <Typography variant="body2" fontWeight="medium">
                    Average (3★)
                  </Typography>
                </Box>
                <Box sx={{
                  flex: 1,
                  height: 8,
                  backgroundColor: 'var(--color-gray-200)',
                  borderRadius: 'var(--radius-full)',
                  overflow: 'hidden',
                  mr: 2
                }}>
                  <Box sx={{
                    width: `${(feedbackData.Feedback.Average / (feedbackData.Feedback.Excellent + feedbackData.Feedback.Good + feedbackData.Feedback.Average + feedbackData.Feedback.Poor + feedbackData.Feedback.VeryPoor)) * 100}%`,
                    height: '100%',
                    backgroundColor: '#f59e0b',
                    borderRadius: 'var(--radius-full)'
                  }} />
                </Box>
                <Typography variant="body2" fontWeight="bold" sx={{ minWidth: 40, textAlign: 'right' }}>
                  {feedbackData.Feedback.Average}
                </Typography>
              </Box>

              {/* Poor (2★) */}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
                  <Box sx={{ width: 8, height: 8, backgroundColor: '#f97316', borderRadius: '50%' }} />
                  <Typography variant="body2" fontWeight="medium">
                    Poor (2★)
                  </Typography>
                </Box>
                <Box sx={{
                  flex: 1,
                  height: 8,
                  backgroundColor: 'var(--color-gray-200)',
                  borderRadius: 'var(--radius-full)',
                  overflow: 'hidden',
                  mr: 2
                }}>
                  <Box sx={{
                    width: `${(feedbackData.Feedback.Poor / (feedbackData.Feedback.Excellent + feedbackData.Feedback.Good + feedbackData.Feedback.Average + feedbackData.Feedback.Poor + feedbackData.Feedback.VeryPoor)) * 100}%`,
                    height: '100%',
                    backgroundColor: '#f97316',
                    borderRadius: 'var(--radius-full)'
                  }} />
                </Box>
                <Typography variant="body2" fontWeight="bold" sx={{ minWidth: 40, textAlign: 'right' }}>
                  {feedbackData.Feedback.Poor}
                </Typography>
              </Box>

              {/* Very Poor (1★) */}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
                  <Box sx={{ width: 8, height: 8, backgroundColor: '#ef4444', borderRadius: '50%' }} />
                  <Typography variant="body2" fontWeight="medium">
                    Very Poor (1★)
                  </Typography>
                </Box>
                <Box sx={{
                  flex: 1,
                  height: 8,
                  backgroundColor: 'var(--color-gray-200)',
                  borderRadius: 'var(--radius-full)',
                  overflow: 'hidden',
                  mr: 2
                }}>
                  <Box sx={{
                    width: `${(feedbackData.Feedback.VeryPoor / (feedbackData.Feedback.Excellent + feedbackData.Feedback.Good + feedbackData.Feedback.Average + feedbackData.Feedback.Poor + feedbackData.Feedback.VeryPoor)) * 100}%`,
                    height: '100%',
                    backgroundColor: '#ef4444',
                    borderRadius: 'var(--radius-full)'
                  }} />
                </Box>
                <Typography variant="body2" fontWeight="bold" sx={{ minWidth: 40, textAlign: 'right' }}>
                  {feedbackData.Feedback.VeryPoor}
                </Typography>
              </Box>

              {/* Summary Cards */}
              <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: 2, mt: 3 }}>
                <Box sx={{
                  p: 2,
                  backgroundColor: '#f0fdf4',
                  borderRadius: 'var(--radius-md)',
                  textAlign: 'center'
                }}>
                  <Typography variant="h5" fontWeight="bold" color="#16a34a">
                    {feedbackData.Percentage.Positive.toFixed(1)}%
                  </Typography>
                  <Typography variant="caption" color="#16a34a">
                    Positive
                  </Typography>
                </Box>
                <Box sx={{
                  p: 2,
                  backgroundColor: '#fffbeb',
                  borderRadius: 'var(--radius-md)',
                  textAlign: 'center'
                }}>
                  <Typography variant="h5" fontWeight="bold" color="#d97706">
                    {feedbackData.Percentage.Neutral.toFixed(1)}%
                  </Typography>
                  <Typography variant="caption" color="#d97706">
                    Neutral
                  </Typography>
                </Box>
                <Box sx={{
                  p: 2,
                  backgroundColor: '#fef2f2',
                  borderRadius: 'var(--radius-md)',
                  textAlign: 'center'
                }}>
                  <Typography variant="h5" fontWeight="bold" color="#dc2626">
                    {feedbackData.Percentage.Negative.toFixed(1)}%
                  </Typography>
                  <Typography variant="caption" color="#dc2626">
                    Negative
                  </Typography>
                </Box>
              </Box>
            </Box>
          ) : (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
              <Typography variant="body2" color="text.secondary">No data available</Typography>
            </Box>
          )}
        </Card>

        {/* Satisfaction Trend */}
        <Card title="📈 Satisfaction Trend" padding="lg" key={`satisfaction-trend-${timeFilter}`}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
              <Typography variant="body2" color="text.secondary">Loading...</Typography>
            </Box>
          ) : error ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
              <Typography variant="body2" color="error">{error}</Typography>
            </Box>
          ) : feedbackData?.Trend ? (
            <Box sx={{ 
              height: '300px', // Fixed height
              width: '100%', // Fixed width to container
              display: 'flex', 
              flexDirection: 'column', 
              justifyContent: 'center', 
              alignItems: 'center', 
              backgroundColor: '#f8fafc', 
              borderRadius: 'var(--radius-md)', 
              position: 'relative',
              overflow: 'auto',
              // Removed padding to give maximum space to chart
              '&::-webkit-scrollbar': {
                height: '6px',
              },
              '&::-webkit-scrollbar-track': {
                backgroundColor: '#f1f5f9',
                borderRadius: '3px',
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: '#cbd5e1',
                borderRadius: '3px',
                '&:hover': {
                  backgroundColor: '#94a3b8',
                },
              },
            }}>
              <SatisfactionTrendChart feedbackData={feedbackData} timeFilter={timeFilter} />
            </Box>
          ) : (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
              <Typography variant="body2" color="text.secondary">No trend data available</Typography>
            </Box>
          )}
        </Card>
      </Box>

    
      {/* <Card title="📊 Feedback Summary" padding="lg">
        <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: 'var(--spacing-6)' }}>
          
          <Box sx={{
            p: 3,
            backgroundColor: '#f8fafc',
            borderRadius: 'var(--radius-md)',
            textAlign: 'center'
          }}>
            <Typography variant="h3" fontWeight="bold" color="text.primary" sx={{ mb: 1 }}>
              2,238
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Total Feedback
            </Typography>
          </Box>

        
          <Box sx={{
            p: 3,
            backgroundColor: '#f0fdf4',
            borderRadius: 'var(--radius-md)',
            textAlign: 'center'
          }}>
            <Typography variant="h3" fontWeight="bold" color="#16a34a" sx={{ mb: 1 }}>
              85.8%
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Positive Sentiment
            </Typography>
          </Box>

       
          <Box sx={{
            p: 3,
            backgroundColor: '#eff6ff',
            borderRadius: 'var(--radius-md)',
            textAlign: 'center'
          }}>
            <Typography variant="h3" fontWeight="bold" color="#2563eb" sx={{ mb: 1 }}>
              4.6/5
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Average Rating
            </Typography>
          </Box>

         
          <Box sx={{
            p: 3,
            backgroundColor: '#fdf4ff',
            borderRadius: 'var(--radius-md)',
            textAlign: 'center'
          }}>
            <Typography variant="h3" fontWeight="bold" color="#9333ea" sx={{ mb: 1 }}>
              +12%
            </Typography>
            <Typography variant="body2" color="text.secondary">
              vs Last Month
            </Typography>
          </Box>
        </Box>
      </Card> */}

    

    
      {/* <Box sx={{
        p: 4,
        backgroundColor: '#f8fafc',
        borderRadius: '8px',
        border: '1px solid #e2e8f0'
      }}>
        <Typography variant="h6" fontWeight="bold" sx={{
          color: '#1e293b',
          textAlign: 'center',
          mb: 4,
          fontSize: '16px'
        }}>
          Agent Collaboration Flow
        </Typography>

        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: 4,
          mb: 3
        }}>
          <Box sx={{ textAlign: 'center', minWidth: '100px' }}>
            <Box sx={{
              width: 48,
              height: 48,
              backgroundColor: '#a855f7',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mx: 'auto',
              mb: 2
            }}>
              <Typography sx={{ fontSize: '20px', color: 'white' }}>💬</Typography>
            </Box>
            <Typography variant="body1" fontWeight="bold" sx={{ color: '#1e293b', mb: 0.5, fontSize: '14px' }}>
              Dona
            </Typography>
            <Typography variant="body2" sx={{ color: '#64748b', mb: 0.5, fontSize: '12px' }}>
              Answers Questions
            </Typography>
            <Typography variant="caption" sx={{
              color: '#7c3aed',
              fontWeight: 'medium',
              fontSize: '11px'
            }}>
              8,136 responses
            </Typography>
          </Box>

         
          <Box sx={{
            color: '#94a3b8',
            fontSize: '20px',
            display: 'flex',
            alignItems: 'center',
            mt: -1
          }}>
            →
          </Box>

        
          <Box sx={{ textAlign: 'center', minWidth: '100px' }}>
            <Box sx={{
              width: 48,
              height: 48,
              backgroundColor: '#22c55e',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mx: 'auto',
              mb: 2
            }}>
              <Typography sx={{ fontSize: '20px', color: 'white' }}>👁️</Typography>
            </Box>
            <Typography variant="body1" fontWeight="bold" sx={{ color: '#1e293b', mb: 0.5, fontSize: '14px' }}>
              Rookie
            </Typography>
            <Typography variant="body2" sx={{ color: '#64748b', mb: 0.5, fontSize: '12px' }}>
              Creates Tours
            </Typography>
            <Typography variant="caption" sx={{
              color: '#16a34a',
              fontWeight: 'medium',
              fontSize: '11px'
            }}>
              1,360 tours
            </Typography>
          </Box>

       
          <Box sx={{
            color: '#94a3b8',
            fontSize: '20px',
            display: 'flex',
            alignItems: 'center',
            mt: -1
          }}>
            →
          </Box>

          
          <Box sx={{ textAlign: 'center', minWidth: '100px' }}>
            <Box sx={{
              width: 48,
              height: 48,
              backgroundColor: '#3b82f6',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mx: 'auto',
              mb: 2
            }}>
              <Typography sx={{ fontSize: '20px', color: 'white' }}>⚙️</Typography>
            </Box>
            <Typography variant="body1" fontWeight="bold" sx={{ color: '#1e293b', mb: 0.5, fontSize: '14px' }}>
              Work Agent
            </Typography>
            <Typography variant="body2" sx={{ color: '#64748b', mb: 0.5, fontSize: '12px' }}>
              Automates Rookie
            </Typography>
            <Typography variant="caption" sx={{
              color: '#2563eb',
              fontWeight: 'medium',
              fontSize: '11px'
            }}>
              711 automations
            </Typography>
          </Box>
        </Box>

      
        <Typography variant="body2" sx={{
          color: '#64748b',
          textAlign: 'center',
          fontSize: '12px',
          maxWidth: '500px',
          mx: 'auto'
        }}>
          Work Agent optimizes Rookie's tour creation process, improving efficiency by 50%
        </Typography>
      </Box> */}
    </>
  );
};

// Satisfaction Trend Chart Component
interface SatisfactionTrendChartProps {
  feedbackData: FeedbackAnalyticsResponse;
  timeFilter: string;
}

const SatisfactionTrendChart: React.FC<SatisfactionTrendChartProps> = ({ feedbackData, timeFilter }) => {
  const trendEntries = Object.entries(feedbackData.Trend);
  
  // Validate data
  if (!trendEntries.length) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
        <Typography variant="body2" color="text.secondary">No trend data available</Typography>
      </Box>
    );
  }
  
  const dataPointCount = trendEntries.length;
  
  // Smart sizing based on data point count - better space utilization
  let chartWidth: number;
  let spacing: number;
  let labelRotation = false;
  
  if (dataPointCount <= 4) {
    // 7 days, 30 days (4 weeks each) - increased horizontal spacing
    chartWidth = 750; // Increased width to accommodate larger spacing
    spacing = 200; // Significantly increased spacing for better distribution
  } else if (dataPointCount <= 6) {
    // 90 days (3 months) - increased spacing with rotation
    spacing = 80; // Increased from 55 to 80
    chartWidth = 100 + (dataPointCount * spacing) + 50;
    labelRotation = true;
  } else {
    // 1 year (12 months) - increased spacing with horizontal scroll
    spacing = 65; // Increased from 45 to 65
    chartWidth = 100 + (dataPointCount * spacing) + 50;
    labelRotation = true;
  }
  
  const chartHeight = 200;
  const leftMargin = 40; // Reduced left margin for more chart space
  const rightMargin = 20; // Reduced right margin for more chart space
  const bottomMargin = labelRotation ? 50 : 30;
  
  // Function to format period labels based on time range
  const formatPeriodLabel = (period: string) => {
    if (dataPointCount > 8) {
      // For 1 year data - show very abbreviated month names
      if (period.includes('2024') || period.includes('2025')) {
        const parts = period.split(' ');
        if (parts.length >= 2) {
          const monthName = parts[0];
          const year = parts[1];
          const shortMonth = monthName.substring(0, 3); // Jan, Feb, etc.
          const shortYear = year.substring(2); // 24, 25
          return `${shortMonth}\n'${shortYear}`; // Stack year below month
        }
      }
      return period.length > 4 ? period.substring(0, 4) : period;
    } else if (dataPointCount > 4) {
      // For 90 days - show month names
      return period.length > 6 ? period.substring(0, 6) : period;
    }
    // For 7/30 days - show full names but truncate if too long
    return period.length > 8 ? period.substring(0, 8) : period;
  };

  return (
    <div style={{ 
      width: '100%', 
      height: '100%', 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center',
      overflow: dataPointCount > 8 ? 'auto' : 'visible'
    }}>
      <svg 
        width="100%" // Always use full width for better filling
        height="250"
        viewBox={`0 0 ${chartWidth} ${240 + bottomMargin}`} 
        style={{ 
          maxWidth: '100%',
          height: '250px'
        }}
        preserveAspectRatio="xMidYMid meet"
      >
        {/* Grid lines */}
        <defs>
          <pattern 
            id={`feedbackGrid-${timeFilter}`} 
            width={dataPointCount <= 4 ? (chartWidth - leftMargin - rightMargin) / Math.max(dataPointCount - 1, 1) : spacing} 
            height="40" 
            patternUnits="userSpaceOnUse"
          >
            <path 
              d={`M ${dataPointCount <= 4 ? (chartWidth - leftMargin - rightMargin) / Math.max(dataPointCount - 1, 1) : spacing} 0 L 0 0 0 40`} 
              fill="none" 
              stroke="#e2e8f0" 
              strokeWidth="0.5"
            />
          </pattern>
        </defs>
        <rect width={chartWidth - rightMargin} height={chartHeight} fill={`url(#feedbackGrid-${timeFilter})`} x={leftMargin} />

        {/* Axes */}
        <line x1={leftMargin} y1="40" x2={leftMargin} y2={chartHeight + 20} stroke="#e2e8f0" strokeWidth="1"/>
        <line x1={leftMargin} y1={chartHeight + 20} x2={chartWidth - rightMargin} y2={chartHeight + 20} stroke="#e2e8f0" strokeWidth="1"/>

        {/* Y-axis labels */}
        {(() => {
          const maxCount = Math.max(...Object.values(feedbackData.Trend).map(t => t.TotalCount), 1);
          
          // Create proper Y-axis scale with unique values
          let yLabels = [];
          if (maxCount <= 5) {
            // For small values, show each integer
            for (let i = maxCount; i >= 0; i--) {
              yLabels.push({ value: i, label: i.toString() });
            }
          } else {
            // For larger values, create 5 evenly spaced labels
            const step = Math.ceil(maxCount / 4);
            const uniqueValues = new Set<number>();
            
            // Add 0 first
            uniqueValues.add(0);
            
            // Add stepped values
            for (let i = 1; i <= 4; i++) {
              const value = Math.min(i * step, maxCount);
              uniqueValues.add(value);
            }
            
            // Ensure max value is included
            uniqueValues.add(maxCount);
            
            // Convert to sorted array
            yLabels = Array.from(uniqueValues)
              .sort((a, b) => b - a) // Sort descending
              .map(value => ({ value, label: value.toString() }));
          }
          
          return yLabels.map((item, index) => (
            <text 
              key={index}
              x={leftMargin - 5} // Adjusted for smaller left margin
              y={40 + (index * (chartHeight - 20) / Math.max(yLabels.length - 1, 1))} 
              fontSize="10" 
              fill="#64748b" 
              textAnchor="end"
              dominantBaseline="middle"
            >
              {item.label}
            </text>
          ));
        })()}

        {/* X-axis labels and trend visualization */}
        {trendEntries.map(([period, data], index) => {
          let xPos: number;
          if (dataPointCount <= 4) {
            // For 7-day view, distribute points evenly across the available width
            const availableChartWidth = chartWidth - leftMargin - rightMargin;
            xPos = leftMargin + (index * (availableChartWidth / Math.max(dataPointCount - 1, 1)));
          } else {
            xPos = leftMargin + (index * spacing);
          }
          
          const maxCount = Math.max(...Object.values(feedbackData.Trend).map(t => t.TotalCount), 1);
          const yPos = chartHeight + 20 - ((data.TotalCount / maxCount) * (chartHeight - 20));
          
          // Format period label based on data range
          const displayPeriod = formatPeriodLabel(period);
          
          return (
            <g key={period}>
              {/* X-axis label */}
              {dataPointCount > 8 && displayPeriod.includes('\n') ? (
                // Multi-line label for 1-year view
                <>
                  <text 
                    x={xPos} 
                    y={chartHeight + 35} 
                    fontSize="9" 
                    fill="#64748b" 
                    textAnchor="middle"
                    fontWeight="bold"
                  >
                    {displayPeriod.split('\n')[0]}
                  </text>
                  <text 
                    x={xPos} 
                    y={chartHeight + 45} 
                    fontSize="8" 
                    fill="#64748b" 
                    textAnchor="middle"
                    fontWeight="bold"
                  >
                    {displayPeriod.split('\n')[1]}
                  </text>
                </>
              ) : (
                // Single line label for other views
                <text 
                  x={xPos} 
                  y={chartHeight + 35} 
                  fontSize={dataPointCount > 8 ? "8" : "11"} 
                  fill="#64748b" 
                  textAnchor="middle"
                  fontWeight="bold"
                  transform={labelRotation && !displayPeriod.includes('\n') ? `rotate(-45, ${xPos}, ${chartHeight + 35})` : ''}
                >
                  {displayPeriod.replace('\n', ' ')}
                </text>
              )}
              
              {/* Data point */}
              <circle 
                cx={xPos} 
                cy={yPos} 
                r={dataPointCount > 8 ? "4" : "5"} 
                fill={
                  data.TrendIndicator === 'increase' ? '#10b981' : 
                  data.TrendIndicator === 'decrease' ? '#ef4444' : '#64748b'
                }
                stroke="white"
                strokeWidth="2"
              />
              
              {/* Value label - only show if space allows */}
              {(dataPointCount <= 8 || data.TotalCount > 0) && (
                <text 
                  x={xPos} 
                  y={yPos - 12} 
                  fontSize={dataPointCount > 8 ? "8" : "9"} 
                  fill={
                    data.TrendIndicator === 'increase' ? '#10b981' : 
                    data.TrendIndicator === 'decrease' ? '#ef4444' : '#64748b'
                  } 
                  textAnchor="middle"
                  fontWeight="bold"
                >
                  {data.TotalCount}
                </text>
              )}
              
              {/* Trend indicator - simplified for 1 year view */}
              {dataPointCount <= 8 && (
                <text 
                  x={xPos} 
                  y={yPos + 20} 
                  fontSize="8" 
                  fill={
                    data.TrendIndicator === 'increase' ? '#10b981' : 
                    data.TrendIndicator === 'decrease' ? '#ef4444' : '#64748b'
                  } 
                  textAnchor="middle"
                >
                  {data.TrendIndicator === 'increase' ? '↗' : 
                   data.TrendIndicator === 'decrease' ? '↘' : '→'}
                  {Math.abs(data.ChangePercentage)}%
                </text>
              )}
                  {/* Connect points with lines if not the first point */}
            {index > 0 && (
              <line 
                x1={dataPointCount <= 4 ? 
                  leftMargin + ((index - 1) * ((chartWidth - leftMargin - rightMargin) / Math.max(dataPointCount - 1, 1))) : 
                  leftMargin + ((index - 1) * spacing)
                } 
                y1={chartHeight + 20 - ((trendEntries[index - 1][1].TotalCount / maxCount) * (chartHeight - 20))}
                x2={xPos} 
                y2={yPos} 
                stroke="#94a3b8" 
                strokeWidth="2"
                strokeLinecap="round"
              />
            )}
            </g>
          );
        })}
      </svg>
      
      {/* Add summary info for 1-year view */}
      {dataPointCount > 8 && (
        <Box sx={{
          position: 'absolute',
          bottom: 5,
          left: '50%',
          transform: 'translateX(-50%)',
          display: 'flex',
          gap: 1.5, // Reduced gap
          backgroundColor: 'rgba(255,255,255,0.95)',
          padding: '4px 8px', // Reduced padding
          borderRadius: '4px',
          border: '1px solid #e2e8f0',
          fontSize: '10px'
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Box sx={{ width: 6, height: 6, backgroundColor: '#10b981', borderRadius: '50%' }} />
            <Typography variant="caption" sx={{ fontSize: '9px' }}>Inc</Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Box sx={{ width: 6, height: 6, backgroundColor: '#ef4444', borderRadius: '50%' }} />
            <Typography variant="caption" sx={{ fontSize: '9px' }}>Dec</Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Box sx={{ width: 6, height: 6, backgroundColor: '#64748b', borderRadius: '50%' }} />
            <Typography variant="caption" sx={{ fontSize: '9px' }}>Stable</Typography>
          </Box>
        </Box>
      )}
    </div>
  );
};

export default OverviewTab;
