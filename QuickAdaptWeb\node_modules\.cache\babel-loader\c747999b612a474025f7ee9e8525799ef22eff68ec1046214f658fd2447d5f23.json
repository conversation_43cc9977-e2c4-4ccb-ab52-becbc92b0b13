{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptWeb\\\\src\\\\components\\\\fileManagement\\\\FileList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { GridToolbarContainer, GridToolbarColumnsButton, GridToolbarFilterButton, GridToolbarDensitySelector } from \"@mui/x-data-grid\";\nimport { Button, Menu, MenuItem, FormControlLabel, IconButton, Link, Dialog, DialogActions, DialogTitle, DialogContent, Box, Card, Tooltip, CardActions, CardMedia, Typography, Modal, TextField, InputAdornment } from \"@mui/material\";\nimport { Edit as EditIcon, Delete as DeleteIcon, SaveAlt as SaveAltIcon, FileUpload, Search as SearchIcon } from \"@mui/icons-material\";\nimport SwapHorizIcon from '@mui/icons-material/SwapHoriz';\nimport loader from \"../../assets/loader.gif\";\nimport { getAllFiles, GetGuidesUsedByFile } from \"../../services/FileManagementService\";\nimport { isSidebarOpen, subscribe } from \"../adminMenu/sidemenustate\";\nimport { UploadImage, DeleteFile, ReplaceFile } from \"../../services/FileManagementService\";\nimport { Trash, PreviewImage, Replace, copy } from \"../../assets/icons/icons\";\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\nimport ArrowForwardIcon from '@mui/icons-material/ArrowForward';\nimport CloseIcon from '@mui/icons-material/Close';\nimport ImageUploadSection from \"../common/ImageUploadSection\";\nimport { useSnackbar } from \"../../SnackbarContext\";\n\n// import IconButton from '@mui/material/IconButton';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FileList = () => {\n  _s();\n  const {\n    openSnackbar\n  } = useSnackbar();\n  const [models, setModels] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [emailiddelete, setemailiddelete] = useState(\"\");\n  const [useridedit, setUserIdEdit] = useState(\"\");\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [sidebarOpen, setSidebarOpen] = useState(isSidebarOpen());\n  const [showPopup, setShowPopup] = useState(false);\n  const [selectedFiles, setSelectedFiles] = useState([]);\n  const [preview, setPreview] = useState(false);\n  const [showEditPopup, setShowEditPopup] = useState(false);\n  const [showDeletePopup, setShowDeletePopup] = useState(false);\n  const [fileUploads, setFileUploads] = useState([]);\n  const [snackbarOpen, setSnackbarOpen] = useState(false);\n  const [snackbarMessage, setSnackbarMessage] = useState(\"\");\n  const [snackbarSeverity, setSnackbarSeverity] = useState(\"success\");\n  const [replaceFileId, setReplaceFileId] = useState(\"\");\n  const [hoverId, setHoverId] = useState(\"\");\n  const [previewMode, setPreviewMode] = useState();\n  const [guideNames, setGuideNames] = useState([]);\n  const [skip, setSkip] = useState(0);\n  const [limit, setLimit] = useState(12);\n  const [filters, setFilters] = useState({});\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(\"\");\n  const [totalCount, setTotalCount] = useState(0);\n  useEffect(() => {\n    //getAllFiles();\n    const unsubscribe = subscribe(setSidebarOpen);\n    return () => unsubscribe();\n  }, []);\n\n  // Debounce search term\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setDebouncedSearchTerm(searchTerm);\n    }, 500);\n    return () => clearTimeout(timer);\n  }, [searchTerm]);\n  const fetchData = async () => {\n    setLoading(true);\n    try {\n      const response = await getAllFiles(skip, limit, debouncedSearchTerm, \"\");\n      if (response) {\n        const files = response === null || response === void 0 ? void 0 : response.results;\n        const count = response === null || response === void 0 ? void 0 : response._count;\n        const uploads = files.map(file => ({\n          ImageId: file.Id,\n          FileName: file.Name || null,\n          Url: file.Url + \"?timestamp=\" + new Date().getTime() || ''\n        }));\n        setModels(uploads);\n        setTotalCount(count);\n      } else {}\n    } catch (error) {} finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    if (!showPopup) {\n      fetchData();\n    }\n  }, [showPopup, skip, debouncedSearchTerm]);\n  const openPopup = () => {\n    setShowPopup(true);\n  };\n  const handleSearchChange = event => {\n    setSearchTerm(event.target.value);\n  };\n\n  // Reset pagination when search term changes\n  useEffect(() => {\n    setSkip(0);\n  }, [debouncedSearchTerm]);\n  const MatEdit = params => {\n    const handleDeleteClick = emailId => {\n      setShowDeletePopup(true);\n      setemailiddelete(emailId);\n    };\n    const handleEditClick = userid => {\n      setShowEditPopup(true);\n      setUserIdEdit(userid);\n    };\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(IconButton, {\n          color: \"secondary\",\n          \"aria-label\": \"edit\",\n          onClick: () => handleEditClick(params.userid),\n          children: /*#__PURE__*/_jsxDEV(EditIcon, {\n            style: {\n              color: \"blue\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 25\n        }, this),\n        label: \"\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(IconButton, {\n          color: \"secondary\",\n          \"aria-label\": \"delete\",\n          onClick: () => handleDeleteClick(params.emailId),\n          children: /*#__PURE__*/_jsxDEV(SwapHorizIcon, {\n            style: {\n              color: \"blue\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 25\n        }, this),\n        label: \"\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 18\n      }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(IconButton, {\n          color: \"secondary\",\n          \"aria-label\": \"delete\",\n          onClick: () => handleDeleteClick(params.emailId),\n          children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n            style: {\n              color: \"blue\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 25\n        }, this),\n        label: \"\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 13\n    }, this);\n  };\n\n  // Define columns\n  const columns = [{\n    field: \"ImageId\",\n    headerName: \"ImageId\",\n    width: sidebarOpen ? 150 : 170,\n    align: 'center',\n    headerAlign: 'center'\n  }, {\n    field: \"FileName\",\n    headerName: \"Name\",\n    width: sidebarOpen ? 220 : 250,\n    align: 'left',\n    headerAlign: 'left'\n  }, {\n    field: \"Url\",\n    headerName: \"Link\",\n    width: sidebarOpen ? 450 : 600,\n    renderCell: params => /*#__PURE__*/_jsxDEV(Link, {\n      href: params.value,\n      target: \"_blank\",\n      rel: \"noopener noreferrer\",\n      color: \"inherit\",\n      children: params.value\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 17\n    }, this),\n    align: 'left',\n    headerAlign: 'left'\n  }, {\n    field: \"actions\",\n    headerName: \"Actions\",\n    sortable: false,\n    width: 300,\n    renderCell: params => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(MatEdit, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 25\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 17\n    }, this),\n    align: 'center',\n    headerAlign: 'center'\n  }];\n\n  // Handle edit action (replace this with your actual implementation)\n  const handleEdit = row => {\n    // Implement your edit logic here\n  };\n  const handleClose = () => {\n    setShowPopup(false);\n    setSelectedFiles([]);\n    setPreview(false);\n  };\n  const CustomToolbar = () => {\n    const handleExportMenuClick = event => {\n      setAnchorEl(event.currentTarget);\n    };\n    const handleExportMenuClose = () => {\n      setAnchorEl(null);\n    };\n    const handleDownloadExcelClick = () => {\n      handleExportMenuClose();\n    };\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: \"flex\",\n        alignItems: \"center\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(GridToolbarContainer, {\n        children: [/*#__PURE__*/_jsxDEV(GridToolbarColumnsButton, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(GridToolbarFilterButton, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(GridToolbarDensitySelector, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        \"aria-controls\": \"export-menu\",\n        \"aria-haspopup\": \"true\",\n        onClick: handleExportMenuClick,\n        style: {\n          marginLeft: \"10px\"\n        },\n        startIcon: /*#__PURE__*/_jsxDEV(SaveAltIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 32\n        }, this),\n        children: \"Export\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Menu, {\n        id: \"export-menu\",\n        anchorEl: anchorEl,\n        keepMounted: true,\n        open: Boolean(anchorEl),\n        onClose: handleExportMenuClose,\n        children: /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: handleDownloadExcelClick,\n          children: \"Download Excel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 13\n    }, this);\n  };\n  const handleFileUpload = event => {\n    const newFiles = Array.from(event.target.files || []);\n    setSelectedFiles(prev => [...prev, ...newFiles]);\n  };\n  const handleUpload = async () => {\n    if (selectedFiles && selectedFiles.length > 0) {\n      try {\n        setLoading(true);\n\n        // Convert FileList to File[]\n        const filesArray = Array.from(selectedFiles);\n        await UploadImage(selectedFiles, setLoading, setShowPopup, setModels, setSelectedFiles);\n        openSnackbar(translate(\"Files Uploaded Successfully\"), \"success\");\n        setSelectedFiles([]);\n        setShowPopup(false);\n        fetchData();\n      } catch (error) {\n        var _err$response;\n        let message = translate(\"Upload failed. Please try again.\");\n        const err = error;\n        if (err !== null && err !== void 0 && (_err$response = err.response) !== null && _err$response !== void 0 && _err$response.data) {\n          message = typeof err.response.data === \"string\" ? err.response.data : err.response.data.message || message;\n        }\n        openSnackbar(message, \"error\");\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n  const copyUrl = async url => {\n    try {\n      await navigator.clipboard.writeText(url);\n      openSnackbar(translate(\"URL Copied Successfully\"), \"success\");\n    } catch (error) {\n      // console.log(error);\n      openSnackbar(translate(\"Failed to copy URL\"), \"error\");\n    }\n  };\n  const deleteFileIconClick = async file => {\n    try {\n      const status = await DeleteFile(file.ImageId);\n      if (status.Success !== false) {\n        openSnackbar(translate(\"File Deleted Successfully\"), \"success\");\n        fetchData();\n      } else {\n        openSnackbar(status.ErrorMessage || translate(\"Error in Deleting File\"), \"error\");\n      }\n    } catch (error) {\n      // console.log(error);\n      openSnackbar(translate(\"Error in Deleting File\"), \"error\");\n    }\n  };\n  const ReplaceImage = async event => {\n    var _event$target$files;\n    const file = (_event$target$files = event.target.files) === null || _event$target$files === void 0 ? void 0 : _event$target$files[0];\n    console.log(replaceFileId, \"Is replace file id\");\n    if (file) {\n      try {\n        setLoading(true);\n        await ReplaceFile(replaceFileId, file, setLoading);\n        setModels([]);\n        fetchData();\n        openSnackbar(translate(\"Image replaced successfully\"), \"success\");\n      } catch (error) {\n        var _err$response2;\n        let message = translate(\"Upload failed. Please try again.\");\n        const err = error;\n        if (err !== null && err !== void 0 && (_err$response2 = err.response) !== null && _err$response2 !== void 0 && _err$response2.data) {\n          message = typeof err.response.data === \"string\" ? err.response.data : err.response.data.message || message;\n        }\n        openSnackbar(message, \"error\");\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n  const setReplaceFileUrl = id => {\n    console.log(id, \"from click\");\n    setReplaceFileId(id);\n  };\n  const previewFile = async model => {\n    console.log(\"Preview clicked\");\n    const names = await GetGuidesUsedByFile(model.ImageId, setLoading);\n    setGuideNames(names);\n    setPreview(true);\n    setPreviewMode(model);\n  };\n  const {\n    t: translate\n  } = useTranslation();\n  const onNextClick = () => {\n    setSkip(skip + 12);\n    //setTop(top + 12);\n  };\n  const onPreviousClick = () => {\n    setSkip(skip - 12 >= 0 ? skip - 12 : 0);\n    //setTop(top-12>=12?top-12:12 )\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"qadpt-web\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-webcontent qadpt-file-mgn\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-head\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-title-sec\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-title\",\n            children: [\" \", translate('File Management')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-file-cnt\",\n            children: [\" \", totalCount, \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 8\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-right-part\",\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            placeholder: translate('Search...'),\n            value: searchTerm,\n            onChange: handleSearchChange,\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"start\",\n                sx: {\n                  mr: 0.5\n                },\n                children: /*#__PURE__*/_jsxDEV(SearchIcon, {\n                  sx: {\n                    fontSize: 20\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 37\n              }, this)\n            },\n            sx: {\n              width: '250px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 27\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowPopup(true),\n            className: \"qadpt-memberButton\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fal fa-add-plus\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 10\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: translate('Upload File')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 19\n            }, this), \"        \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 9\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 18\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: showPopup,\n        onClose: handleClose,\n        className: \"qadpt-upldpopup\",\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          className: \"qadpt-upldpopup-title\",\n          children: translate('Upload')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 484,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(ImageUploadSection, {\n            handleFileUpload: handleFileUpload,\n            selectedFiles: selectedFiles,\n            setSelectedFiles: setSelectedFiles\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleClose,\n            className: \"qadpt-cancelbtn\",\n            children: translate('Cancel')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            className: \"qadpt-upldbtn\",\n            onClick: async () => {\n              await handleUpload();\n            },\n            disabled: !selectedFiles || selectedFiles.length === 0,\n            children: translate('Upload')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 480,\n        columnNumber: 18\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"Loaderstyles\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: loader,\n          alt: \"Spinner\",\n          className: \"LoaderSpinnerStyles\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 526,\n        columnNumber: 22\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-file-grd\",\n          children: models.length === 0 && !loading ? /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            align: \"center\",\n            sx: {\n              width: \"100%\",\n              padding: \"20px\",\n              color: \"#999\"\n            },\n            children: translate(\"No Images Found\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 9\n          }, this) : models.map((model, index) => /*#__PURE__*/_jsxDEV(Card, {\n            className: \"qadpt-file-card\",\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-imgcont\",\n              onMouseEnter: () => setHoverId(model.ImageId),\n              onMouseLeave: () => setHoverId(\"\"),\n              children: [/*#__PURE__*/_jsxDEV(CardMedia, {\n                component: \"img\",\n                image: model.Url || \"\",\n                alt: model.FileName || \"Image\",\n                onClick: () => previewFile(model),\n                className: `qadpt-cardimage ${hoverId === model.ImageId ? \"qadpt-image-blur\" : \"\"}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 37\n              }, this), hoverId == model.ImageId && /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  position: \"absolute\",\n                  display: \"flex\",\n                  justifyContent: \"center\",\n                  alignItems: \"center\"\n                },\n                onClick: () => previewFile(model),\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: PreviewImage,\n                  alt: \"Preview Image\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 573,\n                  columnNumber: 49\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => deleteFileIconClick(model),\n                className: \"qadpt-deletebtn\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: Trash,\n                  alt: \"Delete\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 585,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n              className: \"qadpt-cardaction\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-filename\",\n                children: model.FileName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 589,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-fileactions\",\n                children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Replace Image\",\n                  arrow: true,\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: event => {\n                      var _document$getElementB;\n                      setReplaceFileUrl(model.ImageId);\n                      event === null || event === void 0 ? void 0 : event.stopPropagation();\n                      (_document$getElementB = document.getElementById(\"file-upload\")) === null || _document$getElementB === void 0 ? void 0 : _document$getElementB.click();\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: Replace,\n                      alt: \"Replace\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 599,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 593,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 592,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: () => copyUrl(model.Url),\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: copy,\n                    alt: \"copy\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 605,\n                    columnNumber: 37\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 604,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  id: \"file-upload\",\n                  style: {\n                    display: \"none\"\n                  },\n                  accept: \"image/*\",\n                  onChange: ReplaceImage\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 607,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 588,\n              columnNumber: 37\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 13\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 1\n        }, this), preview && /*#__PURE__*/_jsxDEV(Modal, {\n          open: preview,\n          onClose: () => {\n            setPreview(false);\n            setPreviewMode(undefined);\n          },\n          \"aria-labelledby\": \"modal-modal-title\",\n          \"aria-describedby\": \"modal-modal-description\",\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-preview-modal\",\n            children: [/*#__PURE__*/_jsxDEV(IconButton, {\n              \"aria-label\": \"close\",\n              className: \"qadpt-closebtn\",\n              onClick: () => {\n                setPreview(false);\n                setPreviewMode(undefined);\n              },\n              children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 644,\n                columnNumber: 5\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 635,\n              columnNumber: 61\n            }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n              src: (previewMode === null || previewMode === void 0 ? void 0 : previewMode.Url) || \"\",\n              alt: \"Model Image\",\n              className: \"qadpt-preview-img\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 647,\n              columnNumber: 45\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-preview-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                id: \"modal-modal-description\",\n                className: \"qadpt-preview-title\",\n                children: previewMode === null || previewMode === void 0 ? void 0 : previewMode.FileName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 649,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-filesusedin\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  id: \"modal-modal-description\",\n                  children: translate('File Used In')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: (guideNames === null || guideNames === void 0 ? void 0 : guideNames.length) < 1 ? translate('No Guides Used this file ') : guideNames\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 656,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 49\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 45\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 622,\n          columnNumber: 37\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-pagination\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              onClick: onPreviousClick,\n              startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 52\n              }, this),\n              variant: \"contained\",\n              disabled: skip === 0,\n              className: \"qadpt-prevbtn\",\n              children: translate('Previous')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              onClick: onNextClick,\n              endIcon: /*#__PURE__*/_jsxDEV(ArrowForwardIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 692,\n                columnNumber: 50\n              }, this),\n              variant: \"contained\",\n              disabled: skip + limit >= totalCount,\n              className: \"qadpt-nextbtn\",\n              children: translate('Next')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 690,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 689,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 677,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 450,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 449,\n    columnNumber: 7\n  }, this);\n};\n_s(FileList, \"4CmTQC0486YTHWwsTRTgbYJfGbA=\", false, function () {\n  return [useSnackbar, useTranslation];\n});\n_c = FileList;\nexport default FileList;\nvar _c;\n$RefreshReg$(_c, \"FileList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "GridToolbarContainer", "GridToolbarColumnsButton", "GridToolbarFilterButton", "GridToolbarDensitySelector", "<PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "FormControlLabel", "IconButton", "Link", "Dialog", "DialogActions", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Box", "Card", "<PERSON><PERSON><PERSON>", "CardActions", "CardMedia", "Typography", "Modal", "TextField", "InputAdornment", "Edit", "EditIcon", "Delete", "DeleteIcon", "SaveAlt", "SaveAltIcon", "FileUpload", "Search", "SearchIcon", "SwapHorizIcon", "loader", "getAllFiles", "GetGuidesUsedByFile", "isSidebarOpen", "subscribe", "UploadImage", "DeleteFile", "ReplaceFile", "Trash", "PreviewImage", "Replace", "copy", "ArrowBackIcon", "ArrowForwardIcon", "CloseIcon", "ImageUploadSection", "useSnackbar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FileList", "_s", "openSnackbar", "models", "setModels", "loading", "setLoading", "emailiddelete", "setemailiddelete", "useridedit", "setUserIdEdit", "anchorEl", "setAnchorEl", "sidebarOpen", "setSidebarOpen", "showPopup", "setShowPopup", "selectedFiles", "setSelectedFiles", "preview", "setPreview", "showEditPopup", "setShowEditPopup", "showDeletePopup", "setShowDeletePopup", "fileUploads", "setFileUploads", "snackbarOpen", "setSnackbarOpen", "snackbarMessage", "setSnackbarMessage", "snackbarSeverity", "setSnackbarSeverity", "replaceFileId", "setReplaceFileId", "hoverId", "setHoverId", "previewMode", "setPreviewMode", "guideNames", "setGuideNames", "skip", "setSkip", "limit", "setLimit", "filters", "setFilters", "searchTerm", "setSearchTerm", "debouncedSearchTerm", "setDebouncedSearchTerm", "totalCount", "setTotalCount", "unsubscribe", "timer", "setTimeout", "clearTimeout", "fetchData", "response", "files", "results", "count", "_count", "uploads", "map", "file", "ImageId", "Id", "FileName", "Name", "Url", "Date", "getTime", "error", "openPopup", "handleSearchChange", "event", "target", "value", "MatEdit", "params", "handleDeleteClick", "emailId", "handleEditClick", "userid", "children", "control", "color", "onClick", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "columns", "field", "headerName", "width", "align", "headerAlign", "renderCell", "href", "rel", "sortable", "display", "justifyContent", "alignItems", "handleEdit", "row", "handleClose", "CustomToolbar", "handleExportMenuClick", "currentTarget", "handleExportMenuClose", "handleDownloadExcelClick", "marginLeft", "startIcon", "id", "keepMounted", "open", "Boolean", "onClose", "handleFileUpload", "newFiles", "Array", "from", "prev", "handleUpload", "length", "filesArray", "translate", "_err$response", "message", "err", "data", "copyUrl", "url", "navigator", "clipboard", "writeText", "deleteFileIconClick", "status", "Success", "ErrorMessage", "ReplaceImage", "_event$target$files", "console", "log", "_err$response2", "setReplaceFileUrl", "previewFile", "model", "names", "t", "onNextClick", "onPreviousClick", "className", "size", "placeholder", "onChange", "InputProps", "startAdornment", "position", "sx", "mr", "fontSize", "disabled", "src", "alt", "variant", "padding", "index", "onMouseEnter", "onMouseLeave", "component", "image", "title", "arrow", "_document$getElementB", "stopPropagation", "document", "getElementById", "click", "type", "accept", "undefined", "endIcon", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/components/fileManagement/FileList.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport {\r\n    GridColDef,\r\n    GridToolbarContainer,\r\n    GridToolbarColumnsButton,\r\n    GridToolbarFilterButton,\r\n    GridToolbarDensitySelector,\r\n} from \"@mui/x-data-grid\";\r\nimport {\r\n    Button,\r\n    Menu,\r\n    MenuItem,\r\n    FormControlLabel,\r\n    IconButton,\r\n    Link,\r\n    Dialog,\r\n    DialogActions,\r\n    DialogTitle,\r\n    DialogContent,\r\n    Box,\r\n    Card,\r\n    Snackbar,\r\n    Alert,\r\n    Tooltip,\r\n    CardContent,\r\n    CardActions,\r\n    CardMedia,\r\n    Typography,\r\n    Modal,\r\n    TextField,\r\n    InputAdornment,\r\n    Grid,\r\n} from \"@mui/material\";\r\nimport {\r\n    Edit as EditIcon,\r\n    Delete as DeleteIcon,\r\n    SaveAlt as SaveAltIcon,\r\n    Opacity,\r\n    FileUpload,\r\n    Search as SearchIcon,\r\n} from \"@mui/icons-material\";\r\nimport SwapHorizIcon from '@mui/icons-material/SwapHoriz';\r\nimport loader from \"../../assets/loader.gif\";\r\nimport CustomGrid from \"../common/Grid\";\r\nimport { getAllFiles,GetGuidesUsedByFile } from \"../../services/FileManagementService\";\r\nimport { isSidebarOpen, subscribe } from \"../adminMenu/sidemenustate\";\r\nimport AddIcon from '@mui/icons-material/Add';\r\nimport { UploadImage,DeleteFile ,ReplaceFile} from \"../../services/FileManagementService\";\r\nimport { height } from \"@mui/system\";\r\nimport { inherits } from \"util\";\r\nimport LinkIcon from '@mui/icons-material/Link';\r\nimport UpgradeIcon from '@mui/icons-material/Upgrade';\r\nimport { Trash, PreviewImage, Replace, copy } from \"../../assets/icons/icons\";\r\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\r\nimport ArrowForwardIcon from '@mui/icons-material/ArrowForward';\r\nimport CloseIcon from '@mui/icons-material/Close';\r\nimport ImageUploadSection from \"../common/ImageUploadSection\";\r\nimport { useSnackbar } from \"../../SnackbarContext\";\r\nimport {\r\n    uploadfile,\r\n    \r\n} from \"../../assets/icons/icons\";\r\n// import IconButton from '@mui/material/IconButton';\r\n\r\n\r\n\r\ninterface FileUpload {\r\n    ImageId: string;\r\n    FileName: string | null;\r\n    Url: string;\r\n}\r\n\r\nconst FileList: React.FC = () => {\r\n    const { openSnackbar } = useSnackbar();\r\n    const [models, setModels] = useState<FileUpload[]>([]);\r\n    const [loading, setLoading] = useState(false);\r\n    const [emailiddelete, setemailiddelete] = useState(\"\");\r\n    const [useridedit, setUserIdEdit] = useState(\"\");\r\n    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);\r\n    const [sidebarOpen, setSidebarOpen] = useState(isSidebarOpen());\r\n    const [showPopup, setShowPopup] = useState(false);\r\n    const [selectedFiles, setSelectedFiles] = useState<File[]>([]);\r\n\r\n    const [preview, setPreview] = useState(false);\r\n    const [showEditPopup, setShowEditPopup] = useState(false);\r\n    const [showDeletePopup, setShowDeletePopup] = useState(false);\r\n    const [fileUploads, setFileUploads] = useState<FileUpload[]>([]);\r\n    const [snackbarOpen, setSnackbarOpen] = useState(false);\r\n    const [snackbarMessage, setSnackbarMessage] = useState(\"\");\r\n    const [snackbarSeverity, setSnackbarSeverity] = useState<\"success\" | \"error\">(\"success\");\r\n    const [replaceFileId, setReplaceFileId] = useState(\"\");\r\n    const [hoverId, setHoverId] = useState(\"\");\r\n    const [previewMode, setPreviewMode] = useState<FileUpload>();\r\n    const [guideNames, setGuideNames] = useState([]);\r\n    const [skip, setSkip] = useState(0);\r\n    const [limit, setLimit] = useState(12);\r\n    const [filters, setFilters] = useState({});\r\n    const [searchTerm, setSearchTerm] = useState(\"\");\r\n    const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(\"\");\r\n    const [totalCount, setTotalCount] = useState(0);\r\n\r\n\r\n\r\n    useEffect(() => {\r\n        //getAllFiles();\r\n        const unsubscribe = subscribe(setSidebarOpen);\r\n        return () => unsubscribe();\r\n    }, []);\r\n\r\n    // Debounce search term\r\n    useEffect(() => {\r\n        const timer = setTimeout(() => {\r\n            setDebouncedSearchTerm(searchTerm);\r\n        }, 500);\r\n\r\n        return () => clearTimeout(timer);\r\n    }, [searchTerm]);\r\n\r\n    const fetchData = async () => {\r\n        setLoading(true);\r\n                        try {\r\n                const response = await getAllFiles(skip,limit,debouncedSearchTerm,\"\");\r\n                            if (response) {\r\n                                const files = response?.results;\r\n\t\t                        const count = response?._count;\r\n                        const uploads: FileUpload[] = files.map((file:any) => ({\r\n                        ImageId: file.Id,\r\n                        FileName: file.Name || null,\r\n                        Url: file.Url + \"?timestamp=\" + new Date().getTime()|| '',\r\n                    }));\r\n                                setModels(uploads);\r\n                                setTotalCount(count);\r\n                } else {\r\n                }\r\n            } catch (error) {\r\n            } finally {\r\n                setLoading(false);\r\n            }\r\n    };\r\n\r\n    useEffect(() => {\r\n\r\n        if (!showPopup) {\r\n            fetchData();\r\n        }\r\n    }, [showPopup,skip,debouncedSearchTerm]);\r\n    \r\n    const openPopup = () => {\r\n\t\tsetShowPopup(true);\r\n\t};\r\n\r\n    const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n        setSearchTerm(event.target.value);\r\n    };\r\n\r\n    // Reset pagination when search term changes\r\n    useEffect(() => {\r\n        setSkip(0);\r\n    }, [debouncedSearchTerm]);\r\n    const MatEdit = (params: any) => {\r\n        const handleDeleteClick = (emailId: string) => {\r\n            setShowDeletePopup(true);\r\n            setemailiddelete(emailId);\r\n        };\r\n\r\n        const handleEditClick = (userid: string) => {\r\n            setShowEditPopup(true);\r\n            setUserIdEdit(userid);\r\n        };\r\n\r\n        return (\r\n            <div>\r\n                <FormControlLabel\r\n                    control={\r\n                        <IconButton\r\n                            color=\"secondary\"\r\n                            aria-label=\"edit\"\r\n                            onClick={() => handleEditClick(params.userid)}\r\n                        >\r\n                            <EditIcon style={{ color: \"blue\" }} />\r\n                        </IconButton>\r\n                    }\r\n                    label={\"\"}\r\n                />\r\n                 <FormControlLabel\r\n                    control={\r\n                        <IconButton\r\n                            color=\"secondary\"\r\n                            aria-label=\"delete\"\r\n                            onClick={() => handleDeleteClick(params.emailId)}\r\n                        >\r\n                            <SwapHorizIcon style={{ color: \"blue\" }} />\r\n                        </IconButton>\r\n                    }\r\n                    label={\"\"}\r\n                />\r\n                <FormControlLabel\r\n                    control={\r\n                        <IconButton\r\n                            color=\"secondary\"\r\n                            aria-label=\"delete\"\r\n                            onClick={() => handleDeleteClick(params.emailId)}\r\n                        >\r\n                            <DeleteIcon style={{ color: \"blue\" }} />\r\n                        </IconButton>\r\n                    }\r\n                    label={\"\"}\r\n                />  \r\n            </div>\r\n        );\r\n    };\r\n\r\n  // Define columns\r\n    const columns: GridColDef[] = [\r\n        {\r\n            field: \"ImageId\",\r\n            headerName: \"ImageId\",\r\n            width: sidebarOpen ? 150 : 170,\r\n            align: 'center',\r\n            headerAlign: 'center',\r\n        },\r\n        {\r\n            field: \"FileName\", \r\n            headerName: \"Name\", \r\n            width: sidebarOpen ? 220 : 250,\r\n            align: 'left',\r\n            headerAlign: 'left',\r\n        },\r\n        {\r\n            field: \"Url\",\r\n            headerName: \"Link\",\r\n            width: sidebarOpen ? 450 : 600,\r\n            renderCell: (params) => (\r\n                <Link href={params.value} target=\"_blank\" rel=\"noopener noreferrer\" color=\"inherit\">\r\n                    {params.value}\r\n                </Link>\r\n            ),\r\n            align: 'left',\r\n            headerAlign: 'left',\r\n        },\r\n        {\r\n            field: \"actions\",\r\n            headerName: \"Actions\",\r\n            sortable: false,\r\n            width: 300,\r\n            renderCell: (params) => (\r\n                <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>\r\n                   \r\n                        <MatEdit/>\r\n                   \r\n                </div>\r\n            ),\r\n            align: 'center',\r\n            headerAlign: 'center',\r\n        },\r\n    ];\r\n\r\n// Handle edit action (replace this with your actual implementation)\r\nconst handleEdit = (row:any) => {\r\n    // Implement your edit logic here\r\n};\r\n   \r\n\r\nconst handleClose = () => {\r\n    setShowPopup(false);\r\n    setSelectedFiles([]);\r\n    setPreview(false);\r\n};\r\n\r\n    const CustomToolbar: React.FC = () => {\r\n        const handleExportMenuClick = (event: React.MouseEvent<HTMLButtonElement>) => {\r\n            setAnchorEl(event.currentTarget);\r\n        };\r\n\r\n        const handleExportMenuClose = () => {\r\n         setAnchorEl(null);\r\n        };\r\n       \r\n        const handleDownloadExcelClick = () => {\r\n            handleExportMenuClose();\r\n        };\r\n\r\n        return (\r\n            <div style={{ display: \"flex\", alignItems: \"center\" }}>\r\n                <GridToolbarContainer>\r\n                    <GridToolbarColumnsButton />\r\n                    <GridToolbarFilterButton />\r\n                    <GridToolbarDensitySelector />\r\n                </GridToolbarContainer>\r\n                <Button\r\n                    aria-controls=\"export-menu\"\r\n                    aria-haspopup=\"true\"\r\n                    onClick={handleExportMenuClick}\r\n                    style={{ marginLeft: \"10px\" }}\r\n                    startIcon={<SaveAltIcon />}\r\n                >\r\n                    Export\r\n                </Button>\r\n                <Menu\r\n                    id=\"export-menu\"\r\n                    anchorEl={anchorEl}\r\n                    keepMounted\r\n                    open={Boolean(anchorEl)}\r\n                    onClose={handleExportMenuClose}\r\n                >\r\n                    <MenuItem onClick={handleDownloadExcelClick}>Download Excel</MenuItem>\r\n                </Menu>\r\n            </div>\r\n        );\r\n    };\r\nconst handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const newFiles = Array.from(event.target.files || []);\r\n    setSelectedFiles(prev => [...prev, ...newFiles]);\r\n};\r\n\r\n\r\n\r\nconst handleUpload = async () => {\r\n    if (selectedFiles && selectedFiles.length > 0) {\r\n        try {\r\n            setLoading(true);\r\n\r\n            // Convert FileList to File[]\r\n            const filesArray = Array.from(selectedFiles);\r\n\r\nawait UploadImage(\r\n    selectedFiles,\r\n    setLoading,\r\n    setShowPopup,\r\n    setModels,\r\n    setSelectedFiles\r\n);\r\n\r\n\r\n             openSnackbar(translate(\"Files Uploaded Successfully\"), \"success\");\r\n\r\n            setSelectedFiles([]);\r\n            setShowPopup(false);\r\n            fetchData();\r\n        } catch (error) {\r\n            let message = translate(\"Upload failed. Please try again.\");\r\n            const err = error as any;\r\n\r\n            if (err?.response?.data) {\r\n                message =\r\n                    typeof err.response.data === \"string\"\r\n                        ? err.response.data\r\n                        : err.response.data.message || message;\r\n            }\r\n\r\n            openSnackbar(message, \"error\");\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    }\r\n};\r\n\r\n\r\n\r\n    const copyUrl = async (url: string) => {\r\n        try {\r\n            await navigator.clipboard.writeText(url);\r\n            openSnackbar(translate(\"URL Copied Successfully\"), \"success\");\r\n\r\n            \r\n            \r\n            \r\n        } catch (error) {\r\n            // console.log(error);\r\n            openSnackbar(translate(\"Failed to copy URL\"), \"error\");\r\n        }\r\n    }\r\n\r\n\r\n\r\n    const deleteFileIconClick = async (file: any) => {\r\n        try {\r\n            const status = await DeleteFile(file.ImageId);\r\n            if (status.Success !== false) {\r\n                openSnackbar(translate(\"File Deleted Successfully\"), \"success\");\r\n                fetchData();\r\n            } else {\r\n                openSnackbar(status.ErrorMessage || translate(\"Error in Deleting File\"), \"error\");\r\n            }\r\n        } catch (error) {\r\n            // console.log(error);\r\n            openSnackbar(translate(\"Error in Deleting File\"), \"error\");\r\n            \r\n        }\r\n    }\r\n\r\n    const ReplaceImage = async (event: React.ChangeEvent<HTMLInputElement>) => {\r\n      const file = event.target.files?.[0];\r\n      console.log(replaceFileId, \"Is replace file id\");\r\n      if (file) {\r\n        try {\r\n          setLoading(true);\r\n          await ReplaceFile(replaceFileId, file, setLoading);\r\n          setModels([]);\r\n\r\n            fetchData();\r\n            openSnackbar(translate(\"Image replaced successfully\"), \"success\");\r\n        } catch (error) {\r\n            let message = translate(\"Upload failed. Please try again.\");\r\n          const err = error as any;\r\n\r\n          if (err?.response?.data) {\r\n            message =\r\n              typeof err.response.data === \"string\"\r\n                ? err.response.data\r\n                : err.response.data.message || message;\r\n          }\r\n          openSnackbar(message, \"error\");\r\n        } finally {\r\n          setLoading(false);\r\n        }\r\n      }\r\n    };\r\n\r\n\r\n    const setReplaceFileUrl = (id : any) => {\r\n        console.log(id, \"from click\");\r\n        setReplaceFileId(id);\r\n    }\r\n\r\n    const previewFile = async (model : any) => {\r\n        console.log(\"Preview clicked\");\r\n        \r\n        const names = await GetGuidesUsedByFile(model.ImageId, setLoading);\r\n        setGuideNames(names);\r\n        setPreview(true);\r\n        setPreviewMode(model);\r\n    }\r\n    const { t: translate } = useTranslation();\r\n\r\n    const onNextClick = () => {\r\n        setSkip(skip+12);\r\n        //setTop(top + 12);\r\n    }\r\n\r\n    const onPreviousClick = () => {\r\n        setSkip(skip - 12 >= 0 ? skip - 12 : 0);\r\n        //setTop(top-12>=12?top-12:12 )\r\n    }\r\n\r\n    return (\r\n      \r\n      <div className='qadpt-web'>\r\n            <div className='qadpt-webcontent qadpt-file-mgn'>\r\n                 <div className=\"qadpt-head\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-title-sec\">\r\n                        <div className=\"qadpt-title\"> {translate('File Management')}</div>\r\n                        <div className=\"qadpt-file-cnt\"> {totalCount} </div>\r\n\t\t\t\t\t\t\t</div>\r\n                    <div className=\"qadpt-right-part\">\r\n                          <TextField\r\n                            size=\"small\"\r\n                            placeholder={translate('Search...')}\r\n                            value={searchTerm}\r\n                            onChange={handleSearchChange}\r\n                            InputProps={{\r\n                                startAdornment: (\r\n                                    <InputAdornment position=\"start\" sx={{ mr: 0.5 }}>\r\n                                        <SearchIcon sx={{ fontSize: 20 }}/>\r\n                                    </InputAdornment>\r\n                                ),\r\n                            }}\r\n                            \r\n                            sx={{ width: '250px' }}\r\n                        />\r\n\t\t\t\t\t\t\t\t<button\r\n               onClick={() => setShowPopup(true)}\r\n                className=\"qadpt-memberButton\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<i className=\"fal fa-add-plus\"></i>\r\n                  <span>{translate('Upload File')}</span>\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t</div>\r\n          </div>\r\n                 <Dialog open={showPopup}\r\n                         onClose={handleClose}\r\n                        className=\"qadpt-upldpopup\"\r\n                    >\r\n                        <DialogTitle className=\"qadpt-upldpopup-title\"\r\n                            \r\n                        >\r\n                            {translate('Upload')}\r\n                            {/* <span\r\n                                onClick={() => document.getElementById(\"file-upload\")?.click()}\r\n                                dangerouslySetInnerHTML={{ __html: uploadfile }}\r\n                                /> */}\r\n                        </DialogTitle>\r\n                        <DialogContent>\r\n                            <ImageUploadSection\r\n    handleFileUpload={handleFileUpload} selectedFiles={selectedFiles} setSelectedFiles={setSelectedFiles}\r\n/>\r\n                        {/* <Box\r\n                                display=\"flex\"\r\n                                justifyContent=\"center\"\r\n                                alignItems=\"center\"\r\n                                minHeight=\"50px\" // Adjust as needed */}\r\n                            {/* > */}\r\n                            {/* <input type=\"file\" onChange={handleFileChange} /> */}\r\n\r\n                                {/* <input type=\"file\" onChange={handleFileUpload} accept=\"image/*\" multiple/> */}\r\n                            {/* </Box> */}\r\n\r\n\r\n                        </DialogContent>\r\n                        <DialogActions >\r\n                            <Button onClick={handleClose}  className=\"qadpt-cancelbtn\">\r\n                            {translate('Cancel')}\r\n                        </Button>\r\n                            <Button className=\"qadpt-upldbtn\"\r\n                                onClick={async () => {\r\n                                    await handleUpload();\r\n                                }}\r\n    disabled={!selectedFiles || selectedFiles.length === 0}\r\n>\r\n    {translate('Upload')}\r\n</Button>\r\n\r\n                        </DialogActions>\r\n                    </Dialog>\r\n                {loading ? (\r\n                     <div className=\"Loaderstyles\">\r\n                        <img\r\n      src={loader}\r\n\t\t\talt=\"Spinner\"\r\n\t\t\tclassName=\"LoaderSpinnerStyles\"\r\n      />\r\n                    </div>\r\n            ) : (\r\n                    <>\r\n<Box className=\"qadpt-file-grd\">\r\n    {models.length === 0 && !loading ? (\r\n        <Typography\r\n            variant=\"body1\"\r\n            align=\"center\"\r\n            sx={{ width: \"100%\", padding: \"20px\", color: \"#999\" }}\r\n        >\r\n            {translate(\"No Images Found\")}\r\n        </Typography>\r\n    ) : (\r\n        models.map((model, index) => (\r\n            <Card key={index} className=\"qadpt-file-card\">\r\n                                    <Box className=\"qadpt-imgcont\"\r\n                                       \r\n                                    onMouseEnter={() => setHoverId(model.ImageId)}\r\n                                    onMouseLeave={() => setHoverId(\"\")}>\r\n                                    <CardMedia\r\n                                        component=\"img\"\r\n                                        image={model.Url||\"\"}\r\n                                            alt={model.FileName || \"Image\"}\r\n                                            \r\n                                           \r\n                                            onClick={()=>previewFile(model)}\r\n                                             className={`qadpt-cardimage ${hoverId === model.ImageId ? \"qadpt-image-blur\" : \"\"}`}\r\n                                        />\r\n                                        \r\n                                        \r\n                                        { hoverId == model.ImageId &&(\r\n                                            <Box sx={{\r\n                                                position: \"absolute\",\r\n                                                display: \"flex\",\r\n                                                justifyContent: \"center\",\r\n                                                alignItems: \"center\",\r\n                                                \r\n                                            }}\r\n                                            onClick={()=>previewFile(model)}\r\n                                            >\r\n                                                {/* <Preview /> */}\r\n                                                <img src={PreviewImage} alt=\"Preview Image\" />\r\n                                                {/* <Typography>Preview</Typography> */}\r\n                                            </Box>)\r\n                                        }\r\n                                        \r\n                                            \r\n                                            \r\n                                    \r\n                                    <IconButton\r\n                                            onClick={() => deleteFileIconClick(model)}\r\n                                            className=\"qadpt-deletebtn\"\r\n                                    >\r\n                                        <img src={Trash} alt=\"Delete\"/>\r\n                                    </IconButton>\r\n                                    </Box>\r\n                                    <CardActions className=\"qadpt-cardaction\">\r\n                                        <div className=\"qadpt-filename\">{model.FileName}</div>\r\n                                        <div className=\"qadpt-fileactions\">\r\n                                    \r\n                                        <Tooltip title=\"Replace Image\" arrow>\r\n                                        <IconButton \r\n                                                onClick={(event) => {\r\n                                                    setReplaceFileUrl(model.ImageId);\r\n                                                event?.stopPropagation();\r\n                                                document.getElementById(\"file-upload\")?.click();\r\n                                            }}>\r\n                                                    <img src={Replace} alt=\"Replace\"/>\r\n                                                    \r\n                                                </IconButton>\r\n                                                \r\n                                            </Tooltip>\r\n                                            <IconButton onClick={() => copyUrl(model.Url)}>\r\n                                    <img src={copy} alt=\"copy\"/>\r\n                                        </IconButton>\r\n                                            <input\r\n                                                type=\"file\"\r\n                                                id=\"file-upload\"\r\n                                                style={{ display: \"none\" }}\r\n                                                accept=\"image/*\"\r\n                                                onChange={ReplaceImage}\r\n                                            />\r\n                                            </div>\r\n                                    </CardActions>\r\n            </Card>\r\n        ))\r\n    )}\r\n</Box>\r\n                            {\r\n                                preview && (\r\n                                    <Modal\r\n                                        open={preview}\r\n                                        onClose={() => {\r\n                                            setPreview(false);\r\n                                            setPreviewMode(undefined);\r\n                                        }\r\n                                            \r\n                                        }\r\n                                        aria-labelledby=\"modal-modal-title\"\r\n                                        aria-describedby=\"modal-modal-description\"\r\n                                        >\r\n                                        <Box className=\"qadpt-preview-modal\">\r\n\r\n                                                            <IconButton\r\n                                                aria-label=\"close\"\r\n                                                className=\"qadpt-closebtn\"\r\n                    onClick={() => {\r\n                        setPreview(false);\r\n                        setPreviewMode(undefined);\r\n                    }}\r\n    \r\n>\r\n    <CloseIcon />\r\n                </IconButton>\r\n                                            \r\n                                            <img src={previewMode?.Url || \"\"} alt=\"Model Image\" className=\"qadpt-preview-img\" />\r\n                                            <div className=\"qadpt-preview-content\">\r\n                                            <div id=\"modal-modal-description\" className=\"qadpt-preview-title\">\r\n                                                {previewMode?.FileName}\r\n                                                </div>\r\n                                                <div className=\"qadpt-filesusedin\">\r\n                                            <div id=\"modal-modal-description\">\r\n                                                {translate('File Used In')}\r\n                                            </div>\r\n                                            <div>\r\n                                                {guideNames?.length<1 ? translate('No Guides Used this file ') : guideNames }\r\n                                                    </div>\r\n                                                    </div>\r\n                                            </div>\r\n                                        </Box>\r\n                                    </Modal>\r\n                                )\r\n                                \r\n                    }\r\n\r\n                    {/* <CustomGrid\r\n                        rows={models}\r\n                        columns={columns}\r\n                        pageSize={100}\r\n                        totalRows={models.length}   \r\n                        onPageChange={(newPage) => newPage}\r\n                        onPageSizeChange={(newPageSize) => newPageSize}\r\n                        rowsPerPageOptions={[10, 20, 50, 100]}\r\n                        Toolbar={CustomToolbar}\r\n                        /> */}\r\n                            <Box className=\"qadpt-pagination\">\r\n                                <div >\r\n                                    <Button\r\n                                        onClick={onPreviousClick}\r\n                                        startIcon={<ArrowBackIcon />}\r\n                                        variant=\"contained\"\r\n                                        disabled={skip === 0}\r\n                                        className=\"qadpt-prevbtn\"\r\n                                    >{translate('Previous')}\r\n                                       \r\n                                    </Button>\r\n                                </div>\r\n                                <div>\r\n                                    <Button\r\n                                        onClick={onNextClick}\r\n                                        endIcon={<ArrowForwardIcon />}\r\n                                        variant=\"contained\"\r\n                                        disabled={skip + limit >= totalCount}\r\n                                        className=\"qadpt-nextbtn\"\r\n                                    >{translate('Next')}\r\n                                        \r\n                                    </Button>\r\n                                </div>\r\n                            </Box>\r\n                        </>\r\n            )}\r\n            </div>\r\n            </div>\r\n    );\r\n};\r\n\r\nexport default FileList;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAEIC,oBAAoB,EACpBC,wBAAwB,EACxBC,uBAAuB,EACvBC,0BAA0B,QACvB,kBAAkB;AACzB,SACIC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,gBAAgB,EAChBC,UAAU,EACVC,IAAI,EACJC,MAAM,EACNC,aAAa,EACbC,WAAW,EACXC,aAAa,EACbC,GAAG,EACHC,IAAI,EAGJC,OAAO,EAEPC,WAAW,EACXC,SAAS,EACTC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,cAAc,QAEX,eAAe;AACtB,SACIC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EAEtBC,UAAU,EACVC,MAAM,IAAIC,UAAU,QACjB,qBAAqB;AAC5B,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,MAAM,MAAM,yBAAyB;AAE5C,SAASC,WAAW,EAACC,mBAAmB,QAAQ,sCAAsC;AACtF,SAASC,aAAa,EAAEC,SAAS,QAAQ,4BAA4B;AAErE,SAASC,WAAW,EAACC,UAAU,EAAEC,WAAW,QAAO,sCAAsC;AAKzF,SAASC,KAAK,EAAEC,YAAY,EAAEC,OAAO,EAAEC,IAAI,QAAQ,0BAA0B;AAC7E,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,kBAAkB,MAAM,8BAA8B;AAC7D,SAASC,WAAW,QAAQ,uBAAuB;;AAKnD;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAUA,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM;IAAEC;EAAa,CAAC,GAAGP,WAAW,CAAC,CAAC;EACtC,MAAM,CAACQ,MAAM,EAAEC,SAAS,CAAC,GAAG7D,QAAQ,CAAe,EAAE,CAAC;EACtD,MAAM,CAAC8D,OAAO,EAAEC,UAAU,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgE,aAAa,EAAEC,gBAAgB,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACkE,UAAU,EAAEC,aAAa,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoE,QAAQ,EAAEC,WAAW,CAAC,GAAGrE,QAAQ,CAAqB,IAAI,CAAC;EAClE,MAAM,CAACsE,WAAW,EAAEC,cAAc,CAAC,GAAGvE,QAAQ,CAACuC,aAAa,CAAC,CAAC,CAAC;EAC/D,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC0E,aAAa,EAAEC,gBAAgB,CAAC,GAAG3E,QAAQ,CAAS,EAAE,CAAC;EAE9D,MAAM,CAAC4E,OAAO,EAAEC,UAAU,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8E,aAAa,EAAEC,gBAAgB,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACgF,eAAe,EAAEC,kBAAkB,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACkF,WAAW,EAAEC,cAAc,CAAC,GAAGnF,QAAQ,CAAe,EAAE,CAAC;EAChE,MAAM,CAACoF,YAAY,EAAEC,eAAe,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsF,eAAe,EAAEC,kBAAkB,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACwF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzF,QAAQ,CAAsB,SAAS,CAAC;EACxF,MAAM,CAAC0F,aAAa,EAAEC,gBAAgB,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC4F,OAAO,EAAEC,UAAU,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC8F,WAAW,EAAEC,cAAc,CAAC,GAAG/F,QAAQ,CAAa,CAAC;EAC5D,MAAM,CAACgG,UAAU,EAAEC,aAAa,CAAC,GAAGjG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkG,IAAI,EAAEC,OAAO,CAAC,GAAGnG,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACoG,KAAK,EAAEC,QAAQ,CAAC,GAAGrG,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsG,OAAO,EAAEC,UAAU,CAAC,GAAGvG,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACwG,UAAU,EAAEC,aAAa,CAAC,GAAGzG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0G,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3G,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAAC4G,UAAU,EAAEC,aAAa,CAAC,GAAG7G,QAAQ,CAAC,CAAC,CAAC;EAI/CC,SAAS,CAAC,MAAM;IACZ;IACA,MAAM6G,WAAW,GAAGtE,SAAS,CAAC+B,cAAc,CAAC;IAC7C,OAAO,MAAMuC,WAAW,CAAC,CAAC;EAC9B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7G,SAAS,CAAC,MAAM;IACZ,MAAM8G,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC3BL,sBAAsB,CAACH,UAAU,CAAC;IACtC,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMS,YAAY,CAACF,KAAK,CAAC;EACpC,CAAC,EAAE,CAACP,UAAU,CAAC,CAAC;EAEhB,MAAMU,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC1BnD,UAAU,CAAC,IAAI,CAAC;IACA,IAAI;MACZ,MAAMoD,QAAQ,GAAG,MAAM9E,WAAW,CAAC6D,IAAI,EAACE,KAAK,EAACM,mBAAmB,EAAC,EAAE,CAAC;MACzD,IAAIS,QAAQ,EAAE;QACV,MAAMC,KAAK,GAAGD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,OAAO;QACrC,MAAMC,KAAK,GAAGH,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEI,MAAM;QAChC,MAAMC,OAAqB,GAAGJ,KAAK,CAACK,GAAG,CAAEC,IAAQ,KAAM;UACvDC,OAAO,EAAED,IAAI,CAACE,EAAE;UAChBC,QAAQ,EAAEH,IAAI,CAACI,IAAI,IAAI,IAAI;UAC3BC,GAAG,EAAEL,IAAI,CAACK,GAAG,GAAG,aAAa,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,IAAG;QAC3D,CAAC,CAAC,CAAC;QACSpE,SAAS,CAAC2D,OAAO,CAAC;QAClBX,aAAa,CAACS,KAAK,CAAC;MACpC,CAAC,MAAM,CACP;IACJ,CAAC,CAAC,OAAOY,KAAK,EAAE,CAChB,CAAC,SAAS;MACNnE,UAAU,CAAC,KAAK,CAAC;IACrB;EACR,CAAC;EAED9D,SAAS,CAAC,MAAM;IAEZ,IAAI,CAACuE,SAAS,EAAE;MACZ0C,SAAS,CAAC,CAAC;IACf;EACJ,CAAC,EAAE,CAAC1C,SAAS,EAAC0B,IAAI,EAACQ,mBAAmB,CAAC,CAAC;EAExC,MAAMyB,SAAS,GAAGA,CAAA,KAAM;IAC1B1D,YAAY,CAAC,IAAI,CAAC;EACnB,CAAC;EAEE,MAAM2D,kBAAkB,GAAIC,KAA0C,IAAK;IACvE5B,aAAa,CAAC4B,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACrC,CAAC;;EAED;EACAtI,SAAS,CAAC,MAAM;IACZkG,OAAO,CAAC,CAAC,CAAC;EACd,CAAC,EAAE,CAACO,mBAAmB,CAAC,CAAC;EACzB,MAAM8B,OAAO,GAAIC,MAAW,IAAK;IAC7B,MAAMC,iBAAiB,GAAIC,OAAe,IAAK;MAC3C1D,kBAAkB,CAAC,IAAI,CAAC;MACxBhB,gBAAgB,CAAC0E,OAAO,CAAC;IAC7B,CAAC;IAED,MAAMC,eAAe,GAAIC,MAAc,IAAK;MACxC9D,gBAAgB,CAAC,IAAI,CAAC;MACtBZ,aAAa,CAAC0E,MAAM,CAAC;IACzB,CAAC;IAED,oBACIvF,OAAA;MAAAwF,QAAA,gBACIxF,OAAA,CAAC5C,gBAAgB;QACbqI,OAAO,eACHzF,OAAA,CAAC3C,UAAU;UACPqI,KAAK,EAAC,WAAW;UACjB,cAAW,MAAM;UACjBC,OAAO,EAAEA,CAAA,KAAML,eAAe,CAACH,MAAM,CAACI,MAAM,CAAE;UAAAC,QAAA,eAE9CxF,OAAA,CAAC3B,QAAQ;YAACuH,KAAK,EAAE;cAAEF,KAAK,EAAE;YAAO;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CACf;QACDC,KAAK,EAAE;MAAG;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eACDhG,OAAA,CAAC5C,gBAAgB;QACdqI,OAAO,eACHzF,OAAA,CAAC3C,UAAU;UACPqI,KAAK,EAAC,WAAW;UACjB,cAAW,QAAQ;UACnBC,OAAO,EAAEA,CAAA,KAAMP,iBAAiB,CAACD,MAAM,CAACE,OAAO,CAAE;UAAAG,QAAA,eAEjDxF,OAAA,CAACnB,aAAa;YAAC+G,KAAK,EAAE;cAAEF,KAAK,EAAE;YAAO;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CACf;QACDC,KAAK,EAAE;MAAG;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eACFhG,OAAA,CAAC5C,gBAAgB;QACbqI,OAAO,eACHzF,OAAA,CAAC3C,UAAU;UACPqI,KAAK,EAAC,WAAW;UACjB,cAAW,QAAQ;UACnBC,OAAO,EAAEA,CAAA,KAAMP,iBAAiB,CAACD,MAAM,CAACE,OAAO,CAAE;UAAAG,QAAA,eAEjDxF,OAAA,CAACzB,UAAU;YAACqH,KAAK,EAAE;cAAEF,KAAK,EAAE;YAAO;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CACf;QACDC,KAAK,EAAE;MAAG;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEd,CAAC;;EAEH;EACE,MAAME,OAAqB,GAAG,CAC1B;IACIC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrBC,KAAK,EAAErF,WAAW,GAAG,GAAG,GAAG,GAAG;IAC9BsF,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE;EACjB,CAAC,EACD;IACIJ,KAAK,EAAE,UAAU;IACjBC,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAErF,WAAW,GAAG,GAAG,GAAG,GAAG;IAC9BsF,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE;EACjB,CAAC,EACD;IACIJ,KAAK,EAAE,KAAK;IACZC,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAErF,WAAW,GAAG,GAAG,GAAG,GAAG;IAC9BwF,UAAU,EAAGrB,MAAM,iBACfnF,OAAA,CAAC1C,IAAI;MAACmJ,IAAI,EAAEtB,MAAM,CAACF,KAAM;MAACD,MAAM,EAAC,QAAQ;MAAC0B,GAAG,EAAC,qBAAqB;MAAChB,KAAK,EAAC,SAAS;MAAAF,QAAA,EAC9EL,MAAM,CAACF;IAAK;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CACT;IACDM,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE;EACjB,CAAC,EACD;IACIJ,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrBO,QAAQ,EAAE,KAAK;IACfN,KAAK,EAAE,GAAG;IACVG,UAAU,EAAGrB,MAAM,iBACfnF,OAAA;MAAK4F,KAAK,EAAE;QAAEgB,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAtB,QAAA,eAExExF,OAAA,CAACkF,OAAO;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEb,CACR;IACDM,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE;EACjB,CAAC,CACJ;;EAEL;EACA,MAAMQ,UAAU,GAAIC,GAAO,IAAK;IAC5B;EAAA,CACH;EAGD,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACtB9F,YAAY,CAAC,KAAK,CAAC;IACnBE,gBAAgB,CAAC,EAAE,CAAC;IACpBE,UAAU,CAAC,KAAK,CAAC;EACrB,CAAC;EAEG,MAAM2F,aAAuB,GAAGA,CAAA,KAAM;IAClC,MAAMC,qBAAqB,GAAIpC,KAA0C,IAAK;MAC1EhE,WAAW,CAACgE,KAAK,CAACqC,aAAa,CAAC;IACpC,CAAC;IAED,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;MACnCtG,WAAW,CAAC,IAAI,CAAC;IAClB,CAAC;IAED,MAAMuG,wBAAwB,GAAGA,CAAA,KAAM;MACnCD,qBAAqB,CAAC,CAAC;IAC3B,CAAC;IAED,oBACIrH,OAAA;MAAK4F,KAAK,EAAE;QAAEgB,OAAO,EAAE,MAAM;QAAEE,UAAU,EAAE;MAAS,CAAE;MAAAtB,QAAA,gBAClDxF,OAAA,CAACnD,oBAAoB;QAAA2I,QAAA,gBACjBxF,OAAA,CAAClD,wBAAwB;UAAA+I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5BhG,OAAA,CAACjD,uBAAuB;UAAA8I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3BhG,OAAA,CAAChD,0BAA0B;UAAA6I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eACvBhG,OAAA,CAAC/C,MAAM;QACH,iBAAc,aAAa;QAC3B,iBAAc,MAAM;QACpB0I,OAAO,EAAEwB,qBAAsB;QAC/BvB,KAAK,EAAE;UAAE2B,UAAU,EAAE;QAAO,CAAE;QAC9BC,SAAS,eAAExH,OAAA,CAACvB,WAAW;UAAAoH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAR,QAAA,EAC9B;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACThG,OAAA,CAAC9C,IAAI;QACDuK,EAAE,EAAC,aAAa;QAChB3G,QAAQ,EAAEA,QAAS;QACnB4G,WAAW;QACXC,IAAI,EAAEC,OAAO,CAAC9G,QAAQ,CAAE;QACxB+G,OAAO,EAAER,qBAAsB;QAAA7B,QAAA,eAE/BxF,OAAA,CAAC7C,QAAQ;UAACwI,OAAO,EAAE2B,wBAAyB;UAAA9B,QAAA,EAAC;QAAc;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEd,CAAC;EACL,MAAM8B,gBAAgB,GAAI/C,KAA0C,IAAK;IACrE,MAAMgD,QAAQ,GAAGC,KAAK,CAACC,IAAI,CAAClD,KAAK,CAACC,MAAM,CAAClB,KAAK,IAAI,EAAE,CAAC;IACrDzC,gBAAgB,CAAC6G,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGH,QAAQ,CAAC,CAAC;EACpD,CAAC;EAID,MAAMI,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI/G,aAAa,IAAIA,aAAa,CAACgH,MAAM,GAAG,CAAC,EAAE;MAC3C,IAAI;QACA3H,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACA,MAAM4H,UAAU,GAAGL,KAAK,CAACC,IAAI,CAAC7G,aAAa,CAAC;QAExD,MAAMjC,WAAW,CACbiC,aAAa,EACbX,UAAU,EACVU,YAAY,EACZZ,SAAS,EACTc,gBACJ,CAAC;QAGYhB,YAAY,CAACiI,SAAS,CAAC,6BAA6B,CAAC,EAAE,SAAS,CAAC;QAElEjH,gBAAgB,CAAC,EAAE,CAAC;QACpBF,YAAY,CAAC,KAAK,CAAC;QACnByC,SAAS,CAAC,CAAC;MACf,CAAC,CAAC,OAAOgB,KAAK,EAAE;QAAA,IAAA2D,aAAA;QACZ,IAAIC,OAAO,GAAGF,SAAS,CAAC,kCAAkC,CAAC;QAC3D,MAAMG,GAAG,GAAG7D,KAAY;QAExB,IAAI6D,GAAG,aAAHA,GAAG,gBAAAF,aAAA,GAAHE,GAAG,CAAE5E,QAAQ,cAAA0E,aAAA,eAAbA,aAAA,CAAeG,IAAI,EAAE;UACrBF,OAAO,GACH,OAAOC,GAAG,CAAC5E,QAAQ,CAAC6E,IAAI,KAAK,QAAQ,GAC/BD,GAAG,CAAC5E,QAAQ,CAAC6E,IAAI,GACjBD,GAAG,CAAC5E,QAAQ,CAAC6E,IAAI,CAACF,OAAO,IAAIA,OAAO;QAClD;QAEAnI,YAAY,CAACmI,OAAO,EAAE,OAAO,CAAC;MAClC,CAAC,SAAS;QACN/H,UAAU,CAAC,KAAK,CAAC;MACrB;IACJ;EACJ,CAAC;EAIG,MAAMkI,OAAO,GAAG,MAAOC,GAAW,IAAK;IACnC,IAAI;MACA,MAAMC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACH,GAAG,CAAC;MACxCvI,YAAY,CAACiI,SAAS,CAAC,yBAAyB,CAAC,EAAE,SAAS,CAAC;IAKjE,CAAC,CAAC,OAAO1D,KAAK,EAAE;MACZ;MACAvE,YAAY,CAACiI,SAAS,CAAC,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAC1D;EACJ,CAAC;EAID,MAAMU,mBAAmB,GAAG,MAAO5E,IAAS,IAAK;IAC7C,IAAI;MACA,MAAM6E,MAAM,GAAG,MAAM7J,UAAU,CAACgF,IAAI,CAACC,OAAO,CAAC;MAC7C,IAAI4E,MAAM,CAACC,OAAO,KAAK,KAAK,EAAE;QAC1B7I,YAAY,CAACiI,SAAS,CAAC,2BAA2B,CAAC,EAAE,SAAS,CAAC;QAC/D1E,SAAS,CAAC,CAAC;MACf,CAAC,MAAM;QACHvD,YAAY,CAAC4I,MAAM,CAACE,YAAY,IAAIb,SAAS,CAAC,wBAAwB,CAAC,EAAE,OAAO,CAAC;MACrF;IACJ,CAAC,CAAC,OAAO1D,KAAK,EAAE;MACZ;MACAvE,YAAY,CAACiI,SAAS,CAAC,wBAAwB,CAAC,EAAE,OAAO,CAAC;IAE9D;EACJ,CAAC;EAED,MAAMc,YAAY,GAAG,MAAOrE,KAA0C,IAAK;IAAA,IAAAsE,mBAAA;IACzE,MAAMjF,IAAI,IAAAiF,mBAAA,GAAGtE,KAAK,CAACC,MAAM,CAAClB,KAAK,cAAAuF,mBAAA,uBAAlBA,mBAAA,CAAqB,CAAC,CAAC;IACpCC,OAAO,CAACC,GAAG,CAACnH,aAAa,EAAE,oBAAoB,CAAC;IAChD,IAAIgC,IAAI,EAAE;MACR,IAAI;QACF3D,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMpB,WAAW,CAAC+C,aAAa,EAAEgC,IAAI,EAAE3D,UAAU,CAAC;QAClDF,SAAS,CAAC,EAAE,CAAC;QAEXqD,SAAS,CAAC,CAAC;QACXvD,YAAY,CAACiI,SAAS,CAAC,6BAA6B,CAAC,EAAE,SAAS,CAAC;MACrE,CAAC,CAAC,OAAO1D,KAAK,EAAE;QAAA,IAAA4E,cAAA;QACZ,IAAIhB,OAAO,GAAGF,SAAS,CAAC,kCAAkC,CAAC;QAC7D,MAAMG,GAAG,GAAG7D,KAAY;QAExB,IAAI6D,GAAG,aAAHA,GAAG,gBAAAe,cAAA,GAAHf,GAAG,CAAE5E,QAAQ,cAAA2F,cAAA,eAAbA,cAAA,CAAed,IAAI,EAAE;UACvBF,OAAO,GACL,OAAOC,GAAG,CAAC5E,QAAQ,CAAC6E,IAAI,KAAK,QAAQ,GACjCD,GAAG,CAAC5E,QAAQ,CAAC6E,IAAI,GACjBD,GAAG,CAAC5E,QAAQ,CAAC6E,IAAI,CAACF,OAAO,IAAIA,OAAO;QAC5C;QACAnI,YAAY,CAACmI,OAAO,EAAE,OAAO,CAAC;MAChC,CAAC,SAAS;QACR/H,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC;EAGD,MAAMgJ,iBAAiB,GAAIhC,EAAQ,IAAK;IACpC6B,OAAO,CAACC,GAAG,CAAC9B,EAAE,EAAE,YAAY,CAAC;IAC7BpF,gBAAgB,CAACoF,EAAE,CAAC;EACxB,CAAC;EAED,MAAMiC,WAAW,GAAG,MAAOC,KAAW,IAAK;IACvCL,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAE9B,MAAMK,KAAK,GAAG,MAAM5K,mBAAmB,CAAC2K,KAAK,CAACtF,OAAO,EAAE5D,UAAU,CAAC;IAClEkC,aAAa,CAACiH,KAAK,CAAC;IACpBrI,UAAU,CAAC,IAAI,CAAC;IAChBkB,cAAc,CAACkH,KAAK,CAAC;EACzB,CAAC;EACD,MAAM;IAAEE,CAAC,EAAEvB;EAAU,CAAC,GAAG1L,cAAc,CAAC,CAAC;EAEzC,MAAMkN,WAAW,GAAGA,CAAA,KAAM;IACtBjH,OAAO,CAACD,IAAI,GAAC,EAAE,CAAC;IAChB;EACJ,CAAC;EAED,MAAMmH,eAAe,GAAGA,CAAA,KAAM;IAC1BlH,OAAO,CAACD,IAAI,GAAG,EAAE,IAAI,CAAC,GAAGA,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC;IACvC;EACJ,CAAC;EAED,oBAEE5C,OAAA;IAAKgK,SAAS,EAAC,WAAW;IAAAxE,QAAA,eACpBxF,OAAA;MAAKgK,SAAS,EAAC,iCAAiC;MAAAxE,QAAA,gBAC3CxF,OAAA;QAAKgK,SAAS,EAAC,YAAY;QAAAxE,QAAA,gBACrCxF,OAAA;UAAKgK,SAAS,EAAC,iBAAiB;UAAAxE,QAAA,gBACfxF,OAAA;YAAKgK,SAAS,EAAC,aAAa;YAAAxE,QAAA,GAAC,GAAC,EAAC8C,SAAS,CAAC,iBAAiB,CAAC;UAAA;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClEhG,OAAA;YAAKgK,SAAS,EAAC,gBAAgB;YAAAxE,QAAA,GAAC,GAAC,EAAClC,UAAU,EAAC,GAAC;UAAA;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC,eACOhG,OAAA;UAAKgK,SAAS,EAAC,kBAAkB;UAAAxE,QAAA,gBAC3BxF,OAAA,CAAC9B,SAAS;YACR+L,IAAI,EAAC,OAAO;YACZC,WAAW,EAAE5B,SAAS,CAAC,WAAW,CAAE;YACpCrD,KAAK,EAAE/B,UAAW;YAClBiH,QAAQ,EAAErF,kBAAmB;YAC7BsF,UAAU,EAAE;cACRC,cAAc,eACVrK,OAAA,CAAC7B,cAAc;gBAACmM,QAAQ,EAAC,OAAO;gBAACC,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAI,CAAE;gBAAAhF,QAAA,eAC7CxF,OAAA,CAACpB,UAAU;kBAAC2L,EAAE,EAAE;oBAAEE,QAAQ,EAAE;kBAAG;gBAAE;kBAAA5E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAExB,CAAE;YAEFuE,EAAE,EAAE;cAAElE,KAAK,EAAE;YAAQ;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eAClBhG,OAAA;YACO2F,OAAO,EAAEA,CAAA,KAAMxE,YAAY,CAAC,IAAI,CAAE;YACjC6I,SAAS,EAAC,oBAAoB;YAAAxE,QAAA,gBAErCxF,OAAA;cAAGgK,SAAS,EAAC;YAAiB;cAAAnE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BhG,OAAA;cAAAwF,QAAA,EAAO8C,SAAS,CAAC,aAAa;YAAC;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,YAAQ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACChG,OAAA,CAACzC,MAAM;QAACoK,IAAI,EAAEzG,SAAU;QAChB2G,OAAO,EAAEZ,WAAY;QACtB+C,SAAS,EAAC,iBAAiB;QAAAxE,QAAA,gBAE3BxF,OAAA,CAACvC,WAAW;UAACuM,SAAS,EAAC,uBAAuB;UAAAxE,QAAA,EAGzC8C,SAAS,CAAC,QAAQ;QAAC;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKX,CAAC,eACdhG,OAAA,CAACtC,aAAa;UAAA8H,QAAA,eACVxF,OAAA,CAACH,kBAAkB;YAC3CiI,gBAAgB,EAAEA,gBAAiB;YAAC1G,aAAa,EAAEA,aAAc;YAACC,gBAAgB,EAAEA;UAAiB;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAaqC,CAAC,eAChBhG,OAAA,CAACxC,aAAa;UAAAgI,QAAA,gBACVxF,OAAA,CAAC/C,MAAM;YAAC0I,OAAO,EAAEsB,WAAY;YAAE+C,SAAS,EAAC,iBAAiB;YAAAxE,QAAA,EACzD8C,SAAS,CAAC,QAAQ;UAAC;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACLhG,OAAA,CAAC/C,MAAM;YAAC+M,SAAS,EAAC,eAAe;YAC7BrE,OAAO,EAAE,MAAAA,CAAA,KAAY;cACjB,MAAMwC,YAAY,CAAC,CAAC;YACxB,CAAE;YAC9BuC,QAAQ,EAAE,CAACtJ,aAAa,IAAIA,aAAa,CAACgH,MAAM,KAAK,CAAE;YAAA5C,QAAA,EAEtD8C,SAAS,CAAC,QAAQ;UAAC;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE8B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,EACZxF,OAAO,gBACHR,OAAA;QAAKgK,SAAS,EAAC,cAAc;QAAAxE,QAAA,eAC1BxF,OAAA;UAClB2K,GAAG,EAAE7L,MAAO;UACf8L,GAAG,EAAC,SAAS;UACbZ,SAAS,EAAC;QAAqB;UAAAnE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACiB,CAAC,gBAENhG,OAAA,CAAAE,SAAA;QAAAsF,QAAA,gBACpBxF,OAAA,CAACrC,GAAG;UAACqM,SAAS,EAAC,gBAAgB;UAAAxE,QAAA,EAC1BlF,MAAM,CAAC8H,MAAM,KAAK,CAAC,IAAI,CAAC5H,OAAO,gBAC5BR,OAAA,CAAChC,UAAU;YACP6M,OAAO,EAAC,OAAO;YACfvE,KAAK,EAAC,QAAQ;YACdiE,EAAE,EAAE;cAAElE,KAAK,EAAE,MAAM;cAAEyE,OAAO,EAAE,MAAM;cAAEpF,KAAK,EAAE;YAAO,CAAE;YAAAF,QAAA,EAErD8C,SAAS,CAAC,iBAAiB;UAAC;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,GAEb1F,MAAM,CAAC6D,GAAG,CAAC,CAACwF,KAAK,EAAEoB,KAAK,kBACpB/K,OAAA,CAACpC,IAAI;YAAaoM,SAAS,EAAC,iBAAiB;YAAAxE,QAAA,gBACrBxF,OAAA,CAACrC,GAAG;cAACqM,SAAS,EAAC,eAAe;cAE9BgB,YAAY,EAAEA,CAAA,KAAMzI,UAAU,CAACoH,KAAK,CAACtF,OAAO,CAAE;cAC9C4G,YAAY,EAAEA,CAAA,KAAM1I,UAAU,CAAC,EAAE,CAAE;cAAAiD,QAAA,gBACnCxF,OAAA,CAACjC,SAAS;gBACNmN,SAAS,EAAC,KAAK;gBACfC,KAAK,EAAExB,KAAK,CAAClF,GAAG,IAAE,EAAG;gBACjBmG,GAAG,EAAEjB,KAAK,CAACpF,QAAQ,IAAI,OAAQ;gBAG/BoB,OAAO,EAAEA,CAAA,KAAI+D,WAAW,CAACC,KAAK,CAAE;gBAC/BK,SAAS,EAAE,mBAAmB1H,OAAO,KAAKqH,KAAK,CAACtF,OAAO,GAAG,kBAAkB,GAAG,EAAE;cAAG;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxF,CAAC,EAGA1D,OAAO,IAAIqH,KAAK,CAACtF,OAAO,iBACtBrE,OAAA,CAACrC,GAAG;gBAAC4M,EAAE,EAAE;kBACLD,QAAQ,EAAE,UAAU;kBACpB1D,OAAO,EAAE,MAAM;kBACfC,cAAc,EAAE,QAAQ;kBACxBC,UAAU,EAAE;gBAEhB,CAAE;gBACFnB,OAAO,EAAEA,CAAA,KAAI+D,WAAW,CAACC,KAAK,CAAE;gBAAAnE,QAAA,eAG5BxF,OAAA;kBAAK2K,GAAG,EAAEpL,YAAa;kBAACqL,GAAG,EAAC;gBAAe;kBAAA/E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAE7C,CAAE,eAMfhG,OAAA,CAAC3C,UAAU;gBACHsI,OAAO,EAAEA,CAAA,KAAMqD,mBAAmB,CAACW,KAAK,CAAE;gBAC1CK,SAAS,EAAC,iBAAiB;gBAAAxE,QAAA,eAE/BxF,OAAA;kBAAK2K,GAAG,EAAErL,KAAM;kBAACsL,GAAG,EAAC;gBAAQ;kBAAA/E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACNhG,OAAA,CAAClC,WAAW;cAACkM,SAAS,EAAC,kBAAkB;cAAAxE,QAAA,gBACrCxF,OAAA;gBAAKgK,SAAS,EAAC,gBAAgB;gBAAAxE,QAAA,EAAEmE,KAAK,CAACpF;cAAQ;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtDhG,OAAA;gBAAKgK,SAAS,EAAC,mBAAmB;gBAAAxE,QAAA,gBAElCxF,OAAA,CAACnC,OAAO;kBAACuN,KAAK,EAAC,eAAe;kBAACC,KAAK;kBAAA7F,QAAA,eACpCxF,OAAA,CAAC3C,UAAU;oBACHsI,OAAO,EAAGZ,KAAK,IAAK;sBAAA,IAAAuG,qBAAA;sBAChB7B,iBAAiB,CAACE,KAAK,CAACtF,OAAO,CAAC;sBACpCU,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEwG,eAAe,CAAC,CAAC;sBACxB,CAAAD,qBAAA,GAAAE,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC,cAAAH,qBAAA,uBAAtCA,qBAAA,CAAwCI,KAAK,CAAC,CAAC;oBACnD,CAAE;oBAAAlG,QAAA,eACMxF,OAAA;sBAAK2K,GAAG,EAAEnL,OAAQ;sBAACoL,GAAG,EAAC;oBAAS;sBAAA/E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAE1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAER,CAAC,eACVhG,OAAA,CAAC3C,UAAU;kBAACsI,OAAO,EAAEA,CAAA,KAAMgD,OAAO,CAACgB,KAAK,CAAClF,GAAG,CAAE;kBAAAe,QAAA,eACtDxF,OAAA;oBAAK2K,GAAG,EAAElL,IAAK;oBAACmL,GAAG,EAAC;kBAAM;oBAAA/E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACThG,OAAA;kBACI2L,IAAI,EAAC,MAAM;kBACXlE,EAAE,EAAC,aAAa;kBAChB7B,KAAK,EAAE;oBAAEgB,OAAO,EAAE;kBAAO,CAAE;kBAC3BgF,MAAM,EAAC,SAAS;kBAChBzB,QAAQ,EAAEf;gBAAa;kBAAAvD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA,GArE3B+E,KAAK;YAAAlF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAsEV,CACT;QACJ;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,EAE0B1E,OAAO,iBACHtB,OAAA,CAAC/B,KAAK;UACF0J,IAAI,EAAErG,OAAQ;UACduG,OAAO,EAAEA,CAAA,KAAM;YACXtG,UAAU,CAAC,KAAK,CAAC;YACjBkB,cAAc,CAACoJ,SAAS,CAAC;UAC7B,CAEC;UACD,mBAAgB,mBAAmB;UACnC,oBAAiB,yBAAyB;UAAArG,QAAA,eAE1CxF,OAAA,CAACrC,GAAG;YAACqM,SAAS,EAAC,qBAAqB;YAAAxE,QAAA,gBAEhBxF,OAAA,CAAC3C,UAAU;cACvB,cAAW,OAAO;cAClB2M,SAAS,EAAC,gBAAgB;cACtDrE,OAAO,EAAEA,CAAA,KAAM;gBACXpE,UAAU,CAAC,KAAK,CAAC;gBACjBkB,cAAc,CAACoJ,SAAS,CAAC;cAC7B,CAAE;cAAArG,QAAA,eAGlBxF,OAAA,CAACJ,SAAS;gBAAAiG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACW,CAAC,eAEehG,OAAA;cAAK2K,GAAG,EAAE,CAAAnI,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiC,GAAG,KAAI,EAAG;cAACmG,GAAG,EAAC,aAAa;cAACZ,SAAS,EAAC;YAAmB;cAAAnE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpFhG,OAAA;cAAKgK,SAAS,EAAC,uBAAuB;cAAAxE,QAAA,gBACtCxF,OAAA;gBAAKyH,EAAE,EAAC,yBAAyB;gBAACuC,SAAS,EAAC,qBAAqB;gBAAAxE,QAAA,EAC5DhD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE+B;cAAQ;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACNhG,OAAA;gBAAKgK,SAAS,EAAC,mBAAmB;gBAAAxE,QAAA,gBACtCxF,OAAA;kBAAKyH,EAAE,EAAC,yBAAyB;kBAAAjC,QAAA,EAC5B8C,SAAS,CAAC,cAAc;gBAAC;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACNhG,OAAA;kBAAAwF,QAAA,EACK,CAAA9C,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE0F,MAAM,IAAC,CAAC,GAAGE,SAAS,CAAC,2BAA2B,CAAC,GAAG5F;gBAAU;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACV,eAcLhG,OAAA,CAACrC,GAAG;UAACqM,SAAS,EAAC,kBAAkB;UAAAxE,QAAA,gBAC7BxF,OAAA;YAAAwF,QAAA,eACIxF,OAAA,CAAC/C,MAAM;cACH0I,OAAO,EAAEoE,eAAgB;cACzBvC,SAAS,eAAExH,OAAA,CAACN,aAAa;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC7B6E,OAAO,EAAC,WAAW;cACnBH,QAAQ,EAAE9H,IAAI,KAAK,CAAE;cACrBoH,SAAS,EAAC,eAAe;cAAAxE,QAAA,EAC3B8C,SAAS,CAAC,UAAU;YAAC;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEf;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACNhG,OAAA;YAAAwF,QAAA,eACIxF,OAAA,CAAC/C,MAAM;cACH0I,OAAO,EAAEmE,WAAY;cACrBgC,OAAO,eAAE9L,OAAA,CAACL,gBAAgB;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC9B6E,OAAO,EAAC,WAAW;cACnBH,QAAQ,EAAE9H,IAAI,GAAGE,KAAK,IAAIQ,UAAW;cACrC0G,SAAS,EAAC,eAAe;cAAAxE,QAAA,EAC3B8C,SAAS,CAAC,MAAM;YAAC;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA,eACR,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAElB,CAAC;AAAC5F,EAAA,CAxnBID,QAAkB;EAAA,QACKL,WAAW,EAwWXlD,cAAc;AAAA;AAAAmP,EAAA,GAzWrC5L,QAAkB;AA0nBxB,eAAeA,QAAQ;AAAC,IAAA4L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}