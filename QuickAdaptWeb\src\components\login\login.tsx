import React, { useState, useEffect } from 'react';
import { Con<PERSON>er, Box, Typography, TextField, Button, Link, IconButton, InputAdornment } from '@mui/material';
import Visibility from '@mui/icons-material/Visibility';
import { useAuth } from '../auth/AuthProvider';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import { LoginService } from "../../services/LoginService";
import { GetUserDetails, encryptPassword } from '../../services/UserService';
import { JSEncrypt } from 'jsencrypt';
import { useNavigate, useLocation } from "react-router-dom";
import { GetUserDetailsById } from '../../services/UserService';
import { LoginUserInfo } from '../../models/LoginUserInfo';
import jwt_decode from "jwt-decode";
import { getAllUsers } from '../../services/UserService';
import { Organization } from "../../models/Organization";
import { User } from "../../models/User";
import { User as Users, UserManager } from 'oidc-client-ts';
import { FormHelperText } from '@mui/material';
import { getOrganizationById } from '../../services/OrganizationService';
import { QuickAdopttext } from "../../assets/icons/icons";
import { checkSessionExpired } from '../../services/APIService';
import { Alert, CircularProgress } from '@mui/material';
//import { useAuth } from '../auth/AuthProvider';
let userLocalData: { [key: string]: any } = {}
let SAinitialsData: string;
let userDetails: User;
const Login: React.FC = () => {
  const { user } = useAuth();
    let UserId: string;
    let OrganizationId: string;
    const location = useLocation();
    const [showSessionExpiredAlert, setShowSessionExpiredAlert] = useState(false);
    const [isAutoLoggingIn, setIsAutoLoggingIn] = useState<boolean>(() => {
    // Check if we have auto-login parameters immediately
    const urlParams = new URLSearchParams(location.search);
    const autoLogin = urlParams.get('auto_login');
    const token = urlParams.get('token');
    const userId = urlParams.get('user_id');
    const orgId = urlParams.get('org_id');

    // If we have auto-login parameters, start in loading state
    return Boolean(autoLogin === 'true' && token && userId && orgId);
  });

  // Check if auto-login was already completed in this session
  const [autoLoginCompleted, setAutoLoginCompleted] = useState(() => {
    return sessionStorage.getItem('autoLoginCompleted') === 'true';
  });

  // Utility function to safely clear localStorage only when not in auto-login flow
  const safeLocalStorageClear = () => {
    const urlParams = new URLSearchParams(location.search);
    const hasAutoLoginParams = urlParams.has('token') && urlParams.has('userId') && urlParams.has('orgId');
    const isAutoLoginCompleted = sessionStorage.getItem('autoLoginCompleted') === 'true';

    if (!hasAutoLoginParams && !isAutoLoginCompleted && !isAutoLoggingIn) {
      localStorage.clear();
    }
  };

    useEffect(() => {
        // Check if user was redirected due to session expiration
        const sessionExpired = checkSessionExpired();
        if (sessionExpired) {
          setShowSessionExpiredAlert(true);
          
          // Auto-hide the alert after 5 seconds
          const timer = setTimeout(() => {
            setShowSessionExpiredAlert(false);
          }, 5000);
          
          return () => clearTimeout(timer);
        }
    }, []);
    
    const [showPassword, setShowPassword] = useState(false);
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [users, setUser] = useState<Users | null>(null);
    const [error, setError] = useState<string | null>(null);
    const [loginUserInfo, setLoginUserInfo] = useState<LoginUserInfo | undefined>(undefined);
    const [response, setresponse] = useState('');
    const [userIds, setuserId] = useState("");
    const [organizationDetails, setOrganizationDetails] = useState<Organization | null>(null);
    const [loginUserDetails, setUserDetails] = useState<User | null>(null);
    const handleClickShowPassword = () => {
        setShowPassword(!showPassword);
    };
    const { signOut, loggedOut } = useAuth();

    const handleEmailChange = (event: any) => {
        setEmail(event.target.value);
    };

    const handlePasswordChange = (event: any) => {
        setPassword(event.target.value);
    };
    const navigate = useNavigate();

useEffect(() => {
  const hasSessionExpired = checkSessionExpired();
  const token = localStorage.getItem("access_token");

  // Check if we're in auto-login flow
  const urlParams = new URLSearchParams(location.search);
  const hasAutoLoginParams = urlParams.has('token') && urlParams.has('user_id') && urlParams.has('org_id');

  // Don't redirect if we're in auto-login flow and it hasn't completed yet
  if (hasAutoLoginParams && !autoLoginCompleted) {
    return;
  }

  if (user && token && !hasSessionExpired) {
    navigate("/", { replace: true });
  }
}, [user, autoLoginCompleted, location.search]);

// Handle automatic login from free trial success
useEffect(() => {
  const handleAutoLogin = async () => {
    const urlParams = new URLSearchParams(location.search);
    const autoLogin = urlParams.get('auto_login');
    const token = urlParams.get('token');
    const email = urlParams.get('email');
    const userId = urlParams.get('user_id');
    const orgId = urlParams.get('org_id');
    const orgName = urlParams.get('org_name');



    if (autoLogin === 'true' && token && email && userId && orgId) {
      setIsAutoLoggingIn(true);

      try {
        // Create user object for localStorage - must match User interface
        const userData: User = {
          UserId: userId,
          EmailId: email,
          OrganizationId: orgId,
          FirstName: email.split('@')[0], // Extract first name from email
          LastName: '',
          UserType: 'Admin',
          UserName: email,
          Password: '',
          ContactNumber: '',
          Gender: '',
          DateofBirth: '',
          AdminDeactivated: false,
          EmailConfirmed: true,
          LoginType: 'Admin',
          ProfilePhoto: '',
          RTL: false,
          TimeZone: ''
        };

        // Create OIDC info object
        const oidcInfo = {
          access_token: token,
          token_type: 'Bearer',
          expires_in: 86400, // 24 hours
          scope: 'openid profile api1'
        };

        // Store authentication data
        localStorage.setItem("access_token", token);
        localStorage.setItem("userType", "Admin");

        // Check if this is a free trial token (non-JWT format)
        let isFreeTrialToken = false;
        try {
          // Try to decode as JWT
          jwt_decode(token);
        } catch (e) {
          // Not a JWT token, likely a free trial token
          isFreeTrialToken = true;
        }

        // Store free trial token flag for API service to use
        if (isFreeTrialToken) {
          sessionStorage.setItem('isFreeTrialToken', 'true');
        }

        const userLocalData: { [key: string]: any } = {};
        userLocalData["oidc-info"] = JSON.stringify(oidcInfo);
        userLocalData["user"] = JSON.stringify(userData);

        // For auto-login, skip the organization details API call to avoid 401 errors
        // The organization details can be fetched later after the user is fully logged in
        userLocalData["orgDetails"] = JSON.stringify({
          OrganizationId: orgId,
          OrganizationName: orgName || 'Organization'
        });

        localStorage.setItem("userInfo", JSON.stringify(userLocalData));

        // Set the global userDetails variable
        userDetails = userData;

        // Mark auto-login as completed and persist in sessionStorage
        setAutoLoginCompleted(true);
        sessionStorage.setItem('autoLoginCompleted', 'true');
        setIsAutoLoggingIn(false);

        // Clear URL parameters
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);

        // Add a small delay to ensure all localStorage operations are completed
        // before navigation to prevent race conditions
        setTimeout(() => {
          // Redirect to the accounts page with free-trial suffix for free trial users
          navigate(`/${orgId}/accounts/free-trial`, { replace: true });
        }, 100);

      } catch (error) {
        console.error('Auto login failed:', error);
        setError('Automatic login failed. Please try logging in manually.');
        setIsAutoLoggingIn(false);
        setAutoLoginCompleted(true);
      }
    } else {
      // No auto-login parameters, mark as completed and persist
      setAutoLoginCompleted(true);
      sessionStorage.setItem('autoLoginCompleted', 'true');
    }
  };

  handleAutoLogin();
}, [location.search, navigate]);

  // Cleanup sessionStorage on component unmount
  useEffect(() => {
    return () => {
      // Only clear if we're actually leaving the login page (not just re-rendering)
      if (!location.pathname.includes('/login')) {
        sessionStorage.removeItem('autoLoginCompleted');
      }
    };
  }, [location.pathname]);
    const handleSubmit = async () => {
        try {
            const organizationId = "1";
            const rememberLogin = true;
            const returnUrl = ""
            const authType = "admin"
            const tenantId = "web"
            if (password === '' || password == null) {
                setError('password should not be empty');
            }
            else if (email === '' || email == null) {
                setError('email should not be empty');
            }
            else {
                const isEncryptionEnabled = process.env.REACT_APP_ENABLE_ENCRYPTION === 'true';
                const publicKey = process.env.REACT_APP_PUBLIC_ENCRYPT_KEY || '';
                const encryptor = new JSEncrypt();
                encryptor.setPublicKey(publicKey);
                const now = new Date().toISOString();
                const encryptedPassword = encryptor.encrypt(password + '|' + now.trim()).toString();
                if (!encryptedPassword) {
                  console.error("Encryption failed");
                  return; 
                }
                const response = await LoginService(email, isEncryptionEnabled ? encryptedPassword : password, organizationId, rememberLogin, returnUrl, authType, tenantId);
                if (response.access_token) {
                    setresponse(response);
                    userLocalData["oidc-info"] = JSON.stringify(response)
                    localStorage.setItem("access_token", response.access_token)
                    const userResponse = await GetUserDetails();
                    setUserDetails(userResponse ? userResponse.data : null);
                    const firstNameInitials = userResponse?.data.FirstName && userResponse?.data.FirstName ? userResponse?.data.FirstName.substring(0, 1).toUpperCase() : '';
                    const lastNameinitials = userResponse?.data && userResponse?.data.LastName ? userResponse?.data.LastName.substring(0, 1).toUpperCase() : '';
                    const finalData = firstNameInitials + lastNameinitials;
                    SAinitialsData = finalData;
                    localStorage.setItem("userType", userResponse?.data?.UserType ?? "");
                    userLocalData["user"] = JSON.stringify(userResponse?.data);
                    const orgDetails = await getOrganizationById(userResponse?.data?.OrganizationId ?? "");
                    userLocalData["orgDetails"] = JSON.stringify(orgDetails);

                    localStorage.setItem("userInfo", JSON.stringify(userLocalData))
                    navigate("/");
                } else {
                    setError(response.error_description);
                }
            }
        }
        catch (error) {
            console.error('Login failed:');
            setError('An unexpected error occurred.'); // Handle unexpected errors
        }
    };
    useEffect(() =>
    {
        const firstNameInitials = userDetails?.FirstName &&  userDetails?.FirstName ? userDetails?.FirstName.substring(0, 1).toUpperCase() : '';
        const lastNameinitials =  userDetails?.LastName &&  userDetails?.LastName ? userDetails?.LastName.substring(0, 1).toUpperCase() : '';
        const finalData = firstNameInitials + lastNameinitials;
        SAinitialsData = finalData;
    }, [userDetails])
    
    async function GetLoginUserInfo(userResponse : User) {
        try {
            const firstNameInitials =  userResponse?.FirstName &&  userResponse?.FirstName ? userResponse?.FirstName.substring(0, 1).toUpperCase() : '';
            const lastNameinitials =  userResponse &&  userResponse?.LastName ? userResponse?.LastName.substring(0, 1).toUpperCase() : '';
            const finalData = firstNameInitials + lastNameinitials;
            SAinitialsData = finalData;
            localStorage.setItem("userType", userResponse?.UserType ?? "");
        } catch (error) {
            console.error('Error fetching user or organization details', error);
        }
    }
    useEffect(() => {
        // Skip token validation if we're in the middle of auto-login or auto-login hasn't completed yet
        if (isAutoLoggingIn || !autoLoginCompleted) {
            return;
        }

        // Also skip if we have auto-login URL parameters (to prevent interference)
        const urlParams = new URLSearchParams(location.search);
        const hasAutoLoginParams = urlParams.has('token') && urlParams.has('user_id') && urlParams.has('org_id');
        if (hasAutoLoginParams) {
            return;
        }

        // Skip if we're not on the login page (token validation should only run on login page)
        if (!location.pathname.includes('/login')) {
            return;
        }

        // Skip token validation if auto-login is in progress or completed
        if (isAutoLoggingIn || autoLoginCompleted) {
            return;
        }

        let token = localStorage.getItem("access_token");
		const userInfo = JSON.parse(localStorage.getItem("userInfo") || '{}');
		let storedUserDetails = null;

		if (userInfo['oidc-info'] && userInfo['user']) {
			storedUserDetails = JSON.parse(userInfo['user']);
			userDetails = storedUserDetails; // Update global variable
			try {
				const oidcInfo = JSON.parse(userInfo['oidc-info']);
				token = oidcInfo.access_token;
			} catch (error) {
				console.error('Error parsing oidc-info:', error);
			}
		} else if (userInfo['user'] && !userInfo['oidc-info']) {
			// Handle auto-login case where we have user data but no oidc-info
			storedUserDetails = JSON.parse(userInfo['user']);
			userDetails = storedUserDetails;
		}
        if (token) {
            try {
                // Try to decode as JWT first (for regular login tokens)
                const loggedinUserInfo = jwt_decode<LoginUserInfo>(token);
                setLoginUserInfo(loggedinUserInfo);
                // Only call GetLoginUserInfo if userDetails is available
                if (userDetails) {
                    GetLoginUserInfo(userDetails);
                }
                UserId = loggedinUserInfo.UserId;
            } catch (error) {
                // If JWT decode fails, check if we have userDetails from localStorage (auto-login flow)
                if (storedUserDetails) {
                    // Create LoginUserInfo from stored userDetails for auto-login flow
                    const autoLoginUserInfo: LoginUserInfo = {
                        EmailId: storedUserDetails.EmailId,
                        Name: storedUserDetails.FirstName + ' ' + storedUserDetails.LastName,
                        OrganizationId: storedUserDetails.OrganizationId,
                        UserId: storedUserDetails.UserId,
                        UserName: storedUserDetails.UserName,
                        UserType: storedUserDetails.UserType,
                        auth_time: Math.floor(Date.now() / 1000),
                        role: storedUserDetails.UserType
                    };
                    setLoginUserInfo(autoLoginUserInfo);
                    GetLoginUserInfo(storedUserDetails);
                    UserId = storedUserDetails.UserId;
                } else {
                    console.error('Token decode failed and no stored userDetails available:', error);
                    signOut();
                }
            }
        }
        else if (!storedUserDetails) {
            // Only sign out if we don't have stored user details (auto-login case)
            signOut();
        }

    }, [user, isAutoLoggingIn, autoLoginCompleted, location.search, location.pathname]);

    // Show loading state during auto login
    if (isAutoLoggingIn) {
        return (
            <Container maxWidth="sm" className="qadpt-superadminlogin">
                <Box mb={4} className="qadpt-brand-logo">
                    <img
                        src={QuickAdopttext}
                        alt="QuickAdopt Logo"
                        className="qadpt-brand-logo-img"
                    />
                </Box>
                <Box className="qadpt-welcome-message" style={{ textAlign: 'center', padding: '40px 0' }}>
                    <CircularProgress size={60} style={{ marginBottom: '20px' }} />
                    <Typography variant="h5" className="qadpt-welcome-message-text">
                        Welcome to QuickAdopt!
                    </Typography>
                    <Typography variant="body1" style={{ marginTop: '10px', color: '#6b7280' }}>
                        Setting up your free trial workspace...
                    </Typography>
                </Box>
            </Container>
        );
    }

    return (


        <Container maxWidth="sm" className="qadpt-superadminlogin">
            <Box mb={4} className="qadpt-brand-logo">
    <img
        src={QuickAdopttext}
        alt="QuickAdopt Logo"
        className="qadpt-brand-logo-img"
    />
            </Box>

            {showSessionExpiredAlert && (
          <Alert 
            severity="error" 
            sx={{ 
              marginBottom: -5,
                width: '50%',
                position: 'relative',
                alignContent: 'center',
                textAlign: 'center', // centers the text
                display: 'flex',
                justifyContent: 'center',
              
            }}
          >
            Your session has expired. Please log in again.
          </Alert>
        )}

            {showSessionExpiredAlert && (
          <Alert 
            severity="error" 
            sx={{ 
              marginBottom: -5,
                width: '50%',
                position: 'relative',
                alignContent: 'center',
                textAlign: 'center', // centers the text
                display: 'flex',
                justifyContent: 'center',
              
            }}
          >
            Your session has expired. Please log in again.
          </Alert>
        )}

            <Box className="qadpt-welcome-message">
                <Typography variant="h4" className="qadpt-welcome-message-text">
                    Welcome back
                </Typography>
            </Box>

            <Box className="qadpt-login-form">
                <Typography className="qadpt-form-label">
                    Email
                </Typography>
                <TextField
                    required
                    fullWidth
                    type="email"
                    id="email"
                    name="Email"
                    autoComplete="Email"
                    autoFocus
                    value={email}
                    onChange={handleEmailChange}
                    placeholder="eg, <EMAIL>"
                    className="qadpt-custom-input"
                />

                <Typography className="qadpt-form-label">
                    Password
                </Typography>
                <TextField
                    required
                    fullWidth
                    type={showPassword ? "text" : "password"}
                    id="password"
                    name="password"
                    autoComplete="password"                    
                    value={password}
                    onChange={handlePasswordChange}
                    placeholder="Enter your password"
                    className="qadpt-custom-input"
                    InputProps={{
                        endAdornment: (
                            <InputAdornment position="end">
                                <IconButton
                                    aria-label="toggle password visibility"
                                    onClick={handleClickShowPassword}
                                    edge="end"
                                >
                                    {/* {showPassword ? <VisibilityOff /> : <Visibility />} */}
                            <i className={`fal ${showPassword ? "fa-eye" : "fa-eye-slash"}`}></i>
                                </IconButton>
                            </InputAdornment>
                        ),
                    }}
                   
                />
                 {error && (
                    <FormHelperText error className="qadpt-text-danger">
                        {error}
                    </FormHelperText>
                )}

                <div className="qadpt-form-label">
                    <span onClick={() => window.open(`/forgotpassword`, '')}>
                        Forgot password?
                    </span>
                </div>
                <Button
                    type="button"
                    fullWidth
                    variant="contained"
                    className="qadpt-btn-default"
                    onClick={handleSubmit}
                > 
                        Continue
                </Button>
            </Box>

            <Box mt={12} className="qadpt-login-footer">
                <Typography variant="body2" className="qadpt-footer-text">
            <Link sx={{ cursor: "pointer" }} className="qadpt-footer-link">Terms of use</Link> |
            <Link sx={{ cursor: "pointer" }} className="qadpt-footer-link">Privacy Policy</Link>
                </Typography>
            </Box>
            </Container>
      
    );


}
export default Login;






//Code based login changes

//   const { signIn ,signOut,loggedOut} = useAuth();
//   useEffect(() => {
//     // Get the user from userManager
//     userManager.getUser().then(user => {
//       if (!user || user.expired) {
//         // If the user is not authenticated or the token is expired, redirect to the identity server login page
//         loggedOut ? signOut() :userManager.signinRedirect() ;        
//       }
//       else {        
//         userManager.signinRedirect();
//       }
//     });
//   }, []);

//   return null;
// };